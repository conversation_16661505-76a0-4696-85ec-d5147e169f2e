{"version": 3, "file": "defaults.js", "sources": ["../../../../../../packages/components/select-v2/src/defaults.ts"], "sourcesContent": ["import { placements } from '@popperjs/core'\nimport {\n  useAriaProps,\n  useEmptyValuesProps,\n  useSizeProp,\n} from '@element-plus/hooks'\nimport {\n  buildProps,\n  definePropType,\n  iconPropType,\n  isBoolean,\n  isNumber,\n} from '@element-plus/utils'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { useTooltipContentProps } from '@element-plus/components/tooltip'\nimport { ArrowDown, CircleClose } from '@element-plus/icons-vue'\nimport { tagProps } from '../../tag'\nimport { defaultProps } from './useProps'\nimport SelectV2 from './select.vue'\n\nimport type { Option, OptionType } from './select.types'\nimport type { Props } from './useProps'\nimport type { EmitFn } from '@element-plus/utils/vue/typescript'\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type {\n  Options,\n  Placement,\n  PopperEffect,\n} from '@element-plus/components/popper'\n\nexport const selectV2Props = buildProps({\n  /**\n   * @description whether creating new items is allowed. To use this, `filterable` must be true\n   */\n  allowCreate: Boolean,\n  /**\n   * @description autocomplete of select input\n   */\n  autocomplete: {\n    type: definePropType<'none' | 'both' | 'list' | 'inline'>(String),\n    default: 'none',\n  },\n  /**\n   * @description for non-filterable Select, this prop decides if the option menu pops up when the input is focused\n   */\n  automaticDropdown: Boolean,\n  /**\n   * @description whether select can be cleared\n   */\n  clearable: Boolean,\n  /**\n   * @description custom clear icon\n   */\n  clearIcon: {\n    type: iconPropType,\n    default: CircleClose,\n  },\n  /**\n   * @description tooltip theme, built-in theme: `dark` / `light`\n   */\n  effect: {\n    type: definePropType<PopperEffect>(String),\n    default: 'light',\n  },\n  /**\n   * @description whether to collapse tags to a text when multiple selecting\n   */\n  collapseTags: Boolean,\n  /**\n   * @description whether show all selected tags when mouse hover text of collapse-tags. To use this, `collapse-tags` must be true\n   */\n  collapseTagsTooltip: Boolean,\n  /**\n   * @description The max tags number to be shown. To use this, `collapse-tags` must be true\n   */\n  maxCollapseTags: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description\n   */\n  defaultFirstOption: Boolean,\n  /**\n   * @description is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description\n   */\n  estimatedOptionHeight: {\n    type: Number,\n    default: undefined,\n  },\n  /**\n   * @description whether Select is filterable\n   */\n  filterable: Boolean,\n  /**\n   * @description custom filter method, the first parameter is the current input value. To use this, `filterable` must be true\n   */\n  filterMethod: {\n    type: definePropType<(query: string) => void>(Function),\n  },\n  /**\n   * @description The height of the dropdown panel, 34px for each item\n   */\n  height: {\n    type: Number,\n    default: 274, // same as select dropdown menu\n  },\n  /**\n   * @description The height of the dropdown item\n   */\n  itemHeight: {\n    type: Number,\n    default: 34,\n  },\n  /**\n   * @description native input id\n   */\n  id: String,\n  /**\n   * @description whether Select is loading data from server\n   */\n  loading: Boolean,\n  /**\n   * @description displayed text while loading data from server, default is 'Loading'\n   */\n  loadingText: String,\n  /**\n   * @description biding value\n   */\n  modelValue: {\n    // eslint-disable-next-line prettier/prettier\n    type: definePropType<\n      any[] | string | number | boolean | Record<string, any> | any\n    >([Array, String, Number, Boolean, Object]),\n    default: undefined,\n  },\n  /**\n   * @description is multiple\n   */\n  multiple: Boolean,\n  /**\n   * @description maximum number of options user can select when multiple is true. No limit when set to 0\n   */\n  multipleLimit: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description the name attribute of select input\n   */\n  name: String,\n  /**\n   * @description displayed text when there is no options, you can also use slot empty, the default is 'No Data'\n   */\n  noDataText: String,\n  /**\n   * @description displayed text when no data matches the filtering query, you can also use slot `empty`, default is 'No matching data'\n   */\n  noMatchText: String,\n  /**\n   * @description function that gets called when the input value changes. Its parameter is the current input value. To use this, `filterable` must be true\n   */\n  remoteMethod: {\n    type: definePropType<(query: string) => void>(Function),\n  },\n  /**\n   * @description whether reserve the keyword after select filtered option.\n   */\n  reserveKeyword: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description data of the options, the key of `value` and `label` can be customize by `props`\n   */\n  options: {\n    type: definePropType<OptionType[]>(Array),\n    required: true,\n  },\n  /**\n   * @description placeholder, the default is 'Please select'\n   */\n  placeholder: {\n    type: String,\n  },\n  /**\n   * @description whether select dropdown is teleported, if `true` it will be teleported to where `append-to` sets\n   */\n  teleported: useTooltipContentProps.teleported,\n  /**\n   * @description when select dropdown is inactive and `persistent` is `false`, select dropdown will be destroyed\n   */\n  persistent: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description custom class name for Select's dropdown\n   */\n  popperClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description [popper.js](https://popper.js.org/docs/v2/) parameters\n   */\n  popperOptions: {\n    type: definePropType<Partial<Options>>(Object),\n    default: () => ({} as Partial<Options>),\n  },\n  /**\n   * @description whether search data from server\n   */\n  remote: Boolean,\n  /**\n   * @description size of component\n   */\n  size: useSizeProp,\n  /**\n   * @description configuration options, see the following table\n   */\n  props: {\n    type: definePropType<Props>(Object),\n    default: () => defaultProps,\n  },\n  /**\n   * @description unique identity key name for value, required when value is an object\n   */\n  valueKey: {\n    type: String,\n    default: 'value',\n  },\n  /**\n   * @description Controls whether the scrollbar is always displayed\n   */\n  scrollbarAlwaysOn: Boolean,\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description offset of the dropdown\n   */\n  offset: {\n    type: Number,\n    default: 12,\n  },\n  /**\n   * @description Determines whether the arrow is displayed\n   */\n  showArrow: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description position of dropdown\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: placements,\n    default: 'bottom-start',\n  },\n  /**\n   * @description list of possible positions for dropdown\n   */\n  fallbackPlacements: {\n    type: definePropType<Placement[]>(Array),\n    default: ['bottom-start', 'top-start', 'right', 'left'],\n  },\n  /**\n   * @description tag type\n   */\n  tagType: { ...tagProps.type, default: 'info' },\n  /**\n   * @description tag effect\n   */\n  tagEffect: { ...tagProps.effect, default: 'light' },\n  /**\n   * @description tabindex for input\n   */\n  tabindex: {\n    type: [String, Number],\n    default: 0,\n  },\n  /**\n   * @description which element the select dropdown appends to\n   */\n  appendTo: useTooltipContentProps.appendTo,\n  /**\n   * @description if it is `true`, the width of the dropdown panel is the same as the input box.\n   * if it is `false`, the width is automatically calculated based on the value of `label`,\n   * or it can be set to a number to make it a fixed width\n   */\n  fitInputWidth: {\n    type: [Boolean, Number],\n    default: true,\n    validator(val) {\n      return isBoolean(val) || isNumber(val)\n    },\n  },\n  suffixIcon: {\n    type: iconPropType,\n    default: ArrowDown,\n  },\n  ...useEmptyValuesProps,\n  ...useAriaProps(['ariaLabel']),\n} as const)\n\nexport const optionV2Props = buildProps({\n  data: Array,\n  disabled: Boolean,\n  hovering: Boolean,\n  item: {\n    type: definePropType<Option>(Object),\n    required: true,\n  },\n  index: Number,\n  style: Object,\n  selected: Boolean,\n  created: Boolean,\n} as const)\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nexport const selectV2Emits = {\n  [UPDATE_MODEL_EVENT]: (val: SelectV2Props['modelValue']) => true,\n  [CHANGE_EVENT]: (val: SelectV2Props['modelValue']) => true,\n  'remove-tag': (val: unknown) => true,\n  'visible-change': (visible: boolean) => true,\n  focus: (evt: FocusEvent) => evt instanceof FocusEvent,\n  blur: (evt: FocusEvent) => evt instanceof FocusEvent,\n  clear: () => true,\n}\nexport const optionV2Emits = {\n  hover: (index?: number) => isNumber(index),\n  select: (val: Option, index?: number) => true,\n}\n/* eslint-enable @typescript-eslint/no-unused-vars */\n\nexport type SelectV2Props = ExtractPropTypes<typeof selectV2Props>\nexport type SelectV2PropsPublic = __ExtractPublicPropTypes<typeof selectV2Props>\nexport type OptionV2Props = ExtractPropTypes<typeof optionV2Props>\nexport type OptionV2PropsPublic = __ExtractPublicPropTypes<typeof optionV2Props>\nexport type SelectV2EmitFn = EmitFn<typeof selectV2Emits>\nexport type OptionV2EmitFn = EmitFn<typeof optionV2Emits>\n\nexport type SelectV2Instance = InstanceType<typeof SelectV2> & unknown\n"], "names": ["buildProps", "definePropType", "iconPropType", "CircleClose", "useTooltipContentProps", "useSizeProp", "defaultProps", "placements", "tagProps", "isBoolean", "isNumber", "ArrowDown", "useEmptyValuesProps", "useAriaProps", "UPDATE_MODEL_EVENT", "CHANGE_EVENT"], "mappings": ";;;;;;;;;;;;;;;;;AAkBY,MAAC,aAAa,GAAGA,kBAAU,CAAC;AACxC,EAAE,WAAW,EAAE,OAAO;AACtB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,iBAAiB,EAAE,OAAO;AAC5B,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEC,iBAAY;AACtB,IAAI,OAAO,EAAEC,oBAAW;AACxB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAEF,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,mBAAmB,EAAE,OAAO;AAC9B,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,kBAAkB,EAAE,OAAO;AAC7B,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,qBAAqB,EAAE;AACzB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,EAAE,EAAE,MAAM;AACZ,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAClE,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,UAAU,EAAEG,8BAAsB,CAAC,UAAU;AAC/C,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAEH,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,IAAI,EAAEI,iBAAW;AACnB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEJ,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAMK,qBAAY;AAC/B,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,iBAAiB,EAAE,OAAO;AAC5B,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEL,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,EAAEM,eAAU;AACtB,IAAI,OAAO,EAAE,cAAc;AAC3B,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAEN,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC;AAC3D,GAAG;AACH,EAAE,OAAO,EAAE,EAAE,GAAGO,YAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE;AAChD,EAAE,SAAS,EAAE,EAAE,GAAGA,YAAQ,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;AACrD,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,QAAQ,EAAEJ,8BAAsB,CAAC,QAAQ;AAC3C,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;AAC3B,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,SAAS,CAAC,GAAG,EAAE;AACnB,MAAM,OAAOK,eAAS,CAAC,GAAG,CAAC,IAAIC,cAAQ,CAAC,GAAG,CAAC,CAAC;AAC7C,KAAK;AACL,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAER,iBAAY;AACtB,IAAI,OAAO,EAAES,kBAAS;AACtB,GAAG;AACH,EAAE,GAAGC,2BAAmB;AACxB,EAAE,GAAGC,oBAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,aAAa,GAAGb,kBAAU,CAAC;AACxC,EAAE,IAAI,EAAE,KAAK;AACb,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,OAAO,EAAE,OAAO;AAClB,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,CAACa,wBAAkB,GAAG,CAAC,GAAG,KAAK,IAAI;AACrC,EAAE,CAACC,kBAAY,GAAG,CAAC,GAAG,KAAK,IAAI;AAC/B,EAAE,YAAY,EAAE,CAAC,GAAG,KAAK,IAAI;AAC7B,EAAE,gBAAgB,EAAE,CAAC,OAAO,KAAK,IAAI;AACrC,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC1C,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB,EAAE;AACU,MAAC,aAAa,GAAG;AAC7B,EAAE,KAAK,EAAE,CAAC,KAAK,KAAKL,cAAQ,CAAC,KAAK,CAAC;AACnC,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK,IAAI;AAC9B;;;;;;;"}