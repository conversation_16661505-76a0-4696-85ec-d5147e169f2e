/*
 独立SSO调试脚本（仅本地开发用）
 用法一（命令行参数）：
   npm run sso:test -- --username=23374368 --password=20050626Zxy.
 用法二（环境变量）：
   SSO_USERNAME=23374368 SSO_PASSWORD='20050626Zxy.' npm run sso:test
 注意：不要把真实账号密码写入仓库；只通过命令行/环境变量传入。
*/

import 'dotenv/config';
import { ssoService } from '@/services/SSOService';
import { logger } from '@/utils/logger';

function getArg(name: string): string | undefined {
  const prefix = `--${name}=`;
  const found = process.argv.find(a => a.startsWith(prefix));
  return found ? found.slice(prefix.length) : undefined;
}

async function main() {
  const username = getArg('username') || (process.env as any)['SSO_USERNAME'];
  const password = getArg('password') || (process.env as any)['SSO_PASSWORD'];

  if (!username || !password) {
    console.error('缺少参数：请通过 --username= 学号 与 --password= 密码 或环境变量 SSO_USERNAME/SSO_PASSWORD 提供凭据');
    process.exit(1);
  }

  console.log('开始调试 SSO 获取信息 ...');

  // 直接调用现有服务
  const result = await ssoService.authenticateWithSSO(String(username), String(password));

  if (!result.success) {
    console.error('SSO 调用失败：', result.error);
    process.exit(2);
  }

  // 按你的标准：必须拿到用户信息才算成功
  if (!result.data) {
    console.error('SSO 返回无 data，视为失败');
    process.exit(3);
  }

  console.log('SSO 成功获取用户信息：');
  console.log(JSON.stringify(result.data, null, 2));
}

main().catch((e) => {
  logger.error('测试脚本发生错误:', e);
  process.exit(10);
});

