'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../utils/vue/props/runtime.js');
var icon = require('../../../utils/vue/icon.js');

const stepProps = runtime.buildProps({
  title: {
    type: String,
    default: ""
  },
  icon: {
    type: icon.iconPropType
  },
  description: {
    type: String,
    default: ""
  },
  status: {
    type: String,
    values: ["", "wait", "process", "finish", "error", "success"],
    default: ""
  }
});

exports.stepProps = stepProps;
//# sourceMappingURL=item.js.map
