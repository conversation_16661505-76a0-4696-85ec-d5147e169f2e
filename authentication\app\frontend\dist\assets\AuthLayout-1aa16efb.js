import{_ as e}from"./index-bcbc0702.js";import{r as s,A as t,aw as a,B as l,C as i,D as r,F as n,G as o,al as c,ay as d,H as p,ae as u,af as f,u as v,az as m,V as y,J as g,aA as h,ad as S,K as b}from"./element-plus-3ab68b46.js";const w={class:"auth-page"},k={key:0,class:"desktop-layout"},A={class:"auth-container"},_={class:"info-panel"},z={class:"info-content"},C={class:"logo-section"},D={class:"features-list"},j={class:"form-panel"},B={class:"form-content"},L={class:"form-header"},T={key:0,class:"progress-indicator"},q={class:"step-number"},x={class:"form-body"},E={key:1,class:"mobile-layout"},$={class:"mobile-header"},F={class:"header-bg"},G={class:"logo-section"},H={key:0,class:"mobile-progress"},I={class:"progress-bar"},J={class:"step-info"},K={class:"step-title"},N={class:"mobile-content"},U={class:"form-container"},V={key:0,class:"mobile-form-header"},W=e({__name:"AuthLayout",props:{title:{type:String,default:"北航身份认证"},subtitle:{type:String,default:"Beijing University of Aeronautics and Astronautics"},pageTitle:{type:String,required:!0},pageDescription:{type:String,required:!0},iconComponent:{type:String,default:"School"},features:{type:Array,default:()=>["安全可靠的身份验证","统一身份认证系统","自动获取学籍信息"]},steps:{type:Array,default:()=>[]},currentStep:{type:Number,default:0},showSteps:{type:Boolean,default:!1}},setup(e){const W=s(!1),M=()=>{W.value=window.innerWidth<=768};return t(()=>{M(),window.addEventListener("resize",M)}),a(()=>{window.removeEventListener("resize",M)}),(s,t)=>{const a=b;return l(),i("div",w,[W.value?(l(),i("div",E,[r("div",$,[r("div",F,[r("div",G,[n(a,{size:"32",color:"#ffffff"},{default:o(()=>[(l(),c(d(e.iconComponent)))]),_:1}),r("h1",null,p(e.title),1)]),e.showSteps?(l(),i("div",H,[r("div",I,[r("div",{class:"progress-fill",style:S({width:(e.currentStep+1)/e.steps.length*100+"%"})},null,4)]),r("div",J,[r("span",null,"步骤 "+p(e.currentStep+1)+" / "+p(e.steps.length),1),r("span",K,p(e.steps[e.currentStep]),1)])])):g("",!0)])]),r("div",N,[r("div",U,[e.showSteps?g("",!0):(l(),i("div",V,[r("h2",null,p(e.pageTitle),1),r("p",null,p(e.pageDescription),1)])),h(s.$slots,"default",{},void 0,!0)])])])):(l(),i("div",k,[r("div",A,[r("div",_,[r("div",z,[r("div",C,[n(a,{size:"48",color:"#ffffff"},{default:o(()=>[(l(),c(d(e.iconComponent)))]),_:1}),r("h1",null,p(e.title),1),r("p",null,p(e.subtitle),1)]),r("div",D,[(l(!0),i(u,null,f(e.features,e=>(l(),i("div",{class:"feature-item",key:e},[n(a,{size:"20",color:"#ffffff"},{default:o(()=>[n(v(m))]),_:1}),r("span",null,p(e),1)]))),128))]),t[0]||(t[0]=r("div",{class:"decorative-elements"},[r("div",{class:"circle circle-1"}),r("div",{class:"circle circle-2"}),r("div",{class:"circle circle-3"})],-1))])]),r("div",j,[r("div",B,[r("div",L,[r("h2",null,p(e.pageTitle),1),r("p",null,p(e.pageDescription),1)]),e.showSteps?(l(),i("div",T,[(l(!0),i(u,null,f(e.steps,(s,t)=>(l(),i("div",{class:y(["step",{active:e.currentStep>=t,completed:e.currentStep>t}]),key:t},[r("div",q,p(t+1),1),r("span",null,p(s),1)],2))),128))])):g("",!0),r("div",x,[h(s.$slots,"default",{},void 0,!0)])])])])]))])}}},[["__scopeId","data-v-abc33738"]]);export{W as A};
