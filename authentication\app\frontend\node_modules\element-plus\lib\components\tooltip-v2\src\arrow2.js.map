{"version": 3, "file": "arrow2.js", "sources": ["../../../../../../packages/components/tooltip-v2/src/arrow.vue"], "sourcesContent": ["<template>\n  <span ref=\"arrowRef\" :style=\"arrowStyle\" :class=\"ns.e('arrow')\" />\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject } from 'vue'\nimport { tooltipV2ContentKey, tooltipV2RootKey } from './constants'\nimport { tooltipV2ArrowProps, tooltipV2ArrowSpecialProps } from './arrow'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElTooltipV2Arrow',\n})\n\nconst props = defineProps({\n  ...tooltipV2ArrowProps,\n  ...tooltipV2ArrowSpecialProps,\n})\n\nconst { ns } = inject(tooltipV2RootKey)!\nconst { arrowRef } = inject(tooltipV2ContentKey)!\n\nconst arrowStyle = computed<CSSProperties>(() => {\n  const { style, width, height } = props\n  const namespace = ns.namespace.value\n\n  return {\n    [`--${namespace}-tooltip-v2-arrow-width`]: `${width}px`,\n    [`--${namespace}-tooltip-v2-arrow-height`]: `${height}px`,\n    [`--${namespace}-tooltip-v2-arrow-border-width`]: `${width / 2}px`,\n    [`--${namespace}-tooltip-v2-arrow-cover-width`]: width / 2 - 1,\n    ...(style || {}),\n  }\n})\n</script>\n"], "names": ["inject", "tooltipV2RootKey", "tooltipV2ContentKey", "computed"], "mappings": ";;;;;;;;;uCAWc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;AAOA,IAAA,MAAM,EAAE,EAAA,EAAO,GAAAA,UAAA,CAAOC,0BAAgB,CAAA,CAAA;AACtC,IAAA,MAAM,EAAE,QAAA,EAAa,GAAAD,UAAA,CAAOE,6BAAmB,CAAA,CAAA;AAE/C,IAAM,MAAA,UAAA,GAAaC,aAAwB,MAAM;AAC/C,MAAA,MAAM,EAAE,KAAA,EAAO,KAAO,EAAA,MAAA,EAAW,GAAA,KAAA,CAAA;AACjC,MAAM,MAAA,SAAA,GAAY,GAAG,SAAU,CAAA,KAAA,CAAA;AAE/B,MAAO,OAAA;AAAA,QACL,CAAC,CAAK,EAAA,EAAA,SAAS,CAAyB,uBAAA,CAAA,GAAG,GAAG,KAAK,CAAA,EAAA,CAAA;AAAA,QACnD,CAAC,CAAK,EAAA,EAAA,SAAS,CAA0B,wBAAA,CAAA,GAAG,GAAG,MAAM,CAAA,EAAA,CAAA;AAAA,QACrD,CAAC,CAAK,EAAA,EAAA,SAAS,gCAAgC,GAAG,CAAA,EAAG,QAAQ,CAAC,CAAA,EAAA,CAAA;AAAA,QAC9D,CAAC,CAAK,EAAA,EAAA,SAAS,CAA+B,6BAAA,CAAA,GAAG,QAAQ,CAAI,GAAA,CAAA;AAAA,QAC7D,GAAI,SAAS,EAAC;AAAA,OAChB,CAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;"}