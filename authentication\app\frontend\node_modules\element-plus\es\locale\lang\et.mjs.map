{"version": 3, "file": "et.mjs", "sources": ["../../../../../packages/locale/lang/et.ts"], "sourcesContent": ["export default {\n  name: 'et',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON><PERSON>',\n      today: '<PERSON>ä<PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON>h<PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: '<PERSON><PERSON> kuupäev',\n      selectTime: '<PERSON><PERSON> kellaaeg',\n      startDate: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n      startTime: 'Algusaeg',\n      endDate: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ev',\n      endTime: '<PERSON><PERSON><PERSON>aeg',\n      prevYear: 'Eelmine aasta',\n      nextYear: '<PERSON><PERSON><PERSON><PERSON> aasta',\n      prevMonth: 'Eelmine kuu',\n      nextMonth: '<PERSON><PERSON><PERSON><PERSON> kuu',\n      year: '',\n      month1: 'Jaanuar',\n      month2: 'Veebruar',\n      month3: 'Märts',\n      month4: 'Aprill',\n      month5: 'Mai',\n      month6: '<PERSON><PERSON>',\n      month7: '<PERSON><PERSON>',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Oktoober',\n      month11: 'November',\n      month12: 'Detsember',\n      // week: 'nädal',\n      weeks: {\n        sun: 'P',\n        mon: 'E',\n        tue: 'T',\n        wed: 'K',\n        thu: 'N',\n        fri: 'R',\n        sat: 'L',\n      },\n      months: {\n        jan: 'Jaan',\n        feb: 'Veeb',\n        mar: 'Mär',\n        apr: 'Apr',\n        may: 'Mai',\n        jun: 'Juun',\n        jul: 'Juul',\n        aug: 'Aug',\n        sep: 'Sept',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dets',\n      },\n    },\n    select: {\n      loading: 'Laadimine',\n      noMatch: 'Sobivad andmed puuduvad',\n      noData: 'Andmed puuduvad',\n      placeholder: 'Vali',\n    },\n    mention: {\n      loading: 'Laadimine',\n    },\n    cascader: {\n      noMatch: 'Sobivad andmed puuduvad',\n      loading: 'Laadimine',\n      placeholder: 'Vali',\n      noData: 'Andmed puuduvad',\n    },\n    pagination: {\n      goto: 'Mine lehele',\n      pagesize: '/page',\n      total: 'Kokku {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Teade',\n      confirm: 'OK',\n      cancel: 'Tühista',\n      error: 'Vigane sisend',\n    },\n    upload: {\n      deleteTip: 'Vajuta \"Kustuta\", et eemaldada',\n      delete: 'Kustuta',\n      preview: 'Eelvaate',\n      continue: 'Jätka',\n    },\n    table: {\n      emptyText: 'Andmed puuduvad',\n      confirmFilter: 'Kinnita',\n      resetFilter: 'Taasta',\n      clearFilter: 'Kõik',\n      sumText: 'Summa',\n    },\n    tree: {\n      emptyText: 'Andmed puuduvad',\n    },\n    transfer: {\n      noMatch: 'Sobivad andmed puuduvad',\n      noData: 'Andmed puuduvad',\n      titles: ['Loend 1', 'Loend 2'],\n      filterPlaceholder: 'Sisesta märksõna',\n      noCheckedFormat: '{total} objekti',\n      hasCheckedFormat: '{checked}/{total} valitud',\n    },\n    image: {\n      error: 'Ebaõnnestus',\n    },\n    pageHeader: {\n      title: 'Tagasi',\n    },\n    popconfirm: {\n      confirmButtonText: 'Jah',\n      cancelButtonText: 'Ei',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,aAAa;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,QAAQ;AACnB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,iBAAiB;AACnC,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,iBAAiB;AAC/B,MAAM,WAAW,EAAE,MAAM;AACzB,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,WAAW;AAC1B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,MAAM,EAAE,iBAAiB;AAC/B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,KAAK,EAAE,eAAe;AAC5B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,gCAAgC;AACjD,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,aAAa,EAAE,SAAS;AAC9B,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,iBAAiB;AAClC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,iBAAiB;AAC/B,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,wBAAwB;AACjD,MAAM,eAAe,EAAE,iBAAiB;AACxC,MAAM,gBAAgB,EAAE,2BAA2B;AACnD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,gBAAgB;AAC7B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}