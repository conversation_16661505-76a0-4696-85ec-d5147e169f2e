import{_ as e}from"./index-bcbc0702.js";/* empty css                 */import{a7 as a,r as t,A as l,aw as r,R as s,B as i,C as o,F as n,D as c,V as d,H as u,J as v,G as p,I as g,E as m,an as h,K as f,L as y}from"./element-plus-3ab68b46.js";const w={name:"CameraCapture",components:{Camera:a},emits:["captured","error"],props:{showWatermark:{type:Boolean,default:!0},overlayText:{type:String,default:""}},setup(e,{emit:a}){const s=t(null),i=t(null),o=t(!0),n=t(!1),c=t(!1),d=t(""),u=t(navigator.userAgent);let v=null,p=null;const g=()=>{const e=new Date,a=e=>e<10?"0"+e:""+e;d.value=`${e.getFullYear()}-${a(e.getMonth()+1)}-${a(e.getDate())} ${a(e.getHours())}:${a(e.getMinutes())}:${a(e.getSeconds())}`};return l(()=>{(async()=>{try{v=await navigator.mediaDevices.getUserMedia({video:{width:{ideal:640},height:{ideal:480},facingMode:"user"}}),s.value&&(s.value.srcObject=v,s.value.onloadedmetadata=()=>{n.value=!0}),g(),p=setInterval(g,1e3)}catch(e){o.value=!1,a("error",e)}})()}),r(()=>{v&&(v.getTracks().forEach(e=>e.stop()),v=null),p&&(clearInterval(p),p=null)}),{videoRef:s,canvasRef:i,hasCamera:o,isReady:n,isCapturing:c,nowString:d,deviceModel:u,capturePhoto:()=>{if(s.value&&i.value&&n.value){c.value=!0;try{const t=s.value,l=i.value,r=l.getContext("2d");l.width=t.videoWidth,l.height=t.videoHeight,r.drawImage(t,0,0,l.width,l.height),e.showWatermark&&(r.fillStyle="rgba(0,0,0,0.6)",r.fillRect(10,l.height-60,380,50),r.fillStyle="#fff",r.font="16px sans-serif",r.fillText(d.value,20,l.height-35),r.fillText(u.value.slice(0,40),20,l.height-15));const o=l.toDataURL("image/jpeg",.9),n={timestamp:(new Date).toISOString(),deviceModel:u.value};a("captured",{image:o,capture:n}),m.success("拍照成功")}catch(t){m.error("拍照失败"),a("error",t)}finally{c.value=!1}}},showWatermark:e.showWatermark}}},C={class:"camera-capture"},k={key:0,class:"no-camera"},x={key:1,class:"camera-container"},R={class:"preview-wrapper"},S={key:0,class:"watermark"},b={key:0},M={ref:"canvasRef",style:{display:"none"}},_={class:"camera-controls"};const D=e(w,[["render",function(e,a,t,l,r,m){const w=h,D=s("Camera"),I=f,T=y;return i(),o("div",C,[l.hasCamera?(i(),o("div",x,[c("div",R,[c("video",{ref:"videoRef",class:d({"camera-video":!0,capturing:l.isCapturing}),autoplay:"",playsinline:"",muted:""},null,2),l.showWatermark?(i(),o("div",S,[c("div",null,u(l.nowString),1),t.overlayText?(i(),o("div",b,u(t.overlayText),1)):v("",!0),c("div",null,u(l.deviceModel),1)])):v("",!0)]),c("canvas",M,null,512),c("div",_,[n(T,{type:"primary",size:"large",loading:l.isCapturing,onClick:l.capturePhoto,disabled:!l.isReady},{default:p(()=>[n(I,null,{default:p(()=>[n(D)]),_:1}),a[0]||(a[0]=g(" 拍照 ",-1))]),_:1,__:[0]},8,["loading","onClick","disabled"])]),a[1]||(a[1]=c("div",{class:"camera-tips"},[c("p",null,"请确保面部清晰可见，点击拍照按钮进行拍摄")],-1))])):(i(),o("div",k,[n(w,{title:"无法访问摄像头",description:"请确保您的设备有摄像头并允许浏览器访问",type:"error",closable:!1,"show-icon":""})]))])}],["__scopeId","data-v-e1db6b18"]]);export{D as C};
