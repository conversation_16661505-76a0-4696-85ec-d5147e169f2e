import{_ as a,u as e,b as t}from"./index-bcbc0702.js";/* empty css                *//* empty css                 *//* empty css                   *//* empty css               */import{r as s,p as i,A as n,B as l,C as o,D as u,F as c,G as r,E as p,X as d,I as v,H as m,al as _,J as y,ae as f,af as g,z as h,b7 as k,L as w,ap as A,bn as b,bo as j,bm as C}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const E={class:"page-container"},R={class:"status-container"},H={class:"card-header"},I={class:"header-tags"},x={class:"actions"},z={class:"meta"},D={class:"audit-row"},L={class:"op"},T={key:0,class:"comment"},O=a({__name:"ApplicationStatus",setup(a){const O=e(),P=s(!1),S=s("none"),V=s(!1),q=s(!1),B=s(!1),F=s(!1),G=s({}),J=s(""),N=i(()=>({external:"外校认证",invite:"邀请码认证",buaa:"本校认证",freshman:"新生认证"}[J.value]||"")),X=i(()=>({none:"无记录",pending:"待审核",rejected:"已驳回",approved:"已通过",revoked:"已撤回"}[S.value]||S.value)),K=i(()=>({none:"info",pending:"warning",rejected:"danger",approved:"success",revoked:"info"}[S.value]||"info"));async function M(){var a,e,s,i,n,l,o;try{P.value=!0;const{applicationApi:u}=await t(()=>import("./application-7212cdc3.js"),["assets/application-7212cdc3.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css","assets/userSession-03354358.js"]),c=await u.getStatus();(null==c?void 0:c.success)&&(S.value=(null==(a=c.data)?void 0:a.status)||"none",V.value=!!(null==(e=c.data)?void 0:e.canCancel),q.value=!!(null==(s=c.data)?void 0:s.canRevoke),B.value=!!(null==(i=c.data)?void 0:i.canEdit),F.value=!!(null==(n=c.data)?void 0:n.canResubmit),J.value=(null==(l=c.data)?void 0:l.type)||"",G.value=(null==(o=c.data)?void 0:o.meta)||{})}catch(u){p.error("状态查询失败")}finally{P.value=!1}}function Q(){O.push({name:"Resubmit"})}async function U(){try{const{applicationApi:a}=await t(()=>import("./application-7212cdc3.js"),["assets/application-7212cdc3.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css","assets/userSession-03354358.js"]),e=await a.cancel("用户取消");(null==e?void 0:e.success)?(p.success("已取消"),await M()):p.error((null==e?void 0:e.message)||"取消失败")}catch(a){p.error("取消失败")}}async function W(){try{await h.confirm("确认撤回？此操作不可逆","提示",{type:"warning"});const{applicationApi:a}=await t(()=>import("./application-7212cdc3.js"),["assets/application-7212cdc3.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css","assets/userSession-03354358.js"]),e=await a.revoke("用户撤回");(null==e?void 0:e.success)?(p.success("已撤回"),await M()):p.error((null==e?void 0:e.message)||"撤回失败")}catch(a){}}function Y(){"external"===J.value?O.push({name:"ExternalAuth"}):"invite"===J.value?O.push({name:"InviteAuth"}):p.info("当前类型不支持在线编辑")}return n(M),(a,e)=>{const t=k,s=w,i=A,n=C,p=b,h=j,O=d;return l(),o("div",E,[u("div",R,[c(O,{class:"status-card",shadow:"hover"},{header:r(()=>[u("div",H,[e[0]||(e[0]=u("span",null,"申请状态",-1)),u("div",I,[c(t,{type:K.value,size:"large"},{default:r(()=>[v(m(X.value),1)]),_:1},8,["type"]),c(t,{type:"info",size:"large"},{default:r(()=>[v(m(N.value),1)]),_:1})])])]),default:r(()=>[u("div",x,[c(s,{onClick:M,loading:P.value},{default:r(()=>e[1]||(e[1]=[v("刷新",-1)])),_:1,__:[1]},8,["loading"]),B.value?(l(),_(s,{key:0,type:"primary",onClick:Y},{default:r(()=>e[2]||(e[2]=[v("去编辑",-1)])),_:1,__:[2]})):y("",!0),F.value?(l(),_(s,{key:1,type:"warning",onClick:Q},{default:r(()=>e[3]||(e[3]=[v("去重提",-1)])),_:1,__:[3]})):y("",!0),V.value?(l(),_(s,{key:2,type:"danger",plain:"",onClick:U},{default:r(()=>e[4]||(e[4]=[v("取消申请",-1)])),_:1,__:[4]})):y("",!0),q.value?(l(),_(s,{key:3,type:"danger",onClick:W},{default:r(()=>e[5]||(e[5]=[v("撤回认证",-1)])),_:1,__:[5]})):y("",!0)]),c(i),u("div",z,[c(t,{type:"info",effect:"plain"},{default:r(()=>[v("重提次数："+m(G.value.resubmissionCount||0),1)]),_:1}),c(t,{type:G.value.isResubmission?"warning":"success",effect:"plain"},{default:r(()=>[v(" 重提交："+m(G.value.isResubmission?"是":"否"),1)]),_:1},8,["type"]),G.value.auditHistory&&G.value.auditHistory.length?y("",!0):(l(),_(n,{key:0,description:"暂无审核记录"}))]),G.value.auditHistory&&G.value.auditHistory.length?(l(),_(h,{key:0},{default:r(()=>[(l(!0),o(f,null,g(G.value.auditHistory,(e,s)=>{return l(),_(p,{key:s,type:e.type||a.mapActionType(e.action),timestamp:(i=e.time,i?new Date(i).toLocaleString("zh-CN"):"")},{default:r(()=>[u("div",D,[c(t,{size:"small",type:a.mapActionType(e.action),effect:"plain"},{default:r(()=>[v(m(e.action),1)]),_:2},1032,["type"]),u("span",L,m(e.operator||"系统"),1),e.comment?(l(),o("span",T,"- "+m(e.comment),1)):y("",!0)])]),_:2},1032,["type","timestamp"]);var i}),128))]),_:1})):y("",!0)]),_:1})])])}}},[["__scopeId","data-v-0fb9260d"]]);export{O as default};
