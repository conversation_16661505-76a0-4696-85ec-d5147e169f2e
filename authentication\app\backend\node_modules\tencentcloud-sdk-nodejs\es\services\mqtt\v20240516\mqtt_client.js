import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("mqtt.tencentcloudapi.com", "2024-05-16", clientConfig);
    }
    async CreateDeviceIdentity(req, cb) {
        return this.request("CreateDeviceIdentity", req, cb);
    }
    async PublishMessage(req, cb) {
        return this.request("PublishMessage", req, cb);
    }
    async DescribeUserList(req, cb) {
        return this.request("DescribeUserList", req, cb);
    }
    async CreateInsPublicEndpoint(req, cb) {
        return this.request("CreateInsPublicEndpoint", req, cb);
    }
    async ModifyTopic(req, cb) {
        return this.request("ModifyTopic", req, cb);
    }
    async DeleteCaCertificate(req, cb) {
        return this.request("DeleteCaCertificate", req, cb);
    }
    async DeactivateDeviceCertificate(req, cb) {
        return this.request("DeactivateDeviceCertificate", req, cb);
    }
    async DescribeTopic(req, cb) {
        return this.request("DescribeTopic", req, cb);
    }
    async DeactivateCaCertificate(req, cb) {
        return this.request("DeactivateCaCertificate", req, cb);
    }
    async CreateJWTAuthenticator(req, cb) {
        return this.request("CreateJWTAuthenticator", req, cb);
    }
    async DeleteDeviceCertificate(req, cb) {
        return this.request("DeleteDeviceCertificate", req, cb);
    }
    async DeleteInstance(req, cb) {
        return this.request("DeleteInstance", req, cb);
    }
    async DescribeMessageByTopic(req, cb) {
        return this.request("DescribeMessageByTopic", req, cb);
    }
    async ModifyJWTAuthenticator(req, cb) {
        return this.request("ModifyJWTAuthenticator", req, cb);
    }
    async ModifyJWKSAuthenticator(req, cb) {
        return this.request("ModifyJWKSAuthenticator", req, cb);
    }
    async DescribeDeviceCertificates(req, cb) {
        return this.request("DescribeDeviceCertificates", req, cb);
    }
    async DescribeInsPublicEndpoints(req, cb) {
        return this.request("DescribeInsPublicEndpoints", req, cb);
    }
    async ModifyInsPublicEndpoint(req, cb) {
        return this.request("ModifyInsPublicEndpoint", req, cb);
    }
    async DescribeDeviceCertificate(req, cb) {
        return this.request("DescribeDeviceCertificate", req, cb);
    }
    async RegisterDeviceCertificate(req, cb) {
        return this.request("RegisterDeviceCertificate", req, cb);
    }
    async ModifyInstanceCertBinding(req, cb) {
        return this.request("ModifyInstanceCertBinding", req, cb);
    }
    async DescribeInsVPCEndpoints(req, cb) {
        return this.request("DescribeInsVPCEndpoints", req, cb);
    }
    async DescribeClientList(req, cb) {
        return this.request("DescribeClientList", req, cb);
    }
    async DescribeProductSKUList(req, cb) {
        return this.request("DescribeProductSKUList", req, cb);
    }
    async DescribeCaCertificates(req, cb) {
        return this.request("DescribeCaCertificates", req, cb);
    }
    async UpdateAuthorizationPolicyPriority(req, cb) {
        return this.request("UpdateAuthorizationPolicyPriority", req, cb);
    }
    async DeleteDeviceIdentity(req, cb) {
        return this.request("DeleteDeviceIdentity", req, cb);
    }
    async CreateTopic(req, cb) {
        return this.request("CreateTopic", req, cb);
    }
    async ActivateDeviceCertificate(req, cb) {
        return this.request("ActivateDeviceCertificate", req, cb);
    }
    async DescribeAuthorizationPolicies(req, cb) {
        return this.request("DescribeAuthorizationPolicies", req, cb);
    }
    async DescribeTopicList(req, cb) {
        return this.request("DescribeTopicList", req, cb);
    }
    async DeleteInsPublicEndpoint(req, cb) {
        return this.request("DeleteInsPublicEndpoint", req, cb);
    }
    async CreateInstance(req, cb) {
        return this.request("CreateInstance", req, cb);
    }
    async DescribeInstanceList(req, cb) {
        return this.request("DescribeInstanceList", req, cb);
    }
    async RevokedDeviceCertificate(req, cb) {
        return this.request("RevokedDeviceCertificate", req, cb);
    }
    async DescribeInstance(req, cb) {
        return this.request("DescribeInstance", req, cb);
    }
    async DescribeMessageDetails(req, cb) {
        return this.request("DescribeMessageDetails", req, cb);
    }
    async DescribeDeviceIdentity(req, cb) {
        return this.request("DescribeDeviceIdentity", req, cb);
    }
    async DeleteTopic(req, cb) {
        return this.request("DeleteTopic", req, cb);
    }
    async DescribeCaCertificate(req, cb) {
        return this.request("DescribeCaCertificate", req, cb);
    }
    async CreateJWKSAuthenticator(req, cb) {
        return this.request("CreateJWKSAuthenticator", req, cb);
    }
    async ModifyUser(req, cb) {
        return this.request("ModifyUser", req, cb);
    }
    async DeleteUser(req, cb) {
        return this.request("DeleteUser", req, cb);
    }
    async ModifyAuthorizationPolicy(req, cb) {
        return this.request("ModifyAuthorizationPolicy", req, cb);
    }
    async DeleteAuthorizationPolicy(req, cb) {
        return this.request("DeleteAuthorizationPolicy", req, cb);
    }
    async ModifyHttpAuthenticator(req, cb) {
        return this.request("ModifyHttpAuthenticator", req, cb);
    }
    async DeleteAuthenticator(req, cb) {
        return this.request("DeleteAuthenticator", req, cb);
    }
    async RegisterCaCertificate(req, cb) {
        return this.request("RegisterCaCertificate", req, cb);
    }
    async DescribeAuthenticator(req, cb) {
        return this.request("DescribeAuthenticator", req, cb);
    }
    async ModifyDeviceIdentity(req, cb) {
        return this.request("ModifyDeviceIdentity", req, cb);
    }
    async ActivateCaCertificate(req, cb) {
        return this.request("ActivateCaCertificate", req, cb);
    }
    async DescribeMessageList(req, cb) {
        return this.request("DescribeMessageList", req, cb);
    }
    async CreateUser(req, cb) {
        return this.request("CreateUser", req, cb);
    }
    async DescribeSharedSubscriptionLag(req, cb) {
        return this.request("DescribeSharedSubscriptionLag", req, cb);
    }
    async ApplyRegistrationCode(req, cb) {
        return this.request("ApplyRegistrationCode", req, cb);
    }
    async CreateAuthorizationPolicy(req, cb) {
        return this.request("CreateAuthorizationPolicy", req, cb);
    }
    async ModifyInstance(req, cb) {
        return this.request("ModifyInstance", req, cb);
    }
    async CreateHttpAuthenticator(req, cb) {
        return this.request("CreateHttpAuthenticator", req, cb);
    }
    async DescribeDeviceIdentities(req, cb) {
        return this.request("DescribeDeviceIdentities", req, cb);
    }
}
