// Aliyun NumberAuth H5 integration helpers
import request from '@/utils/request'

// 动态加载本地 SDK（已由后端/工程放在 public 路径或静态路径）
export async function loadNumberAuthSdk(): Promise<void> {
  if ((window as any).__numberAuthLoaded) return
  await new Promise<void>((resolve, reject) => {
    const s = document.createElement('script')
    // 按你的项目静态资源路径调整：这里放在 /numberAuth/numberAuth-web-sdk.js
    s.src = '/numberAuth/numberAuth-web-sdk.js'
    s.onload = () => { (window as any).__numberAuthLoaded = true; resolve() }
    s.onerror = () => reject(new Error('numberAuth sdk load failed'))
    document.head.appendChild(s)
  })
}

// 获取 accessCode（按常见 SDK 入口尝试；建议根据控制台 API 固化实现）
export async function getAccessCodeFromSdk(): Promise<string> {
  const appId = (import.meta as any).env?.VITE_NUMBERAUTH_APP_ID
  const sceneId = (import.meta as any).env?.VITE_NUMBERAUTH_SCENE_ID
  const config = { appId, sceneId }
  const w: any = window as any

  const candidates: Array<{ ctor?: string; obj?: string; method: string }> = [
    { ctor: 'AuthPnsWeb', method: 'getToken' },
    { ctor: 'AuthPns', method: 'getAccessCode' },
    { ctor: 'NumberAuth', method: 'getAccessCode' },
    { ctor: 'AliyunNumberAuth', method: 'getAccessCode' },
    { obj: 'authPnsWeb', method: 'getToken' },
  ]
  for (const c of candidates) {
    try {
      if (c.ctor && typeof w[c.ctor] === 'function') {
        const client = new w[c.ctor](config)
        if (typeof client[c.method] === 'function') {
          const ret = await client[c.method]()
          const accessCode = ret?.accessCode || ret?.token || ret?.data?.accessCode || ret?.data?.token
          if (accessCode) return String(accessCode)
        }
      }
      if (c.obj && w[c.obj] && typeof w[c.obj][c.method] === 'function') {
        const ret = await w[c.obj][c.method](config)
        const accessCode = ret?.accessCode || ret?.token || ret?.data?.accessCode || ret?.data?.token
        if (accessCode) return String(accessCode)
      }
    } catch (_) { /* ignore and try next */ }
  }
  throw new Error('未找到可用的一键取号入口，请检查 SDK 暴露的对象与方法')
}

// 向后端取号（accessCode）
export async function fetchMobileByAccessCode(accessCode: string): Promise<{ success: boolean; phone?: string; message?: string }> {
  const res = await request.post('/api/auth/numberauth/get-mobile', { accessCode })
  return res
}

