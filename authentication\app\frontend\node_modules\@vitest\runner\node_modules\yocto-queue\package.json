{"name": "yocto-queue", "version": "1.2.1", "description": "Tiny queue data structure", "license": "MIT", "repository": "sindresorhus/yocto-queue", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=12.20"}, "scripts": {"//test": "xo && ava && tsd", "test": "ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}}