import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("dts.tencentcloudapi.com", "2021-12-06", clientConfig);
    }
    async ResumeSubscribe(req, cb) {
        return this.request("ResumeSubscribe", req, cb);
    }
    async SkipSyncCheckItem(req, cb) {
        return this.request("SkipSyncCheckItem", req, cb);
    }
    async DestroyIsolatedSubscribe(req, cb) {
        return this.request("DestroyIsolatedSubscribe", req, cb);
    }
    async CreateModifyCheckSyncJob(req, cb) {
        return this.request("CreateModifyCheckSyncJob", req, cb);
    }
    async ModifyMigrationJob(req, cb) {
        return this.request("ModifyMigrationJob", req, cb);
    }
    async DescribeSyncJobs(req, cb) {
        return this.request("DescribeSyncJobs", req, cb);
    }
    async ModifyMigrateRuntimeAttribute(req, cb) {
        return this.request("ModifyMigrateRuntimeAttribute", req, cb);
    }
    async CreateMigrationService(req, cb) {
        return this.request("CreateMigrationService", req, cb);
    }
    async DescribeSubscribeJobs(req, cb) {
        return this.request("DescribeSubscribeJobs", req, cb);
    }
    async ModifyMigrateRateLimit(req, cb) {
        return this.request("ModifyMigrateRateLimit", req, cb);
    }
    async DescribeSubscribeReturnable(req, cb) {
        return this.request("DescribeSubscribeReturnable", req, cb);
    }
    async IsolateSyncJob(req, cb) {
        return this.request("IsolateSyncJob", req, cb);
    }
    async StopCompare(req, cb) {
        return this.request("StopCompare", req, cb);
    }
    async CreateSyncJob(req, cb) {
        return this.request("CreateSyncJob", req, cb);
    }
    async ConfigureSubscribeJob(req, cb) {
        return this.request("ConfigureSubscribeJob", req, cb);
    }
    async DescribeConsumerGroups(req, cb) {
        return this.request("DescribeConsumerGroups", req, cb);
    }
    async CreateConsumerGroup(req, cb) {
        return this.request("CreateConsumerGroup", req, cb);
    }
    async ResetConsumerGroupOffset(req, cb) {
        return this.request("ResetConsumerGroupOffset", req, cb);
    }
    async DeleteCompareTask(req, cb) {
        return this.request("DeleteCompareTask", req, cb);
    }
    async StartSubscribe(req, cb) {
        return this.request("StartSubscribe", req, cb);
    }
    async StartSyncJob(req, cb) {
        return this.request("StartSyncJob", req, cb);
    }
    async StopSyncJob(req, cb) {
        return this.request("StopSyncJob", req, cb);
    }
    async CreateCheckSyncJob(req, cb) {
        return this.request("CreateCheckSyncJob", req, cb);
    }
    async DescribeCompareReport(req, cb) {
        return this.request("DescribeCompareReport", req, cb);
    }
    async ContinueMigrateJob(req, cb) {
        return this.request("ContinueMigrateJob", req, cb);
    }
    async PauseMigrateJob(req, cb) {
        return this.request("PauseMigrateJob", req, cb);
    }
    async ModifySubscribeObjects(req, cb) {
        return this.request("ModifySubscribeObjects", req, cb);
    }
    async ConfigureSyncJob(req, cb) {
        return this.request("ConfigureSyncJob", req, cb);
    }
    async IsolateSubscribe(req, cb) {
        return this.request("IsolateSubscribe", req, cb);
    }
    async StartModifySyncJob(req, cb) {
        return this.request("StartModifySyncJob", req, cb);
    }
    async ResizeSyncJob(req, cb) {
        return this.request("ResizeSyncJob", req, cb);
    }
    async ModifySubscribeAutoRenewFlag(req, cb) {
        return this.request("ModifySubscribeAutoRenewFlag", req, cb);
    }
    async ModifySyncJobConfig(req, cb) {
        return this.request("ModifySyncJobConfig", req, cb);
    }
    async ResumeMigrateJob(req, cb) {
        return this.request("ResumeMigrateJob", req, cb);
    }
    async StopMigrateJob(req, cb) {
        return this.request("StopMigrateJob", req, cb);
    }
    async PauseSyncJob(req, cb) {
        return this.request("PauseSyncJob", req, cb);
    }
    async ModifySyncRateLimit(req, cb) {
        return this.request("ModifySyncRateLimit", req, cb);
    }
    async StartCompare(req, cb) {
        return this.request("StartCompare", req, cb);
    }
    async DescribeSubscribeCheckJob(req, cb) {
        return this.request("DescribeSubscribeCheckJob", req, cb);
    }
    async SkipCheckItem(req, cb) {
        return this.request("SkipCheckItem", req, cb);
    }
    async ContinueSyncJob(req, cb) {
        return this.request("ContinueSyncJob", req, cb);
    }
    async IsolateMigrateJob(req, cb) {
        return this.request("IsolateMigrateJob", req, cb);
    }
    async ModifyConsumerGroupDescription(req, cb) {
        return this.request("ModifyConsumerGroupDescription", req, cb);
    }
    async DescribeMigrateDBInstances(req, cb) {
        return this.request("DescribeMigrateDBInstances", req, cb);
    }
    async ResetSyncJob(req, cb) {
        return this.request("ResetSyncJob", req, cb);
    }
    async ModifyConsumerGroupPassword(req, cb) {
        return this.request("ModifyConsumerGroupPassword", req, cb);
    }
    async CreateSubscribeCheckJob(req, cb) {
        return this.request("CreateSubscribeCheckJob", req, cb);
    }
    async CreateMigrateCheckJob(req, cb) {
        return this.request("CreateMigrateCheckJob", req, cb);
    }
    async DescribeCheckSyncJobResult(req, cb) {
        return this.request("DescribeCheckSyncJobResult", req, cb);
    }
    async DescribeCompareTasks(req, cb) {
        return this.request("DescribeCompareTasks", req, cb);
    }
    async ModifyCompareTask(req, cb) {
        return this.request("ModifyCompareTask", req, cb);
    }
    async DescribeSubscribeDetail(req, cb) {
        return this.request("DescribeSubscribeDetail", req, cb);
    }
    async DeleteConsumerGroup(req, cb) {
        return this.request("DeleteConsumerGroup", req, cb);
    }
    async DescribeMigrationJobs(req, cb) {
        return this.request("DescribeMigrationJobs", req, cb);
    }
    async RecoverMigrateJob(req, cb) {
        return this.request("RecoverMigrateJob", req, cb);
    }
    async CreateSubscribe(req, cb) {
        return this.request("CreateSubscribe", req, cb);
    }
    async RecoverSyncJob(req, cb) {
        return this.request("RecoverSyncJob", req, cb);
    }
    async ResetSubscribe(req, cb) {
        return this.request("ResetSubscribe", req, cb);
    }
    async CreateCompareTask(req, cb) {
        return this.request("CreateCompareTask", req, cb);
    }
    async DescribeModifyCheckSyncJobResult(req, cb) {
        return this.request("DescribeModifyCheckSyncJobResult", req, cb);
    }
    async ModifySubscribeName(req, cb) {
        return this.request("ModifySubscribeName", req, cb);
    }
    async DestroyMigrateJob(req, cb) {
        return this.request("DestroyMigrateJob", req, cb);
    }
    async DescribeMigrationDetail(req, cb) {
        return this.request("DescribeMigrationDetail", req, cb);
    }
    async DescribeOffsetByTime(req, cb) {
        return this.request("DescribeOffsetByTime", req, cb);
    }
    async ResumeSyncJob(req, cb) {
        return this.request("ResumeSyncJob", req, cb);
    }
    async DescribeMigrationCheckJob(req, cb) {
        return this.request("DescribeMigrationCheckJob", req, cb);
    }
    async StartMigrateJob(req, cb) {
        return this.request("StartMigrateJob", req, cb);
    }
    async ModifyMigrateJobSpec(req, cb) {
        return this.request("ModifyMigrateJobSpec", req, cb);
    }
    async DestroySyncJob(req, cb) {
        return this.request("DestroySyncJob", req, cb);
    }
    async ModifyCompareTaskName(req, cb) {
        return this.request("ModifyCompareTaskName", req, cb);
    }
    async ModifyMigrateName(req, cb) {
        return this.request("ModifyMigrateName", req, cb);
    }
    async CompleteMigrateJob(req, cb) {
        return this.request("CompleteMigrateJob", req, cb);
    }
}
