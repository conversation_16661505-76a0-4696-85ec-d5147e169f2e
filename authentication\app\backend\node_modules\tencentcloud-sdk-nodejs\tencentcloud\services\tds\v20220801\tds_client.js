"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Client = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * Copyright (c) 2018 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
const abstract_client_1 = require("../../../common/abstract_client");
/**
 * tds client
 * @class
 */
class Client extends abstract_client_1.AbstractClient {
    constructor(clientConfig) {
        super("tds.tencentcloudapi.com", "2022-08-01", clientConfig);
    }
    /**
     * 查询设备风险
     */
    async DescribeFraudBase(req, cb) {
        return this.request("DescribeFraudBase", req, cb);
    }
    /**
     * 查询设备标识
     */
    async DescribeTrustedID(req, cb) {
        return this.request("DescribeTrustedID", req, cb);
    }
    /**
     * 查询设备标识及风险
     */
    async DescribeFraudPremium(req, cb) {
        return this.request("DescribeFraudPremium", req, cb);
    }
    /**
     * 查询设备标识及风险（金融旗舰版）
     */
    async DescribeFinanceFraudUltimate(req, cb) {
        return this.request("DescribeFinanceFraudUltimate", req, cb);
    }
    /**
     * 查询设备标识及风险（旗舰版）
     */
    async DescribeFraudUltimate(req, cb) {
        return this.request("DescribeFraudUltimate", req, cb);
    }
}
exports.Client = Client;
