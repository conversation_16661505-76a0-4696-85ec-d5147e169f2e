import{_ as a,c as e,u as l,d as t}from"./index-bcbc0702.js";/* empty css                        *//* empty css                  *//* empty css                         */import"./el-tooltip-4ed993c7.js";import{r as s,p as d,A as u,B as n,C as i,F as o,G as r,D as c,J as _,al as f,u as m,I as p,H as v,E as g,z as h,K as x,L as y,aE as b,aF as w,aG as j,aH as k,aI as I,aJ as S,aK as B,aL as C,aM as E,aN as F,R as G,aO as H,aP as L,aQ as T,aR as z,aS as A,a0 as J,a5 as K,aT as R,a4 as q,aU as D,a2 as M,aV as N,aW as O}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const P={class:"admin-layout"},Q={class:"sidebar-header"},U={key:0,class:"logo"},V={class:"header-left"},W={class:"header-right"},X={class:"user-info"},Y={key:0,class:"username"},Z=a({__name:"AdminLayout",setup(a){const Z=e(),$=l(),aa=t(),ea=s(!1),la=s(""),ta=d(()=>Z.path),sa=d(()=>aa.user),da=d(()=>({"/admin/dashboard":"仪表板","/admin/users":"用户管理","/admin/pending":"待审核用户","/admin/config":"系统配置","/admin/logs":"审计日志","/admin/reports":"统计报告","/admin/security":"安全审计"}[Z.path]||"管理后台")),ua=()=>{ea.value=!ea.value,localStorage.setItem("admin_sidebar_collapsed",ea.value)},na=async a=>{switch(a){case"profile":g.info("个人资料功能开发中");break;case"settings":g.info("账户设置功能开发中");break;case"logout":await ia()}},ia=async()=>{try{await h.confirm("确定要退出登录吗？","确认退出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await aa.logout(),$.push("/admin/login")}catch(a){}},oa=a=>{a!==Z.path&&$.push(a).catch(a=>{g.error("页面导航失败")})};return u(()=>{const a=localStorage.getItem("admin_sidebar_collapsed");null!==a&&(ea.value="true"===a)}),(a,e)=>{const l=x,t=y,s=b,d=w,u=j,g=k,h=I,Z=S,$=B,aa=C,ia=E,ra=F,ca=G("router-view"),_a=H,fa=L;return n(),i("div",P,[o(fa,null,{default:r(()=>[o(u,{width:ea.value?"64px":"240px",class:"sidebar"},{default:r(()=>[c("div",Q,[ea.value?_("",!0):(n(),i("div",U,e[0]||(e[0]=[c("h3",null,"管理后台",-1)]))),o(t,{text:"",onClick:ua,class:"collapse-btn"},{default:r(()=>[o(l,null,{default:r(()=>[ea.value?(n(),f(m(T),{key:0})):(n(),f(m(z),{key:1}))]),_:1})]),_:1})]),o(d,{"default-active":ta.value,collapse:ea.value,"unique-opened":!0,class:"sidebar-menu",onSelect:oa},{default:r(()=>[o(s,{index:"/admin/dashboard"},{title:r(()=>e[1]||(e[1]=[p("仪表板",-1)])),default:r(()=>[o(l,null,{default:r(()=>[o(m(A))]),_:1})]),_:1}),o(s,{index:"/admin/users"},{title:r(()=>e[2]||(e[2]=[p("用户管理",-1)])),default:r(()=>[o(l,null,{default:r(()=>[o(m(J))]),_:1})]),_:1}),o(s,{index:"/admin/pending"},{title:r(()=>e[3]||(e[3]=[p("待审核用户",-1)])),default:r(()=>[o(l,null,{default:r(()=>[o(m(K))]),_:1})]),_:1}),o(s,{index:"/admin/config"},{title:r(()=>e[4]||(e[4]=[p("系统配置",-1)])),default:r(()=>[o(l,null,{default:r(()=>[o(m(R))]),_:1})]),_:1}),o(s,{index:"/admin/logs"},{title:r(()=>e[5]||(e[5]=[p("审计日志",-1)])),default:r(()=>[o(l,null,{default:r(()=>[o(m(q))]),_:1})]),_:1}),o(s,{index:"/admin/reports"},{title:r(()=>e[6]||(e[6]=[p("统计报告",-1)])),default:r(()=>[o(l,null,{default:r(()=>[o(m(D))]),_:1})]),_:1}),o(s,{index:"/admin/security"},{title:r(()=>e[7]||(e[7]=[p("安全审计",-1)])),default:r(()=>[o(l,null,{default:r(()=>[o(m(M))]),_:1})]),_:1})]),_:1},8,["default-active","collapse"])]),_:1},8,["width"]),o(fa,null,{default:r(()=>[o(ra,{class:"header"},{default:r(()=>[c("div",V,[o(h,{separator:"/"},{default:r(()=>[o(g,{to:{path:"/admin"}},{default:r(()=>e[8]||(e[8]=[p("管理后台",-1)])),_:1,__:[8]}),o(g,null,{default:r(()=>[p(v(da.value),1)]),_:1})]),_:1})]),c("div",W,[o(ia,{onCommand:na},{dropdown:r(()=>[o(aa,null,{default:r(()=>[o($,{command:"profile"},{default:r(()=>[o(l,null,{default:r(()=>[o(m(J))]),_:1}),e[9]||(e[9]=p(" 个人资料 ",-1))]),_:1,__:[9]}),o($,{command:"settings"},{default:r(()=>[o(l,null,{default:r(()=>[o(m(R))]),_:1}),e[10]||(e[10]=p(" 账户设置 ",-1))]),_:1,__:[10]}),o($,{divided:"",command:"logout"},{default:r(()=>[o(l,null,{default:r(()=>[o(m(N))]),_:1}),e[11]||(e[11]=p(" 退出登录 ",-1))]),_:1,__:[11]})]),_:1})]),default:r(()=>{var a;return[c("div",X,[o(Z,{size:32,src:la.value},{default:r(()=>[o(l,null,{default:r(()=>[o(m(J))]),_:1})]),_:1},8,["src"]),ea.value?_("",!0):(n(),i("span",Y,v((null==(a=sa.value)?void 0:a.name)||"管理员"),1)),o(l,null,{default:r(()=>[o(m(O))]),_:1})])]}),_:1})])]),_:1}),o(_a,{class:"main-content"},{default:r(()=>[o(ca)]),_:1})]),_:1})]),_:1})])}}},[["__scopeId","data-v-c8a54315"]]);export{Z as default};
