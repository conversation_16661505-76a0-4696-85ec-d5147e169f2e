{"version": 3, "file": "menu-item-group.mjs", "sources": ["../../../../../../packages/components/menu/src/menu-item-group.ts"], "sourcesContent": ["import type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const menuItemGroupProps = {\n  /**\n   * @description group title\n   */\n  title: String,\n} as const\nexport type MenuItemGroupProps = ExtractPropTypes<typeof menuItemGroupProps>\nexport type MenuItemGroupPropsPublic = __ExtractPublicPropTypes<\n  typeof menuItemGroupProps\n>\n"], "names": [], "mappings": "AAAY,MAAC,kBAAkB,GAAG;AAClC,EAAE,KAAK,EAAE,MAAM;AACf;;;;"}