import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { ModifyQAAttrRangeResponse, DeleteAgentRequest, GetWsTokenResponse, ListUsageCallDetailRequest, CheckAttributeLabelExistRequest, DescribeAppAgentListResponse, DescribeQAResponse, GetAppKnowledgeCountRequest, StopDocParseResponse, ListSelectDocResponse, DescribeReleaseInfoRequest, DeleteDocRequest, CreateWorkflowRunResponse, GetAnswerTypeDataCountRequest, DescribeAppResponse, UploadAttributeLabelResponse, CreateDocCateResponse, DeleteDocCateResponse, ExportAttributeLabelResponse, ModifyAppResponse, ExportQAListResponse, GetAppSecretResponse, CreateWorkflowRunRequest, CreateQACateResponse, ModifyDocAttrRangeResponse, CreateSharedKnowledgeRequest, DescribeQARequest, DescribeWorkflowRunRequest, DescribeUnsatisfiedReplyContextRequest, ListAppRequest, UpdateSharedKnowledgeRequest, ModifyQAAttrRangeRequest, ListQARequest, DescribeKnowledgeUsageResponse, DeleteSharedKnowledgeResponse, ListUnsatisfiedReplyRequest, DeleteQARequest, GetLikeDataCountResponse, IsTransferIntentResponse, CreateDocCateRequest, ListReferShareKnowledgeResponse, ListDocResponse, ModifyQARequest, GetDocPreviewRequest, ListDocCateResponse, ListRejectedQuestionPreviewResponse, CreateAppResponse, DescribeAttributeLabelRequest, CreateQARequest, ListSharedKnowledgeRequest, ListQAResponse, DeleteAttributeLabelRequest, DescribeRobotBizIDByAppKeyResponse, RenameDocResponse, DescribeNodeRunRequest, ListDocCateRequest, DescribeDocResponse, CreateReleaseRequest, GroupDocRequest, CheckAttributeLabelReferRequest, CreateSharedKnowledgeResponse, CreateAgentRequest, UpdateVarResponse, ModifyAttributeLabelRequest, ListWorkflowRunsRequest, ListUnsatisfiedReplyResponse, GetAppSecretRequest, DeleteQACateResponse, ListReleaseDocPreviewResponse, StopWorkflowRunRequest, RenameDocRequest, StopWorkflowRunResponse, GenerateQAResponse, GetTaskStatusResponse, DescribeConcurrencyUsageGraphRequest, DescribeTokenUsageGraphRequest, VerifyQAResponse, RetryDocAuditRequest, SaveDocRequest, RetryDocParseRequest, ListAppKnowledgeDetailResponse, ModifyQACateResponse, DescribeAttributeLabelResponse, DescribeSharedKnowledgeRequest, ListQACateResponse, GetVarListRequest, RetryDocParseResponse, DeleteAgentResponse, CreateRejectedQuestionResponse, ListReleaseQAPreviewResponse, ExportUnsatisfiedReplyRequest, GroupQAResponse, CreateAppRequest, DescribeReleaseRequest, DescribeDocRequest, ListReleaseConfigPreviewRequest, ListReleaseDocPreviewRequest, DescribeUnsatisfiedReplyContextResponse, CreateRejectedQuestionRequest, DescribeKnowledgeUsagePieGraphRequest, DescribeReferResponse, ListDocRequest, GetAnswerTypeDataCountResponse, GroupQARequest, RateMsgRecordRequest, DescribeCallStatsGraphRequest, GetMsgRecordResponse, DescribeAppRequest, DescribeKnowledgeUsageRequest, DeleteSharedKnowledgeRequest, DescribeReleaseResponse, DescribeConcurrencyUsageGraphResponse, ModifyAgentRequest, ModifyRejectedQuestionRequest, DescribeKnowledgeUsagePieGraphResponse, ListRejectedQuestionResponse, DescribeTokenUsageGraphResponse, ModifyAttributeLabelResponse, RetryDocAuditResponse, ModifyDocCateRequest, ReferShareKnowledgeResponse, RetryReleaseResponse, ModifyDocRequest, GetLikeDataCountRequest, DeleteRejectedQuestionResponse, CreateAttributeLabelRequest, ListAppResponse, DescribeSharedKnowledgeResponse, DeleteVarRequest, ListRejectedQuestionPreviewRequest, ListReleaseConfigPreviewResponse, DeleteRejectedQuestionRequest, ExportQAListRequest, GetDocPreviewResponse, DescribeConcurrencyUsageResponse, ListUsageCallDetailResponse, CreateReleaseResponse, ModifyAppRequest, DeleteAttributeLabelResponse, RetryReleaseRequest, UpdateVarRequest, VerifyQARequest, DeleteQACateRequest, GenerateQARequest, GetWsTokenRequest, DeleteVarResponse, ListReferShareKnowledgeRequest, DescribeTokenUsageResponse, DeleteAppResponse, ListWorkflowRunsResponse, ListAttributeLabelRequest, DeleteDocCateRequest, DeleteDocResponse, ListSharedKnowledgeResponse, DescribeSegmentsResponse, DescribeConcurrencyUsageRequest, GroupDocResponse, UploadAttributeLabelRequest, IgnoreUnsatisfiedReplyRequest, ExportAttributeLabelRequest, DescribeTokenUsageRequest, GetAppKnowledgeCountResponse, CreateAttributeLabelResponse, DescribeAppAgentListRequest, ListAppKnowledgeDetailRequest, CreateQAResponse, CreateAgentResponse, ModifyQACateRequest, ListQACateRequest, ListReleaseQAPreviewRequest, DescribeReleaseInfoResponse, IsTransferIntentRequest, DescribeWorkflowRunResponse, DeleteAppRequest, ModifyRejectedQuestionResponse, GetVarListResponse, ListRejectedQuestionRequest, UpdateSharedKnowledgeResponse, DescribeSearchStatsGraphResponse, DescribeStorageCredentialRequest, ModifyDocCateResponse, SaveDocResponse, ListReleaseResponse, ListModelResponse, DescribeNodeRunResponse, ListAttributeLabelResponse, ModifyQAResponse, DeleteQAResponse, DescribeCallStatsGraphResponse, GetMsgRecordRequest, DescribeStorageCredentialResponse, CheckAttributeLabelExistResponse, GetTaskStatusRequest, CheckAttributeLabelReferResponse, RateMsgRecordResponse, CreateVarResponse, ModifyDocAttrRangeRequest, CreateQACateRequest, DescribeRobotBizIDByAppKeyRequest, CreateVarRequest, ExportUnsatisfiedReplyResponse, StopDocParseRequest, ModifyDocResponse, DescribeSegmentsRequest, DescribeSearchStatsGraphRequest, DescribeReferRequest, IgnoreUnsatisfiedReplyResponse, ListReleaseRequest, ModifyAgentResponse, ListModelRequest, ReferShareKnowledgeRequest, ListSelectDocRequest } from "./lke_models";
/**
 * lke client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 查询不满意回复列表
     */
    ListUnsatisfiedReply(req: ListUnsatisfiedReplyRequest, cb?: (error: string, rep: ListUnsatisfiedReplyResponse) => void): Promise<ListUnsatisfiedReplyResponse>;
    /**
     * 删除属性标签
     */
    DeleteAttributeLabel(req: DeleteAttributeLabelRequest, cb?: (error: string, rep: DeleteAttributeLabelResponse) => void): Promise<DeleteAttributeLabelResponse>;
    /**
     * 查询自定义变量列表
     */
    GetVarList(req: GetVarListRequest, cb?: (error: string, rep: GetVarListResponse) => void): Promise<GetVarListResponse>;
    /**
     * 查询企业知识库容量饼图
     */
    DescribeKnowledgeUsagePieGraph(req: DescribeKnowledgeUsagePieGraphRequest, cb?: (error: string, rep: DescribeKnowledgeUsagePieGraphResponse) => void): Promise<DescribeKnowledgeUsagePieGraphResponse>;
    /**
     * 批量修改问答适用范围
     */
    ModifyQAAttrRange(req: ModifyQAAttrRangeRequest, cb?: (error: string, rep: ModifyQAAttrRangeResponse) => void): Promise<ModifyQAAttrRangeResponse>;
    /**
     * 文档生成问答时，可通过该接口获得当前支持生成问答的文档列表，当前不支持xlsx、xls、csv格式的文档生成问答，且文档需处于待发布或已发布状态才可生成问答。
     */
    ListSelectDoc(req: ListSelectDocRequest, cb?: (error: string, rep: ListSelectDocResponse) => void): Promise<ListSelectDocResponse>;
    /**
     * 批量修改文档适用范围
     */
    ModifyDocAttrRange(req: ModifyDocAttrRangeRequest, cb?: (error: string, rep: ModifyDocAttrRangeResponse) => void): Promise<ModifyDocAttrRangeResponse>;
    /**
     * 创建发布
     */
    CreateRelease(req: CreateReleaseRequest, cb?: (error: string, rep: CreateReleaseResponse) => void): Promise<CreateReleaseResponse>;
    /**
     * 删除问答
     */
    DeleteQA(req: DeleteQARequest, cb?: (error: string, rep: DeleteQAResponse) => void): Promise<DeleteQAResponse>;
    /**
     * 此接口用来停止正在进行的工作流异步运行实例。
     */
    StopWorkflowRun(req: StopWorkflowRunRequest, cb?: (error: string, rep: StopWorkflowRunResponse) => void): Promise<StopWorkflowRunResponse>;
    /**
     * 修改Agent信息
     */
    ModifyAgent(req: ModifyAgentRequest, cb?: (error: string, rep: ModifyAgentResponse) => void): Promise<ModifyAgentResponse>;
    /**
     * 创建标签
     */
    CreateAttributeLabel(req: CreateAttributeLabelRequest, cb?: (error: string, rep: CreateAttributeLabelResponse) => void): Promise<CreateAttributeLabelResponse>;
    /**
     * 获取Doc分类
     */
    ListDocCate(req: ListDocCateRequest, cb?: (error: string, rep: ListDocCateResponse) => void): Promise<ListDocCateResponse>;
    /**
     * 列举共享知识库。
     */
    ListSharedKnowledge(req: ListSharedKnowledgeRequest, cb?: (error: string, rep: ListSharedKnowledgeResponse) => void): Promise<ListSharedKnowledgeResponse>;
    /**
     * 查询属性标签详情
     */
    DescribeAttributeLabel(req: DescribeAttributeLabelRequest, cb?: (error: string, rep: DescribeAttributeLabelResponse) => void): Promise<DescribeAttributeLabelResponse>;
    /**
     * 发布拒答问题预览
     */
    ListRejectedQuestionPreview(req: ListRejectedQuestionPreviewRequest, cb?: (error: string, rep: ListRejectedQuestionPreviewResponse) => void): Promise<ListRejectedQuestionPreviewResponse>;
    /**
     * 查询共享知识库。
     */
    DescribeSharedKnowledge(req: DescribeSharedKnowledgeRequest, cb?: (error: string, rep: DescribeSharedKnowledgeResponse) => void): Promise<DescribeSharedKnowledgeResponse>;
    /**
     * 文档解析重试
     */
    RetryDocParse(req: RetryDocParseRequest, cb?: (error: string, rep: RetryDocParseResponse) => void): Promise<RetryDocParseResponse>;
    /**
     * 录入问答
     */
    CreateQA(req: CreateQARequest, cb?: (error: string, rep: CreateQAResponse) => void): Promise<CreateQAResponse>;
    /**
     * 接口调用token折线图
     */
    DescribeTokenUsageGraph(req: DescribeTokenUsageGraphRequest, cb?: (error: string, rep: DescribeTokenUsageGraphResponse) => void): Promise<DescribeTokenUsageGraphResponse>;
    /**
     * 文档生成问答
     */
    GenerateQA(req: GenerateQARequest, cb?: (error: string, rep: GenerateQAResponse) => void): Promise<GenerateQAResponse>;
    /**
     * 获取企业下应用详情
     */
    DescribeApp(req: DescribeAppRequest, cb?: (error: string, rep: DescribeAppResponse) => void): Promise<DescribeAppResponse>;
    /**
     * 获取ws token
     */
    GetWsToken(req: GetWsTokenRequest, cb?: (error: string, rep: GetWsTokenResponse) => void): Promise<GetWsTokenResponse>;
    /**
     * 查询属性标签列表
     */
    ListAttributeLabel(req: ListAttributeLabelRequest, cb?: (error: string, rep: ListAttributeLabelResponse) => void): Promise<ListAttributeLabelResponse>;
    /**
     * 发布配置项预览
     */
    ListReleaseConfigPreview(req: ListReleaseConfigPreviewRequest, cb?: (error: string, rep: ListReleaseConfigPreviewResponse) => void): Promise<ListReleaseConfigPreviewResponse>;
    /**
     * 查看应用引用了哪些共享知识库，可以看到共享知识库的基础信息，包括名称，id等
     */
    ListReferShareKnowledge(req: ListReferShareKnowledgeRequest, cb?: (error: string, rep: ListReferShareKnowledgeResponse) => void): Promise<ListReferShareKnowledgeResponse>;
    /**
     * 查询搜索服务调用折线图
     */
    DescribeSearchStatsGraph(req: DescribeSearchStatsGraphRequest, cb?: (error: string, rep: DescribeSearchStatsGraphResponse) => void): Promise<DescribeSearchStatsGraphResponse>;
    /**
     * 此接口可查询已创建的所有工作流异步运行实例。
     */
    ListWorkflowRuns(req: ListWorkflowRunsRequest, cb?: (error: string, rep: ListWorkflowRunsResponse) => void): Promise<ListWorkflowRunsResponse>;
    /**
     * 校验问答
     */
    VerifyQA(req: VerifyQARequest, cb?: (error: string, rep: VerifyQAResponse) => void): Promise<VerifyQAResponse>;
    /**
     * 创建Doc分类
     */
    CreateDocCate(req: CreateDocCateRequest, cb?: (error: string, rep: CreateDocCateResponse) => void): Promise<CreateDocCateResponse>;
    /**
     * 检查属性下的标签名是否存在
     */
    CheckAttributeLabelExist(req: CheckAttributeLabelExistRequest, cb?: (error: string, rep: CheckAttributeLabelExistResponse) => void): Promise<CheckAttributeLabelExistResponse>;
    /**
     * 文档重命名
     */
    RenameDoc(req: RenameDocRequest, cb?: (error: string, rep: RenameDocResponse) => void): Promise<RenameDocResponse>;
    /**
     * 文档详情
     */
    DescribeDoc(req: DescribeDocRequest, cb?: (error: string, rep: DescribeDocResponse) => void): Promise<DescribeDocResponse>;
    /**
     * 列表查询单次调用明细
     */
    ListUsageCallDetail(req: ListUsageCallDetailRequest, cb?: (error: string, rep: ListUsageCallDetailResponse) => void): Promise<ListUsageCallDetailResponse>;
    /**
     * 获取文件上传临时密钥
     */
    DescribeStorageCredential(req: DescribeStorageCredentialRequest, cb?: (error: string, rep: DescribeStorageCredentialResponse) => void): Promise<DescribeStorageCredentialResponse>;
    /**
     * 点赞点踩消息
     */
    RateMsgRecord(req: RateMsgRecordRequest, cb?: (error: string, rep: RateMsgRecordResponse) => void): Promise<RateMsgRecordResponse>;
    /**
     * 文档列表
     */
    ListReleaseQAPreview(req: ListReleaseQAPreviewRequest, cb?: (error: string, rep: ListReleaseQAPreviewResponse) => void): Promise<ListReleaseQAPreviewResponse>;
    /**
     * 更新QA分类
     */
    ModifyQACate(req: ModifyQACateRequest, cb?: (error: string, rep: ModifyQACateResponse) => void): Promise<ModifyQACateResponse>;
    /**
     * 删除应用
     */
    DeleteApp(req: DeleteAppRequest, cb?: (error: string, rep: DeleteAppResponse) => void): Promise<DeleteAppResponse>;
    /**
     * 检查属性标签引用
     */
    CheckAttributeLabelRefer(req: CheckAttributeLabelReferRequest, cb?: (error: string, rep: CheckAttributeLabelReferResponse) => void): Promise<CheckAttributeLabelReferResponse>;
    /**
     * 创建共享知识库。
     */
    CreateSharedKnowledge(req: CreateSharedKnowledgeRequest, cb?: (error: string, rep: CreateSharedKnowledgeResponse) => void): Promise<CreateSharedKnowledgeResponse>;
    /**
     * 获取不满意回复上下文
     */
    DescribeUnsatisfiedReplyContext(req: DescribeUnsatisfiedReplyContextRequest, cb?: (error: string, rep: DescribeUnsatisfiedReplyContextResponse) => void): Promise<DescribeUnsatisfiedReplyContextResponse>;
    /**
     * Doc分组
     */
    GroupDoc(req: GroupDocRequest, cb?: (error: string, rep: GroupDocResponse) => void): Promise<GroupDocResponse>;
    /**
     * 获取企业下应用列表
     */
    ListApp(req: ListAppRequest, cb?: (error: string, rep: ListAppResponse) => void): Promise<ListAppResponse>;
    /**
     * 接口调用token详情
     */
    DescribeTokenUsage(req: DescribeTokenUsageRequest, cb?: (error: string, rep: DescribeTokenUsageResponse) => void): Promise<DescribeTokenUsageResponse>;
    /**
     * 通过DescribeWorkflowRun接口获取了工作流异步运行的整体内容，其中包含了基本的节点信息，再通用本接口可查看节点的运行详情（包括输入、输出、日志等）。
     */
    DescribeNodeRun(req: DescribeNodeRunRequest, cb?: (error: string, rep: DescribeNodeRunResponse) => void): Promise<DescribeNodeRunResponse>;
    /**
     * Doc分类删除
     */
    DeleteDocCate(req: DeleteDocCateRequest, cb?: (error: string, rep: DeleteDocCateResponse) => void): Promise<DeleteDocCateResponse>;
    /**
     * 获取QA分类
     */
    ListQACate(req: ListQACateRequest, cb?: (error: string, rep: ListQACateResponse) => void): Promise<ListQACateResponse>;
    /**
     * 问答详情
     */
    DescribeQA(req: DescribeQARequest, cb?: (error: string, rep: DescribeQAResponse) => void): Promise<DescribeQAResponse>;
    /**
     * 创建知识引擎应用。
     */
    CreateApp(req: CreateAppRequest, cb?: (error: string, rep: CreateAppResponse) => void): Promise<CreateAppResponse>;
    /**
     * 修改文档
     */
    ModifyDoc(req: ModifyDocRequest, cb?: (error: string, rep: ModifyDocResponse) => void): Promise<ModifyDocResponse>;
    /**
     * 删除Agent
     */
    DeleteAgent(req: DeleteAgentRequest, cb?: (error: string, rep: DeleteAgentResponse) => void): Promise<DeleteAgentResponse>;
    /**
     * 创建QA分类
     */
    CreateQACate(req: CreateQACateRequest, cb?: (error: string, rep: CreateQACateResponse) => void): Promise<CreateQACateResponse>;
    /**
     * 更新共享知识库。
     */
    UpdateSharedKnowledge(req: UpdateSharedKnowledgeRequest, cb?: (error: string, rep: UpdateSharedKnowledgeResponse) => void): Promise<UpdateSharedKnowledgeResponse>;
    /**
     * 导出属性标签
     */
    ExportAttributeLabel(req: ExportAttributeLabelRequest, cb?: (error: string, rep: ExportAttributeLabelResponse) => void): Promise<ExportAttributeLabelResponse>;
    /**
     * 获取来源详情列表
     */
    DescribeRefer(req: DescribeReferRequest, cb?: (error: string, rep: DescribeReferResponse) => void): Promise<DescribeReferResponse>;
    /**
     * 查询知识库用量
     */
    DescribeKnowledgeUsage(req?: DescribeKnowledgeUsageRequest, cb?: (error: string, rep: DescribeKnowledgeUsageResponse) => void): Promise<DescribeKnowledgeUsageResponse>;
    /**
     * 删除共享知识库。
     */
    DeleteSharedKnowledge(req: DeleteSharedKnowledgeRequest, cb?: (error: string, rep: DeleteSharedKnowledgeResponse) => void): Promise<DeleteSharedKnowledgeResponse>;
    /**
     * 修改Doc分类
     */
    ModifyDocCate(req: ModifyDocCateRequest, cb?: (error: string, rep: ModifyDocCateResponse) => void): Promise<ModifyDocCateResponse>;
    /**
     * 文档列表
     */
    ListDoc(req: ListDocRequest, cb?: (error: string, rep: ListDocResponse) => void): Promise<ListDocResponse>;
    /**
     * 本接口用来创建工作流的异步运行实例，创建成功后工作流会在后台异步运行，接口返回工作流运行实例ID（WorkflowRunId）等信息。后面可通过调用DescribeWorkflowRun接口查工作流运行的详情。
注意：工作流的异步运行是基于应用的，需要先把对应的应用配置成“单工作流模式”，并且打开“异步调用”的开关，才能创建成功。
     */
    CreateWorkflowRun(req: CreateWorkflowRunRequest, cb?: (error: string, rep: CreateWorkflowRunResponse) => void): Promise<CreateWorkflowRunResponse>;
    /**
     * 问答列表
     */
    ListQA(req: ListQARequest, cb?: (error: string, rep: ListQAResponse) => void): Promise<ListQAResponse>;
    /**
     * 创建变量
     */
    CreateVar(req: CreateVarRequest, cb?: (error: string, rep: CreateVarResponse) => void): Promise<CreateVarResponse>;
    /**
     * 点赞点踩数据统计
     */
    GetLikeDataCount(req: GetLikeDataCountRequest, cb?: (error: string, rep: GetLikeDataCountResponse) => void): Promise<GetLikeDataCountResponse>;
    /**
     * 通过appKey获取应用业务ID
     */
    DescribeRobotBizIDByAppKey(req: DescribeRobotBizIDByAppKeyRequest, cb?: (error: string, rep: DescribeRobotBizIDByAppKeyResponse) => void): Promise<DescribeRobotBizIDByAppKeyResponse>;
    /**
     * 导出QA列表
     */
    ExportQAList(req: ExportQAListRequest, cb?: (error: string, rep: ExportQAListResponse) => void): Promise<ExportQAListResponse>;
    /**
     * 你创建一个Agent
     */
    CreateAgent(req: CreateAgentRequest, cb?: (error: string, rep: CreateAgentResponse) => void): Promise<CreateAgentResponse>;
    /**
     * 上传导入属性标签
     */
    UploadAttributeLabel(req: UploadAttributeLabelRequest, cb?: (error: string, rep: UploadAttributeLabelResponse) => void): Promise<UploadAttributeLabelResponse>;
    /**
     * 获取文档预览信息
     */
    GetDocPreview(req: GetDocPreviewRequest, cb?: (error: string, rep: GetDocPreviewResponse) => void): Promise<GetDocPreviewResponse>;
    /**
     * 并发调用响应
     */
    DescribeConcurrencyUsage(req: DescribeConcurrencyUsageRequest, cb?: (error: string, rep: DescribeConcurrencyUsageResponse) => void): Promise<DescribeConcurrencyUsageResponse>;
    /**
     * 修改拒答问题
     */
    ModifyRejectedQuestion(req: ModifyRejectedQuestionRequest, cb?: (error: string, rep: ModifyRejectedQuestionResponse) => void): Promise<ModifyRejectedQuestionResponse>;
    /**
     * 是否意图转人工
     */
    IsTransferIntent(req: IsTransferIntentRequest, cb?: (error: string, rep: IsTransferIntentResponse) => void): Promise<IsTransferIntentResponse>;
    /**
     * 忽略不满意回复
     */
    IgnoreUnsatisfiedReply(req: IgnoreUnsatisfiedReplyRequest, cb?: (error: string, rep: IgnoreUnsatisfiedReplyResponse) => void): Promise<IgnoreUnsatisfiedReplyResponse>;
    /**
     * 更新变量
     */
    UpdateVar(req: UpdateVarRequest, cb?: (error: string, rep: UpdateVarResponse) => void): Promise<UpdateVarResponse>;
    /**
     * 应用引用共享知识库，可以引用一个或多个，每次都是全量覆盖
     */
    ReferShareKnowledge(req: ReferShareKnowledgeRequest, cb?: (error: string, rep: ReferShareKnowledgeResponse) => void): Promise<ReferShareKnowledgeResponse>;
    /**
     * 发布列表
     */
    ListRelease(req: ListReleaseRequest, cb?: (error: string, rep: ListReleaseResponse) => void): Promise<ListReleaseResponse>;
    /**
     * 获取模型列表
     */
    ListModel(req: ListModelRequest, cb?: (error: string, rep: ListModelResponse) => void): Promise<ListModelResponse>;
    /**
     * 获取拒答问题
     */
    ListRejectedQuestion(req: ListRejectedQuestionRequest, cb?: (error: string, rep: ListRejectedQuestionResponse) => void): Promise<ListRejectedQuestionResponse>;
    /**
     * 分类删除
     */
    DeleteQACate(req: DeleteQACateRequest, cb?: (error: string, rep: DeleteQACateResponse) => void): Promise<DeleteQACateResponse>;
    /**
     * 导出不满意回复
     */
    ExportUnsatisfiedReply(req: ExportUnsatisfiedReplyRequest, cb?: (error: string, rep: ExportUnsatisfiedReplyResponse) => void): Promise<ExportUnsatisfiedReplyResponse>;
    /**
     * 发布暂停后重试
     */
    RetryRelease(req: RetryReleaseRequest, cb?: (error: string, rep: RetryReleaseResponse) => void): Promise<RetryReleaseResponse>;
    /**
     * 更新问答
     */
    ModifyQA(req: ModifyQARequest, cb?: (error: string, rep: ModifyQAResponse) => void): Promise<ModifyQAResponse>;
    /**
     * 知识库文档问答保存。
将文件存储到应用的知识库内需要三步：
1.获取临时密钥，参考[接口文档](https://cloud.tencent.com/document/product/1759/105050)。获取临时密钥不同参数组合权限不一样，可参考 [智能体开发平台操作 cos 指南](https://cloud.tencent.com/document/product/1759/116238)
2.调用腾讯云提供的 cos 存储接口，将文件存储到智能体开发平台 cos 中：具体可参考[ COS SDK 概览](https://cloud.tencent.com/document/product/436/6474), 注意使用的是临时密钥的方式操作 COS
3.调用本接口，将文件的基础信息存储到智能体开发平台中。
以上步骤可参考[文档](https://cloud.tencent.com/document/product/1759/108903)，文档最后有[代码demo](https://cloud.tencent.com/document/product/1759/108903#demo)，可作为参考。
     */
    SaveDoc(req: SaveDocRequest, cb?: (error: string, rep: SaveDocResponse) => void): Promise<SaveDocResponse>;
    /**
     * 删除文档
     */
    DeleteDoc(req: DeleteDocRequest, cb?: (error: string, rep: DeleteDocResponse) => void): Promise<DeleteDocResponse>;
    /**
     * 删除拒答问题
     */
    DeleteRejectedQuestion(req: DeleteRejectedQuestionRequest, cb?: (error: string, rep: DeleteRejectedQuestionResponse) => void): Promise<DeleteRejectedQuestionResponse>;
    /**
     * 删除变量
     */
    DeleteVar(req: DeleteVarRequest, cb?: (error: string, rep: DeleteVarResponse) => void): Promise<DeleteVarResponse>;
    /**
     * 修改应用请求结构体
     */
    ModifyApp(req: ModifyAppRequest, cb?: (error: string, rep: ModifyAppResponse) => void): Promise<ModifyAppResponse>;
    /**
     * 获取应用密钥
     */
    GetAppSecret(req: GetAppSecretRequest, cb?: (error: string, rep: GetAppSecretResponse) => void): Promise<GetAppSecretResponse>;
    /**
     * 编辑属性标签
     */
    ModifyAttributeLabel(req: ModifyAttributeLabelRequest, cb?: (error: string, rep: ModifyAttributeLabelResponse) => void): Promise<ModifyAttributeLabelResponse>;
    /**
     * 回答类型数据统计
     */
    GetAnswerTypeDataCount(req: GetAnswerTypeDataCountRequest, cb?: (error: string, rep: GetAnswerTypeDataCountResponse) => void): Promise<GetAnswerTypeDataCountResponse>;
    /**
     * 创建了工作流的异步运行实例后，通过本接口可以查询整体的运行详情。
     */
    DescribeWorkflowRun(req: DescribeWorkflowRunRequest, cb?: (error: string, rep: DescribeWorkflowRunResponse) => void): Promise<DescribeWorkflowRunResponse>;
    /**
     * 查询指定应用下的Agent列表
     */
    DescribeAppAgentList(req: DescribeAppAgentListRequest, cb?: (error: string, rep: DescribeAppAgentListResponse) => void): Promise<DescribeAppAgentListResponse>;
    /**
     * 文档解析重试
     */
    RetryDocAudit(req: RetryDocAuditRequest, cb?: (error: string, rep: RetryDocAuditResponse) => void): Promise<RetryDocAuditResponse>;
    /**
     * 发布文档预览
     */
    ListReleaseDocPreview(req: ListReleaseDocPreviewRequest, cb?: (error: string, rep: ListReleaseDocPreviewResponse) => void): Promise<ListReleaseDocPreviewResponse>;
    /**
     * 发布详情
     */
    DescribeRelease(req: DescribeReleaseRequest, cb?: (error: string, rep: DescribeReleaseResponse) => void): Promise<DescribeReleaseResponse>;
    /**
     * 获取模型列表
     */
    GetAppKnowledgeCount(req: GetAppKnowledgeCountRequest, cb?: (error: string, rep: GetAppKnowledgeCountResponse) => void): Promise<GetAppKnowledgeCountResponse>;
    /**
     * 并发调用折线图
     */
    DescribeConcurrencyUsageGraph(req: DescribeConcurrencyUsageGraphRequest, cb?: (error: string, rep: DescribeConcurrencyUsageGraphResponse) => void): Promise<DescribeConcurrencyUsageGraphResponse>;
    /**
     * 获取聊天历史
根据会话session id获取聊天历史（仅保留180天内的历史对话数据）
     */
    GetMsgRecord(req: GetMsgRecordRequest, cb?: (error: string, rep: GetMsgRecordResponse) => void): Promise<GetMsgRecordResponse>;
    /**
     * 创建拒答问题
     */
    CreateRejectedQuestion(req: CreateRejectedQuestionRequest, cb?: (error: string, rep: CreateRejectedQuestionResponse) => void): Promise<CreateRejectedQuestionResponse>;
    /**
     * 接口调用折线图
     */
    DescribeCallStatsGraph(req: DescribeCallStatsGraphRequest, cb?: (error: string, rep: DescribeCallStatsGraphResponse) => void): Promise<DescribeCallStatsGraphResponse>;
    /**
     * QA分组
     */
    GroupQA(req: GroupQARequest, cb?: (error: string, rep: GroupQAResponse) => void): Promise<GroupQAResponse>;
    /**
     * 获取片段详情
     */
    DescribeSegments(req: DescribeSegmentsRequest, cb?: (error: string, rep: DescribeSegmentsResponse) => void): Promise<DescribeSegmentsResponse>;
    /**
     * 获取任务状态
     */
    GetTaskStatus(req: GetTaskStatusRequest, cb?: (error: string, rep: GetTaskStatusResponse) => void): Promise<GetTaskStatusResponse>;
    /**
     * 终止文档解析
     */
    StopDocParse(req: StopDocParseRequest, cb?: (error: string, rep: StopDocParseResponse) => void): Promise<StopDocParseResponse>;
    /**
     * 列表查询知识库容量详情
     */
    ListAppKnowledgeDetail(req: ListAppKnowledgeDetailRequest, cb?: (error: string, rep: ListAppKnowledgeDetailResponse) => void): Promise<ListAppKnowledgeDetailResponse>;
    /**
     * 拉取发布按钮状态、最后发布时间
     */
    DescribeReleaseInfo(req: DescribeReleaseInfoRequest, cb?: (error: string, rep: DescribeReleaseInfoResponse) => void): Promise<DescribeReleaseInfoResponse>;
}
