{"version": 3, "file": "popconfirm2.js", "sources": ["../../../../../../packages/components/popconfirm/src/popconfirm.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"tooltipRef\"\n    trigger=\"click\"\n    effect=\"light\"\n    v-bind=\"$attrs\"\n    :popper-class=\"`${ns.namespace.value}-popover`\"\n    :popper-style=\"style\"\n    :teleported=\"teleported\"\n    :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n    :hide-after=\"hideAfter\"\n    :persistent=\"persistent\"\n  >\n    <template #content>\n      <div :class=\"ns.b()\">\n        <div :class=\"ns.e('main')\">\n          <el-icon\n            v-if=\"!hideIcon && icon\"\n            :class=\"ns.e('icon')\"\n            :style=\"{ color: iconColor }\"\n          >\n            <component :is=\"icon\" />\n          </el-icon>\n          {{ title }}\n        </div>\n        <div :class=\"ns.e('action')\">\n          <slot name=\"actions\" :confirm=\"confirm\" :cancel=\"cancel\">\n            <el-button\n              size=\"small\"\n              :type=\"cancelButtonType === 'text' ? '' : cancelButtonType\"\n              :text=\"cancelButtonType === 'text'\"\n              @click=\"cancel\"\n            >\n              {{ finalCancelButtonText }}\n            </el-button>\n            <el-button\n              size=\"small\"\n              :type=\"confirmButtonType === 'text' ? '' : confirmButtonType\"\n              :text=\"confirmButtonType === 'text'\"\n              @click=\"confirm\"\n            >\n              {{ finalConfirmButtonText }}\n            </el-button>\n          </slot>\n        </div>\n      </div>\n    </template>\n    <template v-if=\"$slots.reference\">\n      <slot name=\"reference\" />\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, unref } from 'vue'\nimport ElButton from '@element-plus/components/button'\nimport ElIcon from '@element-plus/components/icon'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { addUnit } from '@element-plus/utils'\nimport { popconfirmEmits, popconfirmProps } from './popconfirm'\n\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'ElPopconfirm',\n})\n\nconst props = defineProps(popconfirmProps)\nconst emit = defineEmits(popconfirmEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('popconfirm')\nconst tooltipRef = ref<TooltipInstance>()\nconst popperRef = computed(() => {\n  return unref(tooltipRef)?.popperRef\n})\n\nconst hidePopper = () => {\n  tooltipRef.value?.onClose?.()\n}\n\nconst style = computed(() => {\n  return {\n    width: addUnit(props.width),\n  }\n})\n\nconst confirm = (e: MouseEvent) => {\n  emit('confirm', e)\n  hidePopper()\n}\nconst cancel = (e: MouseEvent) => {\n  emit('cancel', e)\n  hidePopper()\n}\n\nconst finalConfirmButtonText = computed(\n  () => props.confirmButtonText || t('el.popconfirm.confirmButtonText')\n)\nconst finalCancelButtonText = computed(\n  () => props.cancelButtonText || t('el.popconfirm.cancelButtonText')\n)\n\ndefineExpose({\n  popperRef,\n  hide: hidePopper,\n})\n</script>\n"], "names": ["useLocale", "useNamespace", "ref", "computed", "unref", "style", "addUnit", "_openBlock", "_createBlock", "_unref", "ElTooltip", "_mergeProps"], "mappings": ";;;;;;;;;;;;;;uCAgEc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAIA,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAKC,qBAAa,YAAY,CAAA,CAAA;AACpC,IAAA,MAAM,aAAaC,OAAqB,EAAA,CAAA;AACxC,IAAM,MAAA,SAAA,GAAYC,aAAS,MAAM;AAC/B,MAAO,IAAA,EAAA,CAAA;AAAmB,MAC3B,OAAA,CAAA,EAAA,GAAAC,SAAA,CAAA,UAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,mBAA4B;AAAA,MAC9B,IAAA,EAAA,EAAA,EAAA,CAAA;AAEA,MAAM,CAAA,EAAA,GAAA,CAAA,EAAA,GAAQ,UAAe,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAC3B,KAAO,CAAA;AAAA,IACL,MAAAC,OAAO,GAAQF,YAAA,CAAA,MAAW;AAAA,MAC5B,OAAA;AAAA,QACD,KAAA,EAAAG,aAAA,CAAA,KAAA,CAAA,KAAA,CAAA;AAED,OAAM,CAAA;AACJ,KAAA,CAAA,CAAA;AACA,IAAW,MAAA,OAAA,GAAA,CAAA,CAAA,KAAA;AAAA,MACb,IAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA;AACA,MAAM,UAAA,EAAA,CAAS;AACb,KAAA,CAAA;AACA,IAAW,MAAA,MAAA,GAAA,CAAA,CAAA,KAAA;AAAA,MACb,IAAA,CAAA,QAAA,EAAA,CAAA,CAAA,CAAA;AAEA,MAAA,UAA+B,EAAA,CAAA;AAAA,KAAA,CAC7B;AAAoE,IACtE,MAAA,sBAAA,GAAAH,YAAA,CAAA,MAAA,KAAA,CAAA,iBAAA,IAAA,CAAA,CAAA,iCAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAM,qBAAwB,GAAAA,YAAA,CAAA,MAAA,KAAA,CAAA,gBAAA,IAAA,CAAA,CAAA,gCAAA,CAAA,CAAA,CAAA;AAAA,IAAA,MACtB,CAAA;AAA4D,MACpE,SAAA;AAEA,MAAa,IAAA,EAAA,UAAA;AAAA,KACX,CAAA,CAAA;AAAA,IAAA,OACM,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACP,OAAAI,aAAA,EAAA,EAAAC,eAAA,CAAAC,SAAA,CAAAC,iBAAA,CAAA,EAAAC,cAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}