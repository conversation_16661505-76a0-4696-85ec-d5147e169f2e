{"version": 3, "file": "node-content.js", "sources": ["../../../../../../packages/components/cascader-panel/src/node-content.tsx"], "sourcesContent": ["import { Comment, defineComponent, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isArray } from '@element-plus/utils'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\n\nimport type { PropType, VNode } from 'vue'\nimport type { CascaderNode } from './types'\n\nfunction isVNodeEmpty(vnodes?: VNode[] | VNode) {\n  return !!(isArray(vnodes)\n    ? vnodes.every(({ type }) => type === Comment)\n    : vnodes?.type === Comment)\n}\n\nexport default defineComponent({\n  name: 'NodeContent',\n  props: {\n    node: {\n      type: Object as PropType<CascaderNode>,\n      required: true,\n    },\n    disabled: Boolean,\n  },\n  setup(props, { emit }) {\n    const ns = useNamespace('cascader-node')\n    const { config, renderLabelFn } = inject(CASCADER_PANEL_INJECTION_KEY)!\n    const { checkOnClickNode, checkOnClickLeaf } = config\n    const { node, disabled } = props\n    const { data, label: nodeLabel } = node\n\n    const label = () => {\n      const renderLabel = renderLabelFn?.({ node, data })\n      return isVNodeEmpty(renderLabel) ? nodeLabel : renderLabel ?? nodeLabel\n    }\n    function handleClick() {\n      if (\n        (checkOnClickNode || (node.isLeaf && checkOnClickLeaf)) &&\n        !disabled\n      ) {\n        emit('handleSelectCheck', !node.checked)\n      }\n    }\n    return () => (\n      <span class={ns.e('label')} onClick={handleClick}>\n        {label()}\n      </span>\n    )\n  },\n})\n"], "names": ["isVNodeEmpty", "vnodes", "type", "defineComponent", "name", "props", "node", "required", "disabled", "Boolean", "useNamespace", "emit", "inject", "CASCADER_PANEL_INJECTION_KEY", "renderLabelFn", "checkOnClickLeaf", "label", "nodeLabel", "renderLabel", "data"], "mappings": ";;;;;;;;;;AAQA,EAASA,OAAAA,CAAAA,EAAAA,cAAAA,CAAAA,MAAaC,CAAAA,GAA0B,MAAA,CAAA,KAAA,CAAA,CAAA;IACvC,IAAA;AACaC,GAAAA,KAAAA,IAAAA,KAAAA,WAAAA,CAAAA,GAAAA,CAAAA,MAAAA,IAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAAA,CAAAA,IAAAA,MAAAA,WAAAA,CAAAA,CAAAA;;AAErB,kBAAAC,mBAAA,CAAA;;AAED,EAAA,KAAA,EAAA;AACEC,IAAAA,IAAM,EADuB;AAE7BC,MAAAA,IAAO,EAAA,MAAA;AACLC,MAAAA,QAAM,EAAA,IAAA;AACJJ,KAAAA;AACAK,IAAAA,QAAAA,EAAQ,OAAE;;AAEZC,EAAAA,KAAAA,CAAAA,KAAUC,EAAAA;IAPiB,IAAA;;IASxB,WAAQC,kBAAA,CAAA,eAAA,CAAA,CAAA;AAAEC,IAAAA,MAAAA;AAAF,MAAU,MAAA;AACrB,MAAA,aAAuB;KACjB,GAAAC,UAAA,CAAAC,kCAAA,CAAA,CAAA;UAAA;AAAUC,MAAAA,gBAAAA;MAAkBF,gBAAOC;KACnC,GAAA,MAAA,CAAA;UAAA;AAAoBE,MAAAA,IAAAA;AAApB,MAAA,QAAN;KACM,GAAA,KAAA,CAAA;UAAA;AAAQP,MAAAA,IAAAA;AAAR,MAAA,KAAN,EAAA,SAAA;KACM,GAAA,IAAA,CAAA;UAAA,KAAA,GAAA,MAAA;AAAQQ,MAAAA,MAAOC,WAAAA,GAAAA,aAAAA,IAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,aAAAA,CAAAA;AAAf,QAA6BX,IAAnC;;OAEMU,CAAAA,CAAAA;MACJ,OAAME,YAAcJ,CAAAA,WAAAA,CAAAA,GAAgB,SAAA,GAAA,WAAA,IAAA,IAAA,GAAA,WAAA,GAAA,SAAA,CAAA;;AAAQK,IAAAA,SAAAA,WAAAA,GAAAA;AAAR,MAAA,IAApC,CAAA,gBAAA,IAAA,IAAA,CAAA,MAAA,IAAA,gBAAA,KAAA,CAAA,QAAA,EAAA;QACOnB,IAAAA,CAAAA,mBAAY,EAAA,CAAA,YAAZ,CAAA,CAAA;OAFT;;AAIA,IAAA,OAAA,qBAAuB,CAAA,MAAA,EAAA;MACrB,OACmB,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA;AAGjBW,MAAAA,SAAI,EAAA,WAAA;AACL,KAAA,EAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA;AACF,GAAA;;;;;"}