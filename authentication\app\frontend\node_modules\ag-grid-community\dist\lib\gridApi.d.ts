import { AlignedGridsService } from "./alignedGridsService";
import { ColumnApi } from "./columns/columnApi";
import { ISizeColumnsToFitParams } from "./columns/columnModel";
import { Context } from "./context/context";
import { CellPosition } from "./entities/cellPositionUtils";
import { ColDef, ColGroupDef, IAggFunc } from "./entities/colDef";
import { Column } from "./entities/column";
import { ChartRef, DomLayoutType, GetChartToolbarItems, GetContextMenuItems, GetMainMenuItems, GetRowIdFunc, GetServerSideGroupKey, GridOptions, IsApplyServerSideTransaction, IsRowMaster, IsRowSelectable, IsServerSideGroup, RowClassParams, RowGroupingDisplayType, ServerSideGroupLevelParams, UseGroupFooter } from "./entities/gridOptions";
import { GetGroupRowAggParams, GetServerSideGroupLevelParamsParams, InitialGroupOrderComparatorParams, IsFullWidthRowParams, IsServerSideGroupOpenByDefaultParams, NavigateToNextCellParams, NavigateToNextHeaderParams, PaginationNumberFormatterParams, PostProcessPopupParams, PostSortRowsParams, ProcessRowParams, RowHeightParams, TabToNextCellParams, TabToNextHeaderParams } from "./interfaces/iCallbackParams";
import { IRowNode, RowPinnedType } from "./interfaces/iRowNode";
import { AgEvent, ColumnEventType, FilterChangedEventSourceType, SelectionEventSourceType } from "./events";
import { RowDropZoneEvents, RowDropZoneParams } from "./gridBodyComp/rowDragFeature";
import { PropertyChangeSet } from "./gridOptionsService";
import { HeaderPosition } from "./headerRendering/common/headerPosition";
import { CsvExportParams, ProcessCellForExportParams } from "./interfaces/exportParams";
import { ICellEditor } from "./interfaces/iCellEditor";
import { ChartDownloadParams, ChartModel, CloseChartToolPanelParams, CreateCrossFilterChartParams, CreatePivotChartParams, CreateRangeChartParams, GetChartImageDataUrlParams, OpenChartToolPanelParams, UpdateChartParams } from './interfaces/IChartService';
import { ClientSideRowModelStep } from "./interfaces/iClientSideRowModel";
import { IClipboardCopyParams, IClipboardCopyRowsParams } from "./interfaces/iClipboardService";
import { IColumnToolPanel } from "./interfaces/iColumnToolPanel";
import { IDatasource } from "./interfaces/iDatasource";
import { ExcelExportMultipleSheetParams, ExcelExportParams } from "./interfaces/iExcelCreator";
import { IFilter } from "./interfaces/iFilter";
import { IFiltersToolPanel } from "./interfaces/iFiltersToolPanel";
import { CellRange, CellRangeParams } from "./interfaces/IRangeService";
import { IRowModel } from "./interfaces/iRowModel";
import { IServerSideDatasource } from "./interfaces/iServerSideDatasource";
import { RefreshServerSideParams } from "./interfaces/iServerSideRowModel";
import { ServerSideGroupLevelState } from "./interfaces/IServerSideStore";
import { ISideBar, SideBarDef } from "./interfaces/iSideBar";
import { IStatusPanel } from "./interfaces/iStatusPanel";
import { IToolPanel } from "./interfaces/iToolPanel";
import { IViewportDatasource } from "./interfaces/iViewportDatasource";
import { RowDataTransaction } from "./interfaces/rowDataTransaction";
import { RowNodeTransaction } from "./interfaces/rowNodeTransaction";
import { ServerSideTransaction, ServerSideTransactionResult } from "./interfaces/serverSideTransaction";
import { ICellRenderer } from "./rendering/cellRenderers/iCellRenderer";
import { OverlayWrapperComponent } from "./rendering/overlays/overlayWrapperComponent";
import { FlashCellsParams, GetCellEditorInstancesParams, GetCellRendererInstancesParams, RedrawRowsParams, RefreshCellsParams } from "./rendering/rowRenderer";
import { IServerSideGroupSelectionState, IServerSideSelectionState } from "./interfaces/iServerSideSelection";
import { DataTypeDefinition } from "./entities/dataType";
import { AdvancedFilterModel } from "./interfaces/advancedFilterModel";
import { LoadSuccessParams } from "./rowNodeCache/rowNodeBlock";
import { IAdvancedFilterBuilderParams } from "./interfaces/iAdvancedFilterBuilderParams";
export interface DetailGridInfo {
    /**
     * Id of the detail grid, the format is `detail_{ROW-ID}`,
     * where `ROW-ID` is the `id` of the parent row.
     */
    id: string;
    /** Grid api of the detail grid. */
    api?: GridApi;
    /** Column api of the detail grid. */
    columnApi?: ColumnApi;
}
export interface StartEditingCellParams {
    /** The row index of the row to start editing */
    rowIndex: number;
    /** The column key of the row to start editing */
    colKey: string | Column;
    /** Set to `'top'` or `'bottom'` to start editing a pinned row */
    rowPinned?: RowPinnedType;
    /** The key to pass to the cell editor */
    key?: string;
}
export declare function unwrapUserComp<T>(comp: T): T;
export declare class GridApi<TData = any> {
    private immutableService;
    private csvCreator;
    private excelCreator;
    private rowRenderer;
    private navigationService;
    private filterManager;
    private columnModel;
    private selectionService;
    private gos;
    private valueService;
    private alignedGridsService;
    private eventService;
    private pinnedRowModel;
    private context;
    private rowModel;
    private sortController;
    private paginationProxy;
    private focusService;
    private dragAndDropService;
    private rangeService;
    private clipboardService;
    private aggFuncService;
    private menuFactory;
    private contextMenuFactory;
    private valueCache;
    private animationFrameService;
    private statusBarService;
    private chartService;
    private undoRedoService;
    private rowNodeBlockLoader;
    private serverSideTransactionManager;
    private ctrlsService;
    private overlayWrapperComp;
    private gridBodyCtrl;
    private sideBarComp;
    private clientSideRowModel;
    private infiniteRowModel;
    private serverSideRowModel;
    private detailGridInfoMap;
    private destroyCalled;
    registerOverlayWrapperComp(overlayWrapperComp: OverlayWrapperComponent): void;
    registerSideBarComp(sideBarComp: ISideBar): void;
    private init;
    /** Used internally by grid. Not intended to be used by the client. Interface may change between releases. */
    __getAlignedGridService(): AlignedGridsService;
    /** Used internally by grid. Not intended to be used by the client. Interface may change between releases. */
    __getContext(): Context;
    private getSetterMethod;
    /** Used internally by grid. Not intended to be used by the client. Interface may change between releases. */
    __setPropertyOnly<K extends keyof GridOptions>(propertyName: K, value: GridOptions[K]): boolean;
    /** Used internally by grid. Not intended to be used by the client. Interface may change between releases. */
    __updateProperty<K extends keyof GridOptions>(propertyName: K, value: GridOptions[K], force: boolean, changeSet?: PropertyChangeSet | undefined): void;
    /** Returns the `gridId` for the current grid as specified via the gridOptions property `gridId` or the auto assigned grid id if none was provided. */
    getGridId(): string;
    /** Register a detail grid with the master grid when it is created. */
    addDetailGridInfo(id: string, gridInfo: DetailGridInfo): void;
    /** Unregister a detail grid from the master grid when it is destroyed. */
    removeDetailGridInfo(id: string): void;
    /** Returns the `DetailGridInfo` corresponding to the supplied `detailGridId`. */
    getDetailGridInfo(id: string): DetailGridInfo | undefined;
    /** Iterates through each `DetailGridInfo` in the grid and calls the supplied callback on each. */
    forEachDetailGridInfo(callback: (gridInfo: DetailGridInfo, index: number) => void): void;
    /** Similar to `exportDataAsCsv`, except returns the result as a string rather than download it. */
    getDataAsCsv(params?: CsvExportParams): string | undefined;
    /** Downloads a CSV export of the grid's data. */
    exportDataAsCsv(params?: CsvExportParams): void;
    private getExcelExportMode;
    private assertNotExcelMultiSheet;
    /** Similar to `exportDataAsExcel`, except instead of downloading a file, it will return a [Blob](https://developer.mozilla.org/en-US/docs/Web/API/Blob) to be processed by the user. */
    getDataAsExcel(params?: ExcelExportParams): string | Blob | undefined;
    /** Downloads an Excel export of the grid's data. */
    exportDataAsExcel(params?: ExcelExportParams): void;
    /** This is method to be used to get the grid's data as a sheet, that will later be exported either by `getMultipleSheetsAsExcel()` or `exportMultipleSheetsAsExcel()`. */
    getSheetDataForExcel(params?: ExcelExportParams): string | undefined;
    /** Similar to `exportMultipleSheetsAsExcel`, except instead of downloading a file, it will return a [Blob](https://developer.mozilla.org/en-US/docs/Web/API/Blob) to be processed by the user. */
    getMultipleSheetsAsExcel(params: ExcelExportMultipleSheetParams): Blob | undefined;
    /** Downloads an Excel export of multiple sheets in one file. */
    exportMultipleSheetsAsExcel(params: ExcelExportMultipleSheetParams): void;
    /** Sets the `treeData` property. */
    setTreeData(newTreeData: boolean): void;
    /**
     * Sets an ARIA property in the grid panel (element with `role=\"grid\"`), and removes an ARIA property when the value is null.
     *
     * Example: `api.setGridAriaProperty('label', 'my grid')` will set `aria-label=\"my grid\"`.
     *
     * `api.setGridAriaProperty('label', null)` will remove the `aria-label` attribute from the grid element.
     */
    setGridAriaProperty(property: string, value: string | null): void;
    private logMissingRowModel;
    /** Set new datasource for Server-Side Row Model. */
    setServerSideDatasource(datasource: IServerSideDatasource): void;
    /**
     * Updates the `cacheBlockSize` when requesting data from the server if `suppressServerSideInfiniteScroll` is not enabled.
     *
     * Note this purges all the cached data and reloads all the rows of the grid.
     * */
    setCacheBlockSize(blockSize: number): void;
    /** Set new datasource for Infinite Row Model. */
    setDatasource(datasource: IDatasource): void;
    /** Set new datasource for Viewport Row Model. */
    setViewportDatasource(viewportDatasource: IViewportDatasource): void;
    /** Set the row data. */
    setRowData(rowData: TData[]): void;
    /** Set the top pinned rows. Call with no rows / undefined to clear top pinned rows. */
    setPinnedTopRowData(rows?: any[]): void;
    /** Set the bottom pinned rows. Call with no rows / undefined to clear bottom pinned rows. */
    setPinnedBottomRowData(rows?: any[]): void;
    /** Gets the number of top pinned rows. */
    getPinnedTopRowCount(): number;
    /** Gets the number of bottom pinned rows. */
    getPinnedBottomRowCount(): number;
    /** Gets the top pinned row with the specified index. */
    getPinnedTopRow(index: number): IRowNode | undefined;
    /** Gets the bottom pinned row with the specified index. */
    getPinnedBottomRow(index: number): IRowNode | undefined;
    /**
     * Call to set new column definitions. The grid will redraw all the column headers, and then redraw all of the rows.
     */
    setColumnDefs(colDefs: (ColDef<TData> | ColGroupDef<TData>)[], source?: ColumnEventType): void;
    /** Call to set new auto group column definition. The grid will recreate any auto-group columns if present. */
    setAutoGroupColumnDef(colDef: ColDef<TData>, source?: ColumnEventType): void;
    /** Call to set new Default Column Definition. */
    setDefaultColDef(colDef: ColDef<TData>, source?: ColumnEventType): void;
    /** Call to set new Column Types. */
    setColumnTypes(columnTypes: {
        string: ColDef<TData>;
    }, source?: ColumnEventType): void;
    expireValueCache(): void;
    /**
     * Returns an object with two properties:
     *  - `top`: The top pixel position of the current scroll in the grid
     *  - `bottom`: The bottom pixel position of the current scroll in the grid
     */
    getVerticalPixelRange(): {
        top: number;
        bottom: number;
    };
    /**
     * Returns an object with two properties:
     * - `left`: The left pixel position of the current scroll in the grid
     * - `right`: The right pixel position of the current scroll in the grid
     */
    getHorizontalPixelRange(): {
        left: number;
        right: number;
    };
    /** If `true`, the horizontal scrollbar will always be present, even if not required. Otherwise, it will only be displayed when necessary. */
    setAlwaysShowHorizontalScroll(show: boolean): void;
    /** If `true`, the vertical scrollbar will always be present, even if not required. Otherwise it will only be displayed when necessary. */
    setAlwaysShowVerticalScroll(show: boolean): void;
    /** Performs change detection on all cells, refreshing cells where required. */
    refreshCells(params?: RefreshCellsParams<TData>): void;
    /** Flash rows, columns or individual cells. */
    flashCells(params?: FlashCellsParams<TData>): void;
    /** Remove row(s) from the DOM and recreate them again from scratch. */
    redrawRows(params?: RedrawRowsParams<TData>): void;
    setFunctionsReadOnly(readOnly: boolean): void;
    /** Redraws the header. Useful if a column name changes, or something else that changes how the column header is displayed. */
    refreshHeader(): void;
    /** Returns `true` if any filter is set. This includes quick filter, column filter, external filter or advanced filter. */
    isAnyFilterPresent(): boolean;
    /** Returns `true` if any column filter is set, otherwise `false`. */
    isColumnFilterPresent(): boolean;
    /** Returns `true` if the Quick Filter is set, otherwise `false`. */
    isQuickFilterPresent(): boolean;
    /**
     * Returns the row model inside the table.
     * From here you can see the original rows, rows after filter has been applied,
     * rows after aggregation has been applied, and the final set of 'to be displayed' rows.
     */
    getModel(): IRowModel;
    /** Expand or collapse a specific row node, optionally expanding/collapsing all of its parent nodes. */
    setRowNodeExpanded(rowNode: IRowNode, expanded: boolean, expandParents?: boolean): void;
    /**
     * Informs the grid that row group expanded state has changed and it needs to rerender the group nodes.
     * Typically called after updating the row node expanded state explicitly, i.e `rowNode.expanded = false`,
     * across multiple groups and you want to update the grid view in a single rerender instead of on every group change.
     */
    onGroupExpandedOrCollapsed(): void;
    /**
     * Refresh the Client-Side Row Model, executing the grouping, filtering and sorting again.
     * Optionally provide the step you wish the refresh to apply from. Defaults to `everything`.
     */
    refreshClientSideRowModel(step?: ClientSideRowModelStep): any;
    /** Returns `true` when there are no more animation frames left to process. */
    isAnimationFrameQueueEmpty(): boolean;
    flushAllAnimationFrames(): void;
    /**
     * Returns the row node with the given ID.
     * The row node ID is the one you provide from the callback `getRowId(params)`,
     * otherwise the ID is a number (cast as string) auto-generated by the grid when
     * the row data is set.
     */
    getRowNode(id: string): IRowNode<TData> | undefined;
    /**
     * Gets the sizes that various UI elements will be rendered at with the current theme.
     * If you override the row or header height using `gridOptions`, the override value you provided will be returned.
     */
    getSizesForCurrentTheme(): {
        rowHeight: number;
        headerHeight: number;
    };
    /** Expand all groups. */
    expandAll(): void;
    /** Collapse all groups. */
    collapseAll(): void;
    /**
     * Registers a callback to a virtual row.
     * A virtual row is a row that is visually rendered on the screen (rows that are not visible because of the scroll position are not rendered).
     * Unlike normal events, you do not need to unregister rendered row listeners.
     * When the rendered row is removed from the grid, all associated rendered row listeners will also be removed.
     * listen for this event if your `cellRenderer` needs to do cleanup when the row no longer exists.
     */
    addRenderedRowListener(eventName: string, rowIndex: number, callback: Function): void;
    /** Get the current Quick Filter text from the grid, or `undefined` if none is set. */
    getQuickFilter(): string | undefined;
    /** Pass a Quick Filter text into the grid for filtering. */
    setQuickFilter(newFilter: string): void;
    /**
     * @deprecated As of v30, hidden columns are excluded from the Quick Filter by default. To include hidden columns, use `setIncludeHiddenColumnsInQuickFilter` instead.
     */
    setExcludeHiddenColumnsFromQuickFilter(value: boolean): void;
    /**
     * Updates the `includeHiddenColumnsInQuickFilter` grid option.
     * By default hidden columns are excluded from the Quick Filter.
     * Set to `true` to include them.
     */
    setIncludeHiddenColumnsInQuickFilter(value: boolean): void;
    /**
     * Updates the `quickFilterParser` grid option,
     * which changes how the Quick Filter splits the Quick Filter text into search terms.
     */
    setQuickFilterParser(quickFilterParser?: (quickFilter: string) => string[]): void;
    /**
     * Updates the `quickFilterMatcher` grid option,
     * which changes the matching logic for whether a row passes the Quick Filter.
     */
    setQuickFilterMatcher(quickFilterMatcher?: (quickFilterParts: string[], rowQuickFilterAggregateText: string) => boolean): void;
    /** Get the state of the Advanced Filter. Used for saving Advanced Filter state */
    getAdvancedFilterModel(): AdvancedFilterModel | null;
    /** Set the state of the Advanced Filter. Used for restoring Advanced Filter state */
    setAdvancedFilterModel(advancedFilterModel: AdvancedFilterModel | null): void;
    /** Enable/disable the Advanced Filter */
    setEnableAdvancedFilter(enabled: boolean): void;
    /**
     * Updates the `includeHiddenColumnsInAdvancedFilter` grid option.
     * By default hidden columns are excluded from the Advanced Filter.
     * Set to `true` to include them.
     */
    setIncludeHiddenColumnsInAdvancedFilter(value: boolean): void;
    /**
     * DOM element to use as the parent for the Advanced Filter, to allow it to appear outside of the grid.
     * Set to `null` to appear inside the grid.
     */
    setAdvancedFilterParent(advancedFilterParent: HTMLElement | null): void;
    /** Updates the Advanced Filter Builder parameters. */
    setAdvancedFilterBuilderParams(params?: IAdvancedFilterBuilderParams): void;
    /** Open the Advanced Filter Builder dialog (if enabled). */
    showAdvancedFilterBuilder(): void;
    /**
     * Set all of the provided nodes selection state to the provided value.
     */
    setNodesSelected(params: {
        nodes: IRowNode[];
        newValue: boolean;
        source?: SelectionEventSourceType;
    }): void;
    /**
     * Select all rows, regardless of filtering and rows that are not visible due to grouping being enabled and their groups not expanded.
     * @param source Source property that will appear in the `selectionChanged` event, defaults to `'apiSelectAll'`
     */
    selectAll(source?: SelectionEventSourceType): void;
    /**
     * Clear all row selections, regardless of filtering.
     * @param source Source property that will appear in the `selectionChanged` event, defaults to `'apiSelectAll'`
     */
    deselectAll(source?: SelectionEventSourceType): void;
    /**
     * Select all filtered rows.
     * @param source Source property that will appear in the `selectionChanged` event, defaults to `'apiSelectAllFiltered'`
     */
    selectAllFiltered(source?: SelectionEventSourceType): void;
    /**
     * Clear all filtered selections.
     * @param source Source property that will appear in the `selectionChanged` event, defaults to `'apiSelectAllFiltered'`
     */
    deselectAllFiltered(source?: SelectionEventSourceType): void;
    /**
     * Returns an object containing rules matching the selected rows in the SSRM.
     *
     * If `groupSelectsChildren=false` the returned object will be flat, and will conform to IServerSideSelectionState.
     * If `groupSelectsChildren=true` the returned object will be hierarchical, and will conform to IServerSideGroupSelectionState.
     */
    getServerSideSelectionState(): IServerSideSelectionState | IServerSideGroupSelectionState | null;
    /**
     * Set the rules matching the selected rows in the SSRM.
     *
     * If `groupSelectsChildren=false` the param will be flat, and should conform to IServerSideSelectionState.
     * If `groupSelectsChildren=true` the param will be hierarchical, and should conform to IServerSideGroupSelectionState.
     */
    setServerSideSelectionState(state: IServerSideSelectionState | IServerSideGroupSelectionState): void;
    /**
     * Select all rows on the current page.
     * @param source Source property that will appear in the `selectionChanged` event, defaults to `'apiSelectAllCurrentPage'`
     */
    selectAllOnCurrentPage(source?: SelectionEventSourceType): void;
    /**
     * Clear all filtered on the current page.
     * @param source Source property that will appear in the `selectionChanged` event, defaults to `'apiSelectAllCurrentPage'`
     */
    deselectAllOnCurrentPage(source?: SelectionEventSourceType): void;
    /**
     * Sets columns to adjust in size to fit the grid horizontally. If inferring cell data types with custom column types
     * and row data is provided asynchronously, the column sizing will happen asynchronously when row data is added.
     * To always perform this synchronously, set `cellDataType = false` on the default column definition.
     **/
    sizeColumnsToFit(params?: ISizeColumnsToFitParams): void;
    /** Show the 'loading' overlay. */
    showLoadingOverlay(): void;
    /** Show the 'no rows' overlay. */
    showNoRowsOverlay(): void;
    /** Hides the overlay if showing. */
    hideOverlay(): void;
    /**
     * Returns an unsorted list of selected nodes.
     * Getting the underlying node (rather than the data) is useful when working with tree / aggregated data,
     * as the node can be traversed.
     */
    getSelectedNodes(): IRowNode<TData>[];
    /** Returns an unsorted list of selected rows (i.e. row data that you provided). */
    getSelectedRows(): TData[];
    /**
     * Returns a list of all selected nodes at 'best cost', a feature to be used with groups / trees.
     * If a group has all its children selected, then the group appears in the result, but not the children.
     * Designed for use with `'children'` as the group selection type, where groups don't actually appear in the selection normally.
     */
    getBestCostNodeSelection(): IRowNode<TData>[] | undefined;
    /** Retrieve rendered nodes. Due to virtualisation this will contain only the current visible rows and those in the buffer. */
    getRenderedNodes(): IRowNode<TData>[];
    /**
     *  Ensures the column is visible by scrolling the table if needed.
     *
     * This will have no effect before the firstDataRendered event has fired.
     *
     * @param key - The column to ensure visible
     * @param position - Where the column will be positioned.
     * - `auto` - Scrolls the minimum amount to make sure the column is visible.
     * - `start` - Scrolls the column to the start of the viewport.
     * - `middle` - Scrolls the column to the middle of the viewport.
     * - `end` - Scrolls the column to the end of the viewport.
    */
    ensureColumnVisible(key: string | Column, position?: 'auto' | 'start' | 'middle' | 'end'): void;
    /**
     * Vertically scrolls the grid until the provided row index is inside the visible viewport.
     * If a position is provided, the grid will attempt to scroll until the row is at the given position within the viewport.
     * This will have no effect before the firstDataRendered event has fired.
     */
    ensureIndexVisible(index: number, position?: 'top' | 'bottom' | 'middle' | null): void;
    /**
     * Vertically scrolls the grid until the provided row (or a row matching the provided comparator) is inside the visible viewport.
     * If a position is provided, the grid will attempt to scroll until the row is at the given position within the viewport.
     * This will have no effect before the firstDataRendered event has fired.
     */
    ensureNodeVisible(nodeSelector: TData | IRowNode<TData> | ((row: IRowNode<TData>) => boolean), position?: 'top' | 'bottom' | 'middle' | null): void;
    /**
     * Similar to `forEachNode`, except lists all the leaf nodes.
     * This effectively goes through all the data that you provided to the grid before the grid performed any grouping.
     * If using tree data, goes through all the nodes for the data you provided, including nodes that have children,
     * but excluding groups the grid created where gaps were missing in the hierarchy.
     */
    forEachLeafNode(callback: (rowNode: IRowNode<TData>) => void): void;
    /**
     * Iterates through each node (row) in the grid and calls the callback for each node.
     * This works similar to the `forEach` method on a JavaScript array.
     * This is called for every node, ignoring any filtering or sorting applied within the grid.
     * If using the Infinite Row Model, then this gets called for each page loaded in the page cache.
     */
    forEachNode(callback: (rowNode: IRowNode<TData>, index: number) => void, includeFooterNodes?: boolean): void;
    /** Similar to `forEachNode`, except skips any filtered out data. */
    forEachNodeAfterFilter(callback: (rowNode: IRowNode<TData>, index: number) => void): void;
    /** Similar to `forEachNodeAfterFilter`, except the callbacks are called in the order the rows are displayed in the grid. */
    forEachNodeAfterFilterAndSort(callback: (rowNode: IRowNode<TData>, index: number) => void): void;
    /**
     * Returns the filter component instance for a column.
     * `key` can be a string field name or a ColDef object (matches on object reference, useful if field names are not unique).
     * If your filter is created asynchronously, `getFilterInstance` will return `null` so you will need to use the `callback` to access the filter instance instead.
     */
    getFilterInstance<TFilter extends IFilter>(key: string | Column, callback?: (filter: TFilter | null) => void): TFilter | null | undefined;
    /** Destroys a filter. Useful to force a particular filter to be created from scratch again. */
    destroyFilter(key: string | Column): void;
    /** Gets the status panel instance corresponding to the supplied `id`. */
    getStatusPanel<TStatusPanel = IStatusPanel>(key: string): TStatusPanel | undefined;
    getColumnDef<TValue = any>(key: string | Column<TValue>): ColDef<TData, TValue> | null;
    /**
     * Returns the current column definitions.
    */
    getColumnDefs(): (ColDef<TData> | ColGroupDef<TData>)[] | undefined;
    /**
     * Informs the grid that a filter has changed. This is typically called after a filter change through one of the filter APIs.
     * @param source The source of the filter change event. If not specified defaults to `'api'`.
     */
    onFilterChanged(source?: FilterChangedEventSourceType): void;
    /**
     * Gets the grid to act as if the sort was changed.
     * Useful if you update some values and want to get the grid to reorder them according to the new values.
     */
    onSortChanged(): void;
    /**
     * Sets the state of all the column filters. Provide it with what you get from `getFilterModel()` to restore filter state.
     * If inferring cell data types, and row data is provided asynchronously and is yet to be set,
     * the filter model will be applied asynchronously after row data is added.
     * To always perform this synchronously, set `cellDataType = false` on the default column definition,
     * or provide cell data types for every column.
     */
    setFilterModel(model: any): void;
    /** Gets the current state of all the column filters. Used for saving filter state. */
    getFilterModel(): {
        [key: string]: any;
    };
    /** Returns the focused cell (or the last focused cell if the grid lost focus). */
    getFocusedCell(): CellPosition | null;
    /** Clears the focused cell. */
    clearFocusedCell(): void;
    /** Sets the focus to the specified cell. `rowPinned` can be either 'top', 'bottom' or null (for not pinned). */
    setFocusedCell(rowIndex: number, colKey: string | Column, rowPinned?: RowPinnedType): void;
    /** Sets the `suppressRowDrag` property. */
    setSuppressRowDrag(value: boolean): void;
    /** Sets the `suppressMoveWhenRowDragging` property. */
    setSuppressMoveWhenRowDragging(value: boolean): void;
    /** Sets the `suppressRowClickSelection` property. */
    setSuppressRowClickSelection(value: boolean): void;
    /** Adds a drop zone outside of the grid where rows can be dropped. */
    addRowDropZone(params: RowDropZoneParams): void;
    /** Removes an external drop zone added by `addRowDropZone`. */
    removeRowDropZone(params: RowDropZoneParams): void;
    /** Returns the `RowDropZoneParams` to be used by another grid's `addRowDropZone` method. */
    getRowDropZoneParams(events?: RowDropZoneEvents): RowDropZoneParams;
    /** Sets the height in pixels for the row containing the column label header. */
    setHeaderHeight(headerHeight?: number): void;
    /**
     * Switch between layout options: `normal`, `autoHeight`, `print`.
     * Defaults to `normal` if no domLayout provided.
     */
    setDomLayout(domLayout?: DomLayoutType): void;
    /** Sets the `enableCellTextSelection` property. */
    setEnableCellTextSelection(selectable: boolean): void;
    /** Sets the preferred direction for the selection fill handle. */
    setFillHandleDirection(direction: 'x' | 'y' | 'xy'): void;
    /** Sets the height in pixels for the rows containing header column groups. */
    setGroupHeaderHeight(headerHeight?: number): void;
    /** Sets the height in pixels for the row containing the floating filters. */
    setFloatingFiltersHeight(headerHeight?: number): void;
    /** Sets the height in pixels for the row containing the columns when in pivot mode. */
    setPivotHeaderHeight(headerHeight?: number): void;
    /** Sets the height in pixels for the row containing header column groups when in pivot mode. */
    setPivotGroupHeaderHeight(headerHeight?: number): void;
    setPivotMode(pivotMode: boolean): void;
    setAnimateRows(animateRows: boolean): void;
    setIsExternalFilterPresent(isExternalFilterPresentFunc: () => boolean): void;
    setDoesExternalFilterPass(doesExternalFilterPassFunc: (node: IRowNode) => boolean): void;
    setNavigateToNextCell(navigateToNextCellFunc: (params: NavigateToNextCellParams) => (CellPosition | null)): void;
    setTabToNextCell(tabToNextCellFunc: (params: TabToNextCellParams) => (CellPosition | null)): void;
    setTabToNextHeader(tabToNextHeaderFunc: (params: TabToNextHeaderParams) => (HeaderPosition | null)): void;
    setNavigateToNextHeader(navigateToNextHeaderFunc: (params: NavigateToNextHeaderParams) => (HeaderPosition | null)): void;
    setRowGroupPanelShow(rowGroupPanelShow: 'always' | 'onlyWhenGrouping' | 'never'): void;
    setGetGroupRowAgg(getGroupRowAggFunc: (params: GetGroupRowAggParams) => any): void;
    setGetBusinessKeyForNode(getBusinessKeyForNodeFunc: (nodes: IRowNode) => string): void;
    setGetChildCount(getChildCountFunc: (dataItem: any) => number): void;
    setProcessRowPostCreate(processRowPostCreateFunc: (params: ProcessRowParams) => void): void;
    setGetRowId(getRowIdFunc: GetRowIdFunc): void;
    setGetRowClass(rowClassFunc: (params: RowClassParams) => string | string[]): void;
    setIsFullWidthRow(isFullWidthRowFunc: (params: IsFullWidthRowParams) => boolean): void;
    setIsRowSelectable(isRowSelectableFunc: IsRowSelectable): void;
    setIsRowMaster(isRowMasterFunc: IsRowMaster): void;
    setPostSortRows(postSortRowsFunc: (params: PostSortRowsParams) => void): void;
    setGetDocument(getDocumentFunc: () => Document): void;
    setGetContextMenuItems(getContextMenuItemsFunc: GetContextMenuItems): void;
    setGetMainMenuItems(getMainMenuItemsFunc: GetMainMenuItems): void;
    setProcessCellForClipboard(processCellForClipboardFunc: (params: ProcessCellForExportParams) => any): void;
    setSendToClipboard(sendToClipboardFunc: (params: {
        data: string;
    }) => void): void;
    setProcessCellFromClipboard(processCellFromClipboardFunc: (params: ProcessCellForExportParams) => any): void;
    /** @deprecated v28 use `setProcessPivotResultColDef` instead */
    setProcessSecondaryColDef(processSecondaryColDefFunc: (colDef: ColDef) => void): void;
    /** @deprecated v28 use `setProcessPivotResultColGroupDef` instead */
    setProcessSecondaryColGroupDef(processSecondaryColGroupDefFunc: (colDef: ColDef) => void): void;
    setProcessPivotResultColDef(processPivotResultColDefFunc: (colDef: ColDef) => void): void;
    setProcessPivotResultColGroupDef(processPivotResultColGroupDefFunc: (colDef: ColDef) => void): void;
    setPostProcessPopup(postProcessPopupFunc: (params: PostProcessPopupParams) => void): void;
    setInitialGroupOrderComparator(initialGroupOrderComparatorFunc: (params: InitialGroupOrderComparatorParams) => number): void;
    setGetChartToolbarItems(getChartToolbarItemsFunc: GetChartToolbarItems): void;
    setPaginationNumberFormatter(paginationNumberFormatterFunc: (params: PaginationNumberFormatterParams) => string): void;
    /** @deprecated v28 use setGetServerSideGroupLevelParams instead */
    setGetServerSideStoreParams(getServerSideStoreParamsFunc: (params: GetServerSideGroupLevelParamsParams) => ServerSideGroupLevelParams): void;
    setGetServerSideGroupLevelParams(getServerSideGroupLevelParamsFunc: (params: GetServerSideGroupLevelParamsParams) => ServerSideGroupLevelParams): void;
    setIsServerSideGroupOpenByDefault(isServerSideGroupOpenByDefaultFunc: (params: IsServerSideGroupOpenByDefaultParams) => boolean): void;
    setIsApplyServerSideTransaction(isApplyServerSideTransactionFunc: IsApplyServerSideTransaction): void;
    setIsServerSideGroup(isServerSideGroupFunc: IsServerSideGroup): void;
    setGetServerSideGroupKey(getServerSideGroupKeyFunc: GetServerSideGroupKey): void;
    setGetRowStyle(rowStyleFunc: (params: RowClassParams) => {}): void;
    setGetRowHeight(rowHeightFunc: (params: RowHeightParams) => number): void;
    private assertSideBarLoaded;
    /** Returns `true` if the side bar is visible. */
    isSideBarVisible(): boolean;
    /** Show/hide the entire side bar, including any visible panel and the tab buttons. */
    setSideBarVisible(show: boolean): void;
    /** Sets the side bar position relative to the grid. Possible values are `'left'` or `'right'`. */
    setSideBarPosition(position: 'left' | 'right'): void;
    /** Opens a particular tool panel. Provide the ID of the tool panel to open. */
    openToolPanel(key: string): void;
    /** Closes the currently open tool panel (if any). */
    closeToolPanel(): void;
    /** Returns the ID of the currently shown tool panel if any, otherwise `null`. */
    getOpenedToolPanel(): string | null;
    /** Force refresh all tool panels by calling their `refresh` method. */
    refreshToolPanel(): void;
    /** Returns `true` if the tool panel is showing, otherwise `false`. */
    isToolPanelShowing(): boolean;
    getToolPanelInstance(id: 'columns'): IColumnToolPanel | undefined;
    getToolPanelInstance(id: 'filters'): IFiltersToolPanel | undefined;
    getToolPanelInstance<TToolPanel = IToolPanel>(id: string): TToolPanel | undefined;
    /** Returns the current side bar configuration. If a shortcut was used, returns the detailed long form. */
    getSideBar(): SideBarDef | undefined;
    /** Resets the side bar to the provided configuration. The parameter is the same as the sideBar grid property. The side bar is re-created from scratch with the new config. */
    setSideBar(def: SideBarDef | string | string[] | boolean): void;
    setSuppressClipboardPaste(value: boolean): void;
    /** Tells the grid to recalculate the row heights. */
    resetRowHeights(): void;
    setGroupRemoveSingleChildren(value: boolean): void;
    setGroupRemoveLowestSingleChildren(value: boolean): void;
    setGroupDisplayType(value: RowGroupingDisplayType): void;
    /**
     * Sets the `groupIncludeFooter` property
     */
    setGroupIncludeFooter(value: boolean | UseGroupFooter<TData>): void;
    /**
     * Sets the `groupIncludeTotalFooter` property
     */
    setGroupIncludeTotalFooter(value: boolean): void;
    setRowClass(className: string | undefined): void;
    /** Sets the `deltaSort` property */
    setDeltaSort(enable: boolean): void;
    /**
     * Sets the `rowCount` and `maxRowFound` properties.
     * The second parameter, `maxRowFound`, is optional and if left out, only `rowCount` is set.
     * Set `rowCount` to adjust the height of the vertical scroll.
     * Set `maxRowFound` to enable / disable searching for more rows.
     * Use this method if you add or remove rows into the dataset and need to reset the number of rows or instruct the grid that the entire row count is no longer known.
     */
    setRowCount(rowCount: number, maxRowFound?: boolean): void;
    /** Tells the grid a row height has changed. To be used after calling `rowNode.setRowHeight(newHeight)`. */
    onRowHeightChanged(): void;
    /**
     * Gets the value for a column for a particular `rowNode` (row).
     * This is useful if you want the raw value of a cell e.g. if implementing your own CSV export.
     */
    getValue<TValue = any>(colKey: string | Column<TValue>, rowNode: IRowNode): TValue | null | undefined;
    /** Add an event listener for the specified `eventType`. Works similar to `addEventListener` for a browser DOM element. */
    addEventListener(eventType: string, listener: Function): void;
    /** Add an event listener for all event types coming from the grid. */
    addGlobalListener(listener: Function): void;
    /** Remove an event listener. */
    removeEventListener(eventType: string, listener: Function): void;
    /** Remove a global event listener. */
    removeGlobalListener(listener: Function): void;
    dispatchEvent(event: AgEvent): void;
    /** Will destroy the grid and release resources. If you are using a framework you do not need to call this, as the grid links in with the framework lifecycle. However if you are using Web Components or native JavaScript, you do need to call this, to avoid a memory leak in your application. */
    destroy(): void;
    private cleanDownReferencesToAvoidMemoryLeakInCaseApplicationIsKeepingReferenceToDestroyedGrid;
    private warnIfDestroyed;
    /** Reset the Quick Filter cache text on every rowNode. */
    resetQuickFilter(): void;
    /** Returns the list of selected cell ranges. */
    getCellRanges(): CellRange[] | null;
    /** Adds the provided cell range to the selected ranges. */
    addCellRange(params: CellRangeParams): void;
    /** Clears the selected ranges. */
    clearRangeSelection(): void;
    /** Reverts the last cell edit. */
    undoCellEditing(): void;
    /** Re-applies the most recently undone cell edit. */
    redoCellEditing(): void;
    /** Returns current number of available cell edit undo operations. */
    getCurrentUndoSize(): number;
    /** Returns current number of available cell edit redo operations. */
    getCurrentRedoSize(): number;
    /** Returns a list of models with information about the charts that are currently rendered from the grid. */
    getChartModels(): ChartModel[] | undefined;
    /** Returns the `ChartRef` using the supplied `chartId`. */
    getChartRef(chartId: string): ChartRef | undefined;
    /** Returns a base64-encoded image data URL for the referenced chartId. */
    getChartImageDataURL(params: GetChartImageDataUrlParams): string | undefined;
    /** Starts a browser-based image download for the referenced chartId. */
    downloadChart(params: ChartDownloadParams): void;
    /** Open the Chart Tool Panel. */
    openChartToolPanel(params: OpenChartToolPanelParams): void;
    /** Close the Chart Tool Panel. */
    closeChartToolPanel(params: CloseChartToolPanelParams): void;
    /** Used to programmatically create charts from a range. */
    createRangeChart(params: CreateRangeChartParams): ChartRef | undefined;
    /** Used to programmatically create pivot charts from a grid. */
    createPivotChart(params: CreatePivotChartParams): ChartRef | undefined;
    /** Used to programmatically create cross filter charts from a range. */
    createCrossFilterChart(params: CreateCrossFilterChartParams): ChartRef | undefined;
    /** Used to programmatically update a chart. */
    updateChart(params: UpdateChartParams): void;
    /** Restores a chart using the `ChartModel` that was previously obtained from `getChartModels()`. */
    restoreChart(chartModel: ChartModel, chartContainer?: HTMLElement): ChartRef | undefined;
    /** Copies data to clipboard by following the same rules as pressing Ctrl+C. */
    copyToClipboard(params?: IClipboardCopyParams): void;
    /** Cuts data to clipboard by following the same rules as pressing Ctrl+X. */
    cutToClipboard(params?: IClipboardCopyParams): void;
    /** Copies the selected rows to the clipboard. */
    copySelectedRowsToClipboard(params?: IClipboardCopyRowsParams): void;
    /** Copies the selected ranges to the clipboard. */
    copySelectedRangeToClipboard(params?: IClipboardCopyParams): void;
    /** Copies the selected range down, similar to `Ctrl + D` in Excel. */
    copySelectedRangeDown(): void;
    /** Pastes the data from the Clipboard into the focused cell of the grid. If no grid cell is focused, calling this method has no effect. */
    pasteFromClipboard(): void;
    /** Shows the column menu after and positions it relative to the provided button element. Use in conjunction with your own header template. */
    showColumnMenuAfterButtonClick(colKey: string | Column, buttonElement: HTMLElement): void;
    /** Shows the column menu after and positions it relative to the mouse event. Use in conjunction with your own header template. */
    showColumnMenuAfterMouseClick(colKey: string | Column, mouseEvent: MouseEvent | Touch): void;
    /** Hides any visible context menu or column menu. */
    hidePopupMenu(): void;
    /** DOM element to use as the popup parent for grid popups (context menu, column menu etc). */
    setPopupParent(ePopupParent: HTMLElement): void;
    /** Navigates the grid focus to the next cell, as if tabbing. */
    tabToNextCell(event?: KeyboardEvent): boolean;
    /** Navigates the grid focus to the previous cell, as if shift-tabbing. */
    tabToPreviousCell(event?: KeyboardEvent): boolean;
    /** Returns the list of active cell renderer instances. */
    getCellRendererInstances(params?: GetCellRendererInstancesParams<TData>): ICellRenderer[];
    /** Returns the list of active cell editor instances. Optionally provide parameters to restrict to certain columns / row nodes. */
    getCellEditorInstances(params?: GetCellEditorInstancesParams<TData>): ICellEditor[];
    /** If the grid is editing, returns back details of the editing cell(s). */
    getEditingCells(): CellPosition[];
    /** If a cell is editing, it stops the editing. Pass `true` if you want to cancel the editing (i.e. don't accept changes). */
    stopEditing(cancel?: boolean): void;
    /** Start editing the provided cell. If another cell is editing, the editing will be stopped in that other cell. */
    startEditingCell(params: StartEditingCellParams): void;
    /** Add an aggregation function with the specified key. */
    addAggFunc(key: string, aggFunc: IAggFunc): void;
    /** Add aggregations function with the specified keys. */
    addAggFuncs(aggFuncs: {
        [key: string]: IAggFunc;
    }): void;
    /** Clears all aggregation functions (including those provided by the grid). */
    clearAggFuncs(): void;
    /** Apply transactions to the server side row model. */
    applyServerSideTransaction(transaction: ServerSideTransaction): ServerSideTransactionResult | undefined;
    /** Batch apply transactions to the server side row model. */
    applyServerSideTransactionAsync(transaction: ServerSideTransaction, callback?: (res: ServerSideTransactionResult) => void): void;
    /**
     * Applies row data to a server side store.
     * New rows will overwrite rows at the same index in the same way as if provided by a datasource success callback.
     *
     * startRow is only applicable when `suppressServerSideInfiniteScroll=true`
    */
    applyServerSideRowData(params: {
        successParams: LoadSuccessParams;
        route?: string[];
        startRow?: number;
    }): void;
    /** Gets all failed server side loads to retry. */
    retryServerSideLoads(): void;
    flushServerSideAsyncTransactions(): void;
    /** Update row data. Pass a transaction object with lists for `add`, `remove` and `update`. */
    applyTransaction(rowDataTransaction: RowDataTransaction<TData>): RowNodeTransaction<TData> | null | undefined;
    /** Same as `applyTransaction` except executes asynchronously for efficiency. */
    applyTransactionAsync(rowDataTransaction: RowDataTransaction<TData>, callback?: (res: RowNodeTransaction<TData>) => void): void;
    /** Executes any remaining asynchronous grid transactions, if any are waiting to be executed. */
    flushAsyncTransactions(): void;
    setSuppressModelUpdateAfterUpdateTransaction(value: boolean): void;
    /**
     * Marks all the currently loaded blocks in the cache for reload.
     * If you have 10 blocks in the cache, all 10 will be marked for reload.
     * The old data will continue to be displayed until the new data is loaded.
     */
    refreshInfiniteCache(): void;
    /**
     * Purges the cache.
     * The grid is then told to refresh. Only the blocks required to display the current data on screen are fetched (typically no more than 2).
     * The grid will display nothing while the new blocks are loaded.
     * Use this to immediately remove the old data from the user.
     */
    purgeInfiniteCache(): void;
    /**
     * Refresh a server-side store level.
     * If you pass no parameters, then the top level store is refreshed.
     * To refresh a child level, pass in the string of keys to get to the desired level.
     * Once the store refresh is complete, the storeRefreshed event is fired.
     */
    refreshServerSide(params?: RefreshServerSideParams): void;
    /** @deprecated v28 use `refreshServerSide` instead */
    refreshServerSideStore(params?: RefreshServerSideParams): void;
    /** @deprecated v28 use `getServerSideGroupLevelState` instead */
    getServerSideStoreState(): ServerSideGroupLevelState[];
    /** Returns info on all server side group levels. */
    getServerSideGroupLevelState(): ServerSideGroupLevelState[];
    /** The row count defines how many rows the grid allows scrolling to. */
    getInfiniteRowCount(): number | undefined;
    /** Returns `true` if grid allows for scrolling past the last row to load more rows, thus providing infinite scroll. */
    isLastRowIndexKnown(): boolean | undefined;
    /**
     * Returns an object representing the state of the cache. This is useful for debugging and understanding how the cache is working.
     */
    getCacheBlockState(): any;
    /** Get the index of the first displayed row due to scrolling (includes invisible rendered rows in the buffer). */
    getFirstDisplayedRow(): number;
    /** Get the index of the last displayed row due to scrolling (includes invisible rendered rows in the buffer). */
    getLastDisplayedRow(): number;
    /** Returns the displayed `RowNode` at the given `index`. */
    getDisplayedRowAtIndex(index: number): IRowNode<TData> | undefined;
    /** Returns the total number of displayed rows. */
    getDisplayedRowCount(): number;
    /** Resets the data type definitions. This will update the columns in the grid. */
    setDataTypeDefinitions(dataTypeDefinitions: {
        [cellDataType: string]: DataTypeDefinition<TData>;
    }): void;
    /**
     * Set whether the grid paginates the data or not.
     *  - `true` to enable pagination
     *  - `false` to disable pagination
     */
    setPagination(value: boolean): void;
    /**
     * Returns `true` when the last page is known.
     * This will always be `true` if you are using the Client-Side Row Model for pagination.
     * Returns `false` when the last page is not known; this only happens when using Infinite Row Model.
     */
    paginationIsLastPageFound(): boolean;
    /** Returns how many rows are being shown per page. */
    paginationGetPageSize(): number;
    /** Sets the `paginationPageSize`, then re-paginates the grid so the changes are applied immediately. */
    paginationSetPageSize(size?: number): void;
    /** Returns the 0-based index of the page which is showing. */
    paginationGetCurrentPage(): number;
    /** Returns the total number of pages. Returns `null` if `paginationIsLastPageFound() === false`. */
    paginationGetTotalPages(): number;
    /** The total number of rows. Returns `null` if `paginationIsLastPageFound() === false`. */
    paginationGetRowCount(): number;
    /** Navigates to the next page. */
    paginationGoToNextPage(): void;
    /** Navigates to the previous page. */
    paginationGoToPreviousPage(): void;
    /** Navigates to the first page. */
    paginationGoToFirstPage(): void;
    /** Navigates to the last page. */
    paginationGoToLastPage(): void;
    /** Goes to the specified page. If the page requested doesn't exist, it will go to the last page. */
    paginationGoToPage(page: number): void;
}
