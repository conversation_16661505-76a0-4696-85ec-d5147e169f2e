{"version": 3, "file": "visual-hidden2.js", "sources": ["../../../../../../packages/components/visual-hidden/src/visual-hidden.vue"], "sourcesContent": ["<template>\n  <span v-bind=\"$attrs\" :style=\"computedStyle\">\n    <slot />\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { visualHiddenProps } from './visual-hidden'\n\nimport type { StyleValue } from 'vue'\n\nconst props = defineProps(visualHiddenProps)\n\ndefineOptions({\n  name: 'ElVisuallyHidden',\n})\n\nconst computedStyle = computed<StyleValue>(() => {\n  return [\n    props.style,\n    {\n      position: 'absolute',\n      border: 0,\n      width: 1,\n      height: 1,\n      padding: 0,\n      margin: -1,\n      overflow: 'hidden',\n      clip: 'rect(0, 0, 0, 0)',\n      whiteSpace: 'nowrap',\n      wordWrap: 'normal',\n    },\n  ]\n})\n</script>\n"], "names": ["computed"], "mappings": ";;;;;;;;uCAcc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAM,MAAA,aAAA,GAAgBA,aAAqB,MAAM;AAC/C,MAAO,OAAA;AAAA,QACL,KAAM,CAAA,KAAA;AAAA,QACN;AAAA,UACE,QAAU,EAAA,UAAA;AAAA,UACV,MAAQ,EAAA,CAAA;AAAA,UACR,KAAO,EAAA,CAAA;AAAA,UACP,MAAQ,EAAA,CAAA;AAAA,UACR,OAAS,EAAA,CAAA;AAAA,UACT,MAAQ,EAAA,CAAA,CAAA;AAAA,UACR,QAAU,EAAA,QAAA;AAAA,UACV,IAAM,EAAA,kBAAA;AAAA,UACN,UAAY,EAAA,QAAA;AAAA,UACZ,QAAU,EAAA,QAAA;AAAA,SACZ;AAAA,OACF,CAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;"}