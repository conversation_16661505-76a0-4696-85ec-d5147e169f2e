{"compilerOptions": {"target": "es6", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": false, "moduleResolution": "node", "declaration": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "rootDir": "src", "outDir": "lib", "types": ["webpack-env", "jest"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "tests"]}