import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("redis.tencentcloudapi.com", "2018-04-12", clientConfig);
    }
    async ModifyInstanceReadOnly(req, cb) {
        return this.request("ModifyInstanceReadOnly", req, cb);
    }
    async DescribeInstanceMonitorBigKeySizeDist(req, cb) {
        return this.request("DescribeInstanceMonitorBigKeySizeDist", req, cb);
    }
    async DescribeTaskList(req, cb) {
        return this.request("DescribeTaskList", req, cb);
    }
    async DeleteReplicationInstance(req, cb) {
        return this.request("DeleteReplicationInstance", req, cb);
    }
    async CleanUpInstance(req, cb) {
        return this.request("CleanUpInstance", req, cb);
    }
    async DescribeInstanceAccount(req, cb) {
        return this.request("DescribeInstanceAccount", req, cb);
    }
    async SwitchAccessNewInstance(req, cb) {
        return this.request("SwitchAccessNewInstance", req, cb);
    }
    async ChangeReplicaToMaster(req, cb) {
        return this.request("ChangeReplicaToMaster", req, cb);
    }
    async DescribeInstanceMonitorTopNCmd(req, cb) {
        return this.request("DescribeInstanceMonitorTopNCmd", req, cb);
    }
    async UpgradeInstanceVersion(req, cb) {
        return this.request("UpgradeInstanceVersion", req, cb);
    }
    async DescribeProductInfo(req, cb) {
        return this.request("DescribeProductInfo", req, cb);
    }
    async DescribeTaskInfo(req, cb) {
        return this.request("DescribeTaskInfo", req, cb);
    }
    async ModifyInstanceBackupMode(req, cb) {
        return this.request("ModifyInstanceBackupMode", req, cb);
    }
    async ModifyBackupDownloadRestriction(req, cb) {
        return this.request("ModifyBackupDownloadRestriction", req, cb);
    }
    async ModifyInstanceAccount(req, cb) {
        return this.request("ModifyInstanceAccount", req, cb);
    }
    async DescribeInstanceSecurityGroup(req, cb) {
        return this.request("DescribeInstanceSecurityGroup", req, cb);
    }
    async DescribeBackupDetail(req, cb) {
        return this.request("DescribeBackupDetail", req, cb);
    }
    async UpgradeVersionToMultiAvailabilityZones(req, cb) {
        return this.request("UpgradeVersionToMultiAvailabilityZones", req, cb);
    }
    async DescribeGlobalReplicationArea(req, cb) {
        return this.request("DescribeGlobalReplicationArea", req, cb);
    }
    async ModifyInstanceParams(req, cb) {
        return this.request("ModifyInstanceParams", req, cb);
    }
    async ClearInstance(req, cb) {
        return this.request("ClearInstance", req, cb);
    }
    async DescribeBackupUrl(req, cb) {
        return this.request("DescribeBackupUrl", req, cb);
    }
    async DescribeSecondLevelBackupInfo(req, cb) {
        return this.request("DescribeSecondLevelBackupInfo", req, cb);
    }
    async ModifyConnectionConfig(req, cb) {
        return this.request("ModifyConnectionConfig", req, cb);
    }
    async DescribeInstanceLogDelivery(req, cb) {
        return this.request("DescribeInstanceLogDelivery", req, cb);
    }
    async ModifyInstance(req, cb) {
        return this.request("ModifyInstance", req, cb);
    }
    async ChangeInstanceRole(req, cb) {
        return this.request("ChangeInstanceRole", req, cb);
    }
    async DescribeInstanceNodeInfo(req, cb) {
        return this.request("DescribeInstanceNodeInfo", req, cb);
    }
    async CreateInstanceAccount(req, cb) {
        return this.request("CreateInstanceAccount", req, cb);
    }
    async ModifyInstanceEvent(req, cb) {
        return this.request("ModifyInstanceEvent", req, cb);
    }
    async UpgradeProxyVersion(req, cb) {
        return this.request("UpgradeProxyVersion", req, cb);
    }
    async DescribeInstanceMonitorTopNCmdTook(req, cb) {
        return this.request("DescribeInstanceMonitorTopNCmdTook", req, cb);
    }
    async ModifyAutoBackupConfig(req, cb) {
        return this.request("ModifyAutoBackupConfig", req, cb);
    }
    async DeleteParamTemplate(req, cb) {
        return this.request("DeleteParamTemplate", req, cb);
    }
    async AddReplicationInstance(req, cb) {
        return this.request("AddReplicationInstance", req, cb);
    }
    async CloneInstances(req, cb) {
        return this.request("CloneInstances", req, cb);
    }
    async CreateParamTemplate(req, cb) {
        return this.request("CreateParamTemplate", req, cb);
    }
    async DisableReplicaReadonly(req, cb) {
        return this.request("DisableReplicaReadonly", req, cb);
    }
    async DescribeInstanceDTSInfo(req, cb) {
        return this.request("DescribeInstanceDTSInfo", req, cb);
    }
    async InquiryPriceUpgradeInstance(req, cb) {
        return this.request("InquiryPriceUpgradeInstance", req, cb);
    }
    async RenewInstance(req, cb) {
        return this.request("RenewInstance", req, cb);
    }
    async ModifyInstanceLogDelivery(req, cb) {
        return this.request("ModifyInstanceLogDelivery", req, cb);
    }
    async AllocateWanAddress(req, cb) {
        return this.request("AllocateWanAddress", req, cb);
    }
    async DescribeProjectSecurityGroups(req, cb) {
        return this.request("DescribeProjectSecurityGroups", req, cb);
    }
    async DescribeTendisSlowLog(req, cb) {
        return this.request("DescribeTendisSlowLog", req, cb);
    }
    async ModifyParamTemplate(req, cb) {
        return this.request("ModifyParamTemplate", req, cb);
    }
    async InquiryPriceCreateInstance(req, cb) {
        return this.request("InquiryPriceCreateInstance", req, cb);
    }
    async ModifyReplicationGroup(req, cb) {
        return this.request("ModifyReplicationGroup", req, cb);
    }
    async DescribeInstanceSupportFeature(req, cb) {
        return this.request("DescribeInstanceSupportFeature", req, cb);
    }
    async DescribeInstanceParams(req, cb) {
        return this.request("DescribeInstanceParams", req, cb);
    }
    async DescribeInstanceMonitorBigKeyTypeDist(req, cb) {
        return this.request("DescribeInstanceMonitorBigKeyTypeDist", req, cb);
    }
    async ReleaseWanAddress(req, cb) {
        return this.request("ReleaseWanAddress", req, cb);
    }
    async DisassociateSecurityGroups(req, cb) {
        return this.request("DisassociateSecurityGroups", req, cb);
    }
    async ManualBackupInstance(req, cb) {
        return this.request("ManualBackupInstance", req, cb);
    }
    async DescribeInstanceSpecBandwidth(req, cb) {
        return this.request("DescribeInstanceSpecBandwidth", req, cb);
    }
    async DeleteInstanceAccount(req, cb) {
        return this.request("DeleteInstanceAccount", req, cb);
    }
    async SwitchProxy(req, cb) {
        return this.request("SwitchProxy", req, cb);
    }
    async DestroyPrepaidInstance(req, cb) {
        return this.request("DestroyPrepaidInstance", req, cb);
    }
    async DescribeInstanceParamRecords(req, cb) {
        return this.request("DescribeInstanceParamRecords", req, cb);
    }
    async DescribeParamTemplateInfo(req, cb) {
        return this.request("DescribeParamTemplateInfo", req, cb);
    }
    async DestroyPostpaidInstance(req, cb) {
        return this.request("DestroyPostpaidInstance", req, cb);
    }
    async StartupInstance(req, cb) {
        return this.request("StartupInstance", req, cb);
    }
    async DescribeInstanceMonitorTookDist(req, cb) {
        return this.request("DescribeInstanceMonitorTookDist", req, cb);
    }
    async DescribeParamTemplates(req, cb) {
        return this.request("DescribeParamTemplates", req, cb);
    }
    async DescribeInstanceBackups(req, cb) {
        return this.request("DescribeInstanceBackups", req, cb);
    }
    async ResetPassword(req, cb) {
        return this.request("ResetPassword", req, cb);
    }
    async RemoveReplicationInstance(req, cb) {
        return this.request("RemoveReplicationInstance", req, cb);
    }
    async UpgradeSmallVersion(req, cb) {
        return this.request("UpgradeSmallVersion", req, cb);
    }
    async ModifyInstancePassword(req, cb) {
        return this.request("ModifyInstancePassword", req, cb);
    }
    async CreateReplicationGroup(req, cb) {
        return this.request("CreateReplicationGroup", req, cb);
    }
    async DescribeInstanceMonitorSIP(req, cb) {
        return this.request("DescribeInstanceMonitorSIP", req, cb);
    }
    async DescribeProjectSecurityGroup(req, cb) {
        return this.request("DescribeProjectSecurityGroup", req, cb);
    }
    async DescribeInstanceMonitorHotKey(req, cb) {
        return this.request("DescribeInstanceMonitorHotKey", req, cb);
    }
    async ModfiyInstancePassword(req, cb) {
        return this.request("ModfiyInstancePassword", req, cb);
    }
    async EnableReplicaReadonly(req, cb) {
        return this.request("EnableReplicaReadonly", req, cb);
    }
    async AssociateSecurityGroups(req, cb) {
        return this.request("AssociateSecurityGroups", req, cb);
    }
    async DescribeSSLStatus(req, cb) {
        return this.request("DescribeSSLStatus", req, cb);
    }
    async DescribeInstanceMonitorBigKey(req, cb) {
        return this.request("DescribeInstanceMonitorBigKey", req, cb);
    }
    async DescribeRedisClusters(req, cb) {
        return this.request("DescribeRedisClusters", req, cb);
    }
    async KillMasterGroup(req, cb) {
        return this.request("KillMasterGroup", req, cb);
    }
    async DescribeCommonDBInstances(req, cb) {
        return this.request("DescribeCommonDBInstances", req, cb);
    }
    async DescribeSlowLog(req, cb) {
        return this.request("DescribeSlowLog", req, cb);
    }
    async DescribeBackupDownloadRestriction(req, cb) {
        return this.request("DescribeBackupDownloadRestriction", req, cb);
    }
    async DescribeInstanceZoneInfo(req, cb) {
        return this.request("DescribeInstanceZoneInfo", req, cb);
    }
    async DescribeInstanceEvents(req, cb) {
        return this.request("DescribeInstanceEvents", req, cb);
    }
    async SwitchInstanceVip(req, cb) {
        return this.request("SwitchInstanceVip", req, cb);
    }
    async DescribeDBSecurityGroups(req, cb) {
        return this.request("DescribeDBSecurityGroups", req, cb);
    }
    async DescribeReplicationGroup(req, cb) {
        return this.request("DescribeReplicationGroup", req, cb);
    }
    async InquiryPriceRenewInstance(req, cb) {
        return this.request("InquiryPriceRenewInstance", req, cb);
    }
    async ModifyMaintenanceWindow(req, cb) {
        return this.request("ModifyMaintenanceWindow", req, cb);
    }
    async ModifyNetworkConfig(req, cb) {
        return this.request("ModifyNetworkConfig", req, cb);
    }
    async DescribeInstanceShards(req, cb) {
        return this.request("DescribeInstanceShards", req, cb);
    }
    async RestoreInstance(req, cb) {
        return this.request("RestoreInstance", req, cb);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async RemoveReplicationGroup(req, cb) {
        return this.request("RemoveReplicationGroup", req, cb);
    }
    async OpenSSL(req, cb) {
        return this.request("OpenSSL", req, cb);
    }
    async DescribeAutoBackupConfig(req, cb) {
        return this.request("DescribeAutoBackupConfig", req, cb);
    }
    async DescribeBandwidthRange(req, cb) {
        return this.request("DescribeBandwidthRange", req, cb);
    }
    async DescribeReplicationGroupInstance(req, cb) {
        return this.request("DescribeReplicationGroupInstance", req, cb);
    }
    async DescribeProxySlowLog(req, cb) {
        return this.request("DescribeProxySlowLog", req, cb);
    }
    async ModifyInstanceAvailabilityZones(req, cb) {
        return this.request("ModifyInstanceAvailabilityZones", req, cb);
    }
    async ModifyDBInstanceSecurityGroups(req, cb) {
        return this.request("ModifyDBInstanceSecurityGroups", req, cb);
    }
    async ChangeMasterInstance(req, cb) {
        return this.request("ChangeMasterInstance", req, cb);
    }
    async UpgradeInstance(req, cb) {
        return this.request("UpgradeInstance", req, cb);
    }
    async DescribeRedisClusterOverview(req, cb) {
        return this.request("DescribeRedisClusterOverview", req, cb);
    }
    async ApplyParamsTemplate(req, cb) {
        return this.request("ApplyParamsTemplate", req, cb);
    }
    async DescribeMaintenanceWindow(req, cb) {
        return this.request("DescribeMaintenanceWindow", req, cb);
    }
    async CreateInstances(req, cb) {
        return this.request("CreateInstances", req, cb);
    }
    async DescribeInstanceDealDetail(req, cb) {
        return this.request("DescribeInstanceDealDetail", req, cb);
    }
    async CloseSSL(req, cb) {
        return this.request("CloseSSL", req, cb);
    }
}
