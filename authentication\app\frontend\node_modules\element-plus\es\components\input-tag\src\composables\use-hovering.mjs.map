{"version": 3, "file": "use-hovering.mjs", "sources": ["../../../../../../../packages/components/input-tag/src/composables/use-hovering.ts"], "sourcesContent": ["import { ref } from 'vue'\n\nexport function useHovering() {\n  const hovering = ref(false)\n\n  const handleMouseEnter = () => {\n    hovering.value = true\n  }\n\n  const handleMouseLeave = () => {\n    hovering.value = false\n  }\n\n  return {\n    hovering,\n    handleMouseEnter,\n    handleMouseLeave,\n  }\n}\n"], "names": [], "mappings": ";;AACO,SAAS,WAAW,GAAG;AAC9B,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,GAAG,CAAC;AACJ;;;;"}