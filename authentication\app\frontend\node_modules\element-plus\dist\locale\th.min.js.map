{"version": 3, "file": "th.min.js", "sources": ["../../../../packages/locale/lang/th.ts"], "sourcesContent": ["export default {\n  name: 'th',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'ตกลง',\n      clear: 'ล้างข้อมูล',\n    },\n    datepicker: {\n      now: 'ตอนนี้',\n      today: 'วันนี้',\n      cancel: 'ยกเลิก',\n      clear: 'ล้างข้อมูล',\n      confirm: 'ตกลง',\n      selectDate: 'เลือกวันที่',\n      selectTime: 'เลือกเวลา',\n      startDate: 'วันที่เริ่มต้น',\n      startTime: 'เวลาเริ่มต้น',\n      endDate: 'วันที่สิ้นสุด',\n      endTime: 'เวลาสิ้นสุด',\n      prevYear: 'ปีก่อนหน้า',\n      nextYear: 'ปีถัดไป',\n      prevMonth: 'เดือนก่อนหน้า',\n      nextMonth: 'เดือนถัดไป',\n      year: 'ปี',\n      month1: 'มกราคม',\n      month2: 'กุมภาพันธ์',\n      month3: 'มีนาคม',\n      month4: 'เมษายน',\n      month5: 'พฤษภาคม',\n      month6: 'มิถุนายน',\n      month7: 'กรกฎาคม',\n      month8: 'สิงหาคม',\n      month9: 'กันยายน',\n      month10: 'ตุลาคม',\n      month11: 'พฤศจิกายน',\n      month12: 'ธันวาคม',\n      // week: 'week',\n      weeks: {\n        sun: 'อา',\n        mon: 'จ',\n        tue: 'อ',\n        wed: 'พ',\n        thu: 'พฤ',\n        fri: 'ศ',\n        sat: 'ส',\n      },\n      months: {\n        jan: 'ม.ค.',\n        feb: 'ก.พ.',\n        mar: 'มี.ค.',\n        apr: 'เม.ย.',\n        may: 'พ.ค.',\n        jun: 'มิ.ย.',\n        jul: 'ก.ค.',\n        aug: 'ส.ค.',\n        sep: 'ก.ย.',\n        oct: 'ต.ค.',\n        nov: 'พ.ย.',\n        dec: 'ธ.ค.',\n      },\n    },\n    select: {\n      loading: 'กำลังโหลด',\n      noMatch: 'ไม่พบข้อมูลที่ตรงกัน',\n      noData: 'ไม่พบข้อมูล',\n      placeholder: 'เลือก',\n    },\n    mention: {\n      loading: 'กำลังโหลด',\n    },\n    cascader: {\n      noMatch: 'ไม่พบข้อมูลที่ตรงกัน',\n      loading: 'กำลังโหลด',\n      placeholder: 'เลือก',\n      noData: 'ไม่พบข้อมูล',\n    },\n    pagination: {\n      goto: 'ไปที่',\n      pagesize: '/หน้า',\n      total: 'ทั้งหมด {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'ข้อความ',\n      confirm: 'ตกลง',\n      cancel: 'ยกเลิก',\n      error: 'คุณป้อนข้อมูลไม่ถูกต้อง',\n    },\n    upload: {\n      deleteTip: 'กดปุ่ม \"ลบ\" เพื่อลบออก',\n      delete: 'ลบ',\n      preview: 'ตัวอย่าง',\n      continue: 'ทำต่อ',\n    },\n    table: {\n      emptyText: 'ไม่พบข้อมูล',\n      confirmFilter: 'ยืนยัน',\n      resetFilter: 'รีเซ็ต',\n      clearFilter: 'ทั้งหมด',\n      sumText: 'รวม',\n    },\n    tour: {\n      next: 'ถัดไป',\n      previous: 'ย้อนกลับ',\n      finish: 'เสร็จสิ้น',\n    },\n    tree: {\n      emptyText: 'ไม่พบข้อมูล',\n    },\n    transfer: {\n      noMatch: 'ไม่พบข้อมูลที่ตรงกัน',\n      noData: 'ไม่พบข้อมูล',\n      titles: ['List 1', 'List 2'], // to be translated\n      filterPlaceholder: 'กรอกคีย์เวิร์ด',\n      noCheckedFormat: '{total} items', // to be translated\n      hasCheckedFormat: '{checked}/{total} checked', // to be translated\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'ย้อนกลับ',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,0BAA0B,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,sCAAsC,CAAC,KAAK,CAAC,sCAAsC,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,8DAA8D,CAAC,OAAO,CAAC,0BAA0B,CAAC,UAAU,CAAC,oEAAoE,CAAC,UAAU,CAAC,wDAAwD,CAAC,SAAS,CAAC,sFAAsF,CAAC,SAAS,CAAC,0EAA0E,CAAC,OAAO,CAAC,gFAAgF,CAAC,OAAO,CAAC,oEAAoE,CAAC,QAAQ,CAAC,8DAA8D,CAAC,QAAQ,CAAC,4CAA4C,CAAC,SAAS,CAAC,gFAAgF,CAAC,SAAS,CAAC,8DAA8D,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,8DAA8D,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,kDAAkD,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,4CAA4C,CAAC,OAAO,CAAC,sCAAsC,CAAC,OAAO,CAAC,wDAAwD,CAAC,OAAO,CAAC,4CAA4C,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,sBAAsB,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,OAAO,CAAC,0HAA0H,CAAC,MAAM,CAAC,oEAAoE,CAAC,WAAW,CAAC,gCAAgC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0HAA0H,CAAC,OAAO,CAAC,wDAAwD,CAAC,WAAW,CAAC,gCAAgC,CAAC,MAAM,CAAC,oEAAoE,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,QAAQ,CAAC,2BAA2B,CAAC,KAAK,CAAC,oDAAoD,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,4CAA4C,CAAC,OAAO,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,4IAA4I,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,kHAAkH,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,kDAAkD,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,oEAAoE,CAAC,aAAa,CAAC,sCAAsC,CAAC,WAAW,CAAC,sCAAsC,CAAC,WAAW,CAAC,4CAA4C,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,QAAQ,CAAC,kDAAkD,CAAC,MAAM,CAAC,wDAAwD,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,oEAAoE,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,0HAA0H,CAAC,MAAM,CAAC,oEAAoE,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,sFAAsF,CAAC,eAAe,CAAC,eAAe,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}