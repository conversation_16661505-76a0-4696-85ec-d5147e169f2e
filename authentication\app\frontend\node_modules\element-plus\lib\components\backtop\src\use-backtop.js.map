{"version": 3, "file": "use-backtop.js", "sources": ["../../../../../../packages/components/backtop/src/use-backtop.ts"], "sourcesContent": ["import { onMounted, ref, shallowRef } from 'vue'\nimport { useEventListener, useThrottleFn } from '@vueuse/core'\nimport { throwError } from '@element-plus/utils'\n\nimport type { SetupContext } from 'vue'\nimport type { BacktopEmits, BacktopProps } from './backtop'\n\nexport const useBackTop = (\n  props: BacktopProps,\n  emit: SetupContext<BacktopEmits>['emit'],\n  componentName: string\n) => {\n  const el = shallowRef<HTMLElement>()\n  const container = shallowRef<Document | HTMLElement>()\n  const visible = ref(false)\n\n  const handleScroll = () => {\n    if (el.value) visible.value = el.value.scrollTop >= props.visibilityHeight\n  }\n\n  const handleClick = (event: MouseEvent) => {\n    el.value?.scrollTo({ top: 0, behavior: 'smooth' })\n    emit('click', event)\n  }\n\n  const handleScrollThrottled = useThrottleFn(handleScroll, 300, true)\n\n  useEventListener(container, 'scroll', handleScrollThrottled)\n  onMounted(() => {\n    container.value = document\n    el.value = document.documentElement\n\n    if (props.target) {\n      el.value = document.querySelector<HTMLElement>(props.target) ?? undefined\n      if (!el.value) {\n        throwError(componentName, `target does not exist: ${props.target}`)\n      }\n      container.value = el.value\n    }\n    // Give visible an initial value, fix #13066\n    handleScroll()\n  })\n\n  return {\n    visible,\n    handleClick,\n  }\n}\n"], "names": ["shallowRef", "ref", "useThrottleFn", "useEventListener", "onMounted", "throwError"], "mappings": ";;;;;;;;AAGY,MAAC,UAAU,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,aAAa,KAAK;AAC1D,EAAE,MAAM,EAAE,GAAGA,cAAU,EAAE,CAAC;AAC1B,EAAE,MAAM,SAAS,GAAGA,cAAU,EAAE,CAAC;AACjC,EAAE,MAAM,OAAO,GAAGC,OAAG,CAAC,KAAK,CAAC,CAAC;AAC7B,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,IAAI,EAAE,CAAC,KAAK;AAChB,MAAM,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,gBAAgB,CAAC;AACnE,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;AACnF,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACzB,GAAG,CAAC;AACJ,EAAE,MAAM,qBAAqB,GAAGC,kBAAa,CAAC,YAAY,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACvE,EAAEC,qBAAgB,CAAC,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;AAC/D,EAAEC,aAAS,CAAC,MAAM;AAClB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC;AAC/B,IAAI,EAAE,CAAC,KAAK,GAAG,QAAQ,CAAC,eAAe,CAAC;AACxC,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;AACtB,MAAM,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC,CAAC;AACnF,MAAM,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE;AACrB,QAAQC,gBAAU,CAAC,aAAa,EAAE,CAAC,uBAAuB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5E,OAAO;AACP,MAAM,SAAS,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;AACjC,KAAK;AACL,IAAI,YAAY,EAAE,CAAC;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}