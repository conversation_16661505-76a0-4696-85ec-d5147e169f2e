{"version": 3, "file": "panel-time-picker.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/props/panel-time-picker.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { timePanelSharedProps } from './shared'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const panelTimePickerProps = buildProps({\n  ...timePanelSharedProps,\n  datetimeRole: String,\n  parsedValue: {\n    type: definePropType<Dayjs>(Object),\n  },\n} as const)\n\nexport type PanelTimePickerProps = ExtractPropTypes<typeof panelTimePickerProps>\nexport type PanelTimePickerPropsPublic = __ExtractPublicPropTypes<\n  typeof panelTimePickerProps\n>\n"], "names": [], "mappings": ";;;AAEY,MAAC,oBAAoB,GAAG,UAAU,CAAC;AAC/C,EAAE,GAAG,oBAAoB;AACzB,EAAE,YAAY,EAAE,MAAM;AACtB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,CAAC;;;;"}