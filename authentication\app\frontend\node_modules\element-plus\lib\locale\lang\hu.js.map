{"version": 3, "file": "hu.js", "sources": ["../../../../../packages/locale/lang/hu.ts"], "sourcesContent": ["export default {\n  name: 'hu',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Törl<PERSON>',\n    },\n    datepicker: {\n      now: 'Most',\n      today: 'Ma',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: 'Törl<PERSON>',\n      confirm: 'OK',\n      selectDate: '<PERSON><PERSON><PERSON>',\n      selectTime: 'Id<PERSON>pont',\n      startDate: 'D<PERSON>tum-tól',\n      startTime: 'Időpont-tól',\n      endDate: 'Dátum-ig',\n      endTime: 'Időpont-ig',\n      prevYear: 'Előző év',\n      nextYear: 'Következő év',\n      prevMonth: '<PERSON><PERSON><PERSON>ő hónap',\n      nextMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hónap',\n      year: '',\n      month1: 'Január',\n      month2: 'Február',\n      month3: '<PERSON><PERSON><PERSON><PERSON>',\n      month4: 'Április',\n      month5: '<PERSON><PERSON><PERSON><PERSON>',\n      month6: '<PERSON><PERSON><PERSON>',\n      month7: '<PERSON><PERSON><PERSON>',\n      month8: '<PERSON><PERSON><PERSON><PERSON>',\n      month9: '<PERSON>zeptember',\n      month10: 'Október',\n      month11: 'November',\n      month12: 'December',\n      weeks: {\n        sun: 'Vas',\n        mon: 'Hét',\n        tue: 'Ked',\n        wed: 'Sze',\n        thu: '<PERSON>sü',\n        fri: 'P<PERSON>',\n        sat: 'Szo',\n      },\n      months: {\n        jan: '<PERSON>',\n        feb: 'Feb',\n        mar: 'Már',\n        apr: '<PERSON>pr',\n        may: 'M<PERSON>j',\n        jun: 'Jún',\n        jul: 'Júl',\n        aug: 'Aug',\n        sep: 'Szep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Betöltés',\n      noMatch: 'Nincs találat',\n      noData: 'Nincs adat',\n      placeholder: 'Válassz',\n    },\n    mention: {\n      loading: 'Betöltés',\n    },\n    cascader: {\n      noMatch: 'Nincs találat',\n      loading: 'Betöltés',\n      placeholder: 'Válassz',\n      noData: 'Nincs adat',\n    },\n    pagination: {\n      goto: 'Ugrás',\n      pagesize: '/oldal',\n      total: 'Össz {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Üzenet',\n      confirm: 'OK',\n      cancel: 'Mégse',\n      error: 'Hibás adat',\n    },\n    upload: {\n      deleteTip: 'kattints a törléshez',\n      delete: 'Törlés',\n      preview: 'Előnézet',\n      continue: 'Tovább',\n    },\n    table: {\n      emptyText: 'Nincs adat',\n      confirmFilter: 'Megerősít',\n      resetFilter: 'Alaphelyet',\n      clearFilter: 'Mind',\n      sumText: 'Összeg',\n    },\n    tree: {\n      emptyText: 'Nincs adat',\n    },\n    transfer: {\n      noMatch: 'Nincs találat',\n      noData: 'Nincs adat',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Kulcsszó',\n      noCheckedFormat: '{total} elem',\n      hasCheckedFormat: '{checked}/{total} kiválasztva',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,cAAc;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,MAAM;AACjB,MAAM,KAAK,EAAE,IAAI;AACjB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,UAAU;AAC5B,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,QAAQ,EAAE,uBAAuB;AACvC,MAAM,QAAQ,EAAE,yBAAyB;AACzC,MAAM,SAAS,EAAE,0BAA0B;AAC3C,MAAM,SAAS,EAAE,4BAA4B;AAC7C,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,WAAW,EAAE,YAAY;AAC/B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,gBAAgB;AAC/B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,WAAW,EAAE,YAAY;AAC/B,MAAM,MAAM,EAAE,YAAY;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,KAAK,EAAE,iBAAiB;AAC9B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,eAAe;AAC5B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,4BAA4B;AAC7C,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,QAAQ,EAAE,WAAW;AAC3B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,aAAa,EAAE,mBAAmB;AACxC,MAAM,WAAW,EAAE,YAAY;AAC/B,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,OAAO,EAAE,WAAW;AAC1B,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,YAAY;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,aAAa;AACtC,MAAM,eAAe,EAAE,cAAc;AACrC,MAAM,gBAAgB,EAAE,kCAAkC;AAC1D,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}