import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("csip.tencentcloudapi.com", "2022-11-21", clientConfig);
    }
    async DescribeUebaRule(req, cb) {
        return this.request("DescribeUebaRule", req, cb);
    }
    async DescribeTaskLogList(req, cb) {
        return this.request("DescribeTaskLogList", req, cb);
    }
    async DeleteDomainAndIp(req, cb) {
        return this.request("DeleteDomainAndIp", req, cb);
    }
    async DescribeVulRiskList(req, cb) {
        return this.request("DescribeVulRiskList", req, cb);
    }
    async DescribeRiskRuleDetail(req, cb) {
        return this.request("DescribeRiskRuleDetail", req, cb);
    }
    async DescribeDbAssetInfo(req, cb) {
        return this.request("DescribeDbAssetInfo", req, cb);
    }
    async DescribeNICAssets(req, cb) {
        return this.request("DescribeNICAssets", req, cb);
    }
    async DescribeAbnormalCallRecord(req, cb) {
        return this.request("DescribeAbnormalCallRecord", req, cb);
    }
    async DescribeRiskCenterVULViewVULRiskList(req, cb) {
        return this.request("DescribeRiskCenterVULViewVULRiskList", req, cb);
    }
    async DescribeDomainAssets(req, cb) {
        return this.request("DescribeDomainAssets", req, cb);
    }
    async UpdateAccessKeyRemark(req, cb) {
        return this.request("UpdateAccessKeyRemark", req, cb);
    }
    async DescribePublicIpAssets(req, cb) {
        return this.request("DescribePublicIpAssets", req, cb);
    }
    async AddNewBindRoleUser(req, cb) {
        return this.request("AddNewBindRoleUser", req, cb);
    }
    async DescribeVULList(req, cb) {
        return this.request("DescribeVULList", req, cb);
    }
    async DescribeCallRecord(req, cb) {
        return this.request("DescribeCallRecord", req, cb);
    }
    async DescribeAccessKeyUserList(req, cb) {
        return this.request("DescribeAccessKeyUserList", req, cb);
    }
    async DescribeListenerList(req, cb) {
        return this.request("DescribeListenerList", req, cb);
    }
    async StopRiskCenterTask(req, cb) {
        return this.request("StopRiskCenterTask", req, cb);
    }
    async DescribeAccessKeyUserDetail(req, cb) {
        return this.request("DescribeAccessKeyUserDetail", req, cb);
    }
    async DescribeRiskDetailList(req, cb) {
        return this.request("DescribeRiskDetailList", req, cb);
    }
    async DeleteRiskScanTask(req, cb) {
        return this.request("DeleteRiskScanTask", req, cb);
    }
    async DescribeConfigCheckRules(req, cb) {
        return this.request("DescribeConfigCheckRules", req, cb);
    }
    async DescribeRiskCenterAssetViewWeakPasswordRiskList(req, cb) {
        return this.request("DescribeRiskCenterAssetViewWeakPasswordRiskList", req, cb);
    }
    async DescribeRiskCenterAssetViewVULRiskList(req, cb) {
        return this.request("DescribeRiskCenterAssetViewVULRiskList", req, cb);
    }
    async DescribeClusterPodAssets(req, cb) {
        return this.request("DescribeClusterPodAssets", req, cb);
    }
    async DescribeOtherCloudAssets(req, cb) {
        return this.request("DescribeOtherCloudAssets", req, cb);
    }
    async DescribeRiskCenterAssetViewCFGRiskList(req, cb) {
        return this.request("DescribeRiskCenterAssetViewCFGRiskList", req, cb);
    }
    async DescribeVpcAssets(req, cb) {
        return this.request("DescribeVpcAssets", req, cb);
    }
    async DescribeSourceIPAsset(req, cb) {
        return this.request("DescribeSourceIPAsset", req, cb);
    }
    async DescribeRiskCenterServerRiskList(req, cb) {
        return this.request("DescribeRiskCenterServerRiskList", req, cb);
    }
    async DescribeCVMAssets(req, cb) {
        return this.request("DescribeCVMAssets", req, cb);
    }
    async ModifyRiskCenterScanTask(req, cb) {
        return this.request("ModifyRiskCenterScanTask", req, cb);
    }
    async CreateAccessKeyCheckTask(req, cb) {
        return this.request("CreateAccessKeyCheckTask", req, cb);
    }
    async DescribeAssetViewVulRiskList(req, cb) {
        return this.request("DescribeAssetViewVulRiskList", req, cb);
    }
    async DescribeTaskLogURL(req, cb) {
        return this.request("DescribeTaskLogURL", req, cb);
    }
    async DescribeCheckViewRisks(req, cb) {
        return this.request("DescribeCheckViewRisks", req, cb);
    }
    async DescribeRiskCenterWebsiteRiskList(req, cb) {
        return this.request("DescribeRiskCenterWebsiteRiskList", req, cb);
    }
    async DescribeCFWAssetStatistics(req, cb) {
        return this.request("DescribeCFWAssetStatistics", req, cb);
    }
    async DescribeRiskRules(req, cb) {
        return this.request("DescribeRiskRules", req, cb);
    }
    async DescribeAssetRiskList(req, cb) {
        return this.request("DescribeAssetRiskList", req, cb);
    }
    async DescribeRepositoryImageAssets(req, cb) {
        return this.request("DescribeRepositoryImageAssets", req, cb);
    }
    async CreateRiskCenterScanTask(req, cb) {
        return this.request("CreateRiskCenterScanTask", req, cb);
    }
    async DescribeExposePath(req, cb) {
        return this.request("DescribeExposePath", req, cb);
    }
    async DescribeGatewayAssets(req, cb) {
        return this.request("DescribeGatewayAssets", req, cb);
    }
    async DescribeTopAttackInfo(req, cb) {
        return this.request("DescribeTopAttackInfo", req, cb);
    }
    async DescribeSearchBugInfo(req, cb) {
        return this.request("DescribeSearchBugInfo", req, cb);
    }
    async DescribeVULRiskDetail(req, cb) {
        return this.request("DescribeVULRiskDetail", req, cb);
    }
    async DescribeAccessKeyAlarm(req, cb) {
        return this.request("DescribeAccessKeyAlarm", req, cb);
    }
    async DescribeVulViewVulRiskList(req, cb) {
        return this.request("DescribeVulViewVulRiskList", req, cb);
    }
    async DescribeUserCallRecord(req, cb) {
        return this.request("DescribeUserCallRecord", req, cb);
    }
    async DescribeHighBaseLineRiskList(req, cb) {
        return this.request("DescribeHighBaseLineRiskList", req, cb);
    }
    async DescribeScanReportList(req, cb) {
        return this.request("DescribeScanReportList", req, cb);
    }
    async CreateDomainAndIp(req, cb) {
        return this.request("CreateDomainAndIp", req, cb);
    }
    async DescribeAlertList(req, cb) {
        return this.request("DescribeAlertList", req, cb);
    }
    async DescribeScanTaskList(req, cb) {
        return this.request("DescribeScanTaskList", req, cb);
    }
    async UpdateAccessKeyAlarmStatus(req, cb) {
        return this.request("UpdateAccessKeyAlarmStatus", req, cb);
    }
    async ModifyUebaRuleSwitch(req, cb) {
        return this.request("ModifyUebaRuleSwitch", req, cb);
    }
    async UpdateAlertStatusList(req, cb) {
        return this.request("UpdateAlertStatusList", req, cb);
    }
    async DescribeScanStatistic(req, cb) {
        return this.request("DescribeScanStatistic", req, cb);
    }
    async DescribeExposeAssetCategory(req, cb) {
        return this.request("DescribeExposeAssetCategory", req, cb);
    }
    async DescribeOrganizationUserInfo(req, cb) {
        return this.request("DescribeOrganizationUserInfo", req, cb);
    }
    async DescribeAssetProcessList(req, cb) {
        return this.request("DescribeAssetProcessList", req, cb);
    }
    async CreateAccessKeySyncTask(req, cb) {
        return this.request("CreateAccessKeySyncTask", req, cb);
    }
    async DescribeRiskCenterPortViewPortRiskList(req, cb) {
        return this.request("DescribeRiskCenterPortViewPortRiskList", req, cb);
    }
    async DescribeSubUserInfo(req, cb) {
        return this.request("DescribeSubUserInfo", req, cb);
    }
    async DescribeRiskCenterAssetViewPortRiskList(req, cb) {
        return this.request("DescribeRiskCenterAssetViewPortRiskList", req, cb);
    }
    async DescribeVULRiskAdvanceCFGList(req, cb) {
        return this.request("DescribeVULRiskAdvanceCFGList", req, cb);
    }
    async DescribeOrganizationInfo(req, cb) {
        return this.request("DescribeOrganizationInfo", req, cb);
    }
    async DescribeExposures(req, cb) {
        return this.request("DescribeExposures", req, cb);
    }
    async ModifyRiskCenterRiskStatus(req, cb) {
        return this.request("ModifyRiskCenterRiskStatus", req, cb);
    }
    async ModifyOrganizationAccountStatus(req, cb) {
        return this.request("ModifyOrganizationAccountStatus", req, cb);
    }
    async DescribeAccessKeyAlarmDetail(req, cb) {
        return this.request("DescribeAccessKeyAlarmDetail", req, cb);
    }
    async DescribeRiskCenterCFGViewCFGRiskList(req, cb) {
        return this.request("DescribeRiskCenterCFGViewCFGRiskList", req, cb);
    }
    async DescribeDbAssets(req, cb) {
        return this.request("DescribeDbAssets", req, cb);
    }
    async DescribeClusterAssets(req, cb) {
        return this.request("DescribeClusterAssets", req, cb);
    }
    async DescribeAccessKeyRisk(req, cb) {
        return this.request("DescribeAccessKeyRisk", req, cb);
    }
    async DescribeSubnetAssets(req, cb) {
        return this.request("DescribeSubnetAssets", req, cb);
    }
    async DescribeCSIPRiskStatistics(req, cb) {
        return this.request("DescribeCSIPRiskStatistics", req, cb);
    }
    async DescribeAccessKeyRiskDetail(req, cb) {
        return this.request("DescribeAccessKeyRiskDetail", req, cb);
    }
    async DescribeAccessKeyAsset(req, cb) {
        return this.request("DescribeAccessKeyAsset", req, cb);
    }
    async DescribeRiskCallRecord(req, cb) {
        return this.request("DescribeRiskCallRecord", req, cb);
    }
    async DescribeCVMAssetInfo(req, cb) {
        return this.request("DescribeCVMAssetInfo", req, cb);
    }
}
