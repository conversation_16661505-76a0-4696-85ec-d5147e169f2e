import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("postgres.tencentcloudapi.com", "2017-03-12", clientConfig);
    }
    async DescribeTasks(req, cb) {
        return this.request("DescribeTasks", req, cb);
    }
    async DescribeBackupDownloadURL(req, cb) {
        return this.request("DescribeBackupDownloadURL", req, cb);
    }
    async DescribeDefaultParameters(req, cb) {
        return this.request("DescribeDefaultParameters", req, cb);
    }
    async DescribeDBErrlogs(req, cb) {
        return this.request("DescribeDBErrlogs", req, cb);
    }
    async DescribeDBInstanceHAConfig(req, cb) {
        return this.request("DescribeDBInstanceHAConfig", req, cb);
    }
    async DescribeBackupOverview(req, cb) {
        return this.request("DescribeBackupOverview", req, cb);
    }
    async DescribeDatabaseObjects(req, cb) {
        return this.request("DescribeDatabaseObjects", req, cb);
    }
    async ModifyAccountRemark(req, cb) {
        return this.request("ModifyAccountRemark", req, cb);
    }
    async SetAutoRenewFlag(req, cb) {
        return this.request("SetAutoRenewFlag", req, cb);
    }
    async ModifyDBInstanceName(req, cb) {
        return this.request("ModifyDBInstanceName", req, cb);
    }
    async DescribeMaintainTimeWindow(req, cb) {
        return this.request("DescribeMaintainTimeWindow", req, cb);
    }
    async DescribeDedicatedClusters(req, cb) {
        return this.request("DescribeDedicatedClusters", req, cb);
    }
    async DeleteParameterTemplate(req, cb) {
        return this.request("DeleteParameterTemplate", req, cb);
    }
    async DescribeDBInstanceParameters(req, cb) {
        return this.request("DescribeDBInstanceParameters", req, cb);
    }
    async CreateDBInstanceNetworkAccess(req, cb) {
        return this.request("CreateDBInstanceNetworkAccess", req, cb);
    }
    async ModifyDBInstanceSpec(req, cb) {
        return this.request("ModifyDBInstanceSpec", req, cb);
    }
    async ModifyDBInstanceSecurityGroups(req, cb) {
        return this.request("ModifyDBInstanceSecurityGroups", req, cb);
    }
    async ModifyMaintainTimeWindow(req, cb) {
        return this.request("ModifyMaintainTimeWindow", req, cb);
    }
    async DescribeDatabases(req, cb) {
        return this.request("DescribeDatabases", req, cb);
    }
    async DescribeSlowQueryAnalysis(req, cb) {
        return this.request("DescribeSlowQueryAnalysis", req, cb);
    }
    async DescribeProductConfig(req, cb) {
        return this.request("DescribeProductConfig", req, cb);
    }
    async DescribeAccountPrivileges(req, cb) {
        return this.request("DescribeAccountPrivileges", req, cb);
    }
    async DeleteReadOnlyGroupNetworkAccess(req, cb) {
        return this.request("DeleteReadOnlyGroupNetworkAccess", req, cb);
    }
    async ModifyDBInstanceChargeType(req, cb) {
        return this.request("ModifyDBInstanceChargeType", req, cb);
    }
    async UnlockAccount(req, cb) {
        return this.request("UnlockAccount", req, cb);
    }
    async DestroyDBInstance(req, cb) {
        return this.request("DestroyDBInstance", req, cb);
    }
    async DescribeDBBackups(req, cb) {
        return this.request("DescribeDBBackups", req, cb);
    }
    async DescribeParameterTemplates(req, cb) {
        return this.request("DescribeParameterTemplates", req, cb);
    }
    async InquiryPriceCreateDBInstances(req, cb) {
        return this.request("InquiryPriceCreateDBInstances", req, cb);
    }
    async DescribeDBInstanceSecurityGroups(req, cb) {
        return this.request("DescribeDBInstanceSecurityGroups", req, cb);
    }
    async CreateBaseBackup(req, cb) {
        return this.request("CreateBaseBackup", req, cb);
    }
    async DescribeParameterTemplateAttributes(req, cb) {
        return this.request("DescribeParameterTemplateAttributes", req, cb);
    }
    async RestoreDBInstanceObjects(req, cb) {
        return this.request("RestoreDBInstanceObjects", req, cb);
    }
    async ModifyDBInstanceDeployment(req, cb) {
        return this.request("ModifyDBInstanceDeployment", req, cb);
    }
    async RenewInstance(req, cb) {
        return this.request("RenewInstance", req, cb);
    }
    async CreateDatabase(req, cb) {
        return this.request("CreateDatabase", req, cb);
    }
    async CreateAccount(req, cb) {
        return this.request("CreateAccount", req, cb);
    }
    async ModifyDBInstanceParameters(req, cb) {
        return this.request("ModifyDBInstanceParameters", req, cb);
    }
    async DescribeBaseBackups(req, cb) {
        return this.request("DescribeBaseBackups", req, cb);
    }
    async DescribeAvailableRecoveryTime(req, cb) {
        return this.request("DescribeAvailableRecoveryTime", req, cb);
    }
    async DescribeRegions(req, cb) {
        return this.request("DescribeRegions", req, cb);
    }
    async ModifyDBInstanceReadOnlyGroup(req, cb) {
        return this.request("ModifyDBInstanceReadOnlyGroup", req, cb);
    }
    async CloneDBInstance(req, cb) {
        return this.request("CloneDBInstance", req, cb);
    }
    async DisIsolateDBInstances(req, cb) {
        return this.request("DisIsolateDBInstances", req, cb);
    }
    async DescribeDBVersions(req, cb) {
        return this.request("DescribeDBVersions", req, cb);
    }
    async ModifyParameterTemplate(req, cb) {
        return this.request("ModifyParameterTemplate", req, cb);
    }
    async CreateParameterTemplate(req, cb) {
        return this.request("CreateParameterTemplate", req, cb);
    }
    async CreateReadOnlyDBInstance(req, cb) {
        return this.request("CreateReadOnlyDBInstance", req, cb);
    }
    async DescribeBackupSummaries(req, cb) {
        return this.request("DescribeBackupSummaries", req, cb);
    }
    async UpgradeDBInstanceMajorVersion(req, cb) {
        return this.request("UpgradeDBInstanceMajorVersion", req, cb);
    }
    async RebalanceReadOnlyGroup(req, cb) {
        return this.request("RebalanceReadOnlyGroup", req, cb);
    }
    async CreateReadOnlyGroupNetworkAccess(req, cb) {
        return this.request("CreateReadOnlyGroupNetworkAccess", req, cb);
    }
    async DescribeEncryptionKeys(req, cb) {
        return this.request("DescribeEncryptionKeys", req, cb);
    }
    async DeleteReadOnlyGroup(req, cb) {
        return this.request("DeleteReadOnlyGroup", req, cb);
    }
    async UpgradeDBInstanceKernelVersion(req, cb) {
        return this.request("UpgradeDBInstanceKernelVersion", req, cb);
    }
    async ResetAccountPassword(req, cb) {
        return this.request("ResetAccountPassword", req, cb);
    }
    async RestartDBInstance(req, cb) {
        return this.request("RestartDBInstance", req, cb);
    }
    async DeleteDBInstanceNetworkAccess(req, cb) {
        return this.request("DeleteDBInstanceNetworkAccess", req, cb);
    }
    async ModifyDBInstancesProject(req, cb) {
        return this.request("ModifyDBInstancesProject", req, cb);
    }
    async DescribeDBXlogs(req, cb) {
        return this.request("DescribeDBXlogs", req, cb);
    }
    async DescribeDBInstanceAttribute(req, cb) {
        return this.request("DescribeDBInstanceAttribute", req, cb);
    }
    async ModifyDBInstanceHAConfig(req, cb) {
        return this.request("ModifyDBInstanceHAConfig", req, cb);
    }
    async DeleteLogBackup(req, cb) {
        return this.request("DeleteLogBackup", req, cb);
    }
    async IsolateDBInstances(req, cb) {
        return this.request("IsolateDBInstances", req, cb);
    }
    async ModifyBaseBackupExpireTime(req, cb) {
        return this.request("ModifyBaseBackupExpireTime", req, cb);
    }
    async ModifyAccountPrivileges(req, cb) {
        return this.request("ModifyAccountPrivileges", req, cb);
    }
    async DescribeSlowQueryList(req, cb) {
        return this.request("DescribeSlowQueryList", req, cb);
    }
    async DescribeLogBackups(req, cb) {
        return this.request("DescribeLogBackups", req, cb);
    }
    async CloseDBExtranetAccess(req, cb) {
        return this.request("CloseDBExtranetAccess", req, cb);
    }
    async AddDBInstanceToReadOnlyGroup(req, cb) {
        return this.request("AddDBInstanceToReadOnlyGroup", req, cb);
    }
    async DescribeAccounts(req, cb) {
        return this.request("DescribeAccounts", req, cb);
    }
    async DescribeCloneDBInstanceSpec(req, cb) {
        return this.request("DescribeCloneDBInstanceSpec", req, cb);
    }
    async DescribeDBInstances(req, cb) {
        return this.request("DescribeDBInstances", req, cb);
    }
    async DescribeBackupDownloadRestriction(req, cb) {
        return this.request("DescribeBackupDownloadRestriction", req, cb);
    }
    async ModifyReadOnlyGroupConfig(req, cb) {
        return this.request("ModifyReadOnlyGroupConfig", req, cb);
    }
    async RemoveDBInstanceFromReadOnlyGroup(req, cb) {
        return this.request("RemoveDBInstanceFromReadOnlyGroup", req, cb);
    }
    async CreateBackupPlan(req, cb) {
        return this.request("CreateBackupPlan", req, cb);
    }
    async DescribeParamsEvent(req, cb) {
        return this.request("DescribeParamsEvent", req, cb);
    }
    async DescribeClasses(req, cb) {
        return this.request("DescribeClasses", req, cb);
    }
    async DescribeOrders(req, cb) {
        return this.request("DescribeOrders", req, cb);
    }
    async SwitchDBInstancePrimary(req, cb) {
        return this.request("SwitchDBInstancePrimary", req, cb);
    }
    async ModifyReadOnlyDBInstanceWeight(req, cb) {
        return this.request("ModifyReadOnlyDBInstanceWeight", req, cb);
    }
    async DescribeBackupPlans(req, cb) {
        return this.request("DescribeBackupPlans", req, cb);
    }
    async ModifySwitchTimePeriod(req, cb) {
        return this.request("ModifySwitchTimePeriod", req, cb);
    }
    async OpenDBExtranetAccess(req, cb) {
        return this.request("OpenDBExtranetAccess", req, cb);
    }
    async ModifyDatabaseOwner(req, cb) {
        return this.request("ModifyDatabaseOwner", req, cb);
    }
    async DeleteAccount(req, cb) {
        return this.request("DeleteAccount", req, cb);
    }
    async ModifyBackupPlan(req, cb) {
        return this.request("ModifyBackupPlan", req, cb);
    }
    async ModifyDBInstanceSSLConfig(req, cb) {
        return this.request("ModifyDBInstanceSSLConfig", req, cb);
    }
    async DescribeZones(req, cb) {
        return this.request("DescribeZones", req, cb);
    }
    async DescribeDBInstanceSSLConfig(req, cb) {
        return this.request("DescribeDBInstanceSSLConfig", req, cb);
    }
    async DeleteBackupPlan(req, cb) {
        return this.request("DeleteBackupPlan", req, cb);
    }
    async InquiryPriceUpgradeDBInstance(req, cb) {
        return this.request("InquiryPriceUpgradeDBInstance", req, cb);
    }
    async CreateReadOnlyGroup(req, cb) {
        return this.request("CreateReadOnlyGroup", req, cb);
    }
    async InquiryPriceRenewDBInstance(req, cb) {
        return this.request("InquiryPriceRenewDBInstance", req, cb);
    }
    async ModifyBackupDownloadRestriction(req, cb) {
        return this.request("ModifyBackupDownloadRestriction", req, cb);
    }
    async DeleteBaseBackup(req, cb) {
        return this.request("DeleteBaseBackup", req, cb);
    }
    async LockAccount(req, cb) {
        return this.request("LockAccount", req, cb);
    }
    async DescribeReadOnlyGroups(req, cb) {
        return this.request("DescribeReadOnlyGroups", req, cb);
    }
    async CreateInstances(req, cb) {
        return this.request("CreateInstances", req, cb);
    }
}
