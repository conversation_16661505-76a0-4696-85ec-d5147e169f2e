import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("cam.tencentcloudapi.com", "2019-01-16", clientConfig);
    }
    async AddUser(req, cb) {
        return this.request("AddUser", req, cb);
    }
    async CreateServiceLinkedRole(req, cb) {
        return this.request("CreateServiceLinkedRole", req, cb);
    }
    async GetUserPermissionBoundary(req, cb) {
        return this.request("GetUserPermissionBoundary", req, cb);
    }
    async GetAccountSummary(req, cb) {
        return this.request("GetAccountSummary", req, cb);
    }
    async DeleteRolePermissionsBoundary(req, cb) {
        return this.request("DeleteRolePermissionsBoundary", req, cb);
    }
    async ListReceiver(req, cb) {
        return this.request("ListReceiver", req, cb);
    }
    async ListAttachedGroupPolicies(req, cb) {
        return this.request("ListAttachedGroupPolicies", req, cb);
    }
    async ConsumeCustomMFAToken(req, cb) {
        return this.request("ConsumeCustomMFAToken", req, cb);
    }
    async UpdateUser(req, cb) {
        return this.request("UpdateUser", req, cb);
    }
    async GetCustomMFATokenInfo(req, cb) {
        return this.request("GetCustomMFATokenInfo", req, cb);
    }
    async GetUser(req, cb) {
        return this.request("GetUser", req, cb);
    }
    async UpdateUserOIDCConfig(req, cb) {
        return this.request("UpdateUserOIDCConfig", req, cb);
    }
    async CreateSubAccountLoginIpPolicy(req, cb) {
        return this.request("CreateSubAccountLoginIpPolicy", req, cb);
    }
    async GetServiceLinkedRoleDeletionStatus(req, cb) {
        return this.request("GetServiceLinkedRoleDeletionStatus", req, cb);
    }
    async DescribeUserOIDCConfig(req, cb) {
        return this.request("DescribeUserOIDCConfig", req, cb);
    }
    async UpdateUserSAMLConfig(req, cb) {
        return this.request("UpdateUserSAMLConfig", req, cb);
    }
    async CreateAccessKey(req, cb) {
        return this.request("CreateAccessKey", req, cb);
    }
    async DeleteUser(req, cb) {
        return this.request("DeleteUser", req, cb);
    }
    async UpdateRoleDescription(req, cb) {
        return this.request("UpdateRoleDescription", req, cb);
    }
    async BuildDataFlowAuthToken(req, cb) {
        return this.request("BuildDataFlowAuthToken", req, cb);
    }
    async UpdateSAMLProvider(req, cb) {
        return this.request("UpdateSAMLProvider", req, cb);
    }
    async UntagRole(req, cb) {
        return this.request("UntagRole", req, cb);
    }
    async DescribeSubAccounts(req, cb) {
        return this.request("DescribeSubAccounts", req, cb);
    }
    async RemoveUserFromGroup(req, cb) {
        return this.request("RemoveUserFromGroup", req, cb);
    }
    async ListSAMLProviders(req, cb) {
        return this.request("ListSAMLProviders", req, cb);
    }
    async ListUsersForGroup(req, cb) {
        return this.request("ListUsersForGroup", req, cb);
    }
    async GetGroup(req, cb) {
        return this.request("GetGroup", req, cb);
    }
    async DeleteOIDCConfig(req, cb) {
        return this.request("DeleteOIDCConfig", req, cb);
    }
    async DeleteAccessKey(req, cb) {
        return this.request("DeleteAccessKey", req, cb);
    }
    async GetUserAppId(req, cb) {
        return this.request("GetUserAppId", req, cb);
    }
    async PutRolePermissionsBoundary(req, cb) {
        return this.request("PutRolePermissionsBoundary", req, cb);
    }
    async DeleteServiceLinkedRole(req, cb) {
        return this.request("DeleteServiceLinkedRole", req, cb);
    }
    async ListUsers(req, cb) {
        return this.request("ListUsers", req, cb);
    }
    async DescribeUserSAMLConfig(req, cb) {
        return this.request("DescribeUserSAMLConfig", req, cb);
    }
    async DetachRolePolicy(req, cb) {
        return this.request("DetachRolePolicy", req, cb);
    }
    async DescribeRoleList(req, cb) {
        return this.request("DescribeRoleList", req, cb);
    }
    async CreateSAMLProvider(req, cb) {
        return this.request("CreateSAMLProvider", req, cb);
    }
    async CreateUserOIDCConfig(req, cb) {
        return this.request("CreateUserOIDCConfig", req, cb);
    }
    async UpdateOIDCConfig(req, cb) {
        return this.request("UpdateOIDCConfig", req, cb);
    }
    async DeleteRole(req, cb) {
        return this.request("DeleteRole", req, cb);
    }
    async AttachGroupPolicy(req, cb) {
        return this.request("AttachGroupPolicy", req, cb);
    }
    async ListPoliciesGrantingServiceAccess(req, cb) {
        return this.request("ListPoliciesGrantingServiceAccess", req, cb);
    }
    async UpdateAccessKey(req, cb) {
        return this.request("UpdateAccessKey", req, cb);
    }
    async UpdatePolicy(req, cb) {
        return this.request("UpdatePolicy", req, cb);
    }
    async ListAttachedUserPolicies(req, cb) {
        return this.request("ListAttachedUserPolicies", req, cb);
    }
    async CreatePolicyVersion(req, cb) {
        return this.request("CreatePolicyVersion", req, cb);
    }
    async ListCollaborators(req, cb) {
        return this.request("ListCollaborators", req, cb);
    }
    async TagRole(req, cb) {
        return this.request("TagRole", req, cb);
    }
    async UpdateRoleConsoleLogin(req, cb) {
        return this.request("UpdateRoleConsoleLogin", req, cb);
    }
    async AttachUserPolicy(req, cb) {
        return this.request("AttachUserPolicy", req, cb);
    }
    async UpdateAssumeRolePolicy(req, cb) {
        return this.request("UpdateAssumeRolePolicy", req, cb);
    }
    async SetMfaFlag(req, cb) {
        return this.request("SetMfaFlag", req, cb);
    }
    async GetSecurityLastUsed(req, cb) {
        return this.request("GetSecurityLastUsed", req, cb);
    }
    async CreateGroup(req, cb) {
        return this.request("CreateGroup", req, cb);
    }
    async DeletePolicy(req, cb) {
        return this.request("DeletePolicy", req, cb);
    }
    async DeletePolicyVersion(req, cb) {
        return this.request("DeletePolicyVersion", req, cb);
    }
    async ListAccessKeys(req, cb) {
        return this.request("ListAccessKeys", req, cb);
    }
    async DeleteGroup(req, cb) {
        return this.request("DeleteGroup", req, cb);
    }
    async CreateMessageReceiver(req, cb) {
        return this.request("CreateMessageReceiver", req, cb);
    }
    async CreateOIDCConfig(req, cb) {
        return this.request("CreateOIDCConfig", req, cb);
    }
    async GetPolicyVersion(req, cb) {
        return this.request("GetPolicyVersion", req, cb);
    }
    async ListAttachedUserAllPolicies(req, cb) {
        return this.request("ListAttachedUserAllPolicies", req, cb);
    }
    async SetDefaultPolicyVersion(req, cb) {
        return this.request("SetDefaultPolicyVersion", req, cb);
    }
    async DetachGroupPolicy(req, cb) {
        return this.request("DetachGroupPolicy", req, cb);
    }
    async DescribeSafeAuthFlag(req, cb) {
        return this.request("DescribeSafeAuthFlag", req, cb);
    }
    async DescribeSafeAuthFlagIntl(req, cb) {
        return this.request("DescribeSafeAuthFlagIntl", req, cb);
    }
    async UpdateRoleSessionDuration(req, cb) {
        return this.request("UpdateRoleSessionDuration", req, cb);
    }
    async ListGroups(req, cb) {
        return this.request("ListGroups", req, cb);
    }
    async ListWeChatWorkSubAccounts(req, cb) {
        return this.request("ListWeChatWorkSubAccounts", req, cb);
    }
    async ListPolicyVersions(req, cb) {
        return this.request("ListPolicyVersions", req, cb);
    }
    async UpdateGroup(req, cb) {
        return this.request("UpdateGroup", req, cb);
    }
    async CreatePolicy(req, cb) {
        return this.request("CreatePolicy", req, cb);
    }
    async DetachUserPolicy(req, cb) {
        return this.request("DetachUserPolicy", req, cb);
    }
    async ListGroupsForUser(req, cb) {
        return this.request("ListGroupsForUser", req, cb);
    }
    async DeleteMessageReceiver(req, cb) {
        return this.request("DeleteMessageReceiver", req, cb);
    }
    async GetSAMLProvider(req, cb) {
        return this.request("GetSAMLProvider", req, cb);
    }
    async CreateRole(req, cb) {
        return this.request("CreateRole", req, cb);
    }
    async ListAttachedRolePolicies(req, cb) {
        return this.request("ListAttachedRolePolicies", req, cb);
    }
    async DeleteSAMLProvider(req, cb) {
        return this.request("DeleteSAMLProvider", req, cb);
    }
    async GetPolicy(req, cb) {
        return this.request("GetPolicy", req, cb);
    }
    async GetRolePermissionBoundary(req, cb) {
        return this.request("GetRolePermissionBoundary", req, cb);
    }
    async DeleteUserPermissionsBoundary(req, cb) {
        return this.request("DeleteUserPermissionsBoundary", req, cb);
    }
    async DescribeOIDCConfig(req, cb) {
        return this.request("DescribeOIDCConfig", req, cb);
    }
    async ListEntitiesForPolicy(req, cb) {
        return this.request("ListEntitiesForPolicy", req, cb);
    }
    async PutUserPermissionsBoundary(req, cb) {
        return this.request("PutUserPermissionsBoundary", req, cb);
    }
    async CreateUserSAMLConfig(req, cb) {
        return this.request("CreateUserSAMLConfig", req, cb);
    }
    async AddUserToGroup(req, cb) {
        return this.request("AddUserToGroup", req, cb);
    }
    async AttachRolePolicy(req, cb) {
        return this.request("AttachRolePolicy", req, cb);
    }
    async DescribeSafeAuthFlagColl(req, cb) {
        return this.request("DescribeSafeAuthFlagColl", req, cb);
    }
    async GetRole(req, cb) {
        return this.request("GetRole", req, cb);
    }
    async ListPolicies(req, cb) {
        return this.request("ListPolicies", req, cb);
    }
    async DisableUserSSO(req, cb) {
        return this.request("DisableUserSSO", req, cb);
    }
}
