{"version": 3, "file": "input-number2.mjs", "sources": ["../../../../../../packages/components/input-number/src/input-number.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ns.b(),\n      ns.m(inputNumberSize),\n      ns.is('disabled', inputNumberDisabled),\n      ns.is('without-controls', !controls),\n      ns.is('controls-right', controlsAtRight),\n      ns.is(align, !!align),\n    ]\"\n    @dragstart.prevent\n  >\n    <span\n      v-if=\"controls\"\n      v-repeat-click=\"decrease\"\n      role=\"button\"\n      :aria-label=\"t('el.inputNumber.decrease')\"\n      :class=\"[ns.e('decrease'), ns.is('disabled', minDisabled)]\"\n      @keydown.enter=\"decrease\"\n    >\n      <slot name=\"decrease-icon\">\n        <el-icon>\n          <arrow-down v-if=\"controlsAtRight\" />\n          <minus v-else />\n        </el-icon>\n      </slot>\n    </span>\n    <span\n      v-if=\"controls\"\n      v-repeat-click=\"increase\"\n      role=\"button\"\n      :aria-label=\"t('el.inputNumber.increase')\"\n      :class=\"[ns.e('increase'), ns.is('disabled', maxDisabled)]\"\n      @keydown.enter=\"increase\"\n    >\n      <slot name=\"increase-icon\">\n        <el-icon>\n          <arrow-up v-if=\"controlsAtRight\" />\n          <plus v-else />\n        </el-icon>\n      </slot>\n    </span>\n    <el-input\n      :id=\"id\"\n      ref=\"input\"\n      type=\"number\"\n      :step=\"step\"\n      :model-value=\"displayValue\"\n      :placeholder=\"placeholder\"\n      :readonly=\"readonly\"\n      :disabled=\"inputNumberDisabled\"\n      :size=\"inputNumberSize\"\n      :max=\"max\"\n      :min=\"min\"\n      :name=\"name\"\n      :aria-label=\"ariaLabel\"\n      :validate-event=\"false\"\n      :inputmode=\"inputmode\"\n      @keydown=\"handleKeydown\"\n      @blur=\"handleBlur\"\n      @focus=\"handleFocus\"\n      @input=\"handleInput\"\n      @change=\"handleInputChange\"\n    >\n      <template v-if=\"$slots.prefix\" #prefix>\n        <slot name=\"prefix\" />\n      </template>\n      <template v-if=\"$slots.suffix\" #suffix>\n        <slot name=\"suffix\" />\n      </template>\n    </el-input>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onMounted, onUpdated, reactive, ref, watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { ElInput } from '@element-plus/components/input'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { vRepeatClick } from '@element-plus/directives'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport {\n  debugWarn,\n  isNumber,\n  isString,\n  isUndefined,\n  throwError,\n} from '@element-plus/utils'\nimport { ArrowDown, ArrowUp, Minus, Plus } from '@element-plus/icons-vue'\nimport {\n  CHANGE_EVENT,\n  EVENT_CODE,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { inputNumberEmits, inputNumberProps } from './input-number'\n\nimport type { InputInstance } from '@element-plus/components/input'\n\ndefineOptions({\n  name: 'ElInputNumber',\n})\n\nconst props = defineProps(inputNumberProps)\nconst emit = defineEmits(inputNumberEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('input-number')\nconst input = ref<InputInstance>()\n\ninterface Data {\n  currentValue: number | null | undefined\n  userInput: null | number | string\n}\nconst data = reactive<Data>({\n  currentValue: props.modelValue,\n  userInput: null,\n})\n\nconst { formItem } = useFormItem()\n\nconst minDisabled = computed(\n  () => isNumber(props.modelValue) && props.modelValue <= props.min\n)\nconst maxDisabled = computed(\n  () => isNumber(props.modelValue) && props.modelValue >= props.max\n)\n\nconst numPrecision = computed(() => {\n  const stepPrecision = getPrecision(props.step)\n  if (!isUndefined(props.precision)) {\n    if (stepPrecision > props.precision) {\n      debugWarn(\n        'InputNumber',\n        'precision should not be less than the decimal places of step'\n      )\n    }\n    return props.precision\n  } else {\n    return Math.max(getPrecision(props.modelValue), stepPrecision)\n  }\n})\nconst controlsAtRight = computed(() => {\n  return props.controls && props.controlsPosition === 'right'\n})\n\nconst inputNumberSize = useFormSize()\nconst inputNumberDisabled = useFormDisabled()\n\nconst displayValue = computed(() => {\n  if (data.userInput !== null) {\n    return data.userInput\n  }\n  let currentValue: number | string | undefined | null = data.currentValue\n  if (isNil(currentValue)) return ''\n  if (isNumber(currentValue)) {\n    if (Number.isNaN(currentValue)) return ''\n    if (!isUndefined(props.precision)) {\n      currentValue = currentValue.toFixed(props.precision)\n    }\n  }\n  return currentValue\n})\nconst toPrecision = (num: number, pre?: number) => {\n  if (isUndefined(pre)) pre = numPrecision.value\n  if (pre === 0) return Math.round(num)\n  let snum = String(num)\n  const pointPos = snum.indexOf('.')\n  if (pointPos === -1) return num\n  const nums = snum.replace('.', '').split('')\n  const datum = nums[pointPos + pre]\n  if (!datum) return num\n  const length = snum.length\n  if (snum.charAt(length - 1) === '5') {\n    snum = `${snum.slice(0, Math.max(0, length - 1))}6`\n  }\n  return Number.parseFloat(Number(snum).toFixed(pre))\n}\nconst getPrecision = (value: number | null | undefined) => {\n  if (isNil(value)) return 0\n  const valueString = value.toString()\n  const dotPosition = valueString.indexOf('.')\n  let precision = 0\n  if (dotPosition !== -1) {\n    precision = valueString.length - dotPosition - 1\n  }\n  return precision\n}\nconst ensurePrecision = (val: number, coefficient: 1 | -1 = 1) => {\n  if (!isNumber(val)) return data.currentValue\n  if (val >= Number.MAX_SAFE_INTEGER && coefficient === 1) {\n    debugWarn(\n      'InputNumber',\n      'The value has reached the maximum safe integer limit.'\n    )\n    return val\n  } else if (val <= Number.MIN_SAFE_INTEGER && coefficient === -1) {\n    debugWarn(\n      'InputNumber',\n      'The value has reached the minimum safe integer limit.'\n    )\n    return val\n  }\n\n  // Solve the accuracy problem of JS decimal calculation by converting the value to integer.\n  return toPrecision(val + props.step * coefficient)\n}\nconst handleKeydown = (event: Event) => {\n  const e = event as KeyboardEvent\n  if (props.disabledScientific && ['e', 'E'].includes(e.key)) {\n    e.preventDefault()\n    return\n  }\n  const keyHandlers = {\n    [EVENT_CODE.up]: () => {\n      e.preventDefault()\n      increase()\n    },\n    [EVENT_CODE.down]: () => {\n      e.preventDefault()\n      decrease()\n    },\n  }\n  keyHandlers[e.key]?.()\n}\nconst increase = () => {\n  if (props.readonly || inputNumberDisabled.value || maxDisabled.value) return\n  const value = Number(displayValue.value) || 0\n  const newVal = ensurePrecision(value)\n  setCurrentValue(newVal)\n  emit(INPUT_EVENT, data.currentValue)\n  setCurrentValueToModelValue()\n}\nconst decrease = () => {\n  if (props.readonly || inputNumberDisabled.value || minDisabled.value) return\n  const value = Number(displayValue.value) || 0\n  const newVal = ensurePrecision(value, -1)\n  setCurrentValue(newVal)\n  emit(INPUT_EVENT, data.currentValue)\n  setCurrentValueToModelValue()\n}\nconst verifyValue = (\n  value: number | string | null | undefined,\n  update?: boolean\n): number | null | undefined => {\n  const { max, min, step, precision, stepStrictly, valueOnClear } = props\n  if (max < min) {\n    throwError('InputNumber', 'min should not be greater than max.')\n  }\n  let newVal = Number(value)\n  if (isNil(value) || Number.isNaN(newVal)) {\n    return null\n  }\n  if (value === '') {\n    if (valueOnClear === null) {\n      return null\n    }\n    newVal = isString(valueOnClear) ? { min, max }[valueOnClear] : valueOnClear\n  }\n  if (stepStrictly) {\n    newVal = toPrecision(Math.round(newVal / step) * step, precision)\n    if (newVal !== value) {\n      update && emit(UPDATE_MODEL_EVENT, newVal)\n    }\n  }\n  if (!isUndefined(precision)) {\n    newVal = toPrecision(newVal, precision)\n  }\n  if (newVal > max || newVal < min) {\n    newVal = newVal > max ? max : min\n    update && emit(UPDATE_MODEL_EVENT, newVal)\n  }\n  return newVal\n}\nconst setCurrentValue = (\n  value: number | string | null | undefined,\n  emitChange = true\n) => {\n  const oldVal = data.currentValue\n  const newVal = verifyValue(value)\n  if (!emitChange) {\n    emit(UPDATE_MODEL_EVENT, newVal!)\n    return\n  }\n  if (oldVal === newVal && value) return\n  data.userInput = null\n  emit(UPDATE_MODEL_EVENT, newVal!)\n  if (oldVal !== newVal) {\n    emit(CHANGE_EVENT, newVal!, oldVal!)\n  }\n  if (props.validateEvent) {\n    formItem?.validate?.('change').catch((err) => debugWarn(err))\n  }\n  data.currentValue = newVal\n}\nconst handleInput = (value: string) => {\n  data.userInput = value\n  const newVal = value === '' ? null : Number(value)\n  emit(INPUT_EVENT, newVal)\n  setCurrentValue(newVal, false)\n}\nconst handleInputChange = (value: string) => {\n  const newVal = value !== '' ? Number(value) : ''\n  if ((isNumber(newVal) && !Number.isNaN(newVal)) || value === '') {\n    setCurrentValue(newVal)\n  }\n  setCurrentValueToModelValue()\n  data.userInput = null\n}\n\nconst focus = () => {\n  input.value?.focus?.()\n}\n\nconst blur = () => {\n  input.value?.blur?.()\n}\n\nconst handleFocus = (event: MouseEvent | FocusEvent) => {\n  emit('focus', event)\n}\n\nconst handleBlur = (event: MouseEvent | FocusEvent) => {\n  data.userInput = null\n  // When non-numeric content is entered into a numeric input box,\n  // the content displayed on the page is not cleared after the value is cleared. #18533\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1398528\n  if (data.currentValue === null && input.value?.input) {\n    input.value.input.value = ''\n  }\n  emit('blur', event)\n  if (props.validateEvent) {\n    formItem?.validate?.('blur').catch((err) => debugWarn(err))\n  }\n}\n\nconst setCurrentValueToModelValue = () => {\n  if (data.currentValue !== props.modelValue) {\n    data.currentValue = props.modelValue\n  }\n}\nconst handleWheel = (e: WheelEvent) => {\n  if (document.activeElement === e.target) e.preventDefault()\n}\n\nwatch(\n  () => props.modelValue,\n  (value, oldValue) => {\n    const newValue = verifyValue(value, true)\n    if (data.userInput === null && newValue !== oldValue) {\n      data.currentValue = newValue\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => props.precision,\n  () => {\n    data.currentValue = verifyValue(props.modelValue)\n  }\n)\nonMounted(() => {\n  const { min, max, modelValue } = props\n  const innerInput = input.value?.input as HTMLInputElement\n  innerInput.setAttribute('role', 'spinbutton')\n  if (Number.isFinite(max)) {\n    innerInput.setAttribute('aria-valuemax', String(max))\n  } else {\n    innerInput.removeAttribute('aria-valuemax')\n  }\n  if (Number.isFinite(min)) {\n    innerInput.setAttribute('aria-valuemin', String(min))\n  } else {\n    innerInput.removeAttribute('aria-valuemin')\n  }\n  innerInput.setAttribute(\n    'aria-valuenow',\n    data.currentValue || data.currentValue === 0\n      ? String(data.currentValue)\n      : ''\n  )\n  innerInput.setAttribute('aria-disabled', String(inputNumberDisabled.value))\n  if (!isNumber(modelValue) && modelValue != null) {\n    let val: number | null = Number(modelValue)\n    if (Number.isNaN(val)) {\n      val = null\n    }\n    emit(UPDATE_MODEL_EVENT, val!)\n  }\n  innerInput.addEventListener('wheel', handleWheel, { passive: false })\n})\nonUpdated(() => {\n  const innerInput = input.value?.input\n  innerInput?.setAttribute('aria-valuenow', `${data.currentValue ?? ''}`)\n})\ndefineExpose({\n  /** @description get focus the input component */\n  focus,\n  /** @description remove focus the input component */\n  blur,\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;;;;;;;;;;;mCAwGc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,cAAc,CAAA,CAAA;AACtC,IAAA,MAAM,QAAQ,GAAmB,EAAA,CAAA;AAMjC,IAAA,MAAM,OAAO,QAAe,CAAA;AAAA,MAC1B,cAAc,KAAM,CAAA,UAAA;AAAA,MACpB,SAAW,EAAA,IAAA;AAAA,KACZ,CAAA,CAAA;AAED,IAAM,MAAA,EAAE,QAAS,EAAA,GAAI,WAAY,EAAA,CAAA;AAEjC,IAAA,MAAM,WAAc,GAAA,QAAA,CAAA,MAAA,QAAA,CAAA,KAAA,CAAA,UAAA,CAAA,IAAA,KAAA,CAAA,UAAA,IAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,IAAA,iBACH,GAAA,eAAqB,QAAA,CAAM,gBAAoB,CAAA,IAAA,KAAA,CAAA,UAAA,IAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,IAChE,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AACA,MAAA,MAAoB,aAAA,GAAA,YAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MAClB,gBAAe,CAAA,eAAqB,CAAA,EAAA;AAA0B,QAChE,IAAA,aAAA,GAAA,KAAA,CAAA,SAAA,EAAA,CAGQ;AACN,QAAA,OAAK,KAAA,CAAA,SAAkB,CAAA;AACrB,OAAI,MAAA;AACF,QAAA,OAAA,IAAA,CAAA,GAAA,CAAA,YAAA,CAAA,KAAA,CAAA,UAAA,CAAA,EAAA,aAAA,CAAA,CAAA;AAAA,OACE;AAAA,KACA,CAAA,CAAA;AAAA,IACF,MAAA,eAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACF,OAAA,KAAA,CAAA,QAAA,IAAA,KAAA,CAAA,gBAAA,KAAA,OAAA,CAAA;AACA,KAAA,CAAA,CAAA;AAAa,IAAA,MACR,eAAA,GAAA,WAAA,EAAA,CAAA;AACL,IAAA,MAAA,mBAAgB,GAAA,eAAmB,EAAA,CAAA;AAA0B,IAC/D,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,IAAA,IAAA,CAAA,SAAA,KAAA,IAAA,EAAA;AACD,QAAM,OAAA,IAAA,CAAA;AACJ,OAAO;AAA6C,MACrD,IAAA,YAAA,GAAA,IAAA,CAAA,YAAA,CAAA;AAED,MAAA,IAAM,kBAAkB,CAAY;AACpC,QAAA;AAEA,MAAM,IAAA,QAAA,CAAA,eAAwB;AAC5B,QAAI,IAAA,yBAAyB,CAAA;AAC3B,UAAA,OAAY,EAAA,CAAA;AAAA,QACd,IAAA,CAAA,WAAA,CAAA,KAAA,CAAA,SAAA,CAAA,EAAA;AACA,UAAI,eAAmD,YAAK,CAAA,OAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAC5D,SAAI;AACJ,OAAI;AACF,MAAA,OAAW,YAAM,CAAY;AAC7B,KAAA,CAAA,CAAA;AACE,IAAe,MAAA,WAAA,GAAA,CAAA,GAAA,EAAA,GAAA,KAAa;AAAuB,MACrD,IAAA,WAAA,CAAA,GAAA,CAAA;AAAA,QACF,GAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AACA,MAAO,IAAA,GAAA,KAAA,CAAA;AAAA,QACR,OAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AACD,MAAM,IAAA,IAAA,GAAA,MAAA,CAAc,GAAC,CAAA,CAAA;AACnB,MAAA,MAAgB,QAAA,GAAA,IAAM,CAAA,OAAmB,CAAA,GAAA,CAAA,CAAA;AACzC,MAAA,IAAI,QAAQ,KAAU,CAAA,CAAA;AACtB,QAAI,OAAA;AACJ,MAAM,MAAA,IAAA,GAAA,IAAW,CAAK,OAAA,CAAA,GAAA,EAAW,EAAA,CAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA;AACjC,MAAI,MAAA,KAAA,GAAA,aAAwB,GAAA,GAAA,CAAA,CAAA;AAC5B,MAAA,IAAA,CAAA;AACA,QAAM,OAAA,GAAA,CAAA;AACN,MAAI,YAAe,GAAA,IAAA,CAAA,MAAA,CAAA;AACnB,MAAA,IAAA,WAAe,CAAK,MAAA,GAAA,CAAA,CAAA,KAAA,GAAA,EAAA;AACpB,QAAA,IAAS,GAAA,CAAA,EAAA,IAAO,CAAS,KAAA,CAAA,CAAA,EAAC,QAAW,CAAA,CAAA,EAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACnC,OAAO;AAAyC,MAClD,OAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,KAAA,CAAA;AAAkD,IACpD,MAAA,YAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAM,IAAA,KAAA,CAAA,KAAA,CAAA;AACJ,QAAI,OAAA,CAAM,CAAK;AACf,MAAM,MAAA,WAAA,GAAc,MAAM,QAAS,EAAA,CAAA;AACnC,MAAM,MAAA,WAAA,GAAc,WAAY,CAAA,OAAA,CAAQ,GAAG,CAAA,CAAA;AAC3C,MAAA,IAAI,SAAY,GAAA,CAAA,CAAA;AAChB,MAAA,IAAI,gBAAgB,CAAI,CAAA,EAAA;AACtB,QAAY,SAAA,GAAA,WAAA,CAAY,SAAS,WAAc,GAAA,CAAA,CAAA;AAAA,OACjD;AACA,MAAO,OAAA,SAAA,CAAA;AAAA,KACT,CAAA;AACA,IAAA,MAAM,eAAkB,GAAA,CAAC,GAAa,EAAA,WAAA,GAAsB,CAAM,KAAA;AAChE,MAAA,IAAI,CAAC,QAAA,CAAS,GAAG,CAAA;AACjB,QAAA,OAAW,IAAA,CAAA,YAA2B,CAAA;AACpC,MAAA,IAAA,GAAA,IAAA,MAAA,CAAA,gBAAA,IAAA,WAAA,KAAA,CAAA,EAAA;AACE,QACA,OAAA,GAAA,CAAA;AAAA,OACF,MAAA,IAAA,GAAA,IAAA,MAAA,CAAA,gBAAA,IAAA,WAAA,KAAA,CAAA,CAAA,EAAA;AACO,QACE,OAAA,GAAA,CAAA;AACT,OAAA;AAAA,MACE,OAAA,WAAA,CAAA,GAAA,GAAA,KAAA,CAAA,IAAA,GAAA,WAAA,CAAA,CAAA;AAAA,KACA,CAAA;AAAA,IACF,MAAA,aAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAO,IAAA,EAAA,CAAA;AAAA,MACT,MAAA,CAAA,GAAA,KAAA,CAAA;AAGA,MAAA,IAAA,KAAmB,CAAA,kBAAY,IAAA,CAAA,GAAA,EAAO,GAAW,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA;AAAA,QACnD,CAAA,CAAA,cAAA,EAAA,CAAA;AACA,QAAM,OAAA;AACJ,OAAA;AACA,MAAI,MAAA;AACF,QAAA,CAAA,UAAiB,CAAA,EAAA,GAAA,MAAA;AACjB,UAAA,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,UACF,QAAA,EAAA,CAAA;AACA,SAAA;AAAoB,QAClB,CAAC,UAAA,CAAW,IAAE,GAAS,MAAA;AACrB,UAAA,CAAA,CAAE,cAAe,EAAA,CAAA;AACjB,UAAS,QAAA,EAAA,CAAA;AAAA,SACX;AAAA,OAAA,CACA;AACE,MAAA,CAAA,EAAA,GAAE,WAAe,CAAA,CAAA,CAAA,GAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA;AACjB,KAAS,CAAA;AAAA,IACX,MAAA,QAAA,GAAA,MAAA;AAAA,MACF,IAAA,KAAA,CAAA,QAAA,IAAA,mBAAA,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA;AACA,QAAY,OAAA;AAAS,MACvB,MAAA,KAAA,GAAA,MAAA,CAAA,YAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACA,MAAA,eAAiB,eAAM,CAAA,KAAA,CAAA,CAAA;AACrB,MAAA,eAAU,CAAA,MAAY,CAAoB,CAAA;AAC1C,MAAA,IAAA,CAAA,WAAc,EAAA,IAAoB,CAAA,YAAA,CAAA,CAAA;AAClC,MAAM;AACN,KAAA,CAAA;AACA,IAAK,MAAA,QAAA,GAAA;AACL,MAA4B,IAAA,KAAA,CAAA,QAAA,IAAA,mBAAA,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA;AAAA,QAC9B,OAAA;AACA,MAAA,oBAAuB,CAAA,YAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACrB,MAAA,MAAU,MAAA,GAAA,eAAgC,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAC1C,MAAA,eAAc,CAAA,MAAoB,CAAA,CAAA;AAClC,MAAM,IAAA,CAAA,WAAyB,EAAA,IAAA,CAAA,YAAA,CAAA,CAAA;AAC/B,MAAA,2BAAsB,EAAA,CAAA;AACtB,KAAK,CAAA;AACL,IAA4B,MAAA,WAAA,GAAA,CAAA,KAAA,EAAA,MAAA,KAAA;AAAA,MAC9B,MAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,EAAA,SAAA,EAAA,YAAA,EAAA,YAAA,EAAA,GAAA,KAAA,CAAA;AACA,MAAM,IAAA,GAAA,GAAA,GAAA,EAAA;AAIJ,QAAA,UAAa,CAAA,aAAsB,EAAA,qCAA+B,CAAA,CAAA;AAClE,OAAA;AACE,MAAA,IAAA,MAAA,GAAW;AAAoD,MACjE,IAAA,KAAA,CAAA,KAAA,CAAA,IAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,EAAA;AACA,QAAI,OAAA,IAAS;AACb,OAAA;AACE,MAAO,IAAA,KAAA,KAAA,EAAA,EAAA;AAAA,QACT,IAAA,YAAA,KAAA,IAAA,EAAA;AACA,UAAI,WAAc,CAAA;AAChB,SAAA;AACE,QAAO,MAAA,GAAA,QAAA,CAAA,YAAA,CAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;AAAA,OACT;AACA,MAAS,IAAA,YAAA,EAAA;AAAsD,QACjE,MAAA,GAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,GAAA,IAAA,CAAA,GAAA,IAAA,EAAA,SAAA,CAAA,CAAA;AACA,QAAA,IAAkB,MAAA,KAAA,KAAA,EAAA;AAChB,UAAA,MAAA,mCAA6C,CAAA,CAAA;AAC7C,SAAA;AACE,OAAU;AAA+B,MAC3C,IAAA,CAAA,WAAA,CAAA,SAAA,CAAA,EAAA;AAAA,QACF,MAAA,GAAA,WAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA;AACA,OAAI;AACF,MAAS,IAAA,MAAA,GAAA,GAAA,IAAA,YAA6B,EAAA;AAAA,QACxC,MAAA,GAAA,MAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,CAAA;AACA,QAAI,MAAA,IAAA,IAAgB,CAAA,kBAAc,EAAA,MAAA,CAAA,CAAA;AAChC,OAAS;AACT,MAAU,OAAA,MAAA,CAAA;AAA+B,KAC3C,CAAA;AACA,IAAO,MAAA,eAAA,GAAA,CAAA,KAAA,EAAA,UAAA,GAAA,IAAA,KAAA;AAAA,MACT,IAAA,EAAA,CAAA;AACA,MAAA,MAAwB,MAAA,GAAA,IAAA,CAAA,YAEtB,CAAA;AAEA,MAAA,MAAM,SAAS,WAAK,CAAA,KAAA,CAAA,CAAA;AACpB,MAAM,IAAA,CAAA,UAAS;AACf,QAAA,IAAiB,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;AACf,QAAA;AACA,OAAA;AAAA,MACF,IAAA,MAAA,KAAA,MAAA,IAAA,KAAA;AACA,QAAI,OAAA;AACJ,MAAA,IAAA,CAAK,SAAY,GAAA,IAAA,CAAA;AACjB,MAAA,IAAA,CAAK,oBAAoB,MAAO,CAAA,CAAA;AAChC,MAAA,IAAI,WAAW,MAAQ,EAAA;AACrB,QAAK,IAAA,CAAA,YAAA,EAAc,QAAS,MAAO,CAAA,CAAA;AAAA,OACrC;AACA,MAAA,IAAI,MAAM,aAAe,EAAA;AACvB,QAAU,CAAA,EAAA,GAAA,QAAA,IAAA,YAAqB,CAAA,GAAA,QAAe,CAAA,QAAA,KAAU,IAAI,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OAC9D;AACA,MAAA,IAAA,CAAK,YAAe,GAAA,MAAA,CAAA;AAAA,KACtB,CAAA;AACA,IAAM,MAAA,WAAA,GAAc,CAAC,KAAkB,KAAA;AACrC,MAAA,IAAA,CAAK,SAAY,GAAA,KAAA,CAAA;AACjB,MAAA,MAAM,MAAS,GAAA,KAAA,KAAU,EAAK,GAAA,IAAA,GAAO,OAAO,KAAK,CAAA,CAAA;AACjD,MAAA,IAAA,CAAK,aAAa,MAAM,CAAA,CAAA;AACxB,MAAA,eAAA,CAAgB,QAAQ,KAAK,CAAA,CAAA;AAAA,KAC/B,CAAA;AACA,IAAM,MAAA,iBAAA,GAAoB,CAAC,KAAkB,KAAA;AAC3C,MAAA,MAAM,MAAS,GAAA,KAAA,KAAU,EAAK,GAAA,MAAA,CAAO,KAAK,CAAI,GAAA,EAAA,CAAA;AAC9C,MAAK,IAAA,QAAA,CAAS,MAAM,CAAK,IAAA,CAAC,OAAO,KAAM,CAAA,MAAM,CAAM,IAAA,KAAA,KAAU,EAAI,EAAA;AAC/D,QAAA,eAAA,CAAgB,MAAM,CAAA,CAAA;AAAA,OACxB;AACA,MAA4B,2BAAA,EAAA,CAAA;AAC5B,MAAA,IAAA,CAAK,SAAY,GAAA,IAAA,CAAA;AAAA,KACnB,CAAA;AAEA,IAAA,MAAM,QAAQ,MAAM;AAClB,MAAA,IAAA,EAAM;AAAe,MACvB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,aAAoB;AAAA,MACtB,IAAA,EAAA,EAAA,EAAA,CAAA;AAEA,MAAM,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAc,KAAoC,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AACtD,KAAA,CAAA;AAAmB,IACrB,MAAA,WAAA,GAAA,CAAA,KAAA,KAAA;AAEA,MAAM,IAAA,CAAA,OAAA,EAAA,KAAiD,CAAA,CAAA;AACrD,KAAA,CAAA;AAIA,IAAA,MAAI,UAAK,GAAA,CAAA,KAAA,KAAyB;AAChC,MAAM,IAAA,EAAA,EAAA,EAAA,CAAA;AAAoB,MAC5B,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA;AACA,MAAA,IAAA,iBAAkB,KAAA,IAAA,KAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA,EAAA;AAClB,QAAA,WAAyB,CAAA,KAAA,CAAA,KAAA,GAAA,EAAA,CAAA;AACvB,OAAU;AAAgD,MAC5D,IAAA,CAAA,MAAA,EAAA,KAAA,CAAA,CAAA;AAAA,MACF,IAAA,KAAA,CAAA,aAAA,EAAA;AAEA,QAAA,CAAA,iCAA0C,QAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AACxC,OAAI;AACF,KAAA,CAAA;AAA0B,IAC5B,MAAA,2BAAA,GAAA,MAAA;AAAA,MACF,IAAA,IAAA,CAAA,YAAA,KAAA,KAAA,CAAA,UAAA,EAAA;AACA,QAAM,IAAA,CAAA,YAAe,GAAkB,KAAA,CAAA,UAAA,CAAA;AACrC,OAAA;AAA0D,KAC5D,CAAA;AAEA,IAAA,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AAAA,MACE,YAAY,CAAA,aAAA,KAAA,CAAA,CAAA,MAAA;AAAA,wBACS,EAAA,CAAA;AACnB,KAAM,CAAA;AACN,IAAA,KAAA,CAAA,MAAS,KAAA,CAAA,UAAsB,EAAA,CAAA,KAAA,EAAA,QAAA,KAAuB;AACpD,MAAA,MAAA,QAAoB,GAAA,WAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,MACtB,IAAA,IAAA,CAAA,SAAA,KAAA,IAAA,IAAA,QAAA,KAAA,QAAA,EAAA;AAAA,QACF,IAAA,CAAA,YAAA,GAAA,QAAA,CAAA;AAAA,OACA;AAAkB,KACpB,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAEA,IAAA,KAAA,CAAA,MAAA,KAAA,CAAA,SAAA,EAAA,MAAA;AAAA,MACE,iBAAY,GAAA,WAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAAA,KAAA,CACZ,CAAM;AACJ,IAAK,SAAA,CAAA,MAAA;AAA2C,MAClD,IAAA,EAAA,CAAA;AAAA,MACF,MAAA,EAAA,GAAA,EAAA,GAAA,EAAA,UAAA,EAAA,GAAA,KAAA,CAAA;AACA,MAAA,MAAA,UAAgB,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AACd,MAAA,UAAQ,CAAA,YAAU,CAAA,MAAe,EAAA,YAAA,CAAA,CAAA;AACjC,MAAM,IAAA,MAAA,CAAA,QAAa,OAAa;AAChC,QAAW,UAAA,CAAA,4BAAiC,EAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAC5C,OAAI,MAAA;AACF,QAAA,UAAA,CAAW,eAAa,CAAA,eAAwB,CAAA,CAAA;AAAI,OAC/C;AACL,MAAA,IAAA,MAAA,CAAA;AAA0C,QAC5C,UAAA,CAAA,YAAA,CAAA,eAAA,EAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,OAAI,MAAA;AACF,QAAA,UAAA,CAAW,eAAa,CAAA,eAAwB,CAAA,CAAA;AAAI,OAC/C;AACL,MAAA,UAAA,CAAA,4BAA0C,EAAA,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,YAAA,KAAA,CAAA,GAAA,MAAA,CAAA,IAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,CAAA;AAAA,MAC5C,UAAA,CAAA,YAAA,CAAA,eAAA,EAAA,MAAA,CAAA,mBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,MAAW,IAAA,CAAA,QAAA,CAAA,UAAA,CAAA,IAAA,UAAA,IAAA,IAAA,EAAA;AAAA,QACT,IAAA,GAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA;AAAA,QACA,IAAA,iBAAqB,EAAK;AAEtB,UACN,GAAA,GAAA,IAAA,CAAA;AACA,SAAA;AACA,QAAA,IAAK,CAAA,kBAAmB,EAAA;AACtB,OAAI;AACJ,MAAI,UAAA,CAAA,gBAAmB,CAAA,OAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,KAAA,EAAA,CAAA,CAAA;AACrB,KAAM,CAAA,CAAA;AAAA,IACR,SAAA,CAAA,MAAA;AACA,MAAA,IAAA,EAAA;AAA6B,MAC/B,MAAA,UAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AACA,MAAA,UAAA,oBAAqC,UAAA,CAAA,YAAe,CAAA,eAAgB,EAAA,CAAA,EAAA,CAAA,EAAA,GAAA,IAAA,CAAA,YAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAAA,KACrE,CAAA,CAAA;AACD,IAAA,MAAA,CAAA;AACE,MAAM,KAAA;AACN,MAAA,IAAA;AAAsE,KACvE,CAAA,CAAA;AACD,IAAa,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QAEX,KAAA,EAAAC,cAAA,CAAA;AAAA,UAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA;AAAA,UAEAA,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAAA,KAAA,CAAA,eAAA,CAAA,CAAA;AAAA,UACDA,KAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,UAAA,EAAAA,KAAA,CAAA,mBAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}