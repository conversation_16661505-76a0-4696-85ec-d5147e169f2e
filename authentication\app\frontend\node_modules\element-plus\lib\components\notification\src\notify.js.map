{"version": 3, "file": "notify.js", "sources": ["../../../../../../packages/components/notification/src/notify.ts"], "sourcesContent": ["import { createVNode, isVNode, render } from 'vue'\nimport {\n  debugWarn,\n  isClient,\n  isElement,\n  isFunction,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\nimport NotificationConstructor from './notification.vue'\nimport { notificationTypes } from './notification'\n\nimport type { Ref, VNode } from 'vue'\nimport type {\n  NotificationOptions,\n  NotificationProps,\n  NotificationQueue,\n  Notify,\n  NotifyFn,\n} from './notification'\n\n// This should be a queue but considering there were `non-autoclosable` notifications.\nconst notifications: Record<\n  NotificationOptions['position'],\n  NotificationQueue\n> = {\n  'top-left': [],\n  'top-right': [],\n  'bottom-left': [],\n  'bottom-right': [],\n}\n\n// the gap size between each notification\nconst GAP_SIZE = 16\nlet seed = 1\n\nconst notify: NotifyFn & Partial<Notify> = function (options = {}, context) {\n  if (!isClient) return { close: () => undefined }\n\n  if (isString(options) || isVNode(options)) {\n    options = { message: options }\n  }\n\n  const position = options.position || 'top-right'\n\n  let verticalOffset = options.offset || 0\n  notifications[position].forEach(({ vm }) => {\n    verticalOffset += (vm.el?.offsetHeight || 0) + GAP_SIZE\n  })\n  verticalOffset += GAP_SIZE\n\n  const id = `notification_${seed++}`\n  const userOnClose = options.onClose\n  const props: Partial<NotificationProps> = {\n    ...options,\n    offset: verticalOffset,\n    id,\n    onClose: () => {\n      close(id, position, userOnClose)\n    },\n  }\n\n  let appendTo: HTMLElement | null = document.body\n  if (isElement(options.appendTo)) {\n    appendTo = options.appendTo\n  } else if (isString(options.appendTo)) {\n    appendTo = document.querySelector(options.appendTo)\n  }\n\n  // should fallback to default value with a warning\n  if (!isElement(appendTo)) {\n    debugWarn(\n      'ElNotification',\n      'the appendTo option is not an HTMLElement. Falling back to document.body.'\n    )\n    appendTo = document.body\n  }\n\n  const container = document.createElement('div')\n\n  const vm = createVNode(\n    NotificationConstructor,\n    props,\n    isFunction(props.message)\n      ? props.message\n      : isVNode(props.message)\n      ? () => props.message\n      : null\n  )\n  vm.appContext = isUndefined(context) ? notify._context : context\n\n  // clean notification element preventing mem leak\n  vm.props!.onDestroy = () => {\n    render(null, container)\n  }\n\n  // instances will remove this item when close function gets called. So we do not need to worry about it.\n  render(vm, container)\n  notifications[position].push({ vm })\n  appendTo.appendChild(container.firstElementChild!)\n\n  return {\n    // instead of calling the onClose function directly, setting this value so that we can have the full lifecycle\n    // for out component, so that all closing steps will not be skipped.\n    close: () => {\n      ;(vm.component!.exposed as { visible: Ref<boolean> }).visible.value =\n        false\n    },\n  }\n}\nnotificationTypes.forEach((type) => {\n  notify[type] = (options = {}, appContext) => {\n    if (isString(options) || isVNode(options)) {\n      options = {\n        message: options,\n      }\n    }\n    return notify({ ...options, type }, appContext)\n  }\n})\n\n/**\n * This function gets called when user click `x` button or press `esc` or the time reached its limitation.\n * Emitted by transition@before-leave event so that we can fetch the current notification.offsetHeight, if this was called\n * by @after-leave the DOM element will be removed from the page thus we can no longer fetch the offsetHeight.\n * @param {String} id notification id to be closed\n * @param {Position} position the positioning strategy\n * @param {Function} userOnClose the callback called when close passed by user\n */\nexport function close(\n  id: string,\n  position: NotificationOptions['position'],\n  userOnClose?: (vm: VNode) => void\n): void {\n  // maybe we can store the index when inserting the vm to notification list.\n  const orientedNotifications = notifications[position]\n  const idx = orientedNotifications.findIndex(\n    ({ vm }) => vm.component?.props.id === id\n  )\n  if (idx === -1) return\n  const { vm } = orientedNotifications[idx]\n  if (!vm) return\n  // calling user's on close function before notification gets removed from DOM.\n  userOnClose?.(vm)\n\n  // note that this is called @before-leave, that's why we were able to fetch this property.\n  const removedHeight = vm.el!.offsetHeight\n  const verticalPos = position.split('-')[0]\n  orientedNotifications.splice(idx, 1)\n  const len = orientedNotifications.length\n  if (len < 1) return\n  // starting from the removing item.\n  for (let i = idx; i < len; i++) {\n    // new position equals the current offsetTop minus removed height plus 16px(the gap size between each item)\n    const { el, component } = orientedNotifications[i].vm\n    const pos =\n      Number.parseInt(el!.style[verticalPos], 10) - removedHeight - GAP_SIZE\n    component!.props.offset = pos\n  }\n}\n\nexport function closeAll(): void {\n  // loop through all directions, close them at once.\n  for (const orientedNotifications of Object.values(notifications)) {\n    orientedNotifications.forEach(({ vm }) => {\n      // same as the previous close method, we'd like to make sure lifecycle gets handle properly.\n      ;(vm.component!.exposed as { visible: Ref<boolean> }).visible.value =\n        false\n    })\n  }\n}\n\nexport function updateOffsets(\n  position: NotificationOptions['position'] = 'top-right'\n) {\n  let verticalOffset = notifications[position][0]?.vm.props?.offset || 0\n\n  for (const { vm } of notifications[position]) {\n    vm.component!.props.offset = verticalOffset\n    verticalOffset += (vm.el?.offsetHeight || 0) + GAP_SIZE\n  }\n}\n\nnotify.closeAll = closeAll\nnotify.updateOffsets = updateOffsets\nnotify._context = null\n\nexport default notify as Notify\n"], "names": ["isClient", "isString", "isVNode", "isElement", "createVNode", "NotificationConstructor", "isFunction", "isUndefined", "render", "notificationTypes"], "mappings": ";;;;;;;;;;;AAWA,MAAM,aAAa,GAAG;AACtB,EAAE,UAAU,EAAE,EAAE;AAChB,EAAE,WAAW,EAAE,EAAE;AACjB,EAAE,aAAa,EAAE,EAAE;AACnB,EAAE,cAAc,EAAE,EAAE;AACpB,CAAC,CAAC;AACF,MAAM,QAAQ,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,GAAG,CAAC,CAAC;AACR,MAAC,MAAM,GAAG,SAAS,OAAO,GAAG,EAAE,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,CAACA,aAAQ;AACf,IAAI,OAAO,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;AACnC,EAAE,IAAIC,eAAQ,CAAC,OAAO,CAAC,IAAIC,WAAO,CAAC,OAAO,CAAC,EAAE;AAC7C,IAAI,OAAO,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;AACnC,GAAG;AACH,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,WAAW,CAAC;AACnD,EAAE,IAAI,cAAc,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;AAC3C,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK;AACnD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,cAAc,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,CAAC,IAAI,QAAQ,CAAC;AAC3F,GAAG,CAAC,CAAC;AACL,EAAE,cAAc,IAAI,QAAQ,CAAC;AAC7B,EAAE,MAAM,EAAE,GAAG,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,EAAE,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;AACtC,EAAE,MAAM,KAAK,GAAG;AAChB,IAAI,GAAG,OAAO;AACd,IAAI,MAAM,EAAE,cAAc;AAC1B,IAAI,EAAE;AACN,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;AACvC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC/B,EAAE,IAAIC,eAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACnC,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AAChC,GAAG,MAAM,IAAIF,eAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACzC,IAAI,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACxD,GAAG;AACH,EAAE,IAAI,CAACE,eAAS,CAAC,QAAQ,CAAC,EAAE;AAE5B,IAAI,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC7B,GAAG;AACH,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAClD,EAAE,MAAM,EAAE,GAAGC,eAAW,CAACC,uBAAuB,EAAE,KAAK,EAAEC,iBAAU,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,OAAO,GAAGJ,WAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,MAAM,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;AAC1J,EAAE,EAAE,CAAC,UAAU,GAAGK,iBAAW,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,QAAQ,GAAG,OAAO,CAAC;AACnE,EAAE,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM;AAC7B,IAAIC,UAAM,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC5B,GAAG,CAAC;AACJ,EAAEA,UAAM,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;AACxB,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACvC,EAAE,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,OAAO;AACT,IAAI,KAAK,EAAE,MAAM;AAEjB,MAAM,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AACjD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE;AACFC,gCAAiB,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACpC,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,EAAE,EAAE,UAAU,KAAK;AAC/C,IAAI,IAAIR,eAAQ,CAAC,OAAO,CAAC,IAAIC,WAAO,CAAC,OAAO,CAAC,EAAE;AAC/C,MAAM,OAAO,GAAG;AAChB,QAAQ,OAAO,EAAE,OAAO;AACxB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,EAAE,GAAG,OAAO,EAAE,IAAI,EAAE,EAAE,UAAU,CAAC,CAAC;AACpD,GAAG,CAAC;AACJ,CAAC,CAAC,CAAC;AACI,SAAS,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE;AACjD,EAAE,MAAM,qBAAqB,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;AACxD,EAAE,MAAM,GAAG,GAAG,qBAAqB,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK;AAC/D,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;AACxE,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC;AAChB,IAAI,OAAO;AACX,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;AAC5C,EAAE,IAAI,CAAC,EAAE;AACT,IAAI,OAAO;AACX,EAAE,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AACjD,EAAE,MAAM,aAAa,GAAG,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC;AAC3C,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,EAAE,qBAAqB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACvC,EAAE,MAAM,GAAG,GAAG,qBAAqB,CAAC,MAAM,CAAC;AAC3C,EAAE,IAAI,GAAG,GAAG,CAAC;AACb,IAAI,OAAO;AACX,EAAE,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClC,IAAI,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC1D,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,GAAG,aAAa,GAAG,QAAQ,CAAC;AACtF,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;AACjC,GAAG;AACH,CAAC;AACM,SAAS,QAAQ,GAAG;AAC3B,EAAE,KAAK,MAAM,qBAAqB,IAAI,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE;AACpE,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK;AAE9C,MAAM,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AACjD,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC;AACM,SAAS,aAAa,CAAC,QAAQ,GAAG,WAAW,EAAE;AACtD,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACjB,EAAE,IAAI,cAAc,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC;AACnI,EAAE,KAAK,MAAM,EAAE,EAAE,EAAE,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE;AAChD,IAAI,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC;AAC/C,IAAI,cAAc,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,KAAK,CAAC,IAAI,QAAQ,CAAC;AAC1F,GAAG;AACH,CAAC;AACD,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC3B,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC;AACrC,MAAM,CAAC,QAAQ,GAAG,IAAI;;;;;;;"}