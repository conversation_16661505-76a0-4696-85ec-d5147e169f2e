{"name": "vue-eslint-parser", "version": "9.4.3", "description": "The ESLint custom parser for `.vue` files.", "engines": {"node": "^14.17.0 || >=16.0.0"}, "main": "index.js", "files": ["index.*"], "peerDependencies": {"eslint": ">=6.0.0"}, "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^8.4.6", "@types/estree": "^1.0.0", "@types/lodash": "^4.14.186", "@types/mocha": "^9.0.0", "@types/node": "^18.8.4", "@types/semver": "^7.3.12", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.12.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.2.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.8.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.8.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "scripts": {"prebuild": "npm run -s clean", "build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "pretest": "run-s build lint", "test": "npm run -s test:mocha", "test:mocha": "mocha --require ts-node/register \"test/*.js\" --reporter dot --timeout 60000", "test:cover": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "ts-node --transpile-only scripts/update-fixtures-ast.js && ts-node --transpile-only scripts/update-fixtures-document-fragment.js", "preversion": "npm test", "version": "npm run -s build", "postversion": "git push && git push --tags", "prewatch": "npm run -s clean", "watch": "run-p watch:*", "watch:tsc": "tsc --module es2015 --watch", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "funding": "https://github.com/sponsors/mysticatea"}