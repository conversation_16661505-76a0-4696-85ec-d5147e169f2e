import{e}from"./index-bcbc0702.js";import"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const t=e("userSession",{state:()=>({editToken:null,editTokenExpiresAt:null,resubmitToken:null}),actions:{setEditToken(e,t){this.editToken=e||null,this.editTokenExpiresAt=e&&t?Date.now()+1e3*t:null},clearEditToken(){this.editToken=null,this.editTokenExpiresAt=null},setResubmitToken(e){this.resubmitToken=e||null},clearResubmitToken(){this.resubmitToken=null}},getters:{hasValidEditToken:e=>!!e.editToken&&(!e.editTokenExpiresAt||e.editTokenExpiresAt>Date.now())}});export{t as useUserSessionStore};
