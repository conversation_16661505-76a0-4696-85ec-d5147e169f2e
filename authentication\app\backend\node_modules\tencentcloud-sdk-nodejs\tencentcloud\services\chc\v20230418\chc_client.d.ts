import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { CreatePersonnelVisitWorkOrderRequest, DescribePositionsRequest, DescribeIdcUnitAssetDetailRequest, CreateMovingWorkOrderRequest, CreateServerModelRequest, DescribeModelVersionListResponse, CreateModelEvaluationWorkOrderRequest, DescribeModelVersionListRequest, CreateNetDeviceModelRequest, DescribeIdcUnitDetailResponse, DescribeIdcsRequest, DescribeIdcUnitAssetDetailResponse, DescribePositionsResponse, DescribePositionStatusSummaryResponse, DescribeDeviceListRequest, CreateSpeciallyQuitWorkOrderRequest, ConfirmCommonServiceWorkOrderRequest, DescribeModelEvaluationWorkOrderDetailRequest, CreateReceivingWorkOrderRequest, DescribeWorkOrderStatisticsRequest, DescribeResourceUsageRequest, CreateCommonServiceWorkOrderRequest, CreateCommonServiceWorkOrderResponse, DescribeIdcUnitDetailRequest, DescribeAvailableModelListRequest, CreatePersonnelVisitWorkOrderResponse, DescribeModelRequest, CreateRackOffWorkOrderRequest, ExportCustomerWorkOrderDetailResponse, DescribeModelTemplateResponse, DescribeWorkOrderListRequest, DescribeWorkOrderTypesResponse, DescribePositionStatusSummaryRequest, DescribeWorkOrderTypesRequest, CreateRackOnWorkOrderRequest, DescribeCampusListResponse, DescribeModelTemplateRequest, CreateMovingWorkOrderResponse, ExportCustomerWorkOrderDetailRequest, DescribeResourceUsageResponse, CreateRackOffWorkOrderResponse, CreatePowerOnWorkOrderResponse, CreateRackOnWorkOrderResponse, DescribePersonnelVisitWorkOrderDetailRequest, DescribeCustomerInfoResponse, CreateServerModelResponse, DescribeCampusListRequest, ModifyWorkOrderTypeCollectFlagResponse, DescribeRacksDistributionRequest, DescribeCommonServiceWorkOrderDetailResponse, ModifyWorkOrderTypeCollectFlagRequest, CreateNetDeviceModelResponse, DescribeCommonServiceWorkOrderDetailRequest, CreateModelEvaluationWorkOrderResponse, DescribeWorkOrderListResponse, DescribeAvailableModelListResponse, DescribeWorkOrderStatisticsResponse, CreateReceivingWorkOrderResponse, DescribePersonnelVisitWorkOrderDetailResponse, CreateQuitWorkOrderRequest, DescribeModelResponse, CreatePowerOnWorkOrderRequest, CreatePowerOffWorkOrderResponse, CreateSpeciallyQuitWorkOrderResponse, ConfirmCommonServiceWorkOrderResponse, CreatePowerOffWorkOrderRequest, CreateQuitWorkOrderResponse, DescribeRacksDistributionResponse, DescribeCustomerInfoRequest, DescribeDeviceListResponse, DescribeDeviceWorkOrderDetailRequest, DescribeModelEvaluationWorkOrderDetailResponse, DescribeRacksResponse, DescribeRacksRequest, DescribeIdcsResponse, DescribeDeviceWorkOrderDetailResponse } from "./chc_models";
/**
 * chc client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 创建设备退出工单
     */
    CreateQuitWorkOrder(req: CreateQuitWorkOrderRequest, cb?: (error: string, rep: CreateQuitWorkOrderResponse) => void): Promise<CreateQuitWorkOrderResponse>;
    /**
     * 获取用户可用的工单类型
     */
    DescribeWorkOrderTypes(req?: DescribeWorkOrderTypesRequest, cb?: (error: string, rep: DescribeWorkOrderTypesResponse) => void): Promise<DescribeWorkOrderTypesResponse>;
    /**
     * 查询设备型号详情
     */
    DescribeModel(req: DescribeModelRequest, cb?: (error: string, rep: DescribeModelResponse) => void): Promise<DescribeModelResponse>;
    /**
     * 获取机架列表
     */
    DescribeRacks(req: DescribeRacksRequest, cb?: (error: string, rep: DescribeRacksResponse) => void): Promise<DescribeRacksResponse>;
    /**
     * 创建设备型号评估工单
     */
    CreateModelEvaluationWorkOrder(req: CreateModelEvaluationWorkOrderRequest, cb?: (error: string, rep: CreateModelEvaluationWorkOrderResponse) => void): Promise<CreateModelEvaluationWorkOrderResponse>;
    /**
     * 新增服务器设备型号
     */
    CreateServerModel(req: CreateServerModelRequest, cb?: (error: string, rep: CreateServerModelResponse) => void): Promise<CreateServerModelResponse>;
    /**
     * 创建设备下架工单
     */
    CreateRackOffWorkOrder(req: CreateRackOffWorkOrderRequest, cb?: (error: string, rep: CreateRackOffWorkOrderResponse) => void): Promise<CreateRackOffWorkOrderResponse>;
    /**
     * 获取用户的型号和对应的版本数量
     */
    DescribeModelVersionList(req: DescribeModelVersionListRequest, cb?: (error: string, rep: DescribeModelVersionListResponse) => void): Promise<DescribeModelVersionListResponse>;
    /**
     * 查询通用服务工单详情
     */
    DescribeCommonServiceWorkOrderDetail(req: DescribeCommonServiceWorkOrderDetailRequest, cb?: (error: string, rep: DescribeCommonServiceWorkOrderDetailResponse) => void): Promise<DescribeCommonServiceWorkOrderDetailResponse>;
    /**
     * 如果当前该工单类型是收藏状态，调用接口后变成未收藏状态，如果是未收藏状态，调用该接口变为收藏状态
     */
    ModifyWorkOrderTypeCollectFlag(req: ModifyWorkOrderTypeCollectFlagRequest, cb?: (error: string, rep: ModifyWorkOrderTypeCollectFlagResponse) => void): Promise<ModifyWorkOrderTypeCollectFlagResponse>;
    /**
     * 查询设备型号评估工单详情
     */
    DescribeModelEvaluationWorkOrderDetail(req: DescribeModelEvaluationWorkOrderDetailRequest, cb?: (error: string, rep: DescribeModelEvaluationWorkOrderDetailResponse) => void): Promise<DescribeModelEvaluationWorkOrderDetailResponse>;
    /**
     * 查询客户信息
     */
    DescribeCustomerInfo(req?: DescribeCustomerInfoRequest, cb?: (error: string, rep: DescribeCustomerInfoResponse) => void): Promise<DescribeCustomerInfoResponse>;
    /**
     * 获取用户可操作的园区列表
     */
    DescribeCampusList(req?: DescribeCampusListRequest, cb?: (error: string, rep: DescribeCampusListResponse) => void): Promise<DescribeCampusListResponse>;
    /**
     * 获取机房管理单元的机位分布
     */
    DescribeRacksDistribution(req: DescribeRacksDistributionRequest, cb?: (error: string, rep: DescribeRacksDistributionResponse) => void): Promise<DescribeRacksDistributionResponse>;
    /**
     * 获取机位列表
     */
    DescribePositions(req: DescribePositionsRequest, cb?: (error: string, rep: DescribePositionsResponse) => void): Promise<DescribePositionsResponse>;
    /**
     * 创建通用工单
     */
    CreateCommonServiceWorkOrder(req: CreateCommonServiceWorkOrderRequest, cb?: (error: string, rep: CreateCommonServiceWorkOrderResponse) => void): Promise<CreateCommonServiceWorkOrderResponse>;
    /**
     * 用于查询设备类工单的工单详情，如：'receiving', 'rackOn', 'powerOn', 'powerOff', 'rackOff', 'quit'
     */
    DescribeDeviceWorkOrderDetail(req: DescribeDeviceWorkOrderDetailRequest, cb?: (error: string, rep: DescribeDeviceWorkOrderDetailResponse) => void): Promise<DescribeDeviceWorkOrderDetailResponse>;
    /**
     * 获取机房内可用的型号列表
     */
    DescribeAvailableModelList(req: DescribeAvailableModelListRequest, cb?: (error: string, rep: DescribeAvailableModelListResponse) => void): Promise<DescribeAvailableModelListResponse>;
    /**
     * 获取设备列表
     */
    DescribeDeviceList(req: DescribeDeviceListRequest, cb?: (error: string, rep: DescribeDeviceListResponse) => void): Promise<DescribeDeviceListResponse>;
    /**
     * 创建设备开电工单
     */
    CreatePowerOnWorkOrder(req: CreatePowerOnWorkOrderRequest, cb?: (error: string, rep: CreatePowerOnWorkOrderResponse) => void): Promise<CreatePowerOnWorkOrderResponse>;
    /**
     * 获取机架总数及各状态对应的数量汇总
     */
    DescribePositionStatusSummary(req: DescribePositionStatusSummaryRequest, cb?: (error: string, rep: DescribePositionStatusSummaryResponse) => void): Promise<DescribePositionStatusSummaryResponse>;
    /**
     * 导出工单详情
     */
    ExportCustomerWorkOrderDetail(req: ExportCustomerWorkOrderDetailRequest, cb?: (error: string, rep: ExportCustomerWorkOrderDetailResponse) => void): Promise<ExportCustomerWorkOrderDetailResponse>;
    /**
     * 创建临时设备退出工单
     */
    CreateSpeciallyQuitWorkOrder(req: CreateSpeciallyQuitWorkOrderRequest, cb?: (error: string, rep: CreateSpeciallyQuitWorkOrderResponse) => void): Promise<CreateSpeciallyQuitWorkOrderResponse>;
    /**
     * 创建设备关电工单
     */
    CreatePowerOffWorkOrder(req: CreatePowerOffWorkOrderRequest, cb?: (error: string, rep: CreatePowerOffWorkOrderResponse) => void): Promise<CreatePowerOffWorkOrderResponse>;
    /**
     * 创建人员到访工单
     */
    CreatePersonnelVisitWorkOrder(req: CreatePersonnelVisitWorkOrderRequest, cb?: (error: string, rep: CreatePersonnelVisitWorkOrderResponse) => void): Promise<CreatePersonnelVisitWorkOrderResponse>;
    /**
     * 查询人员到访工单详情
     */
    DescribePersonnelVisitWorkOrderDetail(req: DescribePersonnelVisitWorkOrderDetailRequest, cb?: (error: string, rep: DescribePersonnelVisitWorkOrderDetailResponse) => void): Promise<DescribePersonnelVisitWorkOrderDetailResponse>;
    /**
     * 查询机房管理单元详情
     */
    DescribeIdcUnitDetail(req: DescribeIdcUnitDetailRequest, cb?: (error: string, rep: DescribeIdcUnitDetailResponse) => void): Promise<DescribeIdcUnitDetailResponse>;
    /**
     * 确认通用服务工单
     */
    ConfirmCommonServiceWorkOrder(req: ConfirmCommonServiceWorkOrderRequest, cb?: (error: string, rep: ConfirmCommonServiceWorkOrderResponse) => void): Promise<ConfirmCommonServiceWorkOrderResponse>;
    /**
     * 创建设备搬迁工单
     */
    CreateMovingWorkOrder(req: CreateMovingWorkOrderRequest, cb?: (error: string, rep: CreateMovingWorkOrderResponse) => void): Promise<CreateMovingWorkOrderResponse>;
    /**
     * 创建新的网络设备型号
     */
    CreateNetDeviceModel(req: CreateNetDeviceModelRequest, cb?: (error: string, rep: CreateNetDeviceModelResponse) => void): Promise<CreateNetDeviceModelResponse>;
    /**
     * 工单统计数据查询
     */
    DescribeWorkOrderStatistics(req?: DescribeWorkOrderStatisticsRequest, cb?: (error: string, rep: DescribeWorkOrderStatisticsResponse) => void): Promise<DescribeWorkOrderStatisticsResponse>;
    /**
     * 查询资源汇总
     */
    DescribeResourceUsage(req: DescribeResourceUsageRequest, cb?: (error: string, rep: DescribeResourceUsageResponse) => void): Promise<DescribeResourceUsageResponse>;
    /**
     * 查询机房管理单元资产详情
     */
    DescribeIdcUnitAssetDetail(req: DescribeIdcUnitAssetDetailRequest, cb?: (error: string, rep: DescribeIdcUnitAssetDetailResponse) => void): Promise<DescribeIdcUnitAssetDetailResponse>;
    /**
     * 创建设备上架工单
     */
    CreateRackOnWorkOrder(req: CreateRackOnWorkOrderRequest, cb?: (error: string, rep: CreateRackOnWorkOrderResponse) => void): Promise<CreateRackOnWorkOrderResponse>;
    /**
     * 获取机房和机房管理单元信息
     */
    DescribeIdcs(req?: DescribeIdcsRequest, cb?: (error: string, rep: DescribeIdcsResponse) => void): Promise<DescribeIdcsResponse>;
    /**
     * 创建设备收货工单
     */
    CreateReceivingWorkOrder(req: CreateReceivingWorkOrderRequest, cb?: (error: string, rep: CreateReceivingWorkOrderResponse) => void): Promise<CreateReceivingWorkOrderResponse>;
    /**
     * 获取型号的填写模板
     */
    DescribeModelTemplate(req: DescribeModelTemplateRequest, cb?: (error: string, rep: DescribeModelTemplateResponse) => void): Promise<DescribeModelTemplateResponse>;
    /**
     * 查询工单列表
     */
    DescribeWorkOrderList(req: DescribeWorkOrderListRequest, cb?: (error: string, rep: DescribeWorkOrderListResponse) => void): Promise<DescribeWorkOrderListResponse>;
}
