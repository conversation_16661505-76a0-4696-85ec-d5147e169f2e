/*! Element Plus v2.10.7 */

var eo = {
  name: "eo",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "<PERSON>",
      clear: "<PERSON><PERSON><PERSON><PERSON>"
    },
    datepicker: {
      now: "Nun",
      today: "<PERSON><PERSON>\u016D",
      cancel: "<PERSON><PERSON><PERSON>",
      clear: "<PERSON><PERSON><PERSON><PERSON>",
      confirm: "<PERSON>",
      selectDate: "<PERSON>ek<PERSON> daton",
      selectTime: "Elektu horon",
      startDate: "Komenca Dato",
      startTime: "Komenca Horo",
      endDate: "Fina Dato",
      endTime: "Fina Horo",
      prevYear: "Anta\u016Da Jaro",
      nextYear: "Sekva J<PERSON>",
      prevMonth: "Anta\u016Da Monato",
      nextMonth: "<PERSON>k<PERSON>",
      year: "J<PERSON>",
      month1: "Januaro",
      month2: "Februaro",
      month3: "Mart<PERSON>",
      month4: "Aprilo",
      month5: "<PERSON><PERSON>",
      month6: "<PERSON><PERSON>",
      month7: "<PERSON>",
      month8: "A\u016Dgusto",
      month9: "Septembro",
      month10: "Ok<PERSON><PERSON>",
      month11: "Novembro",
      month12: "Decem<PERSON>",
      week: "Semajn<PERSON>",
      weeks: {
        sun: "Dim",
        mon: "<PERSON>n",
        tue: "Mar",
        wed: "Mer",
        thu: "\u0134a\u016D",
        fri: "Ven",
        sat: "Sab"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "Maj",
        jun: "Jun",
        jul: "Jul",
        aug: "A\u016Dg",
        sep: "Sep",
        oct: "Okt",
        nov: "Nov",
        dec: "Dec"
      }
    },
    select: {
      loading: "\u015Car\u011Dante",
      noMatch: "Neniuj kongruaj datumoj",
      noData: "Neniuj datumoj",
      placeholder: "Bonvolu elekti"
    },
    mention: {
      loading: "\u015Car\u011Dante"
    },
    cascader: {
      noMatch: "Neniuj kongruaj datumoj",
      loading: "\u015Car\u011Dante",
      placeholder: "Bonvolu elekti",
      noData: "Neniuj datumoj"
    },
    pagination: {
      goto: "Iru al",
      pagesize: "/ pa\u011Do",
      total: "Entute {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Mesa\u011Do",
      confirm: "Bone",
      cancel: "Nuligi",
      error: "Nevalida Enigo!"
    },
    upload: {
      deleteTip: 'Premu "Delete" por forigi',
      delete: "Forigi",
      preview: "Anta\u016Drigardi",
      continue: "Da\u016Drigi"
    },
    table: {
      emptyText: "Neniuj datumoj",
      confirmFilter: "Konfirmi",
      resetFilter: "Restarigi",
      clearFilter: "\u0108iuj",
      sumText: "Sumo"
    },
    tree: {
      emptyText: "Neniuj datumoj"
    },
    transfer: {
      noMatch: "Neniuj kongruaj datumoj",
      noData: "Neniuj datumoj",
      titles: ["Listo 1", "Listo 2"],
      filterPlaceholder: "Enigu \u015Dlosilvorton",
      noCheckedFormat: "{total} elementoj",
      hasCheckedFormat: "{checked}/{total} elektitaj"
    },
    image: {
      error: "MALSUKCESIS"
    },
    pageHeader: {
      title: "Reen"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { eo as default };
