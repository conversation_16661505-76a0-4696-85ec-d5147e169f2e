import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("oceanus.tencentcloudapi.com", "2019-04-22", clientConfig);
    }
    async DescribeTreeJobs(req, cb) {
        return this.request("DescribeTreeJobs", req, cb);
    }
    async DescribeTreeResources(req, cb) {
        return this.request("DescribeTreeResources", req, cb);
    }
    async CopyJobs(req, cb) {
        return this.request("CopyJobs", req, cb);
    }
    async RunSqlGatewayStatement(req, cb) {
        return this.request("RunSqlGatewayStatement", req, cb);
    }
    async DeleteResourceConfigs(req, cb) {
        return this.request("DeleteResourceConfigs", req, cb);
    }
    async CreateWorkSpace(req, cb) {
        return this.request("CreateWorkSpace", req, cb);
    }
    async CreateResourceConfig(req, cb) {
        return this.request("CreateResourceConfig", req, cb);
    }
    async DeleteTableConfig(req, cb) {
        return this.request("DeleteTableConfig", req, cb);
    }
    async DescribeFolder(req, cb) {
        return this.request("DescribeFolder", req, cb);
    }
    async DeleteWorkSpace(req, cb) {
        return this.request("DeleteWorkSpace", req, cb);
    }
    async ModifyFolder(req, cb) {
        return this.request("ModifyFolder", req, cb);
    }
    async DescribeJobSavepoint(req, cb) {
        return this.request("DescribeJobSavepoint", req, cb);
    }
    async DescribeJobSubmissionLog(req, cb) {
        return this.request("DescribeJobSubmissionLog", req, cb);
    }
    async DescribeResources(req, cb) {
        return this.request("DescribeResources", req, cb);
    }
    async DescribeJobRuntimeInfo(req, cb) {
        return this.request("DescribeJobRuntimeInfo", req, cb);
    }
    async FetchSqlGatewayStatementResult(req, cb) {
        return this.request("FetchSqlGatewayStatementResult", req, cb);
    }
    async DescribeJobEvents(req, cb) {
        return this.request("DescribeJobEvents", req, cb);
    }
    async DescribeResourceConfigs(req, cb) {
        return this.request("DescribeResourceConfigs", req, cb);
    }
    async DeleteFolders(req, cb) {
        return this.request("DeleteFolders", req, cb);
    }
    async DescribeWorkSpaces(req, cb) {
        return this.request("DescribeWorkSpaces", req, cb);
    }
    async CreateJob(req, cb) {
        return this.request("CreateJob", req, cb);
    }
    async TriggerJobSavepoint(req, cb) {
        return this.request("TriggerJobSavepoint", req, cb);
    }
    async RunJobs(req, cb) {
        return this.request("RunJobs", req, cb);
    }
    async DeleteJobs(req, cb) {
        return this.request("DeleteJobs", req, cb);
    }
    async CreateFolder(req, cb) {
        return this.request("CreateFolder", req, cb);
    }
    async CheckSavepoint(req, cb) {
        return this.request("CheckSavepoint", req, cb);
    }
    async CreateJobConfig(req, cb) {
        return this.request("CreateJobConfig", req, cb);
    }
    async DescribeClusters(req, cb) {
        return this.request("DescribeClusters", req, cb);
    }
    async DeleteJobConfigs(req, cb) {
        return this.request("DeleteJobConfigs", req, cb);
    }
    async StopJobs(req, cb) {
        return this.request("StopJobs", req, cb);
    }
    async DescribeResourceRelatedJobs(req, cb) {
        return this.request("DescribeResourceRelatedJobs", req, cb);
    }
    async ModifyWorkSpace(req, cb) {
        return this.request("ModifyWorkSpace", req, cb);
    }
    async DeleteResources(req, cb) {
        return this.request("DeleteResources", req, cb);
    }
    async DescribeJobs(req, cb) {
        return this.request("DescribeJobs", req, cb);
    }
    async ModifyJob(req, cb) {
        return this.request("ModifyJob", req, cb);
    }
    async CreateResource(req, cb) {
        return this.request("CreateResource", req, cb);
    }
    async DescribeSystemResources(req, cb) {
        return this.request("DescribeSystemResources", req, cb);
    }
    async GetMetaTable(req, cb) {
        return this.request("GetMetaTable", req, cb);
    }
    async DescribeJobConfigs(req, cb) {
        return this.request("DescribeJobConfigs", req, cb);
    }
}
