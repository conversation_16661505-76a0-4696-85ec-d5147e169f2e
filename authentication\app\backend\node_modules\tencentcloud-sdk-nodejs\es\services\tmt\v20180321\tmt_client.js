import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("tmt.tencentcloudapi.com", "2018-03-21", clientConfig);
    }
    async LanguageDetect(req, cb) {
        return this.request("LanguageDetect", req, cb);
    }
    async TextTranslateBatch(req, cb) {
        return this.request("TextTranslateBatch", req, cb);
    }
    async ImageTranslateLLM(req, cb) {
        return this.request("ImageTranslateLLM", req, cb);
    }
    async GetFileTranslate(req, cb) {
        return this.request("GetFileTranslate", req, cb);
    }
    async ImageTranslate(req, cb) {
        return this.request("ImageTranslate", req, cb);
    }
    async TextTranslate(req, cb) {
        return this.request("TextTranslate", req, cb);
    }
    async FileTranslate(req, cb) {
        return this.request("FileTranslate", req, cb);
    }
    async SpeechTranslate(req, cb) {
        return this.request("SpeechTranslate", req, cb);
    }
}
