import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("dnspod.tencentcloudapi.com", "2021-03-23", clientConfig);
    }
    async DescribeDomainPurview(req, cb) {
        return this.request("DescribeDomainPurview", req, cb);
    }
    async ModifySubdomainStatus(req, cb) {
        return this.request("ModifySubdomainStatus", req, cb);
    }
    async DescribeSubdomainAnalytics(req, cb) {
        return this.request("DescribeSubdomainAnalytics", req, cb);
    }
    async CreateTXTRecord(req, cb) {
        return this.request("CreateTXTRecord", req, cb);
    }
    async CheckSnapshotRollback(req, cb) {
        return this.request("CheckSnapshotRollback", req, cb);
    }
    async ModifyVasAutoRenewStatus(req, cb) {
        return this.request("ModifyVasAutoRenewStatus", req, cb);
    }
    async DescribeDomainWhois(req, cb) {
        return this.request("DescribeDomainWhois", req, cb);
    }
    async CreateDomainBatch(req, cb) {
        return this.request("CreateDomainBatch", req, cb);
    }
    async DescribeDomainShareInfo(req, cb) {
        return this.request("DescribeDomainShareInfo", req, cb);
    }
    async CreateLineGroup(req, cb) {
        return this.request("CreateLineGroup", req, cb);
    }
    async DescribeDomainAndRecordList(req, cb) {
        return this.request("DescribeDomainAndRecordList", req, cb);
    }
    async DescribeDomain(req, cb) {
        return this.request("DescribeDomain", req, cb);
    }
    async DescribeRecordType(req, cb) {
        return this.request("DescribeRecordType", req, cb);
    }
    async ModifyRecordGroup(req, cb) {
        return this.request("ModifyRecordGroup", req, cb);
    }
    async ModifySnapshotConfig(req, cb) {
        return this.request("ModifySnapshotConfig", req, cb);
    }
    async ModifyRecord(req, cb) {
        return this.request("ModifyRecord", req, cb);
    }
    async ModifyDomainOwner(req, cb) {
        return this.request("ModifyDomainOwner", req, cb);
    }
    async DeleteRecordBatch(req, cb) {
        return this.request("DeleteRecordBatch", req, cb);
    }
    async RollbackRecordSnapshot(req, cb) {
        return this.request("RollbackRecordSnapshot", req, cb);
    }
    async DescribeRecordExistExceptDefaultNS(req, cb) {
        return this.request("DescribeRecordExistExceptDefaultNS", req, cb);
    }
    async CreateSnapshot(req, cb) {
        return this.request("CreateSnapshot", req, cb);
    }
    async ModifyRecordFields(req, cb) {
        return this.request("ModifyRecordFields", req, cb);
    }
    async DeleteDomainBatch(req, cb) {
        return this.request("DeleteDomainBatch", req, cb);
    }
    async DescribeSnapshotRollbackResult(req, cb) {
        return this.request("DescribeSnapshotRollbackResult", req, cb);
    }
    async ModifyDomainUnlock(req, cb) {
        return this.request("ModifyDomainUnlock", req, cb);
    }
    async DescribeDomainList(req, cb) {
        return this.request("DescribeDomainList", req, cb);
    }
    async DescribeSnapshotConfig(req, cb) {
        return this.request("DescribeSnapshotConfig", req, cb);
    }
    async DescribeUserDetail(req, cb) {
        return this.request("DescribeUserDetail", req, cb);
    }
    async ModifyDomainStatus(req, cb) {
        return this.request("ModifyDomainStatus", req, cb);
    }
    async DescribeDomainAnalytics(req, cb) {
        return this.request("DescribeDomainAnalytics", req, cb);
    }
    async ModifyPackageAutoRenew(req, cb) {
        return this.request("ModifyPackageAutoRenew", req, cb);
    }
    async CreateRecord(req, cb) {
        return this.request("CreateRecord", req, cb);
    }
    async DescribeRecordLineCategoryList(req, cb) {
        return this.request("DescribeRecordLineCategoryList", req, cb);
    }
    async DescribeDomainLogList(req, cb) {
        return this.request("DescribeDomainLogList", req, cb);
    }
    async CreateDomainGroup(req, cb) {
        return this.request("CreateDomainGroup", req, cb);
    }
    async DescribeDomainPreview(req, cb) {
        return this.request("DescribeDomainPreview", req, cb);
    }
    async ModifyRecordToGroup(req, cb) {
        return this.request("ModifyRecordToGroup", req, cb);
    }
    async ModifyTXTRecord(req, cb) {
        return this.request("ModifyTXTRecord", req, cb);
    }
    async DeleteDomain(req, cb) {
        return this.request("DeleteDomain", req, cb);
    }
    async DescribeDomainVipList(req, cb) {
        return this.request("DescribeDomainVipList", req, cb);
    }
    async DescribeDomainGroupList(req, cb) {
        return this.request("DescribeDomainGroupList", req, cb);
    }
    async DeleteDomainCustomLine(req, cb) {
        return this.request("DeleteDomainCustomLine", req, cb);
    }
    async DescribeDomainAliasList(req, cb) {
        return this.request("DescribeDomainAliasList", req, cb);
    }
    async DescribeSnapshotList(req, cb) {
        return this.request("DescribeSnapshotList", req, cb);
    }
    async CreateSubDomainsAnalyticsFile(req, cb) {
        return this.request("CreateSubDomainsAnalyticsFile", req, cb);
    }
    async DescribeVasList(req, cb) {
        return this.request("DescribeVasList", req, cb);
    }
    async DeleteSnapshot(req, cb) {
        return this.request("DeleteSnapshot", req, cb);
    }
    async CreateDeal(req, cb) {
        return this.request("CreateDeal", req, cb);
    }
    async ModifyDomainToGroup(req, cb) {
        return this.request("ModifyDomainToGroup", req, cb);
    }
    async CreateDomain(req, cb) {
        return this.request("CreateDomain", req, cb);
    }
    async ModifyDomainLock(req, cb) {
        return this.request("ModifyDomainLock", req, cb);
    }
    async RollbackSnapshot(req, cb) {
        return this.request("RollbackSnapshot", req, cb);
    }
    async DescribeRecordGroupList(req, cb) {
        return this.request("DescribeRecordGroupList", req, cb);
    }
    async DescribeRecordLineList(req, cb) {
        return this.request("DescribeRecordLineList", req, cb);
    }
    async ModifyRecordRemark(req, cb) {
        return this.request("ModifyRecordRemark", req, cb);
    }
    async DescribeLineGroupList(req, cb) {
        return this.request("DescribeLineGroupList", req, cb);
    }
    async ModifyLineGroup(req, cb) {
        return this.request("ModifyLineGroup", req, cb);
    }
    async CreateLineGroupCopy(req, cb) {
        return this.request("CreateLineGroupCopy", req, cb);
    }
    async CheckRecordSnapshotRollback(req, cb) {
        return this.request("CheckRecordSnapshotRollback", req, cb);
    }
    async PayOrderWithBalance(req, cb) {
        return this.request("PayOrderWithBalance", req, cb);
    }
    async DescribeFileInfoByJobId(req, cb) {
        return this.request("DescribeFileInfoByJobId", req, cb);
    }
    async DescribePackageDetail(req, cb) {
        return this.request("DescribePackageDetail", req, cb);
    }
    async DescribeRecordFilterList(req, cb) {
        return this.request("DescribeRecordFilterList", req, cb);
    }
    async ModifyRecordBatch(req, cb) {
        return this.request("ModifyRecordBatch", req, cb);
    }
    async DescribeRecordSnapshotRollbackResult(req, cb) {
        return this.request("DescribeRecordSnapshotRollbackResult", req, cb);
    }
    async CreateSubdomainValidateTXTValue(req, cb) {
        return this.request("CreateSubdomainValidateTXTValue", req, cb);
    }
    async DescribeRecordList(req, cb) {
        return this.request("DescribeRecordList", req, cb);
    }
    async DescribeSnapshotRollbackTask(req, cb) {
        return this.request("DescribeSnapshotRollbackTask", req, cb);
    }
    async DeleteRecord(req, cb) {
        return this.request("DeleteRecord", req, cb);
    }
    async DeleteRecordGroup(req, cb) {
        return this.request("DeleteRecordGroup", req, cb);
    }
    async DownloadSnapshot(req, cb) {
        return this.request("DownloadSnapshot", req, cb);
    }
    async ModifyRecordStatus(req, cb) {
        return this.request("ModifyRecordStatus", req, cb);
    }
    async ModifyDomainCustomLine(req, cb) {
        return this.request("ModifyDomainCustomLine", req, cb);
    }
    async DescribeVASStatistic(req, cb) {
        return this.request("DescribeVASStatistic", req, cb);
    }
    async DeleteShareDomain(req, cb) {
        return this.request("DeleteShareDomain", req, cb);
    }
    async DeleteLineGroup(req, cb) {
        return this.request("DeleteLineGroup", req, cb);
    }
    async CreateRecordGroup(req, cb) {
        return this.request("CreateRecordGroup", req, cb);
    }
    async ModifyDynamicDNS(req, cb) {
        return this.request("ModifyDynamicDNS", req, cb);
    }
    async CreateRecordBatch(req, cb) {
        return this.request("CreateRecordBatch", req, cb);
    }
    async CreateDomainCustomLine(req, cb) {
        return this.request("CreateDomainCustomLine", req, cb);
    }
    async CreateDomainsAnalyticsFile(req, cb) {
        return this.request("CreateDomainsAnalyticsFile", req, cb);
    }
    async DescribeRecord(req, cb) {
        return this.request("DescribeRecord", req, cb);
    }
    async DescribeDomainShareUserList(req, cb) {
        return this.request("DescribeDomainShareUserList", req, cb);
    }
    async ModifyDomainRemark(req, cb) {
        return this.request("ModifyDomainRemark", req, cb);
    }
    async DescribeResolveCount(req, cb) {
        return this.request("DescribeResolveCount", req, cb);
    }
    async DescribeSubdomainValidateStatus(req, cb) {
        return this.request("DescribeSubdomainValidateStatus", req, cb);
    }
    async DeleteDomainAlias(req, cb) {
        return this.request("DeleteDomainAlias", req, cb);
    }
    async DescribeBatchTask(req, cb) {
        return this.request("DescribeBatchTask", req, cb);
    }
    async DescribeDomainCustomLineList(req, cb) {
        return this.request("DescribeDomainCustomLineList", req, cb);
    }
    async DescribeDomainFilterList(req, cb) {
        return this.request("DescribeDomainFilterList", req, cb);
    }
    async CreateDomainAlias(req, cb) {
        return this.request("CreateDomainAlias", req, cb);
    }
}
