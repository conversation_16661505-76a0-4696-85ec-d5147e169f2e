'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var divider$1 = require('./src/divider2.js');
var divider = require('./src/divider.js');
var install = require('../../utils/vue/install.js');

const ElDivider = install.withInstall(divider$1["default"]);

exports.dividerProps = divider.dividerProps;
exports.ElDivider = ElDivider;
exports["default"] = ElDivider;
//# sourceMappingURL=index.js.map
