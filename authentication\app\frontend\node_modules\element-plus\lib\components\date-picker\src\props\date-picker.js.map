{"version": 3, "file": "date-picker.js", "sources": ["../../../../../../../packages/components/date-picker/src/props/date-picker.ts"], "sourcesContent": ["import { timePickerDefaultProps } from '@element-plus/components/time-picker'\nimport { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { IDatePickerType } from '../date-picker.type'\n\nexport const datePickerProps = buildProps({\n  ...timePickerDefaultProps,\n  /**\n   * @description type of the picker\n   */\n  type: {\n    type: definePropType<IDatePickerType>(String),\n    default: 'date',\n  },\n} as const)\n\nexport type DatePickerProps = ExtractPropTypes<typeof datePickerProps>\nexport type DatePickerPropsPublic = __ExtractPublicPropTypes<\n  typeof datePickerProps\n>\n"], "names": ["buildProps", "timePickerDefaultProps", "definePropType"], "mappings": ";;;;;;;AAEY,MAAC,eAAe,GAAGA,kBAAU,CAAC;AAC1C,EAAE,GAAGC,4BAAsB;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,CAAC;;;;"}