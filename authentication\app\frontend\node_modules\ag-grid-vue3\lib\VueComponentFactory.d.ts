export declare class VueComponentFactory {
    private static getComponentDefinition;
    private static addParamsToProps;
    static createAndMountComponent(component: any, params: any, parent: any, provides: any): {
        componentInstance: any;
        element: any;
        destroy: () => void;
    } | undefined;
    static mount(component: any, props: any, parent: any, provides: any): {
        vNode: any;
        destroy: () => void;
        el: any;
    };
    static searchForComponentInstance(parent: any, component: any, maxDepth?: number, suppressError?: boolean): any;
}
