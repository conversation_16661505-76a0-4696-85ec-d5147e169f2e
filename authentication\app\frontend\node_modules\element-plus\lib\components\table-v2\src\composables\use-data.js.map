{"version": 3, "file": "use-data.js", "sources": ["../../../../../../../packages/components/table-v2/src/composables/use-data.ts"], "sourcesContent": ["import { computed, ref, unref, watch } from 'vue'\nimport { isArray } from '@element-plus/utils'\n\nimport type { TableV2Props } from '../table'\nimport type { KeyType } from '../types'\nimport type { UseRowReturn } from './use-row'\n\ntype UseDataProps = {\n  expandedRowKeys: UseRowReturn['expandedRowKeys']\n  lastRenderedRowIndex: UseRowReturn['lastRenderedRowIndex']\n  resetAfterIndex: UseRowReturn['resetAfterIndex']\n}\n\nexport const useData = (\n  props: TableV2Props,\n  { expandedRowKeys, lastRenderedRowIndex, resetAfterIndex }: UseDataProps\n) => {\n  const depthMap = ref<Record<KeyType, number>>({})\n\n  const flattenedData = computed(() => {\n    const depths: Record<KeyType, number> = {}\n    const { data, rowKey } = props\n\n    const _expandedRowKeys = unref(expandedRowKeys)\n\n    if (!_expandedRowKeys || !_expandedRowKeys.length) return data\n\n    const array: any[] = []\n    const keysSet = new Set()\n    _expandedRowKeys.forEach((x) => keysSet.add(x))\n\n    let copy: any[] = data.slice()\n    copy.forEach((x) => (depths[x[rowKey]] = 0))\n    while (copy.length > 0) {\n      const item = copy.shift()!\n\n      array.push(item)\n      if (\n        keysSet.has(item[rowKey]) &&\n        isArray(item.children) &&\n        item.children.length > 0\n      ) {\n        copy = [...item.children, ...copy]\n        item.children.forEach(\n          (child: any) => (depths[child[rowKey]] = depths[item[rowKey]] + 1)\n        )\n      }\n    }\n\n    depthMap.value = depths\n    return array\n  })\n\n  const data = computed(() => {\n    const { data, expandColumnKey } = props\n    return expandColumnKey ? unref(flattenedData) : data\n  })\n\n  watch(data, (val, prev) => {\n    if (val !== prev) {\n      lastRenderedRowIndex.value = -1\n      resetAfterIndex(0, true)\n    }\n  })\n\n  return {\n    data,\n    depthMap,\n  }\n}\n\nexport type UseDataReturn = ReturnType<typeof useData>\n"], "names": ["ref", "computed", "unref", "isArray", "watch"], "mappings": ";;;;;;;AAEY,MAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE,eAAe,EAAE,oBAAoB,EAAE,eAAe,EAAE,KAAK;AAC9F,EAAE,MAAM,QAAQ,GAAGA,OAAG,CAAC,EAAE,CAAC,CAAC;AAC3B,EAAE,MAAM,aAAa,GAAGC,YAAQ,CAAC,MAAM;AACvC,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;AACtB,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AAC1C,IAAI,MAAM,gBAAgB,GAAGC,SAAK,CAAC,eAAe,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,MAAM;AACrD,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;AACrB,IAAI,MAAM,OAAO,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC9C,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/C,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;AAChC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvB,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAIC,cAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3F,QAAQ,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAC;AAC3C,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3F,OAAO;AACP,KAAK;AACL,IAAI,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC;AAC5B,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,IAAI,GAAGF,YAAQ,CAAC,MAAM;AAC9B,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,KAAK,CAAC;AACnD,IAAI,OAAO,eAAe,GAAGC,SAAK,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC;AAC1D,GAAG,CAAC,CAAC;AACL,EAAEE,SAAK,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,IAAI,KAAK;AAC7B,IAAI,IAAI,GAAG,KAAK,IAAI,EAAE;AACtB,MAAM,oBAAoB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACtC,MAAM,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,GAAG,CAAC;AACJ;;;;"}