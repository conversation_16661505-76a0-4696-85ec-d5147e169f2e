import { Column, RowHeightCallbackParams, XmlElement } from "ag-grid-community";
import { ExcelCalculatedImage } from "./excelInterfaces";
export declare const pixelsToPoint: (pixels: number) => number;
export declare const pointsToPixel: (points: number) => number;
export declare const pixelsToEMU: (value: number) => number;
export declare const getFontFamilyId: (name?: string | undefined) => number | undefined;
export declare const getHeightFromProperty: (rowIndex: number, height?: number | ((params: RowHeightCallbackParams) => number) | undefined) => number | undefined;
export declare const setExcelImageTotalWidth: (image: ExcelCalculatedImage, columnsToExport: Column[]) => void;
export declare const setExcelImageTotalHeight: (image: ExcelCalculatedImage, rowHeight?: number | ((params: RowHeightCallbackParams) => number) | undefined) => void;
export declare const createXmlPart: (body: XmlElement) => string;
export declare const getExcelColumnName: (colIdx: number) => string;
