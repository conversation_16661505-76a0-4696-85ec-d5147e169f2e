{"version": 3, "file": "root.js", "sources": ["../../../../../../packages/components/tooltip-v2/src/root.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\ntype StateUpdater = (state: boolean) => void\n\nexport const tooltipV2RootProps = buildProps({\n  delayDuration: {\n    type: Number,\n    default: 300,\n  },\n  defaultOpen: Boolean,\n  open: {\n    type: Boolean,\n    default: undefined,\n  },\n  onOpenChange: {\n    type: definePropType<StateUpdater>(Function),\n  },\n  'onUpdate:open': {\n    type: definePropType<StateUpdater>(Function),\n  },\n} as const)\n\nexport type TooltipV2RootProps = ExtractPropTypes<typeof tooltipV2RootProps>\nexport type TooltipV2RootPropsPublic = __ExtractPublicPropTypes<\n  typeof tooltipV2RootProps\n>\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,kBAAkB,GAAGA,kBAAU,CAAC;AAC7C,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,WAAW,EAAE,OAAO;AACtB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEC,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,CAAC;;;;"}