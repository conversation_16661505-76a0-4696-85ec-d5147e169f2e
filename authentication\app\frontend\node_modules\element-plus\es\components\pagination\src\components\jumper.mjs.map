{"version": 3, "file": "jumper.mjs", "sources": ["../../../../../../../packages/components/pagination/src/components/jumper.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Jumper from './jumper.vue'\n\nexport const paginationJumperProps = buildProps({\n  size: {\n    type: String,\n    values: componentSizes,\n  },\n} as const)\n\nexport type PaginationJumperProps = ExtractPropTypes<\n  typeof paginationJumperProps\n>\nexport type PaginationJumperPropsPublic = __ExtractPublicPropTypes<\n  typeof paginationJumperProps\n>\n\nexport type PaginationJumperInstance = InstanceType<typeof Jumper> & unknown\n"], "names": [], "mappings": ";;;AAEY,MAAC,qBAAqB,GAAG,UAAU,CAAC;AAChD,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,cAAc;AAC1B,GAAG;AACH,CAAC;;;;"}