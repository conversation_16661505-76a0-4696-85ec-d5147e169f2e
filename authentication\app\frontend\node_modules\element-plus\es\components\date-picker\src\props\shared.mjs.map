{"version": 3, "file": "shared.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/props/shared.ts"], "sourcesContent": ["import { buildProps, definePropType, isArray } from '@element-plus/utils'\nimport { datePickTypes } from '@element-plus/constants'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { DatePickType } from '@element-plus/constants'\n\nconst selectionModes = [\n  'date',\n  'dates',\n  'year',\n  'years',\n  'month',\n  'months',\n  'week',\n  'range',\n]\n\nexport type RangeState = {\n  endDate: null | Dayjs\n  selecting: boolean\n}\n\nexport type DisabledDateType = (date: Date) => boolean\n\nexport const datePickerSharedProps = buildProps({\n  disabledDate: {\n    type: definePropType<DisabledDateType>(Function),\n  },\n  date: {\n    type: definePropType<Dayjs>(Object),\n    required: true,\n  },\n  minDate: {\n    type: definePropType<Dayjs | null>(Object),\n  },\n  maxDate: {\n    type: definePropType<Dayjs | null>(Object),\n  },\n  parsedValue: {\n    type: definePropType<Dayjs | Dayjs[]>([Object, Array]),\n  },\n  rangeState: {\n    type: definePropType<RangeState>(Object),\n    default: () => ({\n      endDate: null,\n      selecting: false,\n    }),\n  },\n} as const)\n\nexport const panelSharedProps = buildProps({\n  type: {\n    type: definePropType<DatePickType>(String),\n    required: true,\n    values: datePickTypes,\n  },\n  dateFormat: String,\n  timeFormat: String,\n  showNow: {\n    type: Boolean,\n    default: true,\n  },\n  showFooter: {\n    type: Boolean,\n    default: true,\n  },\n  showWeekNumber: Boolean,\n} as const)\n\nexport const panelRangeSharedProps = buildProps({\n  unlinkPanels: Boolean,\n  visible: Boolean,\n  showFooter: {\n    type: Boolean,\n    default: true,\n  },\n  parsedValue: {\n    type: definePropType<Dayjs[]>(Array),\n  },\n} as const)\n\nexport const selectionModeWithDefault = (\n  mode: typeof selectionModes[number]\n) => {\n  return {\n    type: String,\n    values: selectionModes,\n    default: mode,\n  }\n}\n\nexport const rangePickerSharedEmits = {\n  pick: (range: [Dayjs, Dayjs]) => isArray(range),\n}\n\nexport type RangePickerSharedEmits = typeof rangePickerSharedEmits\nexport type PanelRangeSharedProps = ExtractPropTypes<\n  typeof panelRangeSharedProps\n>\nexport type PanelRangeSharedPropsPublic = __ExtractPublicPropTypes<\n  typeof panelRangeSharedProps\n>\n"], "names": [], "mappings": ";;;;AAEA,MAAM,cAAc,GAAG;AACvB,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,OAAO;AACT,CAAC,CAAC;AACU,MAAC,qBAAqB,GAAG,UAAU,CAAC;AAChD,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,SAAS,EAAE,KAAK;AACtB,KAAK,CAAC;AACN,GAAG;AACH,CAAC,EAAE;AACS,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,MAAM,EAAE,aAAa;AACzB,GAAG;AACH,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,cAAc,EAAE,OAAO;AACzB,CAAC,EAAE;AACS,MAAC,qBAAqB,GAAG,UAAU,CAAC;AAChD,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,CAAC,EAAE;AACS,MAAC,wBAAwB,GAAG,CAAC,IAAI,KAAK;AAClD,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,cAAc;AAC1B,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,sBAAsB,GAAG;AACtC,EAAE,IAAI,EAAE,CAAC,KAAK,KAAK,OAAO,CAAC,KAAK,CAAC;AACjC;;;;"}