# 前端生产环境变量（Vite 只会注入以 VITE_ 开头的变量到浏览器）
# 生产环境建议前端静态资源由 Nginx 提供，并通过 /api 反向代理到后端

# 应用信息
VITE_APP_NAME=北航QQ身份认证系统
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=北京航空航天大学QQ身份认证系统

# API 配置（生产下通常同域反代 /api 到后端服务）
VITE_API_BASE_URL=/api
VITE_API_TIMEOUT=20000

# 认证配置
VITE_JWT_STORAGE_KEY=buaa_auth_token
VITE_REFRESH_TOKEN_KEY=buaa_refresh_token
VITE_TOKEN_EXPIRE_TIME=3600

# 功能开关
VITE_ENABLE_REGISTRATION=false
VITE_ENABLE_SSO_AUTH=true
VITE_ENABLE_FRESHMAN_AUTH=true
VITE_ENABLE_EXTERNAL_AUTH=true
VITE_ENABLE_INVITE_AUTH=true

# 文件上传配置
VITE_MAX_FILE_SIZE=16777216
VITE_ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,webp
VITE_UPLOAD_ENDPOINT=/api/upload

# 验证码/冷却时间
VITE_CAPTCHA_ENABLED=true
VITE_SMS_COOLDOWN=60
VITE_EMAIL_COOLDOWN=60

# 安全配置
VITE_CSRF_ENABLED=true
VITE_RATE_LIMIT_ENABLED=true
VITE_SESSION_TIMEOUT=3600

# 主题/外观
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true
VITE_THEME_STORAGE_KEY=buaa_theme

# 语言
VITE_DEFAULT_LOCALE=zh-CN
VITE_SUPPORTED_LOCALES=zh-CN,en

# 分析与监控（生产可按需开启）
VITE_ENABLE_ANALYTICS=false
VITE_GOOGLE_ANALYTICS_ID=
VITE_SENTRY_DSN=

# 调试/日志（生产保持收敛）
VITE_ENABLE_DEBUG=false
VITE_LOG_LEVEL=info

# 外部服务
VITE_QQ_CONNECT_APP_ID=
VITE_SSO_URL=https://sso.example.edu.cn

# 资源/CDN（若使用CDN，设置为完整URL前缀；否则留空）
VITE_CDN_BASE_URL=
VITE_STATIC_BASE_URL=

# 缓存
VITE_CACHE_ENABLED=true
VITE_CACHE_TTL=300

# 错误处理
VITE_ERROR_REPORTING_ENABLED=true
VITE_SHOW_ERROR_DETAILS=false

# 性能
VITE_ENABLE_SW=true
VITE_ENABLE_PREFETCH=true

# 社交/站点
VITE_ENABLE_SOCIAL_SHARE=false
VITE_SITE_URL=https://auth.buaa.edu.cn

# 联系/法律
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_SUPPORT_PHONE=010-82317114
VITE_PRIVACY_POLICY_URL=/privacy
VITE_TERMS_OF_SERVICE_URL=/terms
VITE_COPYRIGHT_YEAR=2025
VITE_COPYRIGHT_HOLDER=北京航空航天大学
