import { defineComponent, onBeforeUnmount, openBlock, createElementBlock, unref, normalizeClass, createElementVNode, createCommentVNode, Fragment, renderList, toDisplayString, createVNode } from 'vue';
import { basicDateTableProps, basicDateTableEmits } from '../props/basic-date-table.mjs';
import { useBasicDateTable, useBasicDateTableDOM } from '../composables/use-basic-date-table.mjs';
import ElDatePickerCell from './basic-cell-render.mjs';
import _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';

const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "basic-date-table",
  props: basicDateTableProps,
  emits: basicDateTableEmits,
  setup(__props, { expose, emit }) {
    const props = __props;
    const {
      WEEKS,
      rows,
      tbodyRef,
      currentCellRef,
      focus,
      isCurrent,
      isWeekActive,
      isSelectedCell,
      handlePickDate,
      handleMouseUp,
      handleMouseDown,
      handleMouseMove,
      handleFocus
    } = useBasicDateTable(props, emit);
    const { tableLabel, tableKls, getCellClasses, getRowKls, weekHeaderClass, t } = useBasicDateTableDOM(props, {
      isCurrent,
      isWeekActive
    });
    let isUnmounting = false;
    onBeforeUnmount(() => {
      isUnmounting = true;
    });
    expose({
      focus
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("table", {
        "aria-label": unref(tableLabel),
        class: normalizeClass(unref(tableKls)),
        cellspacing: "0",
        cellpadding: "0",
        role: "grid",
        onClick: unref(handlePickDate),
        onMousemove: unref(handleMouseMove),
        onMousedown: unref(handleMouseDown),
        onMouseup: unref(handleMouseUp)
      }, [
        createElementVNode("tbody", {
          ref_key: "tbodyRef",
          ref: tbodyRef
        }, [
          createElementVNode("tr", null, [
            _ctx.showWeekNumber ? (openBlock(), createElementBlock("th", {
              key: 0,
              scope: "col",
              class: normalizeClass(unref(weekHeaderClass))
            }, null, 2)) : createCommentVNode("v-if", true),
            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(WEEKS), (week, key) => {
              return openBlock(), createElementBlock("th", {
                key,
                "aria-label": unref(t)("el.datepicker.weeksFull." + week),
                scope: "col"
              }, toDisplayString(unref(t)("el.datepicker.weeks." + week)), 9, ["aria-label"]);
            }), 128))
          ]),
          (openBlock(true), createElementBlock(Fragment, null, renderList(unref(rows), (row, rowKey) => {
            return openBlock(), createElementBlock("tr", {
              key: rowKey,
              class: normalizeClass(unref(getRowKls)(row[1]))
            }, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(row, (cell, columnKey) => {
                return openBlock(), createElementBlock("td", {
                  key: `${rowKey}.${columnKey}`,
                  ref_for: true,
                  ref: (el) => !unref(isUnmounting) && unref(isSelectedCell)(cell) && (currentCellRef.value = el),
                  class: normalizeClass(unref(getCellClasses)(cell)),
                  "aria-current": cell.isCurrent ? "date" : void 0,
                  "aria-selected": cell.isCurrent,
                  tabindex: unref(isSelectedCell)(cell) ? 0 : -1,
                  onFocus: unref(handleFocus)
                }, [
                  createVNode(unref(ElDatePickerCell), { cell }, null, 8, ["cell"])
                ], 42, ["aria-current", "aria-selected", "tabindex", "onFocus"]);
              }), 128))
            ], 2);
          }), 128))
        ], 512)
      ], 42, ["aria-label", "onClick", "onMousemove", "onMousedown", "onMouseup"]);
    };
  }
});
var DateTable = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "basic-date-table.vue"]]);

export { DateTable as default };
//# sourceMappingURL=basic-date-table.mjs.map
