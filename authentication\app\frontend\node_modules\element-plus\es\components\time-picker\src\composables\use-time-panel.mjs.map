{"version": 3, "file": "use-time-panel.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/composables/use-time-panel.ts"], "sourcesContent": ["import type { Dayjs } from 'dayjs'\nimport type {\n  GetDisabledHours,\n  GetDisabledMinutes,\n  GetDisabledSeconds,\n} from '../common/props'\n\ntype UseTimePanelProps = {\n  getAvailableHours: GetDisabledHours\n  getAvailableMinutes: GetDisabledMinutes\n  getAvailableSeconds: GetDisabledSeconds\n}\n\nexport const useTimePanel = ({\n  getAvailableHours,\n  getAvailableMinutes,\n  getAvailableSeconds,\n}: UseTimePanelProps) => {\n  const getAvailableTime = (\n    date: Dayjs,\n    role: string,\n    first: boolean,\n    compareDate?: Dayjs\n  ) => {\n    const availableTimeGetters = {\n      hour: getAvailableHours,\n      minute: getAvailableMinutes,\n      second: getAvailableSeconds,\n    } as const\n    let result = date\n    ;(['hour', 'minute', 'second'] as const).forEach((type) => {\n      if (availableTimeGetters[type]) {\n        let availableTimeSlots: number[]\n        const method = availableTimeGetters[type]\n        switch (type) {\n          case 'minute': {\n            availableTimeSlots = (method as typeof getAvailableMinutes)(\n              result.hour(),\n              role,\n              compareDate\n            )\n            break\n          }\n          case 'second': {\n            availableTimeSlots = (method as typeof getAvailableSeconds)(\n              result.hour(),\n              result.minute(),\n              role,\n              compareDate\n            )\n            break\n          }\n          default: {\n            availableTimeSlots = (method as typeof getAvailableHours)(\n              role,\n              compareDate\n            )\n            break\n          }\n        }\n\n        if (\n          availableTimeSlots?.length &&\n          !availableTimeSlots.includes(result[type]())\n        ) {\n          const pos = first ? 0 : availableTimeSlots.length - 1\n          result = result[type](availableTimeSlots[pos]) as unknown as Dayjs\n        }\n      }\n    })\n    return result\n  }\n\n  const timePickerOptions: Record<string, (...args: any[]) => void> = {}\n\n  const onSetOption = ([key, val]: [string, (...args: any[]) => void]) => {\n    timePickerOptions[key] = val\n  }\n\n  return {\n    timePickerOptions,\n\n    getAvailableTime,\n    onSetOption,\n  }\n}\n"], "names": [], "mappings": "AAAY,MAAC,YAAY,GAAG,CAAC;AAC7B,EAAE,iBAAiB;AACnB,EAAE,mBAAmB;AACrB,EAAE,mBAAmB;AACrB,CAAC,KAAK;AACN,EAAE,MAAM,gBAAgB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,KAAK;AAC/D,IAAI,MAAM,oBAAoB,GAAG;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,MAAM,EAAE,mBAAmB;AACjC,MAAM,MAAM,EAAE,mBAAmB;AACjC,KAAK,CAAC;AACN,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC;AACtB,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACnD,MAAM,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE;AACtC,QAAQ,IAAI,kBAAkB,CAAC;AAC/B,QAAQ,MAAM,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAClD,QAAQ,QAAQ,IAAI;AACpB,UAAU,KAAK,QAAQ,EAAE;AACzB,YAAY,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;AAC1E,YAAY,MAAM;AAClB,WAAW;AACX,UAAU,KAAK,QAAQ,EAAE;AACzB,YAAY,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;AAC3F,YAAY,MAAM;AAClB,WAAW;AACX,UAAU,SAAS;AACnB,YAAY,kBAAkB,GAAG,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAC3D,YAAY,MAAM;AAClB,WAAW;AACX,SAAS;AACT,QAAQ,IAAI,CAAC,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,kBAAkB,CAAC,MAAM,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC/H,UAAU,MAAM,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;AAChE,UAAU,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;AACzD,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,EAAE,CAAC;AAC/B,EAAE,MAAM,WAAW,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK;AACtC,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AACjC,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}