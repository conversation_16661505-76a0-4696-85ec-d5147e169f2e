# BuaaBykcCrawler
北航博雅课程 Python + requests 爬虫接口。

提供最基本的登录，查询，选课，退选接口。

你可以在此之上开发自己的程序。

2022年9月6日更新，适配了新的接口协议。

2022年3月3日更新，适配了新的加密模式。

# Usage(demo)

1. 安装 python

2. 安装依赖包，在终端输入以下命令，如果出现报错请咨询你的计算机专业的朋友。

```
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

3. 输入以下指令，运行bykc.py。第一次运行会生成一个config.json并直接退出。

```
python3 main.py
```

4. 填写config.json中的username和password字段。

5. 重新运行main.py。
