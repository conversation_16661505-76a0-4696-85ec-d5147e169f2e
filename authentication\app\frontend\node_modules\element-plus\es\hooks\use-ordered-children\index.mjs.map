{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-ordered-children/index.ts"], "sourcesContent": ["import {\n  defineComponent,\n  h,\n  isVNode,\n  onMounted,\n  shallowRef,\n  triggerRef,\n} from 'vue'\nimport { flattedChildren } from '@element-plus/utils'\n\nimport type { Component, ComponentInternalInstance, VNode } from 'vue'\n\ntype ChildEssential = {\n  uid: number\n  getVnode: () => VNode\n}\n\nconst getOrderedChildren = <T>(\n  vm: ComponentInternalInstance,\n  childComponentName: string,\n  children: Record<number, T>\n): T[] => {\n  const nodes = flattedChildren(vm.subTree).filter(\n    (n): n is VNode =>\n      isVNode(n) &&\n      (n.type as Component)?.name === childComponentName &&\n      !!n.component\n  )\n  const uids = nodes.map((n) => n.component!.uid)\n  return uids.map((uid) => children[uid]).filter((p) => !!p)\n}\n\nexport const useOrderedChildren = <T extends ChildEssential>(\n  vm: ComponentInternalInstance,\n  childComponentName: string\n) => {\n  const children = shallowRef<Record<number, T>>({})\n  const orderedChildren = shallowRef<T[]>([])\n  const nodesMap = new WeakMap<ParentNode, Node[]>()\n\n  const addChild = (child: T) => {\n    children.value[child.uid] = child\n    triggerRef(children)\n\n    onMounted(() => {\n      const childNode = child.getVnode().el! as Node\n      const parentNode = childNode.parentNode!\n\n      if (!nodesMap.has(parentNode)) {\n        nodesMap.set(parentNode, [])\n\n        const originalFn = parentNode.insertBefore.bind(parentNode)\n        parentNode.insertBefore = <T extends Node>(\n          node: T,\n          anchor: Node | null\n        ) => {\n          // Schedule a job to update `orderedChildren` if the root element of child components is moved\n          const shouldSortChildren = nodesMap\n            .get(parentNode)!\n            .some((el) => node === el || anchor === el)\n          if (shouldSortChildren) triggerRef(children)\n\n          return originalFn(node, anchor)\n        }\n      }\n\n      nodesMap.get(parentNode)!.push(childNode)\n    })\n  }\n\n  const removeChild = (child: T) => {\n    delete children.value[child.uid]\n    triggerRef(children)\n\n    const childNode = child.getVnode().el! as Node\n    const parentNode = childNode.parentNode!\n    const childNodes = nodesMap.get(parentNode)!\n    const index = childNodes.indexOf(childNode)\n    childNodes.splice(index, 1)\n  }\n\n  const sortChildren = () => {\n    orderedChildren.value = getOrderedChildren(\n      vm,\n      childComponentName,\n      children.value\n    )\n  }\n\n  const IsolatedRenderer = (props: { render: () => VNode[] }) => {\n    return props.render()\n  }\n\n  // TODO: Refactor `el-description` before converting this to a functional component\n  const ChildrenSorter = defineComponent({\n    setup(_, { slots }) {\n      return () => {\n        sortChildren()\n\n        return slots.default\n          ? // Create a new `ReactiveEffect` to ensure `ChildrenSorter` doesn't track any extra dependencies\n            // @ts-ignore TODO: Remove this after Vue is upgraded\n            h(IsolatedRenderer, {\n              render: slots.default,\n            })\n          : null\n      }\n    },\n  })\n\n  return {\n    children: orderedChildren,\n    addChild,\n    removeChild,\n    ChildrenSorter,\n  }\n}\n"], "names": [], "mappings": ";;;AASA,MAAM,kBAAkB,GAAG,CAAC,EAAE,EAAE,kBAAkB,EAAE,QAAQ,KAAK;AACjE,EAAE,MAAM,KAAK,GAAG,eAAe,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK;AAC1D,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,MAAM,kBAAkB,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC5G,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACjD,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC;AACU,MAAC,kBAAkB,GAAG,CAAC,EAAE,EAAE,kBAAkB,KAAK;AAC9D,EAAE,MAAM,QAAQ,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;AAClC,EAAE,MAAM,eAAe,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;AACzC,EAAE,MAAM,QAAQ,mBAAmB,IAAI,OAAO,EAAE,CAAC;AACjD,EAAE,MAAM,QAAQ,GAAG,CAAC,KAAK,KAAK;AAC9B,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACtC,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;AACzB,IAAI,SAAS,CAAC,MAAM;AACpB,MAAM,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC5C,MAAM,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;AAC9C,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AACrC,QAAQ,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AACrC,QAAQ,MAAM,UAAU,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACpE,QAAQ,UAAU,CAAC,YAAY,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK;AACpD,UAAU,MAAM,kBAAkB,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,KAAK,EAAE,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC;AACzG,UAAU,IAAI,kBAAkB;AAChC,YAAY,UAAU,CAAC,QAAQ,CAAC,CAAC;AACjC,UAAU,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC1C,SAAS,CAAC;AACV,OAAO;AACP,MAAM,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/C,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrC,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;AACzB,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC1C,IAAI,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;AAC5C,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAChD,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAChD,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAChC,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM;AAC7B,IAAI,eAAe,CAAC,KAAK,GAAG,kBAAkB,CAAC,EAAE,EAAE,kBAAkB,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvF,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,KAAK,KAAK;AACtC,IAAI,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,eAAe,CAAC;AACzC,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;AACxB,MAAM,OAAO,MAAM;AACnB,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,OAAO,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,gBAAgB,EAAE;AACnD,UAAU,MAAM,EAAE,KAAK,CAAC,OAAO;AAC/B,SAAS,CAAC,GAAG,IAAI,CAAC;AAClB,OAAO,CAAC;AACR,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,QAAQ,EAAE,eAAe;AAC7B,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}