declare type ValidationContext = {
    target: any;
};
export declare type ValidatePredicate = {
    (v: any, ctx: ValidationContext): boolean;
    message?: string;
};
export declare function Validate(predicate: ValidatePredicate): PropertyDecorator;
export declare function predicateWithMessage(predicate: ValidatePredicate, message: string): ValidatePredicate;
export declare const OPTIONAL: (v: any, ctx: ValidationContext, predicate: ValidatePredicate) => boolean;
export declare const ARRAY: (length?: number | undefined, predicate?: ValidatePredicate | undefined) => ValidatePredicate;
export declare const OPT_ARRAY: (length?: number | undefined) => ValidatePredicate;
export declare const AND: (...predicates: ValidatePredicate[]) => ValidatePredicate;
export declare const OR: (...predicates: ValidatePredicate[]) => ValidatePredicate;
export declare const LESS_THAN: (otherField: string) => ValidatePredicate;
export declare const GREATER_THAN: (otherField: string) => ValidatePredicate;
export declare const FUNCTION: ValidatePredicate;
export declare const OPT_FUNCTION: ValidatePredicate;
export declare const BOOLEAN: ValidatePredicate;
export declare const OPT_BOOLEAN: ValidatePredicate;
export declare const STRING: ValidatePredicate;
export declare const OPT_STRING: ValidatePredicate;
export declare const DATE: ValidatePredicate;
export declare const OPT_DATE: ValidatePredicate;
export declare const DATE_ARRAY: ValidatePredicate;
export declare const DATETIME_MS: ValidatePredicate;
export declare const OPT_DATETIME_MS: ValidatePredicate;
export declare const OPT_DATE_OR_DATETIME_MS: ValidatePredicate;
export declare const COLOR_STRING: ValidatePredicate;
export declare const OPT_COLOR_STRING: ValidatePredicate;
export declare const COLOR_STRING_ARRAY: ValidatePredicate;
export declare const OPT_COLOR_STRING_ARRAY: ValidatePredicate;
export declare function NUMBER(min?: number, max?: number): ValidatePredicate;
export declare function OPT_NUMBER(min?: number, max?: number): ValidatePredicate;
export declare function NUMBER_OR_NAN(min?: number, max?: number): ValidatePredicate;
export declare const NUMBER_ARRAY: ValidatePredicate;
export declare const OPT_NUMBER_ARRAY: ValidatePredicate;
export declare const STRING_ARRAY: ValidatePredicate;
export declare const OPT_STRING_ARRAY: ValidatePredicate;
export declare function STRING_UNION(...values: string[]): ValidatePredicate;
export declare const BOOLEAN_ARRAY: ValidatePredicate;
export declare const OPT_BOOLEAN_ARRAY: ValidatePredicate;
export declare const FONT_STYLE: ValidatePredicate;
export declare const OPT_FONT_STYLE: ValidatePredicate;
export declare const FONT_WEIGHT: ValidatePredicate;
export declare const OPT_FONT_WEIGHT: ValidatePredicate;
export declare const LINE_DASH: ValidatePredicate;
export declare const OPT_LINE_DASH: ValidatePredicate;
export declare const LINE_CAP: ValidatePredicate;
export declare const OPT_LINE_CAP: ValidatePredicate;
export declare const LINE_JOIN: ValidatePredicate;
export declare const OPT_LINE_JOIN: ValidatePredicate;
export declare const POSITION: ValidatePredicate;
export declare const INTERACTION_RANGE: ValidatePredicate;
export declare const TEXT_WRAP: ValidatePredicate;
export {};
//# sourceMappingURL=validation.d.ts.map