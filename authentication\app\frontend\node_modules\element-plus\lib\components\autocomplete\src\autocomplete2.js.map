{"version": 3, "file": "autocomplete2.js", "sources": ["../../../../../../packages/components/autocomplete/src/autocomplete.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"popperRef\"\n    :visible=\"suggestionVisible\"\n    :placement=\"placement\"\n    :fallback-placements=\"['bottom-start', 'top-start']\"\n    :popper-class=\"[ns.e('popper'), popperClass]\"\n    :teleported=\"teleported\"\n    :append-to=\"appendTo\"\n    :gpu-acceleration=\"false\"\n    pure\n    manual-mode\n    effect=\"light\"\n    trigger=\"click\"\n    :transition=\"`${ns.namespace.value}-zoom-in-top`\"\n    persistent\n    role=\"listbox\"\n    @before-show=\"onSuggestionShow\"\n    @hide=\"onHide\"\n  >\n    <div\n      ref=\"listboxRef\"\n      :class=\"[ns.b(), $attrs.class]\"\n      :style=\"styles\"\n      role=\"combobox\"\n      aria-haspopup=\"listbox\"\n      :aria-expanded=\"suggestionVisible\"\n      :aria-owns=\"listboxId\"\n    >\n      <el-input\n        ref=\"inputRef\"\n        v-bind=\"mergeProps(passInputProps, $attrs)\"\n        :model-value=\"modelValue\"\n        :disabled=\"disabled\"\n        @input=\"handleInput\"\n        @change=\"handleChange\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n        @clear=\"handleClear\"\n        @keydown.up.prevent=\"highlight(highlightedIndex - 1)\"\n        @keydown.down.prevent=\"highlight(highlightedIndex + 1)\"\n        @keydown.enter=\"handleKeyEnter\"\n        @keydown.tab=\"close\"\n        @keydown.esc=\"handleKeyEscape\"\n        @mousedown=\"handleMouseDown\"\n      >\n        <template v-if=\"$slots.prepend\" #prepend>\n          <slot name=\"prepend\" />\n        </template>\n        <template v-if=\"$slots.append\" #append>\n          <slot name=\"append\" />\n        </template>\n        <template v-if=\"$slots.prefix\" #prefix>\n          <slot name=\"prefix\" />\n        </template>\n        <template v-if=\"$slots.suffix\" #suffix>\n          <slot name=\"suffix\" />\n        </template>\n      </el-input>\n    </div>\n    <template #content>\n      <div\n        ref=\"regionRef\"\n        :class=\"[ns.b('suggestion'), ns.is('loading', suggestionLoading)]\"\n        :style=\"{\n          [fitInputWidth ? 'width' : 'minWidth']: dropdownWidth,\n          outline: 'none',\n        }\"\n        role=\"region\"\n      >\n        <div\n          v-if=\"$slots.header\"\n          :class=\"ns.be('suggestion', 'header')\"\n          @click.stop\n        >\n          <slot name=\"header\" />\n        </div>\n        <el-scrollbar\n          :id=\"listboxId\"\n          tag=\"ul\"\n          :wrap-class=\"ns.be('suggestion', 'wrap')\"\n          :view-class=\"ns.be('suggestion', 'list')\"\n          role=\"listbox\"\n        >\n          <li v-if=\"suggestionLoading\">\n            <slot name=\"loading\">\n              <el-icon :class=\"ns.is('loading')\">\n                <Loading />\n              </el-icon>\n            </slot>\n          </li>\n          <template v-else>\n            <li\n              v-for=\"(item, index) in suggestions\"\n              :id=\"`${listboxId}-item-${index}`\"\n              :key=\"index\"\n              :class=\"{ highlighted: highlightedIndex === index }\"\n              role=\"option\"\n              :aria-selected=\"highlightedIndex === index\"\n              @click=\"handleSelect(item)\"\n            >\n              <slot :item=\"item\">{{ item[valueKey] }}</slot>\n            </li>\n          </template>\n        </el-scrollbar>\n        <div\n          v-if=\"$slots.footer\"\n          :class=\"ns.be('suggestion', 'footer')\"\n          @click.stop\n        >\n          <slot name=\"footer\" />\n        </div>\n      </div>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  mergeProps,\n  onBeforeUnmount,\n  onMounted,\n  ref,\n  useAttrs as useRawAttrs,\n} from 'vue'\nimport { debounce, pick } from 'lodash-unified'\nimport { onClickOutside } from '@vueuse/core'\nimport { Loading } from '@element-plus/icons-vue'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { isArray, throwError } from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport ElInput, { inputProps } from '@element-plus/components/input'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElIcon from '@element-plus/components/icon'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { autocompleteEmits, autocompleteProps } from './autocomplete'\n\nimport type { AutocompleteData } from './autocomplete'\nimport type { StyleValue } from 'vue'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\nimport type { InputInstance } from '@element-plus/components/input'\n\nconst COMPONENT_NAME = 'ElAutocomplete'\ndefineOptions({\n  name: COMPONENT_NAME,\n  inheritAttrs: false,\n})\n\nconst props = defineProps(autocompleteProps)\nconst emit = defineEmits(autocompleteEmits)\n\nconst passInputProps = computed(() => pick(props, Object.keys(inputProps)))\n\nconst rawAttrs = useRawAttrs()\nconst disabled = useFormDisabled()\nconst ns = useNamespace('autocomplete')\n\nconst inputRef = ref<InputInstance>()\nconst regionRef = ref<HTMLElement>()\nconst popperRef = ref<TooltipInstance>()\nconst listboxRef = ref<HTMLElement>()\n\nlet readonly = false\nlet ignoreFocusEvent = false\nconst suggestions = ref<AutocompleteData>([])\nconst highlightedIndex = ref(-1)\nconst dropdownWidth = ref('')\nconst activated = ref(false)\nconst suggestionDisabled = ref(false)\nconst loading = ref(false)\n\nconst listboxId = useId()\nconst styles = computed(() => rawAttrs.style as StyleValue)\n\nconst suggestionVisible = computed(() => {\n  const isValidData = suggestions.value.length > 0\n  return (isValidData || loading.value) && activated.value\n})\n\nconst suggestionLoading = computed(() => !props.hideLoading && loading.value)\n\nconst refInput = computed<HTMLInputElement[]>(() => {\n  if (inputRef.value) {\n    return Array.from<HTMLInputElement>(\n      inputRef.value.$el.querySelectorAll('input')\n    )\n  }\n  return []\n})\n\nconst onSuggestionShow = () => {\n  if (suggestionVisible.value) {\n    dropdownWidth.value = `${inputRef.value!.$el.offsetWidth}px`\n  }\n}\n\nconst onHide = () => {\n  highlightedIndex.value = -1\n}\n\nconst getData = async (queryString: string) => {\n  if (suggestionDisabled.value) return\n\n  const cb = (suggestionList: AutocompleteData) => {\n    loading.value = false\n    if (suggestionDisabled.value) return\n\n    if (isArray(suggestionList)) {\n      suggestions.value = suggestionList\n      highlightedIndex.value = props.highlightFirstItem ? 0 : -1\n    } else {\n      throwError(COMPONENT_NAME, 'autocomplete suggestions must be an array')\n    }\n  }\n\n  loading.value = true\n  if (isArray(props.fetchSuggestions)) {\n    cb(props.fetchSuggestions)\n  } else {\n    const result = await props.fetchSuggestions(queryString, cb)\n    if (isArray(result)) cb(result)\n  }\n}\nconst debouncedGetData = debounce(getData, props.debounce)\n\nconst handleInput = (value: string) => {\n  const valuePresented = !!value\n\n  emit(INPUT_EVENT, value)\n  emit(UPDATE_MODEL_EVENT, value)\n\n  suggestionDisabled.value = false\n  activated.value ||= valuePresented\n\n  if (!props.triggerOnFocus && !value) {\n    suggestionDisabled.value = true\n    suggestions.value = []\n    return\n  }\n\n  debouncedGetData(value)\n}\n\nconst handleMouseDown = (event: MouseEvent) => {\n  if (disabled.value) return\n  if (\n    (event.target as HTMLElement)?.tagName !== 'INPUT' ||\n    refInput.value.includes(document.activeElement as HTMLInputElement)\n  ) {\n    activated.value = true\n  }\n}\n\nconst handleChange = (value: string) => {\n  emit(CHANGE_EVENT, value)\n}\n\nconst handleFocus = (evt: FocusEvent) => {\n  if (!ignoreFocusEvent) {\n    activated.value = true\n    emit('focus', evt)\n    const queryString = props.modelValue ?? ''\n    if (props.triggerOnFocus && !readonly) {\n      debouncedGetData(String(queryString))\n    }\n  } else {\n    ignoreFocusEvent = false\n  }\n}\n\nconst handleBlur = (evt: FocusEvent) => {\n  setTimeout(() => {\n    // validate current focus event is inside el-tooltip-content\n    // if so, ignore the blur event and the next focus event\n    if (popperRef.value?.isFocusInsideContent()) {\n      ignoreFocusEvent = true\n      return\n    }\n    activated.value && close()\n    emit('blur', evt)\n  })\n}\n\nconst handleClear = () => {\n  activated.value = false\n  emit(UPDATE_MODEL_EVENT, '')\n  emit('clear')\n}\n\nconst handleKeyEnter = async () => {\n  if (\n    suggestionVisible.value &&\n    highlightedIndex.value >= 0 &&\n    highlightedIndex.value < suggestions.value.length\n  ) {\n    handleSelect(suggestions.value[highlightedIndex.value])\n  } else if (props.selectWhenUnmatched) {\n    emit('select', { value: props.modelValue })\n    suggestions.value = []\n    highlightedIndex.value = -1\n  }\n}\n\nconst handleKeyEscape = (evt: Event) => {\n  if (suggestionVisible.value) {\n    evt.preventDefault()\n    evt.stopPropagation()\n    close()\n  }\n}\n\nconst close = () => {\n  activated.value = false\n}\n\nconst focus = () => {\n  inputRef.value?.focus()\n}\n\nconst blur = () => {\n  inputRef.value?.blur()\n}\n\nconst handleSelect = async (item: any) => {\n  emit(INPUT_EVENT, item[props.valueKey])\n  emit(UPDATE_MODEL_EVENT, item[props.valueKey])\n  emit('select', item)\n  suggestions.value = []\n  highlightedIndex.value = -1\n}\n\nconst highlight = (index: number) => {\n  if (!suggestionVisible.value || loading.value) return\n\n  if (index < 0) {\n    highlightedIndex.value = -1\n    return\n  }\n\n  if (index >= suggestions.value.length) {\n    index = suggestions.value.length - 1\n  }\n  const suggestion = regionRef.value!.querySelector(\n    `.${ns.be('suggestion', 'wrap')}`\n  )!\n  const suggestionList = suggestion.querySelectorAll<HTMLElement>(\n    `.${ns.be('suggestion', 'list')} li`\n  )!\n  const highlightItem = suggestionList[index]\n  const scrollTop = suggestion.scrollTop\n  const { offsetTop, scrollHeight } = highlightItem\n\n  if (offsetTop + scrollHeight > scrollTop + suggestion.clientHeight) {\n    suggestion.scrollTop += scrollHeight\n  }\n  if (offsetTop < scrollTop) {\n    suggestion.scrollTop -= scrollHeight\n  }\n  highlightedIndex.value = index\n  // TODO: use Volar generate dts to fix it.\n  ;(inputRef.value as any).ref!.setAttribute(\n    'aria-activedescendant',\n    `${listboxId.value}-item-${highlightedIndex.value}`\n  )\n}\n\nconst stopHandle = onClickOutside(listboxRef, () => {\n  // Prevent closing if focus is inside popper content\n  if (popperRef.value?.isFocusInsideContent()) return\n  suggestionVisible.value && close()\n})\n\nonBeforeUnmount(() => {\n  stopHandle?.()\n})\n\nonMounted(() => {\n  // TODO: use Volar generate dts to fix it.\n  ;(inputRef.value as any).ref!.setAttribute('role', 'textbox')\n  ;(inputRef.value as any).ref!.setAttribute('aria-autocomplete', 'list')\n  ;(inputRef.value as any).ref!.setAttribute('aria-controls', 'id')\n  ;(inputRef.value as any).ref!.setAttribute(\n    'aria-activedescendant',\n    `${listboxId.value}-item-${highlightedIndex.value}`\n  )\n  // get readonly attr\n  readonly = (inputRef.value as any).ref!.hasAttribute('readonly')\n})\n\ndefineExpose({\n  /** @description the index of the currently highlighted item */\n  highlightedIndex,\n  /** @description autocomplete whether activated */\n  activated,\n  /** @description remote search loading status */\n  loading,\n  /** @description el-input component instance */\n  inputRef,\n  /** @description el-tooltip component instance */\n  popperRef,\n  /** @description fetch suggestions result */\n  suggestions,\n  /** @description triggers when a suggestion is clicked */\n  handleSelect,\n  /** @description handle keyboard enter event */\n  handleKeyEnter,\n  /** @description focus the input element */\n  focus,\n  /** @description blur the input element */\n  blur,\n  /** @description close suggestion */\n  close,\n  /** @description highlight an item in a suggestion */\n  highlight,\n  /** @description loading suggestion list */\n  getData,\n})\n</script>\n"], "names": ["computed", "pick", "inputProps", "useRawAttrs", "useFormDisabled", "useNamespace", "ref", "useId", "isArray", "throwError", "debounce", "INPUT_EVENT", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "onClickOutside", "onBeforeUnmount", "onMounted", "_openBlock", "_createBlock", "_unref", "ElTooltip"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;uCAqJc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB;;;;;;;AAKA,IAAM,MAAA,cAAA,GAAiBA,aAAS,MAAMC,kBAAA,CAAK,OAAO,MAAO,CAAA,IAAA,CAAKC,gBAAU,CAAC,CAAC,CAAA,CAAA;AAE1E,IAAA,MAAM,WAAWC,YAAY,EAAA,CAAA;AAC7B,IAAA,MAAM,WAAWC,kCAAgB,EAAA,CAAA;AACjC,IAAM,MAAA,EAAA,GAAKC,mBAAa,cAAc,CAAA,CAAA;AAEtC,IAAA,MAAM,WAAWC,OAAmB,EAAA,CAAA;AACpC,IAAA,MAAM,YAAYA,OAAiB,EAAA,CAAA;AACnC,IAAA,MAAM,YAAYA,OAAqB,EAAA,CAAA;AACvC,IAAA,MAAM,aAAaA,OAAiB,EAAA,CAAA;AAEpC,IAAA,IAAI,QAAW,GAAA,KAAA,CAAA;AACf,IAAA,IAAI,gBAAmB,GAAA,KAAA,CAAA;AACvB,IAAM,MAAA,WAAA,GAAcA,OAAsB,CAAA,EAAE,CAAA,CAAA;AAC5C,IAAM,MAAA,gBAAA,GAAmBA,QAAI,CAAE,CAAA,CAAA,CAAA;AAC/B,IAAM,MAAA,aAAA,GAAgBA,QAAI,EAAE,CAAA,CAAA;AAC5B,IAAM,MAAA,SAAA,GAAYA,QAAI,KAAK,CAAA,CAAA;AAC3B,IAAM,MAAA,kBAAA,GAAqBA,QAAI,KAAK,CAAA,CAAA;AACpC,IAAM,MAAA,OAAA,GAAUA,QAAI,KAAK,CAAA,CAAA;AAEzB,IAAA,MAAM,YAAYC,aAAM,EAAA,CAAA;AACxB,IAAA,MAAM,MAAS,GAAAP,YAAA,CAAS,MAAM,QAAA,CAAS,KAAmB,CAAA,CAAA;AAE1D,IAAM,MAAA,iBAAA,GAAoBA,aAAS,MAAM;AACvC,MAAM,MAAA,WAAA,GAAc,WAAY,CAAA,KAAA,CAAM,MAAS,GAAA,CAAA,CAAA;AAC/C,MAAQ,OAAA,CAAA,WAAA,IAAe,OAAQ,CAAA,KAAA,KAAU,SAAU,CAAA,KAAA,CAAA;AAAA,KACpD,CAAA,CAAA;AAED,IAAA,MAAM,oBAAoBA,YAAS,CAAA,MAAM,CAAC,KAAM,CAAA,WAAA,IAAe,QAAQ,KAAK,CAAA,CAAA;AAE5E,IAAM,MAAA,QAAA,GAAWA,aAA6B,MAAM;AAClD,MAAA,IAAI,SAAS,KAAO,EAAA;AAClB,QAAA,OAAO,KAAM,CAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAAA,OAAA;AACgC,MAC7C,OAAA,EAAA,CAAA;AAAA,KACF,CAAA,CAAA;AACA,IAAA,MAAA,gBAAQ,GAAA,MAAA;AAAA,MACT,IAAA,iBAAA,CAAA,KAAA,EAAA;AAED,QAAA,sBAA+B,CAAA,EAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AAC7B,OAAA;AACE,KAAA,CAAA;AAAwD,IAC1D,MAAA,MAAA,GAAA,MAAA;AAAA,MACF,gBAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,OAAA,GAAA,OAAyB,WAAA,KAAA;AAAA,MAC3B,IAAA,kBAAA,CAAA,KAAA;AAEA,QAAM,OAAA;AACJ,MAAA,0BAA8B,KAAA;AAE9B,QAAM,OAAA,CAAA,KAA2C,GAAA,KAAA,CAAA;AAC/C,QAAA,IAAA,kBAAgB,CAAA,KAAA;AAChB,UAAA;AAEA,QAAI,IAAAQ,cAAA,CAAQ,cAAc,CAAG,EAAA;AAC3B,UAAA,WAAA,CAAY,KAAQ,GAAA,cAAA,CAAA;AACpB,UAAiB,gBAAA,CAAA,KAAA,GAAQ,KAAM,CAAA,kBAAA,GAAqB,CAAI,GAAA,CAAA,CAAA,CAAA;AAAA,SACnD,MAAA;AACL,UAAAC,gBAAA,CAAW,gBAAgB,2CAA2C,CAAA,CAAA;AAAA,SACxE;AAAA,OACF,CAAA;AAEA,MAAA,OAAA,CAAQ,KAAQ,GAAA,IAAA,CAAA;AAChB,MAAI,IAAAD,cAAA,CAAQ,KAAM,CAAA,gBAAgB,CAAG,EAAA;AACnC,QAAA,EAAA,CAAG,MAAM,gBAAgB,CAAA,CAAA;AAAA,OACpB,MAAA;AACL,QAAA,MAAM,MAAS,GAAA,MAAM,KAAM,CAAA,gBAAA,CAAiB,aAAa,EAAE,CAAA,CAAA;AAC3D,QAAA,IAAIA,cAAQ,CAAA,MAAM,CAAG;AAAS,UAChC,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,OACF;AACA,KAAA,CAAA;AAEA,IAAM,MAAA,gBAAiC,GAAAE,sBAAA,CAAA,OAAA,EAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACrC,IAAM,MAAA,WAAA,GAAA,CAAA,KAAkB,KAAC;AAEzB,MAAA,oBAAuB,GAAA,CAAA,CAAA,KAAA,CAAA;AACvB,MAAA,IAAA,CAAKC,0BAAoB;AAEzB,MAAA,IAAA,CAAAC,wBAA2B,EAAA,KAAA,CAAA,CAAA;AAC3B,MAAA,kBAAoB,CAAA,KAAA,GAAA,KAAA,CAAA;AAEpB,MAAA,SAAK,CAAA,KAAwB,KAAA,SAAA,CAAC,KAAO,GAAA,cAAA,CAAA,CAAA;AACnC,MAAA,IAAA,CAAA,KAAA,CAAA,cAA2B,IAAA,CAAA,KAAA,EAAA;AAC3B,QAAA,wBAAqB,GAAA,IAAA,CAAA;AACrB,QAAA,WAAA,CAAA,KAAA,GAAA,EAAA,CAAA;AAAA,QACF,OAAA;AAEA,OAAA;AAAsB,MACxB,gBAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,eAAgB,GAAA,CAAA,KAAA,KAAA;AACpB,MACG,IAAA,EAAA,CAAA;AAGD,MAAA,IAAA,QAAU,CAAQ,KAAA;AAAA,QACpB,OAAA;AAAA,MACF,IAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,MAAA,OAAA,IAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,QAAA,CAAA,aAAA,CAAA,EAAA;AAEA,QAAM,SAAA,CAAA,KAAA,GAAgB,IAAkB,CAAA;AACtC,OAAA;AAAwB,KAC1B,CAAA;AAEA,IAAM,MAAA,YAAA,GAAe,CAAoB,KAAA,KAAA;AACvC,MAAA,IAAI,CAACC,kBAAkB,EAAA,KAAA,CAAA,CAAA;AACrB,KAAA,CAAA;AACA,IAAA,MAAA,cAAiB,CAAA,GAAA,KAAA;AACjB,MAAM,IAAA,EAAA,CAAA;AACN,MAAI,IAAA,CAAA,gBAAwB,EAAA;AAC1B,QAAiB,SAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAmB,QACtC,IAAA,CAAA,OAAA,EAAA,GAAA,CAAA,CAAA;AAAA,QACK,MAAA,WAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AACL,QAAmB,IAAA,KAAA,CAAA,cAAA,IAAA,CAAA,QAAA,EAAA;AAAA,UACrB,gBAAA,CAAA,MAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AAAA,SACF;AAEA,OAAM,MAAA;AACJ,QAAA,gBAAiB,GAAA,KAAA,CAAA;AAGf,OAAI;AACF,KAAmB,CAAA;AACnB,IAAA,MAAA,UAAA,GAAA,CAAA,GAAA,KAAA;AAAA,MACF,UAAA,CAAA,MAAA;AACA,QAAA,IAAA,EAAA,CAAA;AACA,QAAA,IAAA,CAAK,cAAW,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,EAAA,EAAA;AAAA,UACjB,gBAAA,GAAA,IAAA,CAAA;AAAA,UACH,OAAA;AAEA,SAAA;AACE,QAAA,SAAkB,CAAA,KAAA,IAAA,KAAA,EAAA,CAAA;AAClB,QAAA;AACA,OAAA,CAAA,CAAA;AAAY,KACd,CAAA;AAEA,IAAA,MAAM,oBAA6B;AACjC,MACE,SAAA,CAAA,KAAA,GAAA;AAIA,MAAA,IAAA,CAAAD,wBAAyB,EAAA,EAAA,CAAA,CAAA;AAA6B,MACxD,IAAA,CAAA;AACE,KAAA,CAAA;AACA,IAAA,MAAA,6BAAqB;AACrB,MAAA,IAAA,iBAAyB,CAAA,KAAA,IAAA,gBAAA,CAAA,KAAA,IAAA,CAAA,IAAA,gBAAA,CAAA,KAAA,GAAA,WAAA,CAAA,KAAA,CAAA,MAAA,EAAA;AAAA,QAC3B,YAAA,CAAA,WAAA,CAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,OACF,MAAA,IAAA,KAAA,CAAA,mBAAA,EAAA;AAEA,QAAM,IAAA,CAAA,QAAA,EAAA,EAAA,KAAkC,EAAA,KAAA,CAAA,UAAA,EAAA,CAAA,CAAA;AACtC,QAAA,oBAAsB,EAAO,CAAA;AAC3B,QAAA,gBAAmB,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACnB,OAAA;AACA,KAAM,CAAA;AAAA,IACR,MAAA,eAAA,GAAA,CAAA,GAAA,KAAA;AAAA,MACF,IAAA,iBAAA,CAAA,KAAA,EAAA;AAEA,QAAA,kBAAoB,EAAA,CAAA;AAClB,QAAA,GAAA,CAAA,eAAkB,EAAA,CAAA;AAAA,QACpB,KAAA,EAAA,CAAA;AAEA,OAAA;AACE,KAAA,CAAA;AAAsB,IACxB,MAAA,KAAA,GAAA,MAAA;AAEA,MAAA,eAAmB,GAAA,KAAA,CAAA;AACjB,KAAA,CAAA;AAAqB,IACvB,MAAA,KAAA,GAAA,MAAA;AAEA,MAAM,IAAA,EAAA,CAAA;AACJ,MAAA,CAAA,EAAA,GAAkB,QAAA,CAAA,KAAA,KAAW,IAAA,GAAA,KAAQ,CAAC,GAAA,EAAA,CAAA,KAAA,EAAA,CAAA;AACtC,KAAA,CAAA;AACA,IAAA,MAAA,aAAmB;AACnB,MAAA,IAAA,EAAA,CAAA;AACA,MAAA,CAAA,EAAA,GAAA,QAAA,CAAA,KAAyB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AAAA,KAC3B,CAAA;AAEA,IAAM,MAAA,YAAY,GAAmB,OAAA,IAAA,KAAA;AACnC,MAAA,IAAI,CAACD,iBAAA,EAAA,IAAA,CAAkB,KAAS,CAAA,QAAA,CAAA,CAAA,CAAQ;AAExC,MAAA,IAAI,yBAAW,EAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AACb,MAAA,IAAA,CAAA,QAAA,EAAA,IAAiB,CAAQ,CAAA;AACzB,MAAA,WAAA,CAAA,KAAA,GAAA,EAAA,CAAA;AAAA,MACF,gBAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AAEA,KAAI,CAAA;AACF,IAAQ,MAAA,SAAA,GAAA,CAAA,KAAY;AAAe,MACrC,IAAA,CAAA,iBAAA,CAAA,KAAA,IAAA,OAAA,CAAA,KAAA;AACA,QAAM,OAAA;AAA8B,MAAA,IAC9B,KAAG,GAAG,CAAA,EAAA;AAAqB,QACjC,gBAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,QAAA;AAAkC,OAAA;AACD,MACjC,IAAA,KAAA,IAAA,WAAA,CAAA,KAAA,CAAA,MAAA,EAAA;AACA,QAAM,KAAA,GAAA,WAAA,CAAgB,eAAe,CAAK,CAAA;AAC1C,OAAA;AACA,MAAM,MAAA,UAAa,GAAA,SAAA,CAAA,KAAiB,CAAA,aAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEpC,MAAA,MAAgB,cAAA,GAAA,UAA2B,CAAA,gBAAA,CAAA,CAAA,CAAA,EAAyB,EAAA,CAAA,EAAA,CAAA,YAAA,EAAA,MAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAClE,MAAA,MAAA,aAAwB,GAAA,cAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MAC1B,MAAA,SAAA,GAAA,UAAA,CAAA,SAAA,CAAA;AACA,MAAA,iBAA2B,EAAA,YAAA,EAAA,GAAA,aAAA,CAAA;AACzB,MAAA,IAAA,SAAW,GAAa,YAAA,GAAA,SAAA,GAAA,UAAA,CAAA,YAAA,EAAA;AAAA,QAC1B,UAAA,CAAA,SAAA,IAAA,YAAA,CAAA;AACA,OAAA;AAEC,MAAC,IAAA,YAA4B,SAAA,EAAA;AAAA,QAC5B,UAAA,CAAA,SAAA,IAAA,YAAA,CAAA;AAAA,OAAA;AACiD,MACnD,gBAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,MACF,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,YAAA,CAAA,uBAAA,EAAA,CAAA,EAAA,SAAA,CAAA,KAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AAEJ,IAAI,MAAA,UAAU,GAAOG,mBAAA,CAAA,UAAqB,EAAG,MAAA;AAC7C,MAAA,IAAA,EAAA,CAAA;AAAiC,MAClC,IAAA,CAAA,EAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,EAAA;AAED,QAAA,OAAA;AACE,MAAa,iBAAA,CAAA,KAAA,IAAA,KAAA,EAAA,CAAA;AAAA,KACd,CAAA,CAAA;AAED,IAAAC,mBAAgB,CAAA,MAAA;AAEd,MAAA,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,EAAA,CAAA;AAAC,KAAA,CAAC,CAAS;AACV,IAAAC,aAAU,CAAA,MAAA;AAEV,MAAC,QAAA,CAAS,MAAc,GAAK,CAAA,YAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA;AAAA,MAC5B,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,YAAA,CAAA,mBAAA,EAAA,MAAA,CAAA,CAAA;AAAA,MAAA,QACG,CAAA,KAAA,CAAU,GAAK,CAAA,4BAA+B,EAAA,IAAA,CAAA,CAAA;AAAA,MACnD,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,YAAA,CAAA,uBAAA,EAAA,CAAA,EAAA,SAAA,CAAA,KAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAEA,MAAA,QAAA,GAAY,QAAS,CAAA,KAAA,CAAc,GAAK,CAAA,YAAA,CAAa,UAAU,CAAA,CAAA;AAAA,KAChE,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MAAA,gBAAA;AAAA,MAEX,SAAA;AAAA,MAAA,OAAA;AAAA,MAEA,QAAA;AAAA,MAAA,SAAA;AAAA,MAEA,WAAA;AAAA,MAAA,YAAA;AAAA,MAEA,cAAA;AAAA,MAAA,KAAA;AAAA,MAEA,IAAA;AAAA,MAAA,KAAA;AAAA,MAEA,SAAA;AAAA,MAAA,OAAA;AAAA,KAEA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAEA,OAAAC,aAAA,EAAA,EAAAC,eAAA,CAAAC,SAAA,CAAAC,iBAAA,CAAA,EAAA;AAAA,QAAA,OAAA,EAAA,WAAA;AAAA,QAEA,GAAA,EAAA,SAAA;AAAA,QAAA,OAAA,EAAAD,SAAA,CAAA,iBAAA,CAAA;AAAA,QAEA,SAAA,EAAA,IAAA,CAAA,SAAA;AAAA,QAAA,qBAAA,EAAA,CAAA,cAAA,EAAA,WAAA,CAAA;AAAA,QAEA,cAAA,EAAA,CAAAA,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;AAAA,QAAA,UAAA,EAAA,IAAA,CAAA,UAAA;AAAA,QAEA,WAAA,EAAA,IAAA,CAAA,QAAA;AAAA,QAAA,kBAAA,EAAA,KAAA;AAAA,QAEA,IAAA,EAAA,EAAA;AAAA,QACD,aAAA,EAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}