{"version": 3, "file": "ms.mjs", "sources": ["../../../../../packages/locale/lang/ms.ts"], "sourcesContent": ["export default {\n  name: 'ms',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb',\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON>',\n      defaultLabel: 'pemilih warna',\n      description:\n        'warna semasa ialah {warna}. tekan enter untuk memilih warna baharu.',\n      alphaLabel: 'pilih nilai alfa',\n    },\n    datepicker: {\n      now: '<PERSON>karang',\n      today: 'Hari ini',\n      cancel: '<PERSON><PERSON>',\n      clear: 'Pa<PERSON>',\n      confirm: 'OK',\n      dateTablePrompt:\n        'Gunakan kekunci anak panah dan masukkan untuk memilih hari dalam bulan tersebut',\n      monthTablePrompt:\n        'Gunakan kekunci anak panah dan masukkan untuk memilih bulan',\n      yearTablePrompt:\n        'Gunakan kekunci anak panah dan masukkan untuk memilih tahun',\n      selectedDate: 'Tarikh yang dipilih',\n      selectDate: 'Pilih tarikh',\n      selectTime: '<PERSON><PERSON>h masa',\n      startDate: '<PERSON><PERSON><PERSON>',\n      startTime: 'Ma<PERSON>',\n      endDate: '<PERSON><PERSON><PERSON>',\n      endTime: 'Masa <PERSON>',\n      prevYear: '<PERSON>hun <PERSON>bel<PERSON>',\n      nextYear: 'Tahun Depan',\n      prevMonth: 'Bulan Sebelumnya',\n      nextMonth: 'Bulan Depan',\n      year: '',\n      month1: 'Januari',\n      month2: 'Februari',\n      month3: 'Mac',\n      month4: 'April',\n      month5: 'Mei',\n      month6: 'Jun',\n      month7: 'Julai',\n      month8: 'Ogos',\n      month9: 'September',\n      month10: 'Oktober',\n      month11: 'November',\n      month12: 'Disember',\n      week: 'minggu',\n      weeks: {\n        sun: 'Ahd',\n        mon: 'Isn',\n        tue: 'Sel',\n        wed: 'Rab',\n        thu: 'Kha',\n        fri: 'Jum',\n        sat: 'Sab',\n      },\n      weeksFull: {\n        sun: 'Ahad',\n        mon: 'Isnin',\n        tue: 'Selasa',\n        wed: 'Rabu',\n        thu: 'Khamis',\n        fri: 'Jumaat',\n        sat: 'Sabtu',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mac',\n        apr: 'Apr',\n        may: 'Mei',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dis',\n      },\n    },\n    inputNumber: {\n      decrease: 'mengurangkan',\n      increase: 'meningkatkan',\n    },\n    select: {\n      loading: 'Memuatkan',\n      noMatch: 'Tiada data yang sepadan',\n      noData: 'Tiada data',\n      placeholder: 'Pilih',\n    },\n    mention: {\n      loading: 'Memuatkan',\n    },\n    dropdown: {\n      toggleDropdown: 'Togol Dropdown',\n    },\n    cascader: {\n      noMatch: 'Tiada data yang sepadan',\n      loading: 'Memuatkan',\n      placeholder: 'Pilih',\n      noData: 'Tiada data',\n    },\n    pagination: {\n      goto: 'Pergi ke',\n      pagesize: '/halaman',\n      total: 'Jumlah {total}',\n      pageClassifier: '',\n      page: 'Halaman',\n      prev: 'Halaman sebelumnya',\n      next: 'Halaman seterusnya',\n      currentPage: 'halaman {pager}',\n      prevPages: 'Halaman {pager} sebelumnya',\n      nextPages: 'Halaman {pager} seterusnya',\n      deprecationWarning:\n        'Penggunaan yang ditamatkan dikesan, sila rujuk dokumentasi el-pagination untuk butiran lanjut',\n    },\n    dialog: {\n      close: 'Tutup dialog ini',\n    },\n    drawer: {\n      close: 'Tutup dialog ini',\n    },\n    messagebox: {\n      title: 'Mesej',\n      confirm: 'OK',\n      Batal: 'Dibatalkan',\n      error: 'Input haram',\n      close: 'Tutup dialog ini',\n    },\n    upload: {\n      deleteTip: 'tekan padam untuk mengalih keluar',\n      delete: 'Padam',\n      preview: 'Pratonton',\n      continue: 'Teruskan',\n    },\n    slider: {\n      defaultLabel: 'peluncur antara {min} dan {maks}',\n      defaultRangeStartLabel: 'pilih nilai mula',\n      defaultRangeEndLabel: 'pilih nilai akhir',\n    },\n    table: {\n      emptyText: 'Tiada Data',\n      confirmFilter: 'OK',\n      resetFilter: 'Reset',\n      clearFilter: 'Semua',\n      sumText: 'Jumlah',\n    },\n    tour: {\n      next: 'Seterusnya',\n      previous: 'Sebelumnya',\n      finish: 'Selesai',\n    },\n    tree: {\n      emptyText: 'Tiada Data',\n    },\n    transfer: {\n      noMatch: 'Tiada data yang sepadan',\n      noData: 'Tiada Data',\n      titles: ['Senarai 1', 'Senarai 2'],\n      filterPlaceholder: 'Masukkan kata kunci',\n      noCheckedFormat: '{total} barang',\n      hasCheckedFormat: '{checked}/{total} diperiksa',\n    },\n    image: {\n      error: 'FAILED',\n    },\n    pageHeader: {\n      title: 'Kembali',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ya',\n      BatalButtonText: 'Tidak',\n    },\n    carousel: {\n      leftArrow: 'Anak panah karusel ke kiri',\n      rightArrow: 'Anak panah karusel ke kanan',\n      indicator: 'Tukar karusel kepada indeks {index}',\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,YAAY,EAAE,eAAe;AACnC,MAAM,WAAW,EAAE,qEAAqE;AACxF,MAAM,UAAU,EAAE,kBAAkB;AACpC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,UAAU;AACrB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,eAAe,EAAE,iFAAiF;AACxG,MAAM,gBAAgB,EAAE,6DAA6D;AACrF,MAAM,eAAe,EAAE,6DAA6D;AACpF,MAAM,YAAY,EAAE,qBAAqB;AACzC,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,UAAU,EAAE,YAAY;AAC9B,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,QAAQ,EAAE,kBAAkB;AAClC,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,OAAO;AACpB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,QAAQ,EAAE,cAAc;AAC9B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,WAAW,EAAE,OAAO;AAC1B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,WAAW;AAC1B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,gBAAgB;AACtC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,SAAS,EAAE,4BAA4B;AAC7C,MAAM,SAAS,EAAE,4BAA4B;AAC7C,MAAM,kBAAkB,EAAE,+FAA+F;AACzH,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,kBAAkB;AAC/B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,kBAAkB;AAC/B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,KAAK,EAAE,kBAAkB;AAC/B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,mCAAmC;AACpD,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,kCAAkC;AACtD,MAAM,sBAAsB,EAAE,kBAAkB;AAChD,MAAM,oBAAoB,EAAE,mBAAmB;AAC/C,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,aAAa,EAAE,IAAI;AACzB,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,MAAM,EAAE,SAAS;AACvB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,YAAY;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;AACxC,MAAM,iBAAiB,EAAE,qBAAqB;AAC9C,MAAM,eAAe,EAAE,gBAAgB;AACvC,MAAM,gBAAgB,EAAE,6BAA6B;AACrD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,SAAS;AACtB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,eAAe,EAAE,OAAO;AAC9B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,4BAA4B;AAC7C,MAAM,UAAU,EAAE,6BAA6B;AAC/C,MAAM,SAAS,EAAE,qCAAqC;AACtD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}