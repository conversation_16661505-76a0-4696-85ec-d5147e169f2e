import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("ccc.tencentcloudapi.com", "2020-02-10", clientConfig);
    }
    async TransferToManual(req, cb) {
        return this.request("TransferToManual", req, cb);
    }
    async ModifyStaff(req, cb) {
        return this.request("ModifyStaff", req, cb);
    }
    async DescribeExtensions(req, cb) {
        return this.request("DescribeExtensions", req, cb);
    }
    async DescribeAILatency(req, cb) {
        return this.request("DescribeAILatency", req, cb);
    }
    async DescribeAutoCalloutTasks(req, cb) {
        return this.request("DescribeAutoCalloutTasks", req, cb);
    }
    async AbortAgentCruiseDialingCampaign(req, cb) {
        return this.request("AbortAgentCruiseDialingCampaign", req, cb);
    }
    async CreateAICall(req, cb) {
        return this.request("CreateAICall", req, cb);
    }
    async DescribeAgentCruiseDialingCampaign(req, cb) {
        return this.request("DescribeAgentCruiseDialingCampaign", req, cb);
    }
    async DeleteCCCSkillGroup(req, cb) {
        return this.request("DeleteCCCSkillGroup", req, cb);
    }
    async StopAutoCalloutTask(req, cb) {
        return this.request("StopAutoCalloutTask", req, cb);
    }
    async DescribePredictiveDialingCampaign(req, cb) {
        return this.request("DescribePredictiveDialingCampaign", req, cb);
    }
    async DescribePSTNActiveSessionList(req, cb) {
        return this.request("DescribePSTNActiveSessionList", req, cb);
    }
    async DeleteExtension(req, cb) {
        return this.request("DeleteExtension", req, cb);
    }
    async DescribeChatMessages(req, cb) {
        return this.request("DescribeChatMessages", req, cb);
    }
    async BindStaffSkillGroupList(req, cb) {
        return this.request("BindStaffSkillGroupList", req, cb);
    }
    async BindNumberCallOutSkillGroup(req, cb) {
        return this.request("BindNumberCallOutSkillGroup", req, cb);
    }
    async DisableCCCPhoneNumber(req, cb) {
        return this.request("DisableCCCPhoneNumber", req, cb);
    }
    async DescribeIMCdrs(req, cb) {
        return this.request("DescribeIMCdrs", req, cb);
    }
    async DescribeIvrAudioList(req, cb) {
        return this.request("DescribeIvrAudioList", req, cb);
    }
    async DeleteStaff(req, cb) {
        return this.request("DeleteStaff", req, cb);
    }
    async ModifyCompanyApply(req, cb) {
        return this.request("ModifyCompanyApply", req, cb);
    }
    async CreateExtension(req, cb) {
        return this.request("CreateExtension", req, cb);
    }
    async ResetExtensionPassword(req, cb) {
        return this.request("ResetExtensionPassword", req, cb);
    }
    async DescribeTelCallInfo(req, cb) {
        return this.request("DescribeTelCallInfo", req, cb);
    }
    async UploadIvrAudio(req, cb) {
        return this.request("UploadIvrAudio", req, cb);
    }
    async DescribeExtension(req, cb) {
        return this.request("DescribeExtension", req, cb);
    }
    async DescribeCarrierPrivilegeNumberApplicants(req, cb) {
        return this.request("DescribeCarrierPrivilegeNumberApplicants", req, cb);
    }
    async DescribeTelSession(req, cb) {
        return this.request("DescribeTelSession", req, cb);
    }
    async CreateCallOutSession(req, cb) {
        return this.request("CreateCallOutSession", req, cb);
    }
    async DescribePredictiveDialingCampaigns(req, cb) {
        return this.request("DescribePredictiveDialingCampaigns", req, cb);
    }
    async DescribeAutoCalloutTask(req, cb) {
        return this.request("DescribeAutoCalloutTask", req, cb);
    }
    async DescribePredictiveDialingSessions(req, cb) {
        return this.request("DescribePredictiveDialingSessions", req, cb);
    }
    async CreateCompanyApply(req, cb) {
        return this.request("CreateCompanyApply", req, cb);
    }
    async ControlAIConversation(req, cb) {
        return this.request("ControlAIConversation", req, cb);
    }
    async DescribeTelCdr(req, cb) {
        return this.request("DescribeTelCdr", req, cb);
    }
    async DescribeSkillGroupInfoList(req, cb) {
        return this.request("DescribeSkillGroupInfoList", req, cb);
    }
    async UnbindNumberCallOutSkillGroup(req, cb) {
        return this.request("UnbindNumberCallOutSkillGroup", req, cb);
    }
    async ModifyOwnNumberApply(req, cb) {
        return this.request("ModifyOwnNumberApply", req, cb);
    }
    async CreatePredictiveDialingCampaign(req, cb) {
        return this.request("CreatePredictiveDialingCampaign", req, cb);
    }
    async CreateCarrierPrivilegeNumberApplicant(req, cb) {
        return this.request("CreateCarrierPrivilegeNumberApplicant", req, cb);
    }
    async DescribeAICallExtractResult(req, cb) {
        return this.request("DescribeAICallExtractResult", req, cb);
    }
    async CreateAgentCruiseDialingCampaign(req, cb) {
        return this.request("CreateAgentCruiseDialingCampaign", req, cb);
    }
    async CreateOwnNumberApply(req, cb) {
        return this.request("CreateOwnNumberApply", req, cb);
    }
    async DescribeCCCBuyInfoList(req, cb) {
        return this.request("DescribeCCCBuyInfoList", req, cb);
    }
    async ResumePredictiveDialingCampaign(req, cb) {
        return this.request("ResumePredictiveDialingCampaign", req, cb);
    }
    async CreateCCCSkillGroup(req, cb) {
        return this.request("CreateCCCSkillGroup", req, cb);
    }
    async UnbindStaffSkillGroupList(req, cb) {
        return this.request("UnbindStaffSkillGroupList", req, cb);
    }
    async ModifyStaffPassword(req, cb) {
        return this.request("ModifyStaffPassword", req, cb);
    }
    async DescribeStaffInfoList(req, cb) {
        return this.request("DescribeStaffInfoList", req, cb);
    }
    async BindNumberCallInInterface(req, cb) {
        return this.request("BindNumberCallInInterface", req, cb);
    }
    async DescribeStaffStatusMetrics(req, cb) {
        return this.request("DescribeStaffStatusMetrics", req, cb);
    }
    async CreateStaff(req, cb) {
        return this.request("CreateStaff", req, cb);
    }
    async CreateIVRSession(req, cb) {
        return this.request("CreateIVRSession", req, cb);
    }
    async CreateAutoCalloutTask(req, cb) {
        return this.request("CreateAutoCalloutTask", req, cb);
    }
    async ModifyExtension(req, cb) {
        return this.request("ModifyExtension", req, cb);
    }
    async UpdateCCCSkillGroup(req, cb) {
        return this.request("UpdateCCCSkillGroup", req, cb);
    }
    async CreateUserSig(req, cb) {
        return this.request("CreateUserSig", req, cb);
    }
    async DescribeTelRecordAsr(req, cb) {
        return this.request("DescribeTelRecordAsr", req, cb);
    }
    async HangUpCall(req, cb) {
        return this.request("HangUpCall", req, cb);
    }
    async PausePredictiveDialingCampaign(req, cb) {
        return this.request("PausePredictiveDialingCampaign", req, cb);
    }
    async CreateAdminURL(req, cb) {
        return this.request("CreateAdminURL", req, cb);
    }
    async DescribeProtectedTelCdr(req, cb) {
        return this.request("DescribeProtectedTelCdr", req, cb);
    }
    async DescribeIMCdrList(req, cb) {
        return this.request("DescribeIMCdrList", req, cb);
    }
    async ForceMemberOffline(req, cb) {
        return this.request("ForceMemberOffline", req, cb);
    }
    async DescribeCallInMetrics(req, cb) {
        return this.request("DescribeCallInMetrics", req, cb);
    }
    async CreateSDKLoginToken(req, cb) {
        return this.request("CreateSDKLoginToken", req, cb);
    }
    async DescribeActiveCarrierPrivilegeNumber(req, cb) {
        return this.request("DescribeActiveCarrierPrivilegeNumber", req, cb);
    }
    async DescribeNumbers(req, cb) {
        return this.request("DescribeNumbers", req, cb);
    }
    async AbortPredictiveDialingCampaign(req, cb) {
        return this.request("AbortPredictiveDialingCampaign", req, cb);
    }
    async DescribeCompanyList(req, cb) {
        return this.request("DescribeCompanyList", req, cb);
    }
    async CreateAIAgentCall(req, cb) {
        return this.request("CreateAIAgentCall", req, cb);
    }
    async DeletePredictiveDialingCampaign(req, cb) {
        return this.request("DeletePredictiveDialingCampaign", req, cb);
    }
    async RestoreMemberOnline(req, cb) {
        return this.request("RestoreMemberOnline", req, cb);
    }
    async UpdatePredictiveDialingCampaign(req, cb) {
        return this.request("UpdatePredictiveDialingCampaign", req, cb);
    }
}
