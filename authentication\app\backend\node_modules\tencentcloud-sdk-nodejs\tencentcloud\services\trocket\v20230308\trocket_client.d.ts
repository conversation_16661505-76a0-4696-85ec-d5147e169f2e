import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { DescribeProductSKUsRequest, ResetConsumerGroupOffsetResponse, DescribeSmoothMigrationTaskListRequest, DescribeRoleListResponse, DeleteMQTTTopicRequest, CreateInstanceResponse, ModifyRoleResponse, DescribeMessageListRequest, DeleteMQTTInsPublicEndpointRequest, ModifyInstanceRequest, CreateMQTTInsPublicEndpointResponse, ImportSourceClusterConsumerGroupsResponse, DeleteTopicRequest, DescribeSmoothMigrationTaskListResponse, DescribeMigrationTaskListResponse, DescribeMQTTProductSKUListResponse, DescribeRoleListRequest, DescribeMQTTClientResponse, DescribeMQTTMessageListResponse, DescribeMigratingTopicStatsResponse, DescribeMQTTProductSKUListRequest, DescribeConsumerLagRequest, DescribeMigratingGroupStatsRequest, DescribeSourceClusterGroupListResponse, DescribeMessageListResponse, DescribeMQTTMessageResponse, DeleteSmoothMigrationTaskRequest, DescribeInstanceListResponse, DescribeConsumerClientListResponse, ModifyInstanceResponse, CreateConsumerGroupRequest, ChangeMigratingTopicToNextStageResponse, DescribeMQTTClientRequest, DescribeSourceClusterGroupListRequest, DescribeMQTTInsVPCEndpointsResponse, DescribeMQTTInsPublicEndpointsResponse, DescribeInstanceListRequest, ResendDeadLetterMessageResponse, DescribeMigrationTaskListRequest, DeleteMQTTUserRequest, ModifyMQTTInstanceCertBindingRequest, CreateMQTTTopicResponse, DescribeMQTTInstanceRequest, ImportSourceClusterTopicsRequest, CreateMQTTUserResponse, DeleteInstanceResponse, DescribeTopicResponse, DeleteMQTTInsPublicEndpointResponse, DeleteInstanceRequest, ModifyRoleRequest, DescribeMQTTInsVPCEndpointsRequest, DescribeFusionInstanceListResponse, CreateTopicRequest, DescribeMessageRequest, DeleteRoleRequest, DescribeMQTTInstanceListResponse, DescribeMigratingTopicStatsRequest, DeleteConsumerGroupRequest, CreateConsumerGroupResponse, RollbackMigratingTopicStageRequest, DescribeMQTTUserListResponse, ModifyMQTTUserResponse, DescribeTopicListByGroupResponse, DescribeMessageTraceResponse, DoHealthCheckOnMigratingTopicRequest, DescribeProducerListRequest, DescribeMigratingTopicListResponse, ModifyMQTTInsPublicEndpointRequest, ModifyMQTTInstanceCertBindingResponse, DeleteMQTTInstanceResponse, DescribeTopicRequest, DeleteMQTTInstanceRequest, DescribeMessageTraceRequest, DeleteSmoothMigrationTaskResponse, CreateTopicResponse, DescribeMigratingGroupStatsResponse, DescribeMQTTInstanceListRequest, ModifyConsumerGroupResponse, CreateMQTTInstanceResponse, DescribeMQTTInstanceResponse, ImportSourceClusterConsumerGroupsRequest, RemoveMigratingTopicRequest, DescribeMessageResponse, DoHealthCheckOnMigratingTopicResponse, DeleteMQTTUserResponse, ImportSourceClusterTopicsResponse, DescribeTopicListRequest, ModifyConsumerGroupRequest, DescribeConsumerGroupRequest, DescribeConsumerGroupListRequest, CreateInstanceRequest, DescribeConsumerGroupResponse, DescribeConsumerClientRequest, ModifyTopicResponse, DescribeConsumerLagResponse, DescribeMQTTInsPublicEndpointsRequest, DeleteRoleResponse, DescribeTopicListByGroupRequest, DescribeMigratingTopicListRequest, ModifyMQTTTopicResponse, DescribeMQTTTopicListRequest, ModifyTopicRequest, DescribeInstanceResponse, DeleteMQTTTopicResponse, DescribeConsumerClientResponse, DescribeMQTTInstanceCertRequest, DescribeProducerListResponse, ModifyMQTTInsPublicEndpointResponse, ResendDeadLetterMessageRequest, CreateMQTTUserRequest, RollbackMigratingTopicStageResponse, DescribeFusionInstanceListRequest, DescribeMQTTMessageRequest, CreateRoleResponse, DescribeMQTTTopicListResponse, ModifyInstanceEndpointRequest, ChangeMigratingTopicToNextStageRequest, DescribeMQTTUserListRequest, ModifyMQTTInstanceRequest, ModifyMQTTInstanceResponse, CreateMQTTInstanceRequest, DescribeTopicListResponse, DescribeConsumerClientListRequest, DescribeMQTTTopicRequest, CreateRoleRequest, DeleteConsumerGroupResponse, ModifyMQTTTopicRequest, DescribeConsumerGroupListResponse, ModifyMQTTUserRequest, CreateMQTTInsPublicEndpointRequest, CreateMQTTTopicRequest, DeleteTopicResponse, DescribeMQTTInstanceCertResponse, DescribeMQTTTopicResponse, ModifyInstanceEndpointResponse, DescribeInstanceRequest, DescribeProductSKUsResponse, RemoveMigratingTopicResponse, DescribeMQTTMessageListRequest, ResetConsumerGroupOffsetRequest } from "./trocket_models";
/**
 * trocket client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 查询消息列表，如查询死信，请设置ConsumerGroup参数
     */
    DescribeMQTTMessageList(req: DescribeMQTTMessageListRequest, cb?: (error: string, rep: DescribeMQTTMessageListResponse) => void): Promise<DescribeMQTTMessageListResponse>;
    /**
     * 更新MQTT集群绑定证书
参数传空，则为删除证书
     */
    ModifyMQTTInstanceCertBinding(req: ModifyMQTTInstanceCertBindingRequest, cb?: (error: string, rep: ModifyMQTTInstanceCertBindingResponse) => void): Promise<ModifyMQTTInstanceCertBindingResponse>;
    /**
     * 修改 RocketMQ 5.x 集群接入点，操作前请先确认接入点已存在。
     */
    ModifyInstanceEndpoint(req: ModifyInstanceEndpointRequest, cb?: (error: string, rep: ModifyInstanceEndpointResponse) => void): Promise<ModifyInstanceEndpointResponse>;
    /**
     * 查询Topic迁移状态列表。

Filters字段为查询过滤器，支持以下条件：
* TopicName 主题名称，支持模糊查询
* MigrationStatus 迁移状态，可参考[MigratingTopic](https://cloud.tencent.com/document/api/1493/96031#MigratingTopic)数据结构
* Namespace 命名空间，仅4.x集群有效

Filters示例：
[{
    "Name": "TopicName",
    "Values": ["topic-a"]
}]
     */
    DescribeMigratingTopicList(req: DescribeMigratingTopicListRequest, cb?: (error: string, rep: DescribeMigratingTopicListResponse) => void): Promise<DescribeMigratingTopicListResponse>;
    /**
     * 根据消息 ID 查询消息轨迹。
     */
    DescribeMessageTrace(req: DescribeMessageTraceRequest, cb?: (error: string, rep: DescribeMessageTraceResponse) => void): Promise<DescribeMessageTraceResponse>;
    /**
     * 修改主题属性
     */
    ModifyTopic(req: ModifyTopicRequest, cb?: (error: string, rep: ModifyTopicResponse) => void): Promise<ModifyTopicResponse>;
    /**
     * 根据消费组获取主题列表，Filter参数使用说明如下：

- TopicName 主题名称，从 [DescribeTopicList](https://cloud.tencent.com/document/api/1493/96030) 接口返回的 [TopicItem](https://cloud.tencent.com/document/api/1493/96031#TopicItem) 或控制台获得。

Filters示例：
[{ "Name": "TopicName", "Values": ["test_topic"] }]
     */
    DescribeTopicListByGroup(req: DescribeTopicListByGroupRequest, cb?: (error: string, rep: DescribeTopicListByGroupResponse) => void): Promise<DescribeTopicListByGroupResponse>;
    /**
     * 修改主题属性
     */
    ModifyMQTTTopic(req: ModifyMQTTTopicRequest, cb?: (error: string, rep: ModifyMQTTTopicResponse) => void): Promise<ModifyMQTTTopicResponse>;
    /**
     * 从迁移列表中移除主题，仅当主题处于初始状态时有效
     */
    RemoveMigratingTopic(req: RemoveMigratingTopicRequest, cb?: (error: string, rep: RemoveMigratingTopicResponse) => void): Promise<RemoveMigratingTopicResponse>;
    /**
     * 删除MQTT访问用户
     */
    DeleteMQTTUser(req: DeleteMQTTUserRequest, cb?: (error: string, rep: DeleteMQTTUserResponse) => void): Promise<DeleteMQTTUserResponse>;
    /**
     * 添加角色
     */
    CreateRole(req: CreateRoleRequest, cb?: (error: string, rep: CreateRoleResponse) => void): Promise<CreateRoleResponse>;
    /**
     * 导入消费者组列表
     */
    ImportSourceClusterConsumerGroups(req: ImportSourceClusterConsumerGroupsRequest, cb?: (error: string, rep: ImportSourceClusterConsumerGroupsResponse) => void): Promise<ImportSourceClusterConsumerGroupsResponse>;
    /**
     * 用于查询平滑迁移任务列表。

查询参数Filters， 支持的字段如下：
* TaskStatus, 任务状态，支持多选
* ConnectionType，网络连接类型，支持多选，参考[SmoothMigrationTaskItem](https://cloud.tencent.com/document/api/1493/96031#SmoothMigrationTaskItem)的说明
* InstanceId，实例ID，精确搜索
* TaskName，任务名称，支持模糊搜索

Filters示例：
[{
    "Name": "InstanceId",
    "Values": ["rmq-1gzecldfg"]
}]
     */
    DescribeSmoothMigrationTaskList(req: DescribeSmoothMigrationTaskListRequest, cb?: (error: string, rep: DescribeSmoothMigrationTaskListResponse) => void): Promise<DescribeSmoothMigrationTaskListResponse>;
    /**
     * 删除 RocketMQ 5.x 集群，删除前请先删除正在使用的主题、消费组和角色信息。
     */
    DeleteInstance(req: DeleteInstanceRequest, cb?: (error: string, rep: DeleteInstanceResponse) => void): Promise<DeleteInstanceResponse>;
    /**
     * 创建消费组
     */
    CreateConsumerGroup(req: CreateConsumerGroupRequest, cb?: (error: string, rep: CreateConsumerGroupResponse) => void): Promise<CreateConsumerGroupResponse>;
    /**
     * 获取产品售卖规格
     */
    DescribeMQTTProductSKUList(req?: DescribeMQTTProductSKUListRequest, cb?: (error: string, rep: DescribeMQTTProductSKUListResponse) => void): Promise<DescribeMQTTProductSKUListResponse>;
    /**
     * 平滑迁移过程获取源集群group列表接口。

Filters字段为查询过滤器，支持以下字段：
* GroupName，消费组名称，支持模糊搜索
* Imported，是否已导入
* ImportStatus，导入状态，参考[SourceClusterGroupConfig](https://cloud.tencent.com/document/api/1493/96031#SourceClusterGroupConfig)的说明
* Namespace，命名空间，仅4.x集群有效

Filters示例：
[{
    "Name": "GroupName",
    "Values": ["group-a"]
}]
     */
    DescribeSourceClusterGroupList(req: DescribeSourceClusterGroupListRequest, cb?: (error: string, rep: DescribeSourceClusterGroupListResponse) => void): Promise<DescribeSourceClusterGroupListResponse>;
    /**
     * 检查迁移中的主题是否处于正常状态，只有处于正常状态的主题，才可以进入下一个迁移阶段
     */
    DoHealthCheckOnMigratingTopic(req: DoHealthCheckOnMigratingTopicRequest, cb?: (error: string, rep: DoHealthCheckOnMigratingTopicResponse) => void): Promise<DoHealthCheckOnMigratingTopicResponse>;
    /**
     * 查询消息详情
     */
    DescribeMessage(req: DescribeMessageRequest, cb?: (error: string, rep: DescribeMessageResponse) => void): Promise<DescribeMessageResponse>;
    /**
     * 查询用户列表，Filter参数使用说明如下：

1. Username，用户名称模糊搜索
     */
    DescribeMQTTUserList(req: DescribeMQTTUserListRequest, cb?: (error: string, rep: DescribeMQTTUserListResponse) => void): Promise<DescribeMQTTUserListResponse>;
    /**
     * 删除MQTT实例的公网接入点
     */
    DeleteMQTTInsPublicEndpoint(req: DeleteMQTTInsPublicEndpointRequest, cb?: (error: string, rep: DeleteMQTTInsPublicEndpointResponse) => void): Promise<DeleteMQTTInsPublicEndpointResponse>;
    /**
     * 查询角色列表，Filter参数使用说明如下：

- RoleName 角色名称，支持模糊搜索，从本接口返回值或控制台获得
- AccessKey AccessKey，支持模糊搜索，从本接口返回值或控制台获得

Filters示例：
[{ "Name": "RoleName", "Values": ["test_role"] }]
     */
    DescribeRoleList(req: DescribeRoleListRequest, cb?: (error: string, rep: DescribeRoleListResponse) => void): Promise<DescribeRoleListResponse>;
    /**
     * 修改MQTT角色
     */
    ModifyMQTTUser(req: ModifyMQTTUserRequest, cb?: (error: string, rep: ModifyMQTTUserResponse) => void): Promise<ModifyMQTTUserResponse>;
    /**
     * 删除消费组。消费者组删除后，消费者组的所有配置和相关数据都会被清空，且无法找回。删除后，在线的消费者客户端会出现报错，建议您提前下线客户端。
     */
    DeleteConsumerGroup(req: DeleteConsumerGroupRequest, cb?: (error: string, rep: DeleteConsumerGroupResponse) => void): Promise<DeleteConsumerGroupResponse>;
    /**
     * 查询主题关联的生产者列表信息，Filters支持以下筛选条件：
- ClientIP，客户端IP
- ClientID，客户端ID
     */
    DescribeProducerList(req: DescribeProducerListRequest, cb?: (error: string, rep: DescribeProducerListResponse) => void): Promise<DescribeProducerListResponse>;
    /**
     * 修改消费组属性
     */
    ModifyConsumerGroup(req: ModifyConsumerGroupRequest, cb?: (error: string, rep: ModifyConsumerGroupResponse) => void): Promise<ModifyConsumerGroupResponse>;
    /**
     * 查询MQTT实例公网接入点
     */
    DescribeMQTTInsVPCEndpoints(req: DescribeMQTTInsVPCEndpointsRequest, cb?: (error: string, rep: DescribeMQTTInsVPCEndpointsResponse) => void): Promise<DescribeMQTTInsVPCEndpointsResponse>;
    /**
     * 查询消费者客户端详情
     */
    DescribeConsumerClient(req: DescribeConsumerClientRequest, cb?: (error: string, rep: DescribeConsumerClientResponse) => void): Promise<DescribeConsumerClientResponse>;
    /**
     * 查看迁移消费组的实时信息
     */
    DescribeMigratingGroupStats(req: DescribeMigratingGroupStatsRequest, cb?: (error: string, rep: DescribeMigratingGroupStatsResponse) => void): Promise<DescribeMigratingGroupStatsResponse>;
    /**
     * 删除角色。请确保该角色相关信息不在当前代码中被使用。删除角色后，原先使用该角色进行生产或消费消息的密钥（AccessKey 和 SecretKey）将立即失效。
     */
    DeleteRole(req: DeleteRoleRequest, cb?: (error: string, rep: DeleteRoleResponse) => void): Promise<DeleteRoleResponse>;
    /**
     * 删除MQTT实例
     */
    DeleteMQTTInstance(req: DeleteMQTTInstanceRequest, cb?: (error: string, rep: DeleteMQTTInstanceResponse) => void): Promise<DeleteMQTTInstanceResponse>;
    /**
     * 查询MQTT消息详情
     */
    DescribeMQTTMessage(req: DescribeMQTTMessageRequest, cb?: (error: string, rep: DescribeMQTTMessageResponse) => void): Promise<DescribeMQTTMessageResponse>;
    /**
     * 更新MQTT实例公网接入点
     */
    ModifyMQTTInsPublicEndpoint(req: ModifyMQTTInsPublicEndpointRequest, cb?: (error: string, rep: ModifyMQTTInsPublicEndpointResponse) => void): Promise<ModifyMQTTInsPublicEndpointResponse>;
    /**
     * 修改迁移中的Topic状态进入下一步
     */
    ChangeMigratingTopicToNextStage(req: ChangeMigratingTopicToNextStageRequest, cb?: (error: string, rep: ChangeMigratingTopicToNextStageResponse) => void): Promise<ChangeMigratingTopicToNextStageResponse>;
    /**
     * 查询指定消费组堆积数。
     */
    DescribeConsumerLag(req: DescribeConsumerLagRequest, cb?: (error: string, rep: DescribeConsumerLagResponse) => void): Promise<DescribeConsumerLagResponse>;
    /**
     * 查询mqtt主题详情
     */
    DescribeMQTTTopic(req: DescribeMQTTTopicRequest, cb?: (error: string, rep: DescribeMQTTTopicResponse) => void): Promise<DescribeMQTTTopicResponse>;
    /**
     * 查询产品售卖规格，针对 RocketMQ 5.x 集群。
     */
    DescribeProductSKUs(req?: DescribeProductSKUsRequest, cb?: (error: string, rep: DescribeProductSKUsResponse) => void): Promise<DescribeProductSKUsResponse>;
    /**
     * 修改 RocketMQ 5.x 集群属性，仅支持修改运行中的集群。
     */
    ModifyInstance(req: ModifyInstanceRequest, cb?: (error: string, rep: ModifyInstanceResponse) => void): Promise<ModifyInstanceResponse>;
    /**
     * 查询消费组详情
     */
    DescribeConsumerGroup(req: DescribeConsumerGroupRequest, cb?: (error: string, rep: DescribeConsumerGroupResponse) => void): Promise<DescribeConsumerGroupResponse>;
    /**
     * 创建主题
     */
    CreateTopic(req: CreateTopicRequest, cb?: (error: string, rep: CreateTopicResponse) => void): Promise<CreateTopicResponse>;
    /**
     * 查询消息列表。如果查询死信消息，请设置ConsumerGroup参数。
     */
    DescribeMessageList(req: DescribeMessageListRequest, cb?: (error: string, rep: DescribeMessageListResponse) => void): Promise<DescribeMessageListResponse>;
    /**
     * 重置消费位点
     */
    ResetConsumerGroupOffset(req: ResetConsumerGroupOffsetRequest, cb?: (error: string, rep: ResetConsumerGroupOffsetResponse) => void): Promise<ResetConsumerGroupOffsetResponse>;
    /**
     * 获取主题列表，Filter参数使用说明如下：

1. TopicName，主题名称模糊搜索
2. TopicType，主题类型查询，支持多选，可选值：Normal,Order,Transaction,DelayScheduled
     */
    DescribeMQTTTopicList(req: DescribeMQTTTopicListRequest, cb?: (error: string, rep: DescribeMQTTTopicListResponse) => void): Promise<DescribeMQTTTopicListResponse>;
    /**
     * 查询实例信息
     */
    DescribeMQTTInstance(req: DescribeMQTTInstanceRequest, cb?: (error: string, rep: DescribeMQTTInstanceResponse) => void): Promise<DescribeMQTTInstanceResponse>;
    /**
     * 创建主题
     */
    CreateMQTTTopic(req: CreateMQTTTopicRequest, cb?: (error: string, rep: CreateMQTTTopicResponse) => void): Promise<CreateMQTTTopicResponse>;
    /**
     * 创建 RocketMQ 5.x 集群
     */
    CreateInstance(req: CreateInstanceRequest, cb?: (error: string, rep: CreateInstanceResponse) => void): Promise<CreateInstanceResponse>;
    /**
     * 删除MQTT主题
     */
    DeleteMQTTTopic(req: DeleteMQTTTopicRequest, cb?: (error: string, rep: DeleteMQTTTopicResponse) => void): Promise<DeleteMQTTTopicResponse>;
    /**
     * 查询 RocketMQ 5.x 集群信息。
     */
    DescribeInstance(req: DescribeInstanceRequest, cb?: (error: string, rep: DescribeInstanceResponse) => void): Promise<DescribeInstanceResponse>;
    /**
     * 导入topic列表
     */
    ImportSourceClusterTopics(req: ImportSourceClusterTopicsRequest, cb?: (error: string, rep: ImportSourceClusterTopicsResponse) => void): Promise<ImportSourceClusterTopicsResponse>;
    /**
     * 获取主题列表，Filter参数使用说明如下：

- TopicName 主题名称，支持模糊搜索，从 [DescribeTopicList](https://cloud.tencent.com/document/api/1493/96030) 接口返回的 [TopicItem](https://cloud.tencent.com/document/api/1493/96031#TopicItem) 或控制台获得
- TopicType 主题类型查询，支持多选，参考 [DescribeTopic](https://cloud.tencent.com/document/api/1493/97945) 接口 TopicType 字段

Filters示例：
 [{ "Name": "TopicName", "Values": ["test_topic"] }]
     */
    DescribeTopicList(req: DescribeTopicListRequest, cb?: (error: string, rep: DescribeTopicListResponse) => void): Promise<DescribeTopicListResponse>;
    /**
     * 重新发送死信消息
     */
    ResendDeadLetterMessage(req: ResendDeadLetterMessageRequest, cb?: (error: string, rep: ResendDeadLetterMessageResponse) => void): Promise<ResendDeadLetterMessageResponse>;
    /**
     * 查询MQTT实例公网接入点
     */
    DescribeMQTTInsPublicEndpoints(req: DescribeMQTTInsPublicEndpointsRequest, cb?: (error: string, rep: DescribeMQTTInsPublicEndpointsResponse) => void): Promise<DescribeMQTTInsPublicEndpointsResponse>;
    /**
     * 查询MQTT集群证书列表
     */
    DescribeMQTTInstanceCert(req: DescribeMQTTInstanceCertRequest, cb?: (error: string, rep: DescribeMQTTInstanceCertResponse) => void): Promise<DescribeMQTTInstanceCertResponse>;
    /**
     * 修改实例属性
     */
    ModifyMQTTInstance(req: ModifyMQTTInstanceRequest, cb?: (error: string, rep: ModifyMQTTInstanceResponse) => void): Promise<ModifyMQTTInstanceResponse>;
    /**
     * 获取实例列表，Filters参数使用说明如下：
1. InstanceName, 名称模糊查询
2. InstanceId，实例ID查询
3. InstanceType, 实例类型查询，支持多选
3. InstanceStatus，实例状态查询，支持多选

当使用TagFilters查询时，Filters参数失效。
     */
    DescribeMQTTInstanceList(req: DescribeMQTTInstanceListRequest, cb?: (error: string, rep: DescribeMQTTInstanceListResponse) => void): Promise<DescribeMQTTInstanceListResponse>;
    /**
     * 获取消费组列表，Filter参数使用说明如下：

- ConsumerGroupName 消费组名称，支持模糊查询，从 [DescribeConsumerGroupList](https://cloud.tencent.com/document/api/1493/101535) 接口返回的 [ConsumeGroupItem](https://cloud.tencent.com/document/api/1493/96031#ConsumeGroupItem) 或控制台获得。
- ConsumeMessageOrderly，投递顺序性，枚举值如下：
    - true 顺序投递
    - false 并发投递

Filters示例：
[{ "Name": "ConsumeMessageOrderly", "Values": ["true"] }]
     */
    DescribeConsumerGroupList(req: DescribeConsumerGroupListRequest, cb?: (error: string, rep: DescribeConsumerGroupListResponse) => void): Promise<DescribeConsumerGroupListResponse>;
    /**
     * 购买新的MQTT实例
     */
    CreateMQTTInstance(req: CreateMQTTInstanceRequest, cb?: (error: string, rep: CreateMQTTInstanceResponse) => void): Promise<CreateMQTTInstanceResponse>;
    /**
     * 查询集群列表，仅支持 5.x 集群。Filters参数使用说明如下：

- InstanceName 集群名称，支持模糊搜索
- InstanceId 腾讯云 RocketMQ 实例 ID，从 [DescribeFusionInstanceList](https://cloud.tencent.com/document/api/1493/106745) 接口或控制台获得
- InstanceType 集群类型，可参考 [InstanceItem](https://cloud.tencent.com/document/api/1493/96031#InstanceItem) 数据结构，支持多选
- InstanceStatus 集群状态，可参考 [InstanceItem](https://cloud.tencent.com/document/api/1493/96031#InstanceItem) 数据结构，支持多选

Filters示例：
[{
    "Name": "InstanceId",
    "Values": ["rmq-72mo3a9o"]
}]
     */
    DescribeInstanceList(req: DescribeInstanceListRequest, cb?: (error: string, rep: DescribeInstanceListResponse) => void): Promise<DescribeInstanceListResponse>;
    /**
     * 查询集群列表，支持 4.x 和 5.x 集群，其中 Filters 参数使用说明如下：

- InstanceName 集群名称，支持模糊查询，从本接口返回值或控制台获得
- InstanceId 集群ID，精确查询，从当前接口或控制台获得
- InstanceType 集群类型，可参考 [InstanceItem](https://cloud.tencent.com/document/api/1493/96031#InstanceItem) 数据结构，支持多选
- Version 集群版本，枚举值如下：
    - 4 RocketMQ 4.x 集群
    - 5 RocketMQ 5.x 集群

Filters示例：
 [{ "Name": "InstanceId", "Values": ["rmq-72mo3a9o"] }]
     */
    DescribeFusionInstanceList(req: DescribeFusionInstanceListRequest, cb?: (error: string, rep: DescribeFusionInstanceListResponse) => void): Promise<DescribeFusionInstanceListResponse>;
    /**
     * 添加mqtt角色
     */
    CreateMQTTUser(req: CreateMQTTUserRequest, cb?: (error: string, rep: CreateMQTTUserResponse) => void): Promise<CreateMQTTUserResponse>;
    /**
     * 修改角色
     */
    ModifyRole(req: ModifyRoleRequest, cb?: (error: string, rep: ModifyRoleResponse) => void): Promise<ModifyRoleResponse>;
    /**
     * 查询 MQTT 客户端详情
     */
    DescribeMQTTClient(req: DescribeMQTTClientRequest, cb?: (error: string, rep: DescribeMQTTClientResponse) => void): Promise<DescribeMQTTClientResponse>;
    /**
     * 查询主题详情，Offset和Limit参数是指订阅该主题的消费组查询分页参数，Filter参数使用说明如下：

- ConsumerGroup 消费组名称，从 [DescribeConsumerGroupList](https://cloud.tencent.com/document/api/1493/101535) 接口返回的 [ConsumeGroupItem](https://cloud.tencent.com/document/api/1493/96031#ConsumeGroupItem) 或控制台获得。

Filters示例：
[{ "Name": "ConsumerGroup", "Values": ["test_group"] }]
     */
    DescribeTopic(req: DescribeTopicRequest, cb?: (error: string, rep: DescribeTopicResponse) => void): Promise<DescribeTopicResponse>;
    /**
     * 获取数据迁移任务列表，Filter参数使用说明如下：

TaskId，根据任务ID精确查找
InstanceId，根据实例ID精确查找
Type，根据任务类型精确查找
     */
    DescribeMigrationTaskList(req: DescribeMigrationTaskListRequest, cb?: (error: string, rep: DescribeMigrationTaskListResponse) => void): Promise<DescribeMigrationTaskListResponse>;
    /**
     * 查询消费组下的客户端连接列表。
     */
    DescribeConsumerClientList(req: DescribeConsumerClientListRequest, cb?: (error: string, rep: DescribeConsumerClientListResponse) => void): Promise<DescribeConsumerClientListResponse>;
    /**
     * 用于查询迁移主题的实时数据
     */
    DescribeMigratingTopicStats(req: DescribeMigratingTopicStatsRequest, cb?: (error: string, rep: DescribeMigratingTopicStatsResponse) => void): Promise<DescribeMigratingTopicStatsResponse>;
    /**
     * 删除主题。主题删除后，主题的所有配置和相关数据都会被清空，且无法找回。
     */
    DeleteTopic(req: DeleteTopicRequest, cb?: (error: string, rep: DeleteTopicResponse) => void): Promise<DeleteTopicResponse>;
    /**
     * 删除平滑迁移任务，只有被取消的任务才可删除
     */
    DeleteSmoothMigrationTask(req: DeleteSmoothMigrationTaskRequest, cb?: (error: string, rep: DeleteSmoothMigrationTaskResponse) => void): Promise<DeleteSmoothMigrationTaskResponse>;
    /**
     * 回滚正在迁移的主题至前一个阶段
     */
    RollbackMigratingTopicStage(req: RollbackMigratingTopicStageRequest, cb?: (error: string, rep: RollbackMigratingTopicStageResponse) => void): Promise<RollbackMigratingTopicStageResponse>;
    /**
     * 为MQTT实例创建公网接入点
     */
    CreateMQTTInsPublicEndpoint(req: CreateMQTTInsPublicEndpointRequest, cb?: (error: string, rep: CreateMQTTInsPublicEndpointResponse) => void): Promise<CreateMQTTInsPublicEndpointResponse>;
}
