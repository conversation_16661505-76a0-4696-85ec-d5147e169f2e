# Normalize Wheel
Mouse wheel normalization across multiple multiple browsers.

## Original source
This code is extracted and from Facebook's [Fixed Data Table](https://github.com/facebook/fixed-data-table). Apart from import fixes, the code is unmodified.

## Usage
Just add it as an dependency in npm.

You can use it as follows:

```js
import normalizeWheel from 'normalize-wheel-es';

document.addEventListener('mousewheel', function (event) {
    const normalized = normalizeWheel(event);

    console.log(normalized.pixelX, normalized.pixelY);
});
```

## License
See the `LICENSE` file.
