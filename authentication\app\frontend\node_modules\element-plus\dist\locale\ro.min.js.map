{"version": 3, "file": "ro.min.js", "sources": ["../../../../packages/locale/lang/ro.ts"], "sourcesContent": ["export default {\n  name: 'ro',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: 'Acum',\n      today: '<PERSON><PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Selectează data',\n      selectTime: 'Selectează ora',\n      startDate: 'Data de început',\n      startTime: 'Ora de început',\n      endDate: 'Data de sfârșit',\n      endTime: 'Ora de sfârșit',\n      prevYear: 'Anul trecut',\n      nextYear: 'Anul următor',\n      prevMonth: 'Luna trecută',\n      nextMonth: '<PERSON> următoare',\n      year: '',\n      month1: '<PERSON><PERSON>rie',\n      month2: 'Februarie',\n      month3: 'Martie',\n      month4: 'Aprilie',\n      month5: 'Mai',\n      month6: 'Iunie',\n      month7: 'Iulie',\n      month8: 'August',\n      month9: 'Septembrie',\n      month10: 'Octombrie',\n      month11: 'Noiembrie',\n      month12: 'Decembrie',\n      // week: 'week',\n      weeks: {\n        sun: 'Du',\n        mon: 'Lu',\n        tue: 'Ma',\n        wed: 'Mi',\n        thu: 'Jo',\n        fri: 'Vi',\n        sat: 'Sâ',\n      },\n      months: {\n        jan: '<PERSON>',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mai',\n        jun: 'Iun',\n        jul: 'Iul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Oct',\n        nov: 'Noi',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Se încarcă',\n      noMatch: 'Nu există date potrivite',\n      noData: 'Nu există date',\n      placeholder: 'Selectează',\n    },\n    mention: {\n      loading: 'Se încarcă',\n    },\n    cascader: {\n      noMatch: 'Nu există date potrivite',\n      loading: 'Se încarcă',\n      placeholder: 'Selectează',\n      noData: 'Nu există date',\n    },\n    pagination: {\n      goto: 'Go to',\n      pagesize: '/pagina',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mesaj',\n      confirm: 'OK',\n      cancel: 'Anulează',\n      error: 'Date introduse eronate',\n    },\n    upload: {\n      deleteTip: 'apăsați pe ștergeți pentru a elimina',\n      delete: 'șterge',\n      preview: 'previzualizare',\n      continue: 'continuă',\n    },\n    table: {\n      emptyText: 'Nu există date',\n      confirmFilter: 'Confirmă',\n      resetFilter: 'Resetează',\n      clearFilter: 'Tot',\n      sumText: 'Suma',\n    },\n    tree: {\n      emptyText: 'Nu există date',\n    },\n    transfer: {\n      noMatch: 'Nu există date potrivite',\n      noData: 'Nu există date',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Introduceți cuvântul cheie',\n      noCheckedFormat: '{total} elemente',\n      hasCheckedFormat: '{checked}/{total} verificate',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,UAAU,CAAC,qBAAqB,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,mBAAmB,CAAC,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,wBAAwB,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,mBAAmB,CAAC,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,+BAA+B,CAAC,MAAM,CAAC,qBAAqB,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,0DAA0D,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,gBAAgB,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,+BAA+B,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,oCAAoC,CAAC,eAAe,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}