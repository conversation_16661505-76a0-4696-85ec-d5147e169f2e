<template>
  <div class="step-email">
    <div class="step-header">
      <h3>邮箱验证</h3>
      <p>请输入 @buaa.edu.cn 邮箱并完成验证码验证</p>
    </div>

    <el-form :model="form" ref="formRef" label-position="top" class="email-form">
      <el-form-item label="学校邮箱" prop="email" :rules="emailRules">
        <el-input v-model="form.email" placeholder="例如：<EMAIL>" clearable size="large" />
      </el-form-item>

      <div class="actions">
        <el-button @click="$emit('prev-step')" size="large">上一步</el-button>
        <el-button type="primary" :loading="sending" @click="sendEmail" size="large">发送验证码</el-button>
      </div>
    </el-form>

    <VerificationCode v-if="codeStage" :email="form.email" @verified="onVerified" @back="codeStage=false" />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import VerificationCode from '@/components/auth/VerificationCode.vue'

const emit = defineEmits(['prev-step','verified','set-email'])

const formRef = ref(null)
const form = reactive({ email: '' })
const sending = ref(false)
const codeStage = ref(false)

const emailRules = [{ validator: (_r, v, cb) => {
  if (!v) return cb(new Error('请输入邮箱'))
  if (!/@buaa\.edu\.cn$/i.test(v)) return cb(new Error('请使用 @buaa.edu.cn 邮箱'))
  cb()
}, trigger: 'blur' }]

const sendEmail = async () => {
  try {
    await formRef.value.validate()
  } catch { return }
  sending.value = true
  try {
    const res = await request.post('/api/auth/email', { email: form.email })
    if (res.success) {
      ElMessage.success('验证码已发送到邮箱')
      emit('set-email', form.email)
      codeStage.value = true
    } else {
      ElMessage.error(res.message || '发送失败')
    }
  } catch (e) {
    ElMessage.error(e.response?.data?.message || e.message || '发送失败')
  } finally {
    sending.value = false
  }
}

const onVerified = () => {
  ElMessage.success('邮箱验证成功')
  emit('verified', { email: form.email })
}
</script>

<style scoped>
.step-email { animation: fadeInUp 0.6s ease-out; }
.email-form { max-width: 520px; margin: 0 auto; }
.actions { display:flex; gap:12px; justify-content:center; margin-top: 4px; }
@keyframes fadeInUp { from { opacity:0; transform: translateY(20px);} to { opacity:1; transform: translateY(0);} }
</style>

