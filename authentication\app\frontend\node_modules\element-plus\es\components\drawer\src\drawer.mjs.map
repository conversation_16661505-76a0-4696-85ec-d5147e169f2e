{"version": 3, "file": "drawer.mjs", "sources": ["../../../../../../packages/components/drawer/src/drawer.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { dialogEmits, dialogProps } from '@element-plus/components/dialog'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const drawerProps = buildProps({\n  ...dialogProps,\n  direction: {\n    type: String,\n    default: 'rtl',\n    values: ['ltr', 'rtl', 'ttb', 'btt'],\n  },\n  size: {\n    type: [String, Number],\n    default: '30%',\n  },\n  withHeader: {\n    type: Boolean,\n    default: true,\n  },\n  modalFade: {\n    type: Boolean,\n    default: true,\n  },\n  headerAriaLevel: {\n    type: String,\n    default: '2',\n  },\n} as const)\n\nexport type DrawerProps = ExtractPropTypes<typeof drawerProps>\nexport type DrawerPropsPublic = __ExtractPublicPropTypes<typeof drawerProps>\n\nexport const drawerEmits = dialogEmits\n"], "names": [], "mappings": ";;;AAEY,MAAC,WAAW,GAAG,UAAU,CAAC;AACtC,EAAE,GAAG,WAAW;AAChB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK;AAClB,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACxC,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,WAAW,GAAG;;;;"}