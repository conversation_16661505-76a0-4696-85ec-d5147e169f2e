import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("kms.tencentcloudapi.com", "2019-01-18", clientConfig);
    }
    async ListDataKeys(req, cb) {
        return this.request("ListDataKeys", req, cb);
    }
    async PostQuantumCryptoVerify(req, cb) {
        return this.request("PostQuantumCryptoVerify", req, cb);
    }
    async Encrypt(req, cb) {
        return this.request("Encrypt", req, cb);
    }
    async Decrypt(req, cb) {
        return this.request("Decrypt", req, cb);
    }
    async UpdateAlias(req, cb) {
        return this.request("UpdateAlias", req, cb);
    }
    async DeleteWhiteBoxKey(req, cb) {
        return this.request("DeleteWhiteBoxKey", req, cb);
    }
    async ImportKeyMaterial(req, cb) {
        return this.request("ImportKeyMaterial", req, cb);
    }
    async DescribeDataKey(req, cb) {
        return this.request("DescribeDataKey", req, cb);
    }
    async GetPublicKey(req, cb) {
        return this.request("GetPublicKey", req, cb);
    }
    async DisableKey(req, cb) {
        return this.request("DisableKey", req, cb);
    }
    async GenerateDataKey(req, cb) {
        return this.request("GenerateDataKey", req, cb);
    }
    async GetServiceStatus(req, cb) {
        return this.request("GetServiceStatus", req, cb);
    }
    async GetDataKeyPlaintext(req, cb) {
        return this.request("GetDataKeyPlaintext", req, cb);
    }
    async AsymmetricSm2Decrypt(req, cb) {
        return this.request("AsymmetricSm2Decrypt", req, cb);
    }
    async PostQuantumCryptoEncrypt(req, cb) {
        return this.request("PostQuantumCryptoEncrypt", req, cb);
    }
    async CancelKeyDeletion(req, cb) {
        return this.request("CancelKeyDeletion", req, cb);
    }
    async GetKeyRotationStatus(req, cb) {
        return this.request("GetKeyRotationStatus", req, cb);
    }
    async ScheduleDataKeyDeletion(req, cb) {
        return this.request("ScheduleDataKeyDeletion", req, cb);
    }
    async DescribeWhiteBoxKeyDetails(req, cb) {
        return this.request("DescribeWhiteBoxKeyDetails", req, cb);
    }
    async BindCloudResource(req, cb) {
        return this.request("BindCloudResource", req, cb);
    }
    async OverwriteWhiteBoxDeviceFingerprints(req, cb) {
        return this.request("OverwriteWhiteBoxDeviceFingerprints", req, cb);
    }
    async ArchiveKey(req, cb) {
        return this.request("ArchiveKey", req, cb);
    }
    async UpdateDataKeyDescription(req, cb) {
        return this.request("UpdateDataKeyDescription", req, cb);
    }
    async UpdateDataKeyName(req, cb) {
        return this.request("UpdateDataKeyName", req, cb);
    }
    async SignByAsymmetricKey(req, cb) {
        return this.request("SignByAsymmetricKey", req, cb);
    }
    async DisableKeys(req, cb) {
        return this.request("DisableKeys", req, cb);
    }
    async CancelDataKeyDeletion(req, cb) {
        return this.request("CancelDataKeyDeletion", req, cb);
    }
    async ReEncrypt(req, cb) {
        return this.request("ReEncrypt", req, cb);
    }
    async EnableWhiteBoxKeys(req, cb) {
        return this.request("EnableWhiteBoxKeys", req, cb);
    }
    async ListAlgorithms(req, cb) {
        return this.request("ListAlgorithms", req, cb);
    }
    async DescribeKey(req, cb) {
        return this.request("DescribeKey", req, cb);
    }
    async ListKeys(req, cb) {
        return this.request("ListKeys", req, cb);
    }
    async GenerateRandom(req, cb) {
        return this.request("GenerateRandom", req, cb);
    }
    async GetDataKeyCiphertextBlob(req, cb) {
        return this.request("GetDataKeyCiphertextBlob", req, cb);
    }
    async PostQuantumCryptoDecrypt(req, cb) {
        return this.request("PostQuantumCryptoDecrypt", req, cb);
    }
    async VerifyByAsymmetricKey(req, cb) {
        return this.request("VerifyByAsymmetricKey", req, cb);
    }
    async EnableDataKeys(req, cb) {
        return this.request("EnableDataKeys", req, cb);
    }
    async ImportDataKey(req, cb) {
        return this.request("ImportDataKey", req, cb);
    }
    async CreateKey(req, cb) {
        return this.request("CreateKey", req, cb);
    }
    async DescribeWhiteBoxKey(req, cb) {
        return this.request("DescribeWhiteBoxKey", req, cb);
    }
    async EncryptByWhiteBox(req, cb) {
        return this.request("EncryptByWhiteBox", req, cb);
    }
    async GetParametersForImport(req, cb) {
        return this.request("GetParametersForImport", req, cb);
    }
    async DisableWhiteBoxKeys(req, cb) {
        return this.request("DisableWhiteBoxKeys", req, cb);
    }
    async DisableDataKey(req, cb) {
        return this.request("DisableDataKey", req, cb);
    }
    async DescribeDataKeys(req, cb) {
        return this.request("DescribeDataKeys", req, cb);
    }
    async DisableKeyRotation(req, cb) {
        return this.request("DisableKeyRotation", req, cb);
    }
    async ListKeyDetail(req, cb) {
        return this.request("ListKeyDetail", req, cb);
    }
    async DisableWhiteBoxKey(req, cb) {
        return this.request("DisableWhiteBoxKey", req, cb);
    }
    async EnableKeys(req, cb) {
        return this.request("EnableKeys", req, cb);
    }
    async ScheduleKeyDeletion(req, cb) {
        return this.request("ScheduleKeyDeletion", req, cb);
    }
    async DescribeWhiteBoxDeviceFingerprints(req, cb) {
        return this.request("DescribeWhiteBoxDeviceFingerprints", req, cb);
    }
    async AsymmetricRsaDecrypt(req, cb) {
        return this.request("AsymmetricRsaDecrypt", req, cb);
    }
    async UnbindCloudResource(req, cb) {
        return this.request("UnbindCloudResource", req, cb);
    }
    async EnableKeyRotation(req, cb) {
        return this.request("EnableKeyRotation", req, cb);
    }
    async CreateWhiteBoxKey(req, cb) {
        return this.request("CreateWhiteBoxKey", req, cb);
    }
    async EnableDataKey(req, cb) {
        return this.request("EnableDataKey", req, cb);
    }
    async EnableWhiteBoxKey(req, cb) {
        return this.request("EnableWhiteBoxKey", req, cb);
    }
    async EnableKey(req, cb) {
        return this.request("EnableKey", req, cb);
    }
    async DeleteImportedKeyMaterial(req, cb) {
        return this.request("DeleteImportedKeyMaterial", req, cb);
    }
    async DescribeKeys(req, cb) {
        return this.request("DescribeKeys", req, cb);
    }
    async DescribeWhiteBoxServiceStatus(req, cb) {
        return this.request("DescribeWhiteBoxServiceStatus", req, cb);
    }
    async UpdateKeyDescription(req, cb) {
        return this.request("UpdateKeyDescription", req, cb);
    }
    async CancelKeyArchive(req, cb) {
        return this.request("CancelKeyArchive", req, cb);
    }
    async DescribeWhiteBoxDecryptKey(req, cb) {
        return this.request("DescribeWhiteBoxDecryptKey", req, cb);
    }
    async PostQuantumCryptoSign(req, cb) {
        return this.request("PostQuantumCryptoSign", req, cb);
    }
    async GetRegions(req, cb) {
        return this.request("GetRegions", req, cb);
    }
    async ListDataKeyDetail(req, cb) {
        return this.request("ListDataKeyDetail", req, cb);
    }
    async DisableDataKeys(req, cb) {
        return this.request("DisableDataKeys", req, cb);
    }
}
