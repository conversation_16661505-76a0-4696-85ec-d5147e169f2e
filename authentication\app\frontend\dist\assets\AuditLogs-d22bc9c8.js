import{_ as e,r as a}from"./index-bcbc0702.js";/* empty css                      *//* empty css                   *//* empty css                *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                             *//* empty css               *//* empty css                       */import{r as l,d as t,A as s,B as d,C as r,F as o,G as n,D as u,u as i,I as p,am as c,b2 as _,al as m,H as f,J as g,E as v,K as b,L as y,ai as h,Y as w,au as j,av as k,b3 as x,Z as R,ba as V,bb as z,b5 as C,b7 as U,b8 as q,b9 as D,X as Y,_ as I,bc as L,bd as O,O as S,be as H}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const J={class:"audit-logs"},M={class:"card-header"},N={class:"header-actions"},E={class:"search-bar"},K={class:"expand-content"},P={key:0,class:"request-data"},A={key:1,class:"response-data"},B={class:"pagination"},T={key:0,class:"log-detail"},F={key:0,class:"data-section"},G={key:1,class:"data-section"},X=e({__name:"AuditLogs",setup(e){const X=l(!1),Z=l([]),$=l(!1),Q=l(null),W=t({keyword:"",action:"",level:"",dateRange:[]}),ee=t({page:1,size:50,total:0}),ae=async()=>{X.value=!0;try{const e={page:ee.page,size:ee.size,...W};W.dateRange&&2===W.dateRange.length&&(e.start_time=W.dateRange[0],e.end_time=W.dateRange[1]);const l=await a.get("/api/admin/audit-logs",{params:e});l.success&&(Z.value=l.data.logs||[],ee.total=l.data.total||0)}catch(e){v.error("加载审计日志失败")}finally{X.value=!1}},le=()=>{ee.page=1,ae()},te=()=>{Object.assign(W,{keyword:"",action:"",level:"",dateRange:[]}),ee.page=1,ae()},se=e=>({info:"info",warning:"warning",error:"danger",critical:"danger"}[e]||"info"),de=e=>({info:"信息",warning:"警告",error:"错误",critical:"严重"}[e]||e),re=e=>({login:"success",register:"primary",audit:"warning",config:"info",export:"info"}[e]||""),oe=e=>({login:"登录",register:"注册",audit:"审核",config:"配置",export:"导出"}[e]||e),ne=e=>e?new Date(e).toLocaleString():"",ue=e=>{if("string"==typeof e)try{return JSON.stringify(JSON.parse(e),null,2)}catch{return e}return JSON.stringify(e,null,2)},ie=async()=>{try{const e={...W};W.dateRange&&2===W.dateRange.length&&(e.start_time=W.dateRange[0],e.end_time=W.dateRange[1]);const l=await a.get("/api/admin/audit-logs/export",{params:e,responseType:"blob"}),t=new Blob([l],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),s=window.URL.createObjectURL(t),d=document.createElement("a");d.href=s,d.download=`audit_logs_${(new Date).toISOString().split("T")[0]}.xlsx`,document.body.appendChild(d),d.click(),document.body.removeChild(d),window.URL.revokeObjectURL(s),v.success("日志导出成功")}catch(e){v.error("导出日志失败")}};return s(()=>{ae()}),(e,a)=>{const l=b,t=y,s=h,v=w,pe=j,ce=k,_e=x,me=R,fe=V,ge=z,ve=C,be=U,ye=q,he=D,we=Y,je=I,ke=L;return d(),r("div",J,[o(we,null,{header:n(()=>[u("div",M,[a[9]||(a[9]=u("span",null,"审计日志",-1)),u("div",N,[o(t,{onClick:ie},{default:n(()=>[o(l,null,{default:n(()=>[o(i(O))]),_:1}),a[7]||(a[7]=p(" 导出日志 ",-1))]),_:1,__:[7]}),o(t,{onClick:ae},{default:n(()=>[o(l,null,{default:n(()=>[o(i(S))]),_:1}),a[8]||(a[8]=p(" 刷新 ",-1))]),_:1,__:[8]})])])]),default:n(()=>[u("div",E,[o(me,{gutter:20},{default:n(()=>[o(v,{span:5},{default:n(()=>[o(s,{modelValue:W.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>W.keyword=e),placeholder:"搜索用户、操作",clearable:"",onKeyup:c(le,["enter"])},{prefix:n(()=>[o(l,null,{default:n(()=>[o(i(H))]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(v,{span:4},{default:n(()=>[o(ce,{modelValue:W.action,"onUpdate:modelValue":a[1]||(a[1]=e=>W.action=e),placeholder:"操作类型",clearable:""},{default:n(()=>[o(pe,{label:"用户登录",value:"login"}),o(pe,{label:"用户注册",value:"register"}),o(pe,{label:"审核操作",value:"audit"}),o(pe,{label:"配置修改",value:"config"}),o(pe,{label:"数据导出",value:"export"})]),_:1},8,["modelValue"])]),_:1}),o(v,{span:4},{default:n(()=>[o(ce,{modelValue:W.level,"onUpdate:modelValue":a[2]||(a[2]=e=>W.level=e),placeholder:"日志级别",clearable:""},{default:n(()=>[o(pe,{label:"信息",value:"info"}),o(pe,{label:"警告",value:"warning"}),o(pe,{label:"错误",value:"error"}),o(pe,{label:"严重",value:"critical"})]),_:1},8,["modelValue"])]),_:1}),o(v,{span:7},{default:n(()=>[o(_e,{modelValue:W.dateRange,"onUpdate:modelValue":a[3]||(a[3]=e=>W.dateRange=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1}),o(v,{span:4},{default:n(()=>[o(t,{type:"primary",onClick:le},{default:n(()=>a[10]||(a[10]=[p("搜索",-1)])),_:1,__:[10]}),o(t,{onClick:te},{default:n(()=>a[11]||(a[11]=[p("重置",-1)])),_:1,__:[11]})]),_:1})]),_:1})]),_((d(),m(ye,{data:Z.value,stripe:"",style:{width:"100%"},"default-sort":{prop:"created_at",order:"descending"}},{default:n(()=>[o(ve,{type:"expand",width:"30"},{default:n(({row:e})=>[u("div",K,[o(ge,{column:2,border:""},{default:n(()=>[o(fe,{label:"请求ID"},{default:n(()=>[p(f(e.request_id),1)]),_:2},1024),o(fe,{label:"会话ID"},{default:n(()=>[p(f(e.session_id),1)]),_:2},1024),o(fe,{label:"用户代理"},{default:n(()=>[p(f(e.user_agent),1)]),_:2},1024),o(fe,{label:"请求路径"},{default:n(()=>[p(f(e.request_path),1)]),_:2},1024),o(fe,{label:"请求方法"},{default:n(()=>[p(f(e.request_method),1)]),_:2},1024),o(fe,{label:"响应状态"},{default:n(()=>[p(f(e.response_status),1)]),_:2},1024),o(fe,{label:"处理时间"},{default:n(()=>[p(f(e.processing_time)+"ms",1)]),_:2},1024),o(fe,{label:"错误信息"},{default:n(()=>[p(f(e.error_message||"无"),1)]),_:2},1024)]),_:2},1024),e.request_data?(d(),r("div",P,[a[12]||(a[12]=u("h4",null,"请求数据：",-1)),u("pre",null,f(ue(e.request_data)),1)])):g("",!0),e.response_data?(d(),r("div",A,[a[13]||(a[13]=u("h4",null,"响应数据：",-1)),u("pre",null,f(ue(e.response_data)),1)])):g("",!0)])]),_:1}),o(ve,{prop:"created_at",label:"时间",width:"180",sortable:""},{default:n(({row:e})=>[p(f(ne(e.created_at)),1)]),_:1}),o(ve,{prop:"level",label:"级别",width:"80"},{default:n(({row:e})=>[o(be,{type:se(e.level),size:"small"},{default:n(()=>[p(f(de(e.level)),1)]),_:2},1032,["type"])]),_:1}),o(ve,{prop:"action",label:"操作",width:"100"},{default:n(({row:e})=>[o(be,{type:re(e.action),size:"small"},{default:n(()=>[p(f(oe(e.action)),1)]),_:2},1032,["type"])]),_:1}),o(ve,{prop:"user",label:"用户",width:"120"}),o(ve,{prop:"ip_address",label:"IP地址",width:"130"}),o(ve,{prop:"description",label:"描述","min-width":"200"}),o(ve,{label:"操作",width:"100",fixed:"right"},{default:n(({row:e})=>[o(t,{size:"small",onClick:a=>{return l=e,Q.value=l,void($.value=!0);var l}},{default:n(()=>a[14]||(a[14]=[p("详情",-1)])),_:2,__:[14]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[ke,X.value]]),u("div",B,[o(he,{"current-page":ee.page,"onUpdate:currentPage":a[4]||(a[4]=e=>ee.page=e),"page-size":ee.size,"onUpdate:pageSize":a[5]||(a[5]=e=>ee.size=e),"page-sizes":[20,50,100,200],total:ee.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ae,onCurrentChange:ae},null,8,["current-page","page-size","total"])])]),_:1}),o(je,{modelValue:$.value,"onUpdate:modelValue":a[6]||(a[6]=e=>$.value=e),title:"日志详情",width:"80%"},{default:n(()=>[Q.value?(d(),r("div",T,[o(ge,{column:2,border:""},{default:n(()=>[o(fe,{label:"时间"},{default:n(()=>[p(f(ne(Q.value.created_at)),1)]),_:1}),o(fe,{label:"级别"},{default:n(()=>[o(be,{type:se(Q.value.level)},{default:n(()=>[p(f(de(Q.value.level)),1)]),_:1},8,["type"])]),_:1}),o(fe,{label:"操作"},{default:n(()=>[o(be,{type:re(Q.value.action)},{default:n(()=>[p(f(oe(Q.value.action)),1)]),_:1},8,["type"])]),_:1}),o(fe,{label:"用户"},{default:n(()=>[p(f(Q.value.user),1)]),_:1}),o(fe,{label:"IP地址"},{default:n(()=>[p(f(Q.value.ip_address),1)]),_:1}),o(fe,{label:"用户代理"},{default:n(()=>[p(f(Q.value.user_agent),1)]),_:1}),o(fe,{label:"请求路径",span:2},{default:n(()=>[p(f(Q.value.request_path),1)]),_:1}),o(fe,{label:"描述",span:2},{default:n(()=>[p(f(Q.value.description),1)]),_:1})]),_:1}),Q.value.request_data?(d(),r("div",F,[a[15]||(a[15]=u("h4",null,"请求数据",-1)),o(s,{"model-value":ue(Q.value.request_data),type:"textarea",rows:10,readonly:""},null,8,["model-value"])])):g("",!0),Q.value.response_data?(d(),r("div",G,[a[16]||(a[16]=u("h4",null,"响应数据",-1)),o(s,{"model-value":ue(Q.value.response_data),type:"textarea",rows:10,readonly:""},null,8,["model-value"])])):g("",!0)])):g("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-1269a8f1"]]);export{X as default};
