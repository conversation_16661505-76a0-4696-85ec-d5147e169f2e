{"version": 3, "file": "content.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/content.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { useAriaProps } from '@element-plus/hooks'\n\nimport type { PopperEffect } from '@element-plus/components/popper'\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { Placement, Strategy, VirtualElement } from '@floating-ui/dom'\n\nconst tooltipV2Strategies = ['absolute', 'fixed'] as const\n\nconst tooltipV2Placements = [\n  'top-start',\n  'top-end',\n  'top',\n  'bottom-start',\n  'bottom-end',\n  'bottom',\n  'left-start',\n  'left-end',\n  'left',\n  'right-start',\n  'right-end',\n  'right',\n] as const\n\nexport const tooltipV2ContentProps = buildProps({\n  arrowPadding: {\n    type: definePropType<number>(Number),\n    default: 5,\n  },\n  effect: {\n    type: definePropType<PopperEffect>(String),\n    default: 'light',\n  },\n  contentClass: String,\n  /**\n   * Placement of tooltip content relative to reference element (when absent it refers to trigger)\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: tooltipV2Placements,\n    default: 'bottom',\n  },\n  /**\n   * Reference element for tooltip content to set its position\n   */\n  reference: {\n    type: definePropType<HTMLElement | VirtualElement | null>(Object),\n    default: null,\n  },\n  offset: {\n    type: Number,\n    default: 8,\n  },\n  strategy: {\n    type: definePropType<Strategy>(String),\n    values: tooltipV2Strategies,\n    default: 'absolute',\n  },\n  showArrow: Boolean,\n  ...useAriaProps(['ariaLabel']),\n} as const)\n\nexport type TooltipV2ContentProps = ExtractPropTypes<\n  typeof tooltipV2ContentProps\n>\n\nexport type TooltipV2ContentPropsPublic = __ExtractPublicPropTypes<\n  typeof tooltipV2ContentProps\n>\n"], "names": [], "mappings": ";;;AAEA,MAAM,mBAAmB,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAClD,MAAM,mBAAmB,GAAG;AAC5B,EAAE,WAAW;AACb,EAAE,SAAS;AACX,EAAE,KAAK;AACP,EAAE,cAAc;AAChB,EAAE,YAAY;AACd,EAAE,QAAQ;AACV,EAAE,YAAY;AACd,EAAE,UAAU;AACZ,EAAE,MAAM;AACR,EAAE,aAAa;AACf,EAAE,WAAW;AACb,EAAE,OAAO;AACT,CAAC,CAAC;AACU,MAAC,qBAAqB,GAAG,UAAU,CAAC;AAChD,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,YAAY,EAAE,MAAM;AACtB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,EAAE,mBAAmB;AAC/B,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,EAAE,mBAAmB;AAC/B,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,GAAG,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC;;;;"}