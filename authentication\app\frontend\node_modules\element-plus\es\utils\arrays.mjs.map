{"version": 3, "file": "arrays.mjs", "sources": ["../../../../packages/utils/arrays.ts"], "sourcesContent": ["import { isArray } from './types'\n\nexport const unique = <T>(arr: T[]) => [...new Set(arr)]\n\ntype Many<T> = T | ReadonlyArray<T>\n// TODO: rename to `ensureArray`\n/** like `_.castArray`, except falsy value returns empty array. */\nexport const castArray = <T>(arr: Many<T>): T[] => {\n  if (!arr && (arr as any) !== 0) return []\n  return isArray(arr) ? arr : [arr as T]\n}\n\n// TODO: remove import alias\n// avoid naming conflicts\nexport { castArray as ensureArray } from 'lodash-unified'\n"], "names": [], "mappings": ";;;AACY,MAAC,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE;AACrC,MAAC,SAAS,GAAG,CAAC,GAAG,KAAK;AAClC,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;AACvB,IAAI,OAAO,EAAE,CAAC;AACd,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACpC;;;;"}