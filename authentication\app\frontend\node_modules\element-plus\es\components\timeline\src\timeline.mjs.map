{"version": 3, "file": "timeline.mjs", "sources": ["../../../../../../packages/components/timeline/src/timeline.ts"], "sourcesContent": ["import { defineComponent, h, provide, renderSlot } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { TIMELINE_INJECTION_KEY } from './tokens'\n\nconst Timeline = defineComponent({\n  name: 'ElTimeline',\n  setup(_, { slots }) {\n    const ns = useNamespace('timeline')\n\n    provide(TIMELINE_INJECTION_KEY, slots)\n\n    /**\n     *  Maybe ,this component will not support prop 'reverse', why ?\n     *\n     *  Example 1:\n     *   <component-a>\n     *     <div>1</div>\n     *     <div>2</div>\n     *   </component-a>\n     *\n     *  Example 2:\n     *   <component-a>\n     *     <div v-for=\"i in 2\" :key=\"i\">{{ i }}</div>\n     *   </component-a>\n     *\n     *  'slots.default()' value in example 1 just like [Vnode, Vnode]\n     *  'slots.default()' value in example 2 just like [Vnode]\n     *\n     *   so i can't reverse the slots, when i use 'v-for' directive.\n     */\n\n    return () => {\n      return h('ul', { class: [ns.b()] }, [renderSlot(slots, 'default')])\n    }\n  },\n})\n\nexport default Timeline\nexport type TimelineInstance = InstanceType<typeof Timeline> & unknown\n"], "names": [], "mappings": ";;;;AAGK,MAAC,QAAQ,GAAG,eAAe,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY;AACpB,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;AACtB,IAAI,MAAM,EAAE,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AACxC,IAAI,OAAO,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;AAC3C,IAAI,OAAO,MAAM;AACjB,MAAM,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AAC1E,KAAK,CAAC;AACN,GAAG;AACH,CAAC;;;;"}