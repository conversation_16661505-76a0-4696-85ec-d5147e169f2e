'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var index = require('./components/infinite-scroll/index.js');
var index$1 = require('./components/loading/index.js');
var index$2 = require('./components/message/index.js');
var index$3 = require('./components/message-box/index.js');
var index$4 = require('./components/notification/index.js');
var index$5 = require('./components/popover/index.js');

var Plugins = [
  index.ElInfiniteScroll,
  index$1.ElLoading,
  index$2.ElMessage,
  index$3.ElMessageBox,
  index$4.ElNotification,
  index$5.ElPopoverDirective
];

exports["default"] = Plugins;
//# sourceMappingURL=plugin.js.map
