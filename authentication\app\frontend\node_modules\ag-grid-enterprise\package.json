{"name": "ag-grid-enterprise", "version": "30.2.1", "description": "AG Grid Enterprise Features", "main": "./dist/ag-grid-enterprise.cjs.js", "module": "./dist/ag-grid-enterprise.auto.esm.js", "types": "main.d.ts", "scripts": {"clean": "rimraf dist main.d.ts .hash", "build": "npm run customise && npx gulp && npm run rollup", "build-prod": "npm run build", "package": "npx gulp copy-umd-files", "test": "if test -z \"$TEAMCITY_PROJECT_NAME\" ; then exec npm run test:dev ; else exec npm run test:ci ; fi", "test:ci": "/bin/sh ./test-modifications.sh", "test:dev": "echo \"No tests to run.\"", "customise": "node customise.js", "rollup": "mkdir -p dist && node build.js"}, "repository": {"type": "git", "url": "https://github.com/ag-grid/ag-grid.git"}, "keywords": ["ag", "ag-grid", "datagrid", "data-grid", "datatable", "data-table", "grid", "table", "react", "table", "angular", "angular-component", "react", "react-component", "reactjs", "enterprise", "vue", "v<PERSON><PERSON><PERSON>"], "author": "<PERSON> <<EMAIL>>", "license": "Commercial", "bugs": {"url": "https://github.com/ag-grid/ag-grid/issues"}, "browserslist": ["> 1%", "last 2 versions", "not ie >= 0", "not ie_mob >= 0", "not blackberry > 0"], "homepage": "https://www.ag-grid.com/", "dependencies": {}, "devDependencies": {"@ag-grid-enterprise/all-modules": "~30.2.1", "@ag-grid-enterprise/advanced-filter": "~30.2.1", "@ag-grid-enterprise/charts": "~30.2.1", "@ag-grid-enterprise/clipboard": "~30.2.1", "@ag-grid-enterprise/column-tool-panel": "~30.2.1", "@ag-grid-enterprise/core": "~30.2.1", "@ag-grid-enterprise/excel-export": "~30.2.1", "@ag-grid-enterprise/filter-tool-panel": "~30.2.1", "@ag-grid-enterprise/master-detail": "~30.2.1", "@ag-grid-enterprise/menu": "~30.2.1", "@ag-grid-enterprise/multi-filter": "~30.2.1", "@ag-grid-enterprise/range-selection": "~30.2.1", "@ag-grid-enterprise/rich-select": "~30.2.1", "@ag-grid-enterprise/row-grouping": "~30.2.1", "@ag-grid-enterprise/server-side-row-model": "~30.2.1", "@ag-grid-enterprise/set-filter": "~30.2.1", "@ag-grid-enterprise/side-bar": "~30.2.1", "@ag-grid-enterprise/sparklines": "~30.2.1", "@ag-grid-enterprise/status-bar": "~30.2.1", "@ag-grid-enterprise/viewport-row-model": "~30.2.1", "ag-charts-community": "~8.2.1", "@rollup/plugin-alias": "^2.2.0", "@types/node": "12.20.20", "ag-grid-community": "~30.2.1", "gulp": "^4.0.0", "gulp-clean": "^0.4.0", "gulp-concat": "^2.6.1", "gulp-header": "^2.0.0", "gulp-rename": "^1.4.0", "gulp-replace": "^1.0.0", "gulp-sourcemaps": "^2.6.0", "gulp-add-src": "1.0.0", "gulp-tsd": "^0.1.0", "gulp-typescript": "^5.0.0", "merge2": "^1.2.0", "rollup": "^1.21.2", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-typescript2": "^0.24.3", "typescript": "~4.3.5", "rimraf": "3.0.2"}}