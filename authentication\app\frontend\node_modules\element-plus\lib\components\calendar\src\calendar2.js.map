{"version": 3, "file": "calendar2.js", "sources": ["../../../../../../packages/components/calendar/src/calendar.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isArray,\n  isDate,\n} from '@element-plus/utils'\nimport { INPUT_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport type CalendarDateType =\n  | 'prev-month'\n  | 'next-month'\n  | 'prev-year'\n  | 'next-year'\n  | 'today'\n\nconst isValidRange = (range: unknown): range is [Date, Date] =>\n  isArray(range) && range.length === 2 && range.every((item) => isDate(item))\n\nexport const calendarProps = buildProps({\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: Date,\n  },\n  /**\n   * @description time range, including start time and end time.\n   *   Start time must be start day of week, end time must be end day of week, the time span cannot exceed two months.\n   */\n  range: {\n    type: definePropType<[Date, Date]>(Array),\n    validator: isValidRange,\n  },\n} as const)\nexport type CalendarProps = ExtractPropTypes<typeof calendarProps>\nexport type CalendarPropsPublic = __ExtractPublicPropTypes<typeof calendarProps>\n\nexport const calendarEmits = {\n  [UPDATE_MODEL_EVENT]: (value: Date) => isDate(value),\n  [INPUT_EVENT]: (value: Date) => isDate(value),\n}\nexport type CalendarEmits = typeof calendarEmits\n"], "names": ["isArray", "isDate", "buildProps", "definePropType", "UPDATE_MODEL_EVENT", "INPUT_EVENT"], "mappings": ";;;;;;;;AAOA,MAAM,YAAY,GAAG,CAAC,KAAK,KAAKA,cAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,KAAKC,aAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAChG,MAAC,aAAa,GAAGC,kBAAU,CAAC;AACxC,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,IAAI;AACd,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,SAAS,EAAE,YAAY;AAC3B,GAAG;AACH,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,CAACC,wBAAkB,GAAG,CAAC,KAAK,KAAKH,aAAM,CAAC,KAAK,CAAC;AAChD,EAAE,CAACI,iBAAW,GAAG,CAAC,KAAK,KAAKJ,aAAM,CAAC,KAAK,CAAC;AACzC;;;;;"}