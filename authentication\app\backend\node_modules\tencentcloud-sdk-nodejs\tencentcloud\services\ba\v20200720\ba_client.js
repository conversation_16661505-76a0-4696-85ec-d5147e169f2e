"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Client = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * Copyright (c) 2018 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
const abstract_client_1 = require("../../../common/abstract_client");
/**
 * ba client
 * @class
 */
class Client extends abstract_client_1.AbstractClient {
    constructor(clientConfig) {
        super("ba.tencentcloudapi.com", "2020-07-20", clientConfig);
    }
    /**
     * 将备案ICP订单下的一个网站信息 同步给订单下其他网站，需要被同步的网站被检查通过(isCheck:true)；
只有指定的网站信息字段能被同步
     */
    async SyncIcpOrderWebInfo(req, cb) {
        return this.request("SyncIcpOrderWebInfo", req, cb);
    }
    /**
     * 创建渠道备案小程序二维码
     */
    async CreateWeappQRUrl(req, cb) {
        return this.request("CreateWeappQRUrl", req, cb);
    }
    /**
     * 获取实名认证信息
     */
    async DescribeGetAuthInfo(req, cb) {
        return this.request("DescribeGetAuthInfo", req, cb);
    }
}
exports.Client = Client;
