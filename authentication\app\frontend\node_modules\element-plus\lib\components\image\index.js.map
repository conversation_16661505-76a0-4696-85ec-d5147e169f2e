{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/image/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Image from './src/image.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElImage: SFCWithInstall<typeof Image> = withInstall(Image)\nexport default ElImage\n\nexport * from './src/image'\n"], "names": ["withInstall", "Image"], "mappings": ";;;;;;;;AAEY,MAAC,OAAO,GAAGA,mBAAW,CAACC,kBAAK;;;;;;;"}