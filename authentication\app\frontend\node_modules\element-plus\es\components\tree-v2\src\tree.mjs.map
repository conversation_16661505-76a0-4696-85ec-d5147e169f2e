{"version": 3, "file": "tree.mjs", "sources": ["../../../../../../packages/components/tree-v2/src/tree.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[ns.b(), { [ns.m('highlight-current')]: highlightCurrent }]\"\n    role=\"tree\"\n  >\n    <fixed-size-list\n      v-if=\"isNotEmpty\"\n      ref=\"listRef\"\n      :class-name=\"ns.b('virtual-list')\"\n      :data=\"flattenTree\"\n      :total=\"flattenTree.length\"\n      :height=\"height\"\n      :item-size=\"treeNodeSize\"\n      :perf-mode=\"perfMode\"\n      :scrollbar-always-on=\"scrollbarAlwaysOn\"\n    >\n      <template #default=\"{ data, index, style }\">\n        <el-tree-node\n          :key=\"data[index].key\"\n          :style=\"style\"\n          :node=\"data[index]\"\n          :expanded=\"data[index].expanded\"\n          :show-checkbox=\"showCheckbox\"\n          :checked=\"isChecked(data[index])\"\n          :indeterminate=\"isIndeterminate(data[index])\"\n          :item-size=\"treeNodeSize\"\n          :disabled=\"isDisabled(data[index])\"\n          :current=\"isCurrent(data[index])\"\n          :hidden-expand-icon=\"isForceHiddenExpandIcon(data[index])\"\n          @click=\"handleNodeClick\"\n          @toggle=\"toggleExpand\"\n          @check=\"handleNodeCheck\"\n          @drop=\"handleNodeDrop\"\n        />\n      </template>\n    </fixed-size-list>\n    <div v-else :class=\"ns.e('empty-block')\">\n      <slot name=\"empty\">\n        <span :class=\"ns.e('empty-text')\">\n          {{ emptyText ?? t('el.tree.emptyText') }}\n        </span>\n      </slot>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, getCurrentInstance, provide, useSlots } from 'vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { formItemContextKey } from '@element-plus/components/form'\nimport { FixedSizeList } from '@element-plus/components/virtual-list'\nimport { useTree } from './composables/useTree'\nimport ElTreeNode from './tree-node.vue'\nimport { ROOT_TREE_INJECTION_KEY, treeEmits, treeProps } from './virtual-tree'\n\ndefineOptions({\n  name: 'ElTreeV2',\n})\n\nconst props = defineProps(treeProps)\nconst emit = defineEmits(treeEmits)\n\nconst slots = useSlots()\n\nconst treeNodeSize = computed(() => props.itemSize)\n\nprovide(ROOT_TREE_INJECTION_KEY, {\n  ctx: {\n    emit,\n    slots,\n  },\n  props,\n  instance: getCurrentInstance()!,\n})\nprovide(formItemContextKey, undefined)\nconst { t } = useLocale()\nconst ns = useNamespace('tree')\nconst {\n  flattenTree,\n  isNotEmpty,\n  listRef,\n  toggleExpand,\n  isIndeterminate,\n  isChecked,\n  isDisabled,\n  isCurrent,\n  isForceHiddenExpandIcon,\n  handleNodeClick,\n  handleNodeDrop,\n  handleNodeCheck,\n  // expose\n  toggleCheckbox,\n  getCurrentNode,\n  getCurrentKey,\n  setCurrentKey,\n  getCheckedKeys,\n  getCheckedNodes,\n  getHalfCheckedKeys,\n  getHalfCheckedNodes,\n  setChecked,\n  setCheckedKeys,\n  filter,\n  setData,\n  getNode,\n  expandNode,\n  collapseNode,\n  setExpandedKeys,\n  scrollToNode,\n  scrollTo,\n} = useTree(props, emit)\n\ndefineExpose({\n  toggleCheckbox,\n  getCurrentNode,\n  getCurrentKey,\n  setCurrentKey,\n  getCheckedKeys,\n  getCheckedNodes,\n  getHalfCheckedKeys,\n  getHalfCheckedNodes,\n  setChecked,\n  setCheckedKeys,\n  filter,\n  setData,\n  getNode,\n  expandNode,\n  collapseNode,\n  setExpandedKeys,\n  scrollToNode,\n  scrollTo,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;mCAuDc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAA,MAAM,YAAe,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,QAAQ,CAAA,CAAA;AAElD,IAAA,OAAA,CAAQ,uBAAyB,EAAA;AAAA,MAC/B,GAAK,EAAA;AAAA,QACH,IAAA;AAAA,QACA,KAAA;AAAA,OACF;AAAA,MACA,KAAA;AAAA,MACA,UAAU,kBAAmB,EAAA;AAAA,KAC9B,CAAA,CAAA;AACD,IAAA,OAAA,CAAQ,oBAAoB,KAAS,CAAA,CAAA,CAAA;AACrC,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA;AAAA,MACJ,WAAA;AAAA,MACA,UAAA;AAAA,MACA,OAAA;AAAA,MACA,YAAA;AAAA,MACA,eAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,SAAA;AAAA,MACA,uBAAA;AAAA,MACA,eAAA;AAAA,MACA,cAAA;AAAA,MACA,eAAA;AAAA,MAAA,cAAA;AAAA,MAEA,cAAA;AAAA,MACA,aAAA;AAAA,MACA,aAAA;AAAA,MACA,cAAA;AAAA,MACA,eAAA;AAAA,MACA,kBAAA;AAAA,MACA,mBAAA;AAAA,MACA,UAAA;AAAA,MACA,cAAA;AAAA,MACA,MAAA;AAAA,MACA,OAAA;AAAA,MACA,OAAA;AAAA,MACA,UAAA;AAAA,MACA,YAAA;AAAA,MACA,eAAA;AAAA,MACA,YAAA;AAAA,MACA,QAAA;AAAA,KACA,GAAA,OAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAAA,IACF,MAAY,CAAA;AAEZ,MAAa,cAAA;AAAA,MACX,cAAA;AAAA,MACA,aAAA;AAAA,MACA,aAAA;AAAA,MACA,cAAA;AAAA,MACA,eAAA;AAAA,MACA,kBAAA;AAAA,MACA,mBAAA;AAAA,MACA,UAAA;AAAA,MACA,cAAA;AAAA,MACA,MAAA;AAAA,MACA,OAAA;AAAA,MACA,OAAA;AAAA,MACA,UAAA;AAAA,MACA,YAAA;AAAA,MACA,eAAA;AAAA,MACA,YAAA;AAAA,MACA,QAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}