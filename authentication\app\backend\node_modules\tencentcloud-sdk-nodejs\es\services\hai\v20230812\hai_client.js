import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("hai.tencentcloudapi.com", "2023-08-12", clientConfig);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async DescribeApplications(req, cb) {
        return this.request("DescribeApplications", req, cb);
    }
    async DescribeMuskPrompts(req, cb) {
        return this.request("DescribeMuskPrompts", req, cb);
    }
    async DescribeScenes(req, cb) {
        return this.request("DescribeScenes", req, cb);
    }
    async ResizeInstanceDisk(req, cb) {
        return this.request("ResizeInstanceDisk", req, cb);
    }
    async DescribeRegions(req, cb) {
        return this.request("DescribeRegions", req, cb);
    }
    async RunInstances(req, cb) {
        return this.request("RunInstances", req, cb);
    }
    async StartInstance(req, cb) {
        return this.request("StartInstance", req, cb);
    }
    async TerminateInstances(req, cb) {
        return this.request("TerminateInstances", req, cb);
    }
    async CreateMuskPrompt(req, cb) {
        return this.request("CreateMuskPrompt", req, cb);
    }
    async ResetInstancesPassword(req, cb) {
        return this.request("ResetInstancesPassword", req, cb);
    }
    async DescribeInstanceNetworkStatus(req, cb) {
        return this.request("DescribeInstanceNetworkStatus", req, cb);
    }
    async DescribeServiceLoginSettings(req, cb) {
        return this.request("DescribeServiceLoginSettings", req, cb);
    }
    async InquirePriceRunInstances(req, cb) {
        return this.request("InquirePriceRunInstances", req, cb);
    }
    async StopInstance(req, cb) {
        return this.request("StopInstance", req, cb);
    }
    async CreateApplication(req, cb) {
        return this.request("CreateApplication", req, cb);
    }
}
