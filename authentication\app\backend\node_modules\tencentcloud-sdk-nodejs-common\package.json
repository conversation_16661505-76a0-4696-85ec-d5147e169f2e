{"name": "tencentcloud-sdk-nodejs-common", "version": "4.1.66", "description": "腾讯云 API NODEJS SDK", "main": "./tencentcloud/index.js", "module": "./es/index.js", "types": "./tencentcloud/index.d.ts", "scripts": {"test": "mocha -t 10000", "build": "concurrently 'npm:build:cjs' 'npm:build:es'", "build:cjs": "tsc -p tsconfig.json", "build:es": "tsc -p tsconfig.es.json", "build:slim": "node ./scripts/build_slim.js", "prettier": "prettier --config prettier.config.js --write 'src/**/*.{tsx,ts,jsx,js,css,json,vue}'", "clean": "rimraf tencentcloud es"}, "engines": {"node": ">=10"}, "files": ["tencentcloud", "es"], "keywords": ["ten<PERSON>clouda<PERSON>", "tencentcloud", "qcloud", "sdk", "js", "nodejs"], "author": "ten<PERSON>clouda<PERSON>", "license": "Apache-2.0", "dependencies": {"form-data": "^3.0.0", "get-stream": "^6.0.0", "https-proxy-agent": "^5.0.0", "is-stream": "^2.0.0", "json-bigint": "^1.0.0", "node-fetch": "^2.2.0", "tslib": "1.13.0", "uuid": "^9.0.1"}, "directories": {"example": "examples", "lib": "lib"}, "repository": {"type": "git", "url": "https://github.com/tencentcloud/tencentcloud-sdk-nodejs"}, "devDependencies": {"@types/form-data": "^2.5.0", "@types/json-bigint": "^1.0.1", "@types/node": "^14.0.26", "@types/node-fetch": "^2.5.7", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^2.34.0", "@typescript-eslint/parser": "^2.34.0", "babel-eslint": "^10.0.2", "chai": "^4.2.0", "concurrently": "^6.5.1", "esbuild": "^0.25.0", "eslint": "^6.8.0", "eslint-plugin-react": "^7.17.0", "mocha": "^8.4.0", "prettier": "^2.3.0", "rimraf": "^5.0.10", "ts-node": "^8.10.2", "typescript": "^3.9.7"}}