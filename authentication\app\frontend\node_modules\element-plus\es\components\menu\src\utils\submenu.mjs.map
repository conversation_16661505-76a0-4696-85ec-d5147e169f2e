{"version": 3, "file": "submenu.mjs", "sources": ["../../../../../../../packages/components/menu/src/utils/submenu.ts"], "sourcesContent": ["// @ts-nocheck\nimport { triggerEvent } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\n\nimport type MenuItem from './menu-item'\n\nclass SubMenu {\n  public subMenuItems: NodeList\n  public subIndex = 0\n  constructor(public parent: MenuItem, public domNode: ParentNode) {\n    this.subIndex = 0\n    this.init()\n  }\n\n  init(): void {\n    this.subMenuItems = this.domNode.querySelectorAll('li')\n    this.addListeners()\n  }\n\n  gotoSubIndex(idx: number): void {\n    if (idx === this.subMenuItems.length) {\n      idx = 0\n    } else if (idx < 0) {\n      idx = this.subMenuItems.length - 1\n    }\n    ;(this.subMenuItems[idx] as HTMLElement).focus()\n    this.subIndex = idx\n  }\n\n  addListeners(): void {\n    const parentNode = this.parent.domNode\n    Array.prototype.forEach.call(this.subMenuItems, (el: Element) => {\n      el.addEventListener('keydown', (event: KeyboardEvent) => {\n        let prevDef = false\n        switch (event.code) {\n          case EVENT_CODE.down: {\n            this.gotoSubIndex(this.subIndex + 1)\n            prevDef = true\n            break\n          }\n          case EVENT_CODE.up: {\n            this.gotoSubIndex(this.subIndex - 1)\n            prevDef = true\n            break\n          }\n          case EVENT_CODE.tab: {\n            triggerEvent(parentNode as HTMLElement, 'mouseleave')\n            break\n          }\n          case EVENT_CODE.enter:\n          case EVENT_CODE.numpadEnter:\n          case EVENT_CODE.space: {\n            prevDef = true\n            ;(event.currentTarget as HTMLElement).click()\n            break\n          }\n        }\n        if (prevDef) {\n          event.preventDefault()\n          event.stopPropagation()\n        }\n        return false\n      })\n    })\n  }\n}\n\nexport default SubMenu\n"], "names": [], "mappings": ";;;AAEA,MAAM,OAAO,CAAC;AACd,EAAE,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE;AAC/B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AAChB,GAAG;AACH,EAAE,IAAI,GAAG;AACT,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;AACxB,GAAG;AACH,EAAE,YAAY,CAAC,GAAG,EAAE;AACpB,IAAI,IAAI,GAAG,KAAK,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;AAC1C,MAAM,GAAG,GAAG,CAAC,CAAC;AACd,KAAK,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE;AACxB,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;AACzC,KAAK;AAEL,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;AACnC,IAAI,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;AACxB,GAAG;AACH,EAAE,YAAY,GAAG;AACjB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC3C,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,KAAK;AAC5D,MAAM,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,KAAK,KAAK;AAChD,QAAQ,IAAI,OAAO,GAAG,KAAK,CAAC;AAC5B,QAAQ,QAAQ,KAAK,CAAC,IAAI;AAC1B,UAAU,KAAK,UAAU,CAAC,IAAI,EAAE;AAChC,YAAY,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACjD,YAAY,OAAO,GAAG,IAAI,CAAC;AAC3B,YAAY,MAAM;AAClB,WAAW;AACX,UAAU,KAAK,UAAU,CAAC,EAAE,EAAE;AAC9B,YAAY,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACjD,YAAY,OAAO,GAAG,IAAI,CAAC;AAC3B,YAAY,MAAM;AAClB,WAAW;AACX,UAAU,KAAK,UAAU,CAAC,GAAG,EAAE;AAC/B,YAAY,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;AACnD,YAAY,MAAM;AAClB,WAAW;AACX,UAAU,KAAK,UAAU,CAAC,KAAK,CAAC;AAChC,UAAU,KAAK,UAAU,CAAC,WAAW,CAAC;AACtC,UAAU,KAAK,UAAU,CAAC,KAAK,EAAE;AACjC,YAAY,OAAO,GAAG,IAAI,CAAC;AAC3B,YAAY,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACxC,YAAY,MAAM;AAClB,WAAW;AACX,SAAS;AACT,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,KAAK,CAAC,cAAc,EAAE,CAAC;AACjC,UAAU,KAAK,CAAC,eAAe,EAAE,CAAC;AAClC,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO,CAAC,CAAC;AACT,KAAK,CAAC,CAAC;AACP,GAAG;AACH;;;;"}