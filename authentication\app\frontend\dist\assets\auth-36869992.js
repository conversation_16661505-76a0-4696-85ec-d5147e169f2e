import{s as e}from"./index-bcbc0702.js";import"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const t={getAuthTypes:()=>e({url:"/auth/types",method:"get"}),checkQQExists:t=>e({url:"/auth/check-qq",method:"post",data:{qq:t}}),submitBuaaAuth:t=>e({url:"/auth/sso",method:"post",data:t}),buaaSSO(e){return this.submitBuaaAuth(e)},sendEmailCode:t=>e({url:"/api/auth/email",method:"post",data:{email:t}}),sendSmsCode:t=>e({url:"/api/auth/sms",method:"post",data:{phone:t}}),verifyEmailCode:(t,a)=>e({url:"/api/auth/verify-email",method:"post",data:{email:t,code:a}}),verifySmsCode:(t,a)=>e({url:"/api/auth/verify-sms",method:"post",data:{phone:t,code:a}}),completeBuaaAuth:t=>e({url:"/api/auth/complete-buaa",method:"post",data:t}),freshman:{getStatus:()=>e({url:"/api/freshman/status",method:"get"}),getCaptcha:()=>e({url:"/api/freshman/captcha",method:"get"}),queryAdmission:t=>e({url:"/api/freshman/query",method:"post",data:t}),complete:t=>e({url:"/api/freshman/verify",method:"post",data:t})},submitFreshmanAuth(t){const{qq:a,category:o,admission_info:i,email:s,phone:r,email_code:u,phone_code:h}=t,m={category:"新生",admissionInfo:i,email:s,phone:r,emailCode:u,phoneCode:h,uploadedImages:[],extraInfo:{}};return e.post("/api/authenticate",{qq:a,verificationData:m})},submitExternalAuth(t){const{qq:a,real_name:o,school:i,student_id:s,email:r,phone:u,department:h,grade:m,photo:d,extraInfo:n}=t,p={category:"外校",realName:o,school:i,studentId:s,email:r,phone:u,department:h,grade:m,uploadedImages:d?[d]:[],extraInfo:n||{}};return e.post("/api/authenticate",{qq:a,verificationData:p})},submitInviteAuth(t){const{qq:a,invite_code:o,real_name:i,school:s,student_id:r,email:u,phone:h}=t,m={category:"其他",inviteCode:o,realName:i,school:s,studentId:r,email:u,phone:h,uploadedImages:[],extraInfo:{}};return e.post("/api/authenticate",{qq:a,verificationData:m})},verifyInviteCode:t=>e.get(`/api/invites/verify/${encodeURIComponent(t)}`),login:t=>e({url:"/api/auth/login",method:"post",data:t}),logout:()=>e({url:"/api/auth/logout",method:"post"}),getUserInfo:()=>e({url:"/api/auth/user",method:"get"}),refreshToken:()=>e({url:"/api/auth/refresh",method:"post"})};export{t as authApi};
