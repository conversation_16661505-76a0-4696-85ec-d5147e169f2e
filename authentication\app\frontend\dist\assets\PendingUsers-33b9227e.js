import{_ as e,r as a}from"./index-bcbc0702.js";/* empty css                      *//* empty css                    *//* empty css                   *//* empty css                     *//* empty css                  *//* empty css                    *//* empty css                       *//* empty css                *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        */import"./el-tooltip-4ed993c7.js";/* empty css                     *//* empty css                 *//* empty css                             *//* empty css               *//* empty css                        *//* empty css                       */import{r as l,d as t,A as s,B as i,C as u,F as o,G as n,D as d,u as r,I as p,am as m,b2 as c,al as _,H as f,ae as v,af as g,J as b,E as y,z as h,K as w,L as k,ai as x,Y as V,au as C,av as j,b3 as q,a$ as z,Z as U,b4 as R,b5 as $,ba as Y,b7 as D,bb as O,bl as Q,bm as S,bn as I,bo as F,b6 as M,bp as B,b8 as P,b9 as E,X as K,bq as L,br as N,aj as T,aY as A,bs as H,bt as J,ak as G,_ as X,bu as Z,bv as W,bw as ee,bx as ae,bc as le,a1 as te,by as se,O as ie,be as ue,bz as oe,bh as ne,ac as de,ah as re}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const pe={class:"pending-users"},me={class:"card-header"},ce={class:"header-actions"},_e={class:"search-bar"},fe={class:"quick-filters"},ve={class:"stats-bar"},ge={class:"table-container"},be={class:"expand-content"},ye={key:0,class:"uploaded-images"},he={class:"image-name"},we={key:1,class:"no-images"},ke={class:"audit-log"},xe={class:"log-content"},Ve={key:0},Ce={class:"log-operator"},je={class:"audit-history"},qe={class:"log-content"},ze={key:0},Ue={class:"log-operator"},Re={class:"pagination"},$e={key:0,class:"user-detail"},Ye={key:0,class:"materials-grid"},De={class:"material-info"},Oe={class:"material-name"},Qe={class:"material-size"},Se={class:"material-time"},Ie={key:1,class:"no-materials"},Fe={class:"log-header"},Me={key:0,class:"log-comment"},Be={class:"log-footer"},Pe={class:"log-operator"},Ee={key:0,class:"log-ip"},Ke={key:0,class:"images-viewer"},Le={class:"image-slide"},Ne={class:"image-info"},Te={key:1,class:"no-images"},Ae=e({__name:"PendingUsers",setup(e){const Ae=l(!1),He=l(!1),Je=l([]),Ge=l([]),Xe=l(!1),Ze=l(!1),We=l(!1),ea=l(!1),aa=l(""),la=l(null),ta=l(null),sa=l([]),ia=l("basic"),ua=l("all"),oa=t({keyword:"",category:"",status:"",dateRange:[]}),na=t({page:1,size:20,total:0}),da=t({prop:"",order:""}),ra=t({total:0,today:0,avgTime:0,approvalRate:0}),pa=t({action:"",comment:"",supplement_requirements:[],other_requirements:"",send_notification:!0}),ma=t({comment:""}),ca={action:[{required:!0,message:"请选择审核结果",trigger:"change"}],comment:[{required:!0,message:"请输入审核意见",trigger:"blur"},{min:10,message:"审核意见至少10个字符",trigger:"blur"}]},_a=async()=>{var e;Ae.value=!0;try{const l={page:na.page,size:na.size,...oa,...da,quick_filter:ua.value};oa.dateRange&&2===oa.dateRange.length&&(l.start_date=oa.dateRange[0],l.end_date=oa.dateRange[1]);const t=await a.get("/api/pending",{params:l});if(t.success){let a=(t.data.pendingUsers||t.data.pending_users||[]).map(e=>({...e,id:e.id,qq:e.qq,real_name:e.realName??e.real_name,school:e.school,category:e.category,status:e.status,email:e.email,phone:e.phone,department:e.department,grade:e.grade,student_id:e.studentId??e.student_id,uploaded_images:e.uploadedImages??e.uploaded_images??[],audit_log:e.auditLog??e.audit_log,audit_history:e.auditHistory??e.audit_history??[],resubmission_count:e.resubmissionCount??e.resubmission_count??0,is_resubmission:e.isResubmission??e.is_resubmission??!1,created_at:e.createdAt??e.created_at,updated_at:e.updatedAt??e.updated_at}));if("resubmission"===ua.value){const e=a.filter(e=>e.is_resubmission||e.resubmission_count>0),l=a.filter(e=>!(e.is_resubmission||e.resubmission_count>0));a=[...e,...l]}Je.value=a,na.total=(null==(e=t.data.pagination)?void 0:e.total)||0,Object.assign(ra,t.data.stats||{})}}catch(l){y.error("加载待审核用户失败")}finally{Ae.value=!1}},fa=()=>{na.page=1,_a()},va=()=>{Object.assign(oa,{keyword:"",category:"",status:"",dateRange:[]}),na.page=1,_a()},ga=e=>{ua.value=e,na.page=1,_a()},ba=e=>{Ge.value=e},ya=({prop:e,order:a})=>{da.prop=e,da.order=a,_a()},ha=(e,a)=>{},wa=e=>({buaa:"primary",freshman:"success",external:"warning",invite:"info"}[e]||""),ka=e=>({buaa:"本校学生",freshman:"新生",external:"外校学生",invite:"邀请码"}[e]||e),xa=e=>({pending:"warning",reviewing:"primary",need_supplement:"info",approved:"success",rejected:"danger"}[e]||""),Va=e=>({pending:"待审核",reviewing:"审核中",need_supplement:"需补充材料",approved:"已通过",rejected:"已拒绝"}[e]||e),Ca=e=>({"提交申请":"primary","开始审核":"warning","审核通过":"success","审核拒绝":"danger","需补充材料":"info","补充材料":"warning","添加备注":"info"}[e]||"primary"),ja=e=>e?new Date(e).toLocaleString("zh-CN"):"",qa=e=>e?e.replace(/(\d{3})(\d{4})(\d{4})/,"$1****$3"):"",za=e=>e?e<1024?e+"B":e<1048576?(e/1024).toFixed(1)+"KB":(e/1048576).toFixed(1)+"MB":"",Ua=e=>{if(!e)return[];try{return JSON.parse(e)}catch{return[{time:(new Date).toISOString(),action:"提交申请",comment:e}]}},Ra=e=>{ta.value={...e},Ze.value=!0,ia.value="basic"},$a=e=>{la.value=e,aa.value=`审核通过 - ${e.real_name}`,Object.assign(pa,{action:"approve",comment:"",supplement_requirements:[],other_requirements:"",send_notification:!0}),Xe.value=!0},Ya=e=>{la.value=e,aa.value=`审核拒绝 - ${e.real_name}`,Object.assign(pa,{action:"reject",comment:"",supplement_requirements:[],other_requirements:"",send_notification:!0}),Xe.value=!0},Da=e=>{la.value=e,aa.value=`需补充材料 - ${e.real_name}`,Object.assign(pa,{action:"supplement",comment:"",supplement_requirements:[],other_requirements:"",send_notification:!0}),Xe.value=!0},Oa=async()=>{const e=document.querySelector("#auditFormRef");if(e)try{await e.validate(),He.value=!0;const l={approved:"approve"===pa.action,reason:pa.comment};(await a.post(`/api/pending/${la.value.id}/audit`,l)).success&&(y.success("审核操作成功"),Xe.value=!1,Ze.value=!1,_a())}catch(l){!1!==l&&y.error("审核操作失败")}finally{He.value=!1}},Qa=async()=>{if(ma.comment.trim())try{(await a.post(`/api/admin/pending/${la.value.id}/comment`,{comment:ma.comment})).success&&(y.success("添加备注成功"),ea.value=!1,_a())}catch(e){y.error("添加备注失败")}else y.warning("请输入备注内容")},Sa=async()=>{if(0!==Ge.value.length)try{await h.confirm(`确定要批量通过 ${Ge.value.length} 个用户的审核吗？`,"确认操作");const e=Ge.value.map(e=>e.id);(await a.post("/api/pending/batch-audit",{ids:e,action:"approve",data:{reason:"批量审核通过"}})).success&&(y.success("批量审核通过成功"),_a())}catch(e){"cancel"!==e&&y.error("批量审核失败")}},Ia=async()=>{if(0!==Ge.value.length)try{const{value:e}=await h.prompt(`确定要批量拒绝 ${Ge.value.length} 个用户的审核吗？请输入拒绝原因：`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/.{10,}/,inputErrorMessage:"拒绝原因至少10个字符"}),l=Ge.value.map(e=>e.id);(await a.post("/api/pending/batch-audit",{ids:l,action:"reject",data:{reason:e}})).success&&(y.success("批量审核拒绝成功"),_a())}catch(e){"cancel"!==e&&y.error("批量审核失败")}};return s(()=>{_a()}),(e,a)=>{const l=w,t=k,s=x,y=V,h=C,da=j,Fa=q,Ma=z,Ba=U,Pa=R,Ea=$,Ka=Y,La=D,Na=O,Ta=Q,Aa=S,Ha=I,Ja=F,Ga=M,Xa=B,Za=P,Wa=E,el=K,al=L,ll=N,tl=T,sl=A,il=H,ul=J,ol=G,nl=X,dl=Z,rl=W,pl=ee,ml=ae,cl=le;return i(),u("div",pe,[o(el,null,{header:n(()=>[d("div",me,[a[30]||(a[30]=d("span",null,"待审核用户",-1)),d("div",ce,[o(t,{type:"success",disabled:0===Ge.value.length,onClick:Sa},{default:n(()=>[o(l,null,{default:n(()=>[o(r(te))]),_:1}),a[27]||(a[27]=p(" 批量通过 ",-1))]),_:1,__:[27]},8,["disabled"]),o(t,{type:"danger",disabled:0===Ge.value.length,onClick:Ia},{default:n(()=>[o(l,null,{default:n(()=>[o(r(se))]),_:1}),a[28]||(a[28]=p(" 批量拒绝 ",-1))]),_:1,__:[28]},8,["disabled"]),o(t,{onClick:_a},{default:n(()=>[o(l,null,{default:n(()=>[o(r(ie))]),_:1}),a[29]||(a[29]=p(" 刷新 ",-1))]),_:1,__:[29]})])])]),default:n(()=>[d("div",_e,[o(Ba,{gutter:20},{default:n(()=>[o(y,{span:5},{default:n(()=>[o(s,{modelValue:oa.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>oa.keyword=e),placeholder:"搜索QQ号、姓名、邮箱、手机号",clearable:"",onKeyup:m(fa,["enter"])},{prefix:n(()=>[o(l,null,{default:n(()=>[o(r(ue))]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(y,{span:3},{default:n(()=>[o(da,{modelValue:oa.category,"onUpdate:modelValue":a[1]||(a[1]=e=>oa.category=e),placeholder:"用户类型",clearable:""},{default:n(()=>[o(h,{label:"本校学生",value:"buaa"}),o(h,{label:"新生",value:"freshman"}),o(h,{label:"外校学生",value:"external"}),o(h,{label:"邀请码",value:"invite"})]),_:1},8,["modelValue"])]),_:1}),o(y,{span:3},{default:n(()=>[o(da,{modelValue:oa.status,"onUpdate:modelValue":a[2]||(a[2]=e=>oa.status=e),placeholder:"审核状态",clearable:""},{default:n(()=>[o(h,{label:"待审核",value:"pending"}),o(h,{label:"审核中",value:"reviewing"}),o(h,{label:"需补充材料",value:"need_supplement"})]),_:1},8,["modelValue"])]),_:1}),o(y,{span:4},{default:n(()=>[o(Fa,{modelValue:oa.dateRange,"onUpdate:modelValue":a[3]||(a[3]=e=>oa.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",size:"default"},null,8,["modelValue"])]),_:1}),o(y,{span:4},{default:n(()=>[o(t,{type:"primary",onClick:fa},{default:n(()=>a[31]||(a[31]=[p("搜索",-1)])),_:1,__:[31]}),o(t,{onClick:va},{default:n(()=>a[32]||(a[32]=[p("重置",-1)])),_:1,__:[32]})]),_:1}),o(y,{span:5},{default:n(()=>[d("div",fe,[o(Ma,null,{default:n(()=>[o(t,{type:"all"===ua.value?"primary":"",onClick:a[4]||(a[4]=e=>ga("all"))},{default:n(()=>a[33]||(a[33]=[p(" 全部 ",-1)])),_:1,__:[33]},8,["type"]),o(t,{type:"urgent"===ua.value?"primary":"",onClick:a[5]||(a[5]=e=>ga("urgent"))},{default:n(()=>a[34]||(a[34]=[p(" 紧急 ",-1)])),_:1,__:[34]},8,["type"]),o(t,{type:"today"===ua.value?"primary":"",onClick:a[6]||(a[6]=e=>ga("today"))},{default:n(()=>a[35]||(a[35]=[p(" 今日 ",-1)])),_:1,__:[35]},8,["type"]),o(t,{type:"resubmission"===ua.value?"primary":"",onClick:a[7]||(a[7]=e=>ga("resubmission"))},{default:n(()=>a[36]||(a[36]=[p(" 复审优先 ",-1)])),_:1,__:[36]},8,["type"])]),_:1})])]),_:1})]),_:1})]),d("div",ve,[o(Ba,{gutter:20},{default:n(()=>[o(y,{span:6},{default:n(()=>[o(Pa,{title:"待审核总数",value:ra.total},null,8,["value"])]),_:1}),o(y,{span:6},{default:n(()=>[o(Pa,{title:"今日新增",value:ra.today},null,8,["value"])]),_:1}),o(y,{span:6},{default:n(()=>[o(Pa,{title:"平均处理时间",value:ra.avgTime,suffix:"小时"},null,8,["value"])]),_:1}),o(y,{span:6},{default:n(()=>[o(Pa,{title:"通过率",value:ra.approvalRate,suffix:"%"},null,8,["value"])]),_:1})]),_:1})]),d("div",ge,[c((i(),_(Za,{data:Je.value,stripe:"",border:"",height:"600",style:{width:"100%"},onSelectionChange:ba,onSortChange:ya,onExpandChange:ha},{default:n(()=>[o(Ea,{type:"selection",width:"50",fixed:"left"}),o(Ea,{type:"expand",width:"50",fixed:"left"},{default:n(({row:e})=>[d("div",be,[o(Ba,{gutter:20},{default:n(()=>[o(y,{span:12},{default:n(()=>[a[37]||(a[37]=d("h4",null,"基本信息",-1)),o(Na,{column:1,border:"",size:"small"},{default:n(()=>[o(Ka,{label:"QQ号"},{default:n(()=>[p(f(e.qq),1)]),_:2},1024),o(Ka,{label:"真实姓名"},{default:n(()=>[p(f(e.real_name),1)]),_:2},1024),o(Ka,{label:"用户类型"},{default:n(()=>[o(La,{type:wa(e.category),size:"small"},{default:n(()=>[p(f(ka(e.category)),1)]),_:2},1032,["type"])]),_:2},1024),o(Ka,{label:"学校"},{default:n(()=>[p(f(e.school),1)]),_:2},1024),o(Ka,{label:"学号/考生号"},{default:n(()=>[p(f(e.student_id||"未填写"),1)]),_:2},1024),o(Ka,{label:"邮箱"},{default:n(()=>[p(f(e.email),1)]),_:2},1024),o(Ka,{label:"手机号"},{default:n(()=>[p(f(qa(e.phone)),1)]),_:2},1024),o(Ka,{label:"申请时间"},{default:n(()=>[p(f(ja(e.created_at)),1)]),_:2},1024)]),_:2},1024)]),_:2,__:[37]},1024),o(y,{span:12},{default:n(()=>[a[39]||(a[39]=d("h4",null,"上传材料",-1)),e.uploaded_images&&e.uploaded_images.length>0?(i(),u("div",ye,[(i(!0),u(v,null,g(e.uploaded_images,(a,l)=>(i(),u("div",{key:l,class:"image-item"},[o(Ta,{src:a.url,alt:a.name,fit:"cover",style:{width:"120px",height:"80px","border-radius":"4px"},"preview-src-list":e.uploaded_images.map(e=>e.url),"initial-index":l},null,8,["src","alt","preview-src-list","initial-index"]),d("p",he,f(a.name),1)]))),128))])):(i(),u("div",we,[o(Aa,{description:"暂无上传材料","image-size":60})])),a[40]||(a[40]=d("h4",{style:{"margin-top":"20px"}},"审核记录",-1)),d("div",ke,[o(Ja,null,{default:n(()=>[(i(!0),u(v,null,g(Ua(e.audit_log),(l,t)=>(i(),_(Ha,{key:t,timestamp:l.time,type:Ca(l.action)},{default:n(()=>[d("div",xe,[d("strong",null,f(l.action),1),l.comment?(i(),u("p",Ve,f(l.comment),1)):b("",!0),d("span",Ce,"操作人: "+f(l.operator||"系统"),1)]),a[38]||(a[38]=d("h4",{style:{"margin-top":"20px"}},"复审历史",-1)),d("div",je,[o(Ja,null,{default:n(()=>[(i(!0),u(v,null,g(e.audit_history||[],(e,a)=>(i(),_(Ha,{key:a,timestamp:ja(e.time),type:Ca(e.action)},{default:n(()=>[d("div",qe,[d("strong",null,f(e.action),1),e.comment?(i(),u("p",ze,f(e.comment),1)):b("",!0),d("span",Ue,"操作人: "+f(e.operator||"系统"),1)])]),_:2},1032,["timestamp","type"]))),128))]),_:2},1024)])]),_:2,__:[38]},1032,["timestamp","type"]))),128))]),_:2},1024)])]),_:2,__:[39,40]},1024)]),_:2},1024)])]),_:1}),o(Ea,{prop:"id",label:"ID",width:"80",sortable:"custom"}),o(Ea,{prop:"qq",label:"QQ号",width:"120",sortable:"custom"},{default:n(({row:e})=>[o(Ga,{type:"primary",onClick:a=>Ra(e)},{default:n(()=>[p(f(e.qq),1)]),_:2},1032,["onClick"])]),_:1}),o(Ea,{prop:"real_name",label:"姓名",width:"100",sortable:"custom"}),o(Ea,{prop:"category",label:"用户类型",width:"100",sortable:"custom"},{default:n(({row:e})=>[o(La,{type:wa(e.category),size:"small"},{default:n(()=>[p(f(ka(e.category)),1)]),_:2},1032,["type"])]),_:1}),o(Ea,{prop:"school",label:"学校",width:"180","show-overflow-tooltip":""}),o(Ea,{prop:"student_id",label:"学号/考生号",width:"120","show-overflow-tooltip":""}),o(Ea,{prop:"email",label:"邮箱",width:"200","show-overflow-tooltip":""},{default:n(({row:e})=>[o(Ga,{href:`mailto:${e.email}`,type:"primary"},{default:n(()=>[p(f(e.email),1)]),_:2},1032,["href"])]),_:1}),o(Ea,{prop:"phone",label:"手机号",width:"120"},{default:n(({row:e})=>[p(f(qa(e.phone)),1)]),_:1}),o(Ea,{prop:"created_at",label:"申请时间",width:"160",sortable:"custom"},{default:n(({row:e})=>[p(f(ja(e.created_at)),1)]),_:1}),o(Ea,{label:"复审",width:"80"},{default:n(({row:e})=>[e.resubmission_count>0||e.is_resubmission?(i(),_(Xa,{key:0,value:"复审",type:"warning"})):b("",!0)]),_:1}),o(Ea,{prop:"status",label:"状态",width:"100",sortable:"custom"},{default:n(({row:e})=>[o(La,{type:xa(e.status),size:"small"},{default:n(()=>[p(f(Va(e.status)),1)]),_:2},1032,["type"])]),_:1}),o(Ea,{label:"材料",width:"80"},{default:n(({row:e})=>[o(Xa,{value:e.uploaded_images?e.uploaded_images.length:0,max:99,type:"primary"},{default:n(()=>[o(t,{size:"small",circle:"",onClick:a=>{return l=e,sa.value=l.uploaded_images||[],void(We.value=!0);var l}},{default:n(()=>[o(l,null,{default:n(()=>[o(r(oe))]),_:1})]),_:2},1032,["onClick"])]),_:2},1032,["value"])]),_:1}),o(Ea,{label:"操作",width:"300",fixed:"right"},{default:n(({row:e})=>[o(Ma,null,{default:n(()=>[o(t,{size:"small",onClick:a=>Ra(e)},{default:n(()=>[o(l,null,{default:n(()=>[o(r(ne))]),_:1}),a[41]||(a[41]=p(" 查看 ",-1))]),_:2,__:[41]},1032,["onClick"]),o(t,{size:"small",type:"success",onClick:a=>$a(e)},{default:n(()=>[o(l,null,{default:n(()=>[o(r(te))]),_:1}),a[42]||(a[42]=p(" 通过 ",-1))]),_:2,__:[42]},1032,["onClick"]),o(t,{size:"small",type:"warning",onClick:a=>Da(e)},{default:n(()=>[o(l,null,{default:n(()=>[o(r(de))]),_:1}),a[43]||(a[43]=p(" 补充 ",-1))]),_:2,__:[43]},1032,["onClick"]),o(t,{size:"small",type:"danger",onClick:a=>Ya(e)},{default:n(()=>[o(l,null,{default:n(()=>[o(r(se))]),_:1}),a[44]||(a[44]=p(" 拒绝 ",-1))]),_:2,__:[44]},1032,["onClick"]),o(t,{size:"small",type:"info",onClick:a=>{return l=e,la.value=l,ma.comment="",void(ea.value=!0);var l}},{default:n(()=>[o(l,null,{default:n(()=>[o(r(re))]),_:1}),a[45]||(a[45]=p(" 备注 ",-1))]),_:2,__:[45]},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[cl,Ae.value]])]),d("div",Re,[o(Wa,{"current-page":na.page,"onUpdate:currentPage":a[8]||(a[8]=e=>na.page=e),"page-size":na.size,"onUpdate:pageSize":a[9]||(a[9]=e=>na.size=e),"page-sizes":[10,20,50,100],total:na.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_a,onCurrentChange:_a},null,8,["current-page","page-size","total"])])]),_:1}),o(nl,{modelValue:Xe.value,"onUpdate:modelValue":a[16]||(a[16]=e=>Xe.value=e),title:aa.value,width:"600px","close-on-click-modal":!1},{footer:n(()=>[o(t,{onClick:a[15]||(a[15]=e=>Xe.value=!1)},{default:n(()=>a[54]||(a[54]=[p("取消",-1)])),_:1,__:[54]}),o(t,{type:"primary",onClick:Oa,loading:He.value},{default:n(()=>a[55]||(a[55]=[p("确定",-1)])),_:1,__:[55]},8,["loading"])]),default:n(()=>[o(ol,{model:pa,rules:ca,ref:"auditFormRef","label-width":"100px"},{default:n(()=>[o(tl,{label:"审核结果",prop:"action"},{default:n(()=>[o(ll,{modelValue:pa.action,"onUpdate:modelValue":a[10]||(a[10]=e=>pa.action=e)},{default:n(()=>[o(al,{value:"approve"},{default:n(()=>a[46]||(a[46]=[p("通过",-1)])),_:1,__:[46]}),o(al,{value:"reject"},{default:n(()=>a[47]||(a[47]=[p("拒绝",-1)])),_:1,__:[47]}),o(al,{value:"supplement"},{default:n(()=>a[48]||(a[48]=[p("需补充材料",-1)])),_:1,__:[48]})]),_:1},8,["modelValue"])]),_:1}),o(tl,{label:"审核意见",prop:"comment"},{default:n(()=>[o(s,{modelValue:pa.comment,"onUpdate:modelValue":a[11]||(a[11]=e=>pa.comment=e),type:"textarea",rows:4,placeholder:"请输入审核意见（必填）",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),"supplement"===pa.action?(i(),_(tl,{key:0,label:"补充要求",prop:"supplement_requirements"},{default:n(()=>[o(il,{modelValue:pa.supplement_requirements,"onUpdate:modelValue":a[12]||(a[12]=e=>pa.supplement_requirements=e)},{default:n(()=>[o(sl,{value:"student_card"},{default:n(()=>a[49]||(a[49]=[p("学生证照片",-1)])),_:1,__:[49]}),o(sl,{value:"id_card"},{default:n(()=>a[50]||(a[50]=[p("身份证照片",-1)])),_:1,__:[50]}),o(sl,{value:"admission_notice"},{default:n(()=>a[51]||(a[51]=[p("录取通知书",-1)])),_:1,__:[51]}),o(sl,{value:"enrollment_certificate"},{default:n(()=>a[52]||(a[52]=[p("在读证明",-1)])),_:1,__:[52]}),o(sl,{value:"other"},{default:n(()=>a[53]||(a[53]=[p("其他材料",-1)])),_:1,__:[53]})]),_:1},8,["modelValue"])]),_:1})):b("",!0),pa.supplement_requirements.includes("other")?(i(),_(tl,{key:1,label:"其他要求"},{default:n(()=>[o(s,{modelValue:pa.other_requirements,"onUpdate:modelValue":a[13]||(a[13]=e=>pa.other_requirements=e),placeholder:"请详细说明需要补充的其他材料",maxlength:"200"},null,8,["modelValue"])]),_:1})):b("",!0),o(tl,{label:"发送通知"},{default:n(()=>[o(ul,{modelValue:pa.send_notification,"onUpdate:modelValue":a[14]||(a[14]=e=>pa.send_notification=e),"active-text":"发送邮件/短信通知","inactive-text":"不发送通知"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),o(nl,{modelValue:Ze.value,"onUpdate:modelValue":a[22]||(a[22]=e=>Ze.value=e),title:"用户详情",width:"900px"},{footer:n(()=>[o(t,{onClick:a[18]||(a[18]=e=>Ze.value=!1)},{default:n(()=>a[56]||(a[56]=[p("关闭",-1)])),_:1,__:[56]}),o(t,{type:"success",onClick:a[19]||(a[19]=e=>$a(ta.value))},{default:n(()=>a[57]||(a[57]=[p("通过",-1)])),_:1,__:[57]}),o(t,{type:"warning",onClick:a[20]||(a[20]=e=>Da(ta.value))},{default:n(()=>a[58]||(a[58]=[p("补充材料",-1)])),_:1,__:[58]}),o(t,{type:"danger",onClick:a[21]||(a[21]=e=>Ya(ta.value))},{default:n(()=>a[59]||(a[59]=[p("拒绝",-1)])),_:1,__:[59]})]),default:n(()=>[ta.value?(i(),u("div",$e,[o(rl,{modelValue:ia.value,"onUpdate:modelValue":a[17]||(a[17]=e=>ia.value=e)},{default:n(()=>[o(dl,{label:"基本信息",name:"basic"},{default:n(()=>[o(Na,{column:2,border:""},{default:n(()=>[o(Ka,{label:"QQ号"},{default:n(()=>[p(f(ta.value.qq),1)]),_:1}),o(Ka,{label:"真实姓名"},{default:n(()=>[p(f(ta.value.real_name),1)]),_:1}),o(Ka,{label:"用户类型"},{default:n(()=>[o(La,{type:wa(ta.value.category)},{default:n(()=>[p(f(ka(ta.value.category)),1)]),_:1},8,["type"])]),_:1}),o(Ka,{label:"学校"},{default:n(()=>[p(f(ta.value.school),1)]),_:1}),o(Ka,{label:"学号/考生号"},{default:n(()=>[p(f(ta.value.student_id||"未填写"),1)]),_:1}),o(Ka,{label:"邮箱地址"},{default:n(()=>[p(f(ta.value.email),1)]),_:1}),o(Ka,{label:"手机号"},{default:n(()=>[p(f(qa(ta.value.phone)),1)]),_:1}),o(Ka,{label:"申请时间"},{default:n(()=>[p(f(ja(ta.value.created_at)),1)]),_:1}),o(Ka,{label:"当前状态"},{default:n(()=>[o(La,{type:xa(ta.value.status)},{default:n(()=>[p(f(Va(ta.value.status)),1)]),_:1},8,["type"])]),_:1}),o(Ka,{label:"最后更新"},{default:n(()=>[p(f(ja(ta.value.updated_at)),1)]),_:1})]),_:1})]),_:1}),o(dl,{label:"上传材料",name:"materials"},{default:n(()=>[ta.value.uploaded_images&&ta.value.uploaded_images.length>0?(i(),u("div",Ye,[(i(!0),u(v,null,g(ta.value.uploaded_images,(e,a)=>(i(),u("div",{key:a,class:"material-item"},[o(el,null,{default:n(()=>[o(Ta,{src:e.url,alt:e.name,fit:"cover",style:{width:"100%",height:"200px"},"preview-src-list":ta.value.uploaded_images.map(e=>e.url),"initial-index":a},null,8,["src","alt","preview-src-list","initial-index"]),d("div",De,[d("p",Oe,f(e.name),1),d("p",Qe,f(za(e.size)),1),d("p",Se,f(ja(e.upload_time)),1)])]),_:2},1024)]))),128))])):(i(),u("div",Ie,[o(Aa,{description:"暂无上传材料"})]))]),_:1}),o(dl,{label:"审核记录",name:"audit"},{default:n(()=>[o(Ja,null,{default:n(()=>[(i(!0),u(v,null,g(Ua(ta.value.audit_log),(e,a)=>(i(),_(Ha,{key:a,timestamp:e.time,type:Ca(e.action)},{default:n(()=>[o(el,{class:"log-card"},{default:n(()=>[d("div",Fe,[d("strong",null,f(e.action),1),o(La,{type:Ca(e.action),size:"small"},{default:n(()=>[p(f(e.status),1)]),_:2},1032,["type"])]),e.comment?(i(),u("p",Me,f(e.comment),1)):b("",!0),d("div",Be,[d("span",Pe,"操作人: "+f(e.operator||"系统"),1),e.ip?(i(),u("span",Ee,"IP: "+f(e.ip),1)):b("",!0)])]),_:2},1024)]),_:2},1032,["timestamp","type"]))),128))]),_:1})]),_:1})]),_:1},8,["modelValue"])])):b("",!0)]),_:1},8,["modelValue"]),o(nl,{modelValue:We.value,"onUpdate:modelValue":a[23]||(a[23]=e=>We.value=e),title:"上传材料",width:"800px"},{default:n(()=>[sa.value&&sa.value.length>0?(i(),u("div",Ke,[o(ml,{height:"400px","indicator-position":"outside"},{default:n(()=>[(i(!0),u(v,null,g(sa.value,(e,a)=>(i(),_(pl,{key:a},{default:n(()=>[d("div",Le,[o(Ta,{src:e.url,alt:e.name,fit:"contain",style:{width:"100%",height:"100%"}},null,8,["src","alt"]),d("div",Ne,[d("h4",null,f(e.name),1),d("p",null,"大小: "+f(za(e.size)),1),d("p",null,"上传时间: "+f(ja(e.upload_time)),1)])])]),_:2},1024))),128))]),_:1})])):(i(),u("div",Te,[o(Aa,{description:"暂无上传材料"})]))]),_:1},8,["modelValue"]),o(nl,{modelValue:ea.value,"onUpdate:modelValue":a[26]||(a[26]=e=>ea.value=e),title:"添加备注",width:"500px"},{footer:n(()=>[o(t,{onClick:a[25]||(a[25]=e=>ea.value=!1)},{default:n(()=>a[60]||(a[60]=[p("取消",-1)])),_:1,__:[60]}),o(t,{type:"primary",onClick:Qa},{default:n(()=>a[61]||(a[61]=[p("确定",-1)])),_:1,__:[61]})]),default:n(()=>[o(ol,{model:ma,ref:"commentFormRef","label-width":"80px"},{default:n(()=>[o(tl,{label:"备注内容",prop:"comment"},{default:n(()=>[o(s,{modelValue:ma.comment,"onUpdate:modelValue":a[24]||(a[24]=e=>ma.comment=e),type:"textarea",rows:4,placeholder:"请输入备注内容",maxlength:"300","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-cee87e38"]]);export{Ae as default};
