import{_ as a,u as s,a as e}from"./index-bcbc0702.js";/* empty css                   *//* empty css               *//* empty css                */import{p as l,r as d,w as t,A as i,B as c,C as n,D as o,F as r,G as u,u as v,H as b,V as p,W as f,J as _,I as m,K as h,L as C,X as g,Y as z,Z as F,_ as y,$ as A,a0 as k,a1 as x,a2 as E,a3 as w,a4 as j,a5 as Q,a6 as S,a7 as V,a8 as D,a9 as I,aa as H,ab as R,ac as W}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const $={class:"page-container"},q={class:"header"},B={class:"header-content"},G={class:"logo"},J={class:"logo-icon"},K={class:"logo-text"},L={class:"nav-actions"},O={class:"main-content"},U={class:"auth-options"},X={class:"options-grid"},Y={class:"card-header"},Z={class:"card-icon"},M={class:"card-content"},N={class:"card-features"},P={class:"feature-item"},T={class:"feature-item"},aa={key:0,class:"disabled-overlay"},sa={class:"card-header"},ea={class:"card-icon"},la={class:"card-content"},da={class:"card-features"},ta={class:"feature-item"},ia={class:"feature-item"},ca={key:0,class:"disabled-overlay"},na={key:1,class:"disabled-overlay"},oa={class:"card-header"},ra={class:"card-icon"},ua={class:"card-content"},va={class:"card-features"},ba={class:"feature-item"},pa={class:"feature-item"},fa={key:0,class:"disabled-overlay"},_a={class:"card-header"},ma={class:"card-icon"},ha={class:"card-content"},Ca={class:"card-features"},ga={class:"feature-item"},za={class:"feature-item"},Fa={key:0,class:"disabled-overlay"},ya={class:"info-section"},Aa={class:"info-icon"},ka={class:"info-icon"},xa={class:"info-icon"},Ea={class:"footer"},wa={class:"footer-content"},ja={class:"maintenance-content"},Qa=a({__name:"Home",setup(a){const Qa=s(),Sa=e(),Va=l(()=>Sa.configs),Da=d(!1),Ia=d(""),Ha=l(()=>Sa.isInFreshmanWindow()),Ra=a=>{const s={buaa:"buaa_enabled",freshman:"freshman_enabled",external:"external_enabled",invite:"invite_enabled"}[a];if(!Sa.isFeatureEnabled(s))return Da.value=!0,void(Ia.value=Sa.getFeatureDisabledReason(s));if("freshman"===a&&!Ha.value)return Da.value=!0,void(Ia.value=Sa.getFeatureDisabledReason("freshman_enabled"));Qa.push({buaa:"/auth/buaa",freshman:"/auth/freshman",external:"/auth/external",invite:"/auth/invite"}[a])};t(()=>Va.value.site_name,a=>{a&&(document.title=a)},{immediate:!0});return i(async()=>{await Sa.init(),Va.value.site_name&&(document.title=Va.value.site_name),Va.value.maintenance_mode&&(Da.value=!0,Ia.value=Va.value.maintenance_message||"系统正在维护中，请稍后再试")}),(a,s)=>{const e=h,l=C,d=g,t=z,i=F,Qa=y;return c(),n("div",$,[o("header",q,[o("div",B,[o("div",G,[o("div",J,[r(e,{size:"32",color:"#fff"},{default:u(()=>[r(v(A))]),_:1})]),o("div",K,[o("h1",null,b(Va.value.site_name||"QQ身份认证系统"),1),s[7]||(s[7]=o("span",{class:"logo-subtitle"},"QQ Authentication System",-1))])]),o("div",L,[r(l,{type:"primary",size:"large",class:"admin-btn",onClick:s[0]||(s[0]=s=>a.$router.push("/admin/login"))},{default:u(()=>[r(e,null,{default:u(()=>[r(v(k))]),_:1}),s[8]||(s[8]=o("span",{class:"btn-text"},"管理员登录",-1))]),_:1,__:[8]})])])]),o("main",O,[o("section",U,[s[28]||(s[28]=o("div",{class:"section-header"},[o("h3",{class:"section-title"},"选择认证方式"),o("p",{class:"section-subtitle"},"根据您的身份选择对应的认证类型")],-1)),o("div",X,[o("div",{class:p(["auth-card",{disabled:!Va.value.buaa_enabled}]),onClick:s[1]||(s[1]=a=>Ra("buaa"))},[o("div",Y,[o("div",Z,[r(e,{size:"32",color:"#409EFF"},{default:u(()=>[r(v(A))]),_:1})]),s[9]||(s[9]=o("div",{class:"card-badge"},"推荐",-1))]),o("div",M,[s[12]||(s[12]=f('<h3 class="card-title" data-v-4459ab3b>本校学生</h3><p class="card-description" data-v-4459ab3b> 本科生/研究生身份认证 </p><div class="card-methods" data-v-4459ab3b><span class="method-tag primary" data-v-4459ab3b>SSO登录</span><span class="method-tag success" data-v-4459ab3b>邮箱验证</span></div>',3)),o("div",N,[o("div",P,[r(e,{size:"16",color:"#67C23A"},{default:u(()=>[r(v(x))]),_:1}),s[10]||(s[10]=o("span",null,"即时认证",-1))]),o("div",T,[r(e,{size:"16",color:"#67C23A"},{default:u(()=>[r(v(x))]),_:1}),s[11]||(s[11]=o("span",null,"安全可靠",-1))])])]),Va.value.buaa_enabled?_("",!0):(c(),n("div",aa,[r(e,{size:"24",color:"#F56C6C"},{default:u(()=>[r(v(E))]),_:1}),s[13]||(s[13]=o("span",null,"暂未开放",-1))]))],2),o("div",{class:p(["auth-card",{disabled:!Va.value.freshman_enabled||!Ha.value}]),onClick:s[2]||(s[2]=a=>Ra("freshman"))},[o("div",sa,[o("div",ea,[r(e,{size:"32",color:"#67C23A"},{default:u(()=>[r(v(w))]),_:1})]),s[14]||(s[14]=o("div",{class:"card-badge new"},"新生专用",-1))]),o("div",la,[s[17]||(s[17]=f('<h3 class="card-title" data-v-4459ab3b>本科新生</h3><p class="card-description" data-v-4459ab3b> 录取信息临时身份认证 </p><div class="card-methods" data-v-4459ab3b><span class="method-tag warning" data-v-4459ab3b>录取查询</span><span class="method-tag success" data-v-4459ab3b>邮箱验证</span></div>',3)),o("div",da,[o("div",ta,[r(e,{size:"16",color:"#E6A23C"},{default:u(()=>[r(v(j))]),_:1}),s[15]||(s[15]=o("span",null,"录取信息验证",-1))]),o("div",ia,[r(e,{size:"16",color:"#E6A23C"},{default:u(()=>[r(v(Q))]),_:1}),s[16]||(s[16]=o("span",null,"临时认证",-1))])])]),Va.value.freshman_enabled?Ha.value?_("",!0):(c(),n("div",na,[r(e,{size:"24",color:"#E6A23C"},{default:u(()=>[r(v(Q))]),_:1}),s[19]||(s[19]=o("span",null,"不在开放时间",-1))])):(c(),n("div",ca,[r(e,{size:"24",color:"#F56C6C"},{default:u(()=>[r(v(E))]),_:1}),s[18]||(s[18]=o("span",null,"暂未开放",-1))]))],2),o("div",{class:p(["auth-card",{disabled:!Va.value.external_enabled}]),onClick:s[3]||(s[3]=a=>Ra("external"))},[o("div",oa,[o("div",ra,[r(e,{size:"32",color:"#E6A23C"},{default:u(()=>[r(v(S))]),_:1})])]),o("div",ua,[s[22]||(s[22]=f('<h3 class="card-title" data-v-4459ab3b>外校学生</h3><p class="card-description" data-v-4459ab3b> 其他高校学生身份认证 </p><div class="card-methods" data-v-4459ab3b><span class="method-tag info" data-v-4459ab3b>学籍证明</span><span class="method-tag success" data-v-4459ab3b>邮箱验证</span></div>',3)),o("div",va,[o("div",ba,[r(e,{size:"16",color:"#909399"},{default:u(()=>[r(v(V))]),_:1}),s[20]||(s[20]=o("span",null,"拍照验证",-1))]),o("div",pa,[r(e,{size:"16",color:"#409EFF"},{default:u(()=>[r(v(D))]),_:1}),s[21]||(s[21]=o("span",null,"材料审核",-1))])])]),Va.value.external_enabled?_("",!0):(c(),n("div",fa,[r(e,{size:"24",color:"#F56C6C"},{default:u(()=>[r(v(E))]),_:1}),s[23]||(s[23]=o("span",null,"暂未开放",-1))]))],2),o("div",{class:p(["auth-card",{disabled:!Va.value.invite_enabled}]),onClick:s[4]||(s[4]=a=>Ra("invite"))},[o("div",_a,[o("div",ma,[r(e,{size:"32",color:"#F56C6C"},{default:u(()=>[r(v(I))]),_:1})])]),o("div",ha,[s[26]||(s[26]=f('<h3 class="card-title" data-v-4459ab3b>邀请认证</h3><p class="card-description" data-v-4459ab3b> 通过邀请码进行认证 </p><div class="card-methods" data-v-4459ab3b><span class="method-tag danger" data-v-4459ab3b>邀请码</span><span class="method-tag success" data-v-4459ab3b>邮箱验证</span></div>',3)),o("div",Ca,[o("div",ga,[r(e,{size:"16",color:"#F56C6C"},{default:u(()=>[r(v(H))]),_:1}),s[24]||(s[24]=o("span",null,"邀请注册",-1))]),o("div",za,[r(e,{size:"16",color:"#409EFF"},{default:u(()=>[r(v(D))]),_:1}),s[25]||(s[25]=o("span",null,"材料审核",-1))])])]),Va.value.invite_enabled?_("",!0):(c(),n("div",Fa,[r(e,{size:"24",color:"#F56C6C"},{default:u(()=>[r(v(E))]),_:1}),s[27]||(s[27]=o("span",null,"暂未开放",-1))]))],2)])]),o("section",ya,[r(i,{gutter:20},{default:u(()=>[r(t,{span:8},{default:u(()=>[r(d,{class:"info-card",shadow:"never"},{default:u(()=>[o("div",Aa,[r(e,{size:"32",color:"#409EFF"},{default:u(()=>[r(v(E))]),_:1})]),s[29]||(s[29]=o("h4",null,"安全保障",-1)),s[30]||(s[30]=o("p",null,"采用AES-256加密，确保您的个人信息安全",-1))]),_:1,__:[29,30]})]),_:1}),r(t,{span:8},{default:u(()=>[r(d,{class:"info-card",shadow:"never"},{default:u(()=>[o("div",ka,[r(e,{size:"32",color:"#67C23A"},{default:u(()=>[r(v(x))]),_:1})]),s[31]||(s[31]=o("h4",null,"快速认证",-1)),s[32]||(s[32]=o("p",null,"多种认证方式，快速完成身份验证",-1))]),_:1,__:[31,32]})]),_:1}),r(t,{span:8},{default:u(()=>[r(d,{class:"info-card",shadow:"never"},{default:u(()=>[o("div",xa,[r(e,{size:"32",color:"#E6A23C"},{default:u(()=>[r(v(R))]),_:1})]),s[33]||(s[33]=o("h4",null,"24小时服务",-1)),s[34]||(s[34]=o("p",null,"全天候提供身份认证服务",-1))]),_:1,__:[33,34]})]),_:1})]),_:1})])]),o("footer",Ea,[o("div",wa,[o("p",null,"© 2025 "+b(Va.value.site_name),1),o("p",null,"如有问题请联系管理员: "+b(Va.value.contact_email),1)])]),r(Qa,{modelValue:Da.value,"onUpdate:modelValue":s[6]||(s[6]=a=>Da.value=a),title:"系统维护提示",width:"400px","close-on-click-modal":!1},{footer:u(()=>[r(l,{type:"primary",onClick:s[5]||(s[5]=a=>Da.value=!1)},{default:u(()=>s[35]||(s[35]=[m(" 我知道了 ",-1)])),_:1,__:[35]})]),default:u(()=>[o("div",ja,[r(e,{size:"48",color:"#E6A23C"},{default:u(()=>[r(v(W))]),_:1}),o("p",null,b(Ia.value),1)])]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-4459ab3b"]]);export{Qa as default};
