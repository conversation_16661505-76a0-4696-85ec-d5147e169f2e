
// THIS FILE IS GENERATED, DO NOT EDIT IT!
@use "sass:string";
$icon-font-codes: (
    aggregation: string.unquote("\"\\") + string.unquote("f101\""),
    arrows: string.unquote("\"\\") + string.unquote("f102\""),
    asc: string.unquote("\"\\") + string.unquote("f103\""),
    cancel: string.unquote("\"\\") + string.unquote("f104\""),
    chart: string.unquote("\"\\") + string.unquote("f105\""),
    checkbox-checked: string.unquote("\"\\") + string.unquote("f106\""),
    checkbox-indeterminate: string.unquote("\"\\") + string.unquote("f107\""),
    checkbox-unchecked: string.unquote("\"\\") + string.unquote("f108\""),
    color-picker: string.unquote("\"\\") + string.unquote("f109\""),
    columns: string.unquote("\"\\") + string.unquote("f10a\""),
    contracted: string.unquote("\"\\") + string.unquote("f10b\""),
    copy: string.unquote("\"\\") + string.unquote("f10c\""),
    cross: string.unquote("\"\\") + string.unquote("f10d\""),
    csv: string.unquote("\"\\") + string.unquote("f10e\""),
    cut: string.unquote("\"\\") + string.unquote("f10f\""),
    desc: string.unquote("\"\\") + string.unquote("f110\""),
    excel: string.unquote("\"\\") + string.unquote("f111\""),
    expanded: string.unquote("\"\\") + string.unquote("f112\""),
    eye-slash: string.unquote("\"\\") + string.unquote("f113\""),
    eye: string.unquote("\"\\") + string.unquote("f114\""),
    filter: string.unquote("\"\\") + string.unquote("f115\""),
    first: string.unquote("\"\\") + string.unquote("f116\""),
    grip: string.unquote("\"\\") + string.unquote("f117\""),
    group: string.unquote("\"\\") + string.unquote("f118\""),
    last: string.unquote("\"\\") + string.unquote("f119\""),
    left: string.unquote("\"\\") + string.unquote("f11a\""),
    linked: string.unquote("\"\\") + string.unquote("f11b\""),
    loading: string.unquote("\"\\") + string.unquote("f11c\""),
    maximize: string.unquote("\"\\") + string.unquote("f11d\""),
    menu: string.unquote("\"\\") + string.unquote("f11e\""),
    minimize: string.unquote("\"\\") + string.unquote("f11f\""),
    next: string.unquote("\"\\") + string.unquote("f120\""),
    none: string.unquote("\"\\") + string.unquote("f121\""),
    not-allowed: string.unquote("\"\\") + string.unquote("f122\""),
    paste: string.unquote("\"\\") + string.unquote("f123\""),
    pin: string.unquote("\"\\") + string.unquote("f124\""),
    pivot: string.unquote("\"\\") + string.unquote("f125\""),
    previous: string.unquote("\"\\") + string.unquote("f126\""),
    radio-button-off: string.unquote("\"\\") + string.unquote("f127\""),
    radio-button-on: string.unquote("\"\\") + string.unquote("f128\""),
    right: string.unquote("\"\\") + string.unquote("f129\""),
    save: string.unquote("\"\\") + string.unquote("f12a\""),
    small-down: string.unquote("\"\\") + string.unquote("f12b\""),
    small-left: string.unquote("\"\\") + string.unquote("f12c\""),
    small-right: string.unquote("\"\\") + string.unquote("f12d\""),
    small-up: string.unquote("\"\\") + string.unquote("f12e\""),
    tick: string.unquote("\"\\") + string.unquote("f12f\""),
    tree-closed: string.unquote("\"\\") + string.unquote("f130\""),
    tree-indeterminate: string.unquote("\"\\") + string.unquote("f131\""),
    tree-open: string.unquote("\"\\") + string.unquote("f132\""),
    unlinked: string.unquote("\"\\") + string.unquote("f133\""),
    up: string.unquote("\"\\") + string.unquote("f134\""),
    down: string.unquote("\"\\") + string.unquote("f135\""),
    plus: string.unquote("\"\\") + string.unquote("f136\""),
    minus: string.unquote("\"\\") + string.unquote("f137\""),
)
