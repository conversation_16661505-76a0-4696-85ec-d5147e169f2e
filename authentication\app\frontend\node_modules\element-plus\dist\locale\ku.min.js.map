{"version": 3, "file": "ku.min.js", "sources": ["../../../../packages/locale/lang/ku.ts"], "sourcesContent": ["export default {\n  name: 'ku',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON>ma<PERSON>',\n      clear: 'Paqij bike',\n    },\n    datepicker: {\n      now: '<PERSON>ha',\n      today: 'Îro',\n      cancel: 'Betal bike',\n      clear: '<PERSON>qij bike',\n      confirm: 'Tema<PERSON>',\n      selectDate: 'Dîrokê bibijêre',\n      selectTime: 'Demê bibijêre',\n      startDate: 'Dîrok<PERSON> Destpêkê',\n      startTime: '<PERSON><PERSON>tpêkê',\n      endDate: 'Dîroka Dawî',\n      endTime: 'Dema Dawî',\n      prevYear: 'Sala <PERSON>',\n      nextYear: '<PERSON>a <PERSON>',\n      prevMonth: '<PERSON>ha <PERSON>',\n      nextMonth: '<PERSON><PERSON>',\n      year: 'Sal',\n      month1: 'Rêbendan',\n      month2: 'Reşemeh',\n      month3: 'Adar',\n      month4: 'Avrêl',\n      month5: 'Gulan',\n      month6: '<PERSON><PERSON><PERSON><PERSON>',\n      month7: 'T<PERSON><PERSON><PERSON>',\n      month8: '<PERSON>av<PERSON><PERSON>',\n      month9: '<PERSON><PERSON><PERSON>',\n      month10: 'Kew<PERSON>êr',\n      month11: 'Sarmawaz',\n      month12: 'Be<PERSON><PERSON>bar',\n      // week: 'week',\n      weeks: {\n        sun: 'Yek',\n        mon: 'Duş',\n        tue: 'Sêş',\n        wed: 'Çar',\n        thu: 'Pên',\n        fri: 'În',\n        sat: 'Şem',\n      },\n      months: {\n        jan: 'Rêb',\n        feb: 'Reş',\n        mar: 'Ada',\n        apr: 'Avr',\n        may: 'Gul',\n        jun: 'Pûş',\n        jul: 'Tîr',\n        aug: 'Gil',\n        sep: 'Rez',\n        oct: 'Kew',\n        nov: 'Sar',\n        dec: 'Ber',\n      },\n    },\n    select: {\n      loading: 'Bardibe',\n      noMatch: 'Li hembere ve agahî tune',\n      noData: 'Agahî tune',\n      placeholder: 'Bibijêre',\n    },\n    mention: {\n      loading: 'Bardibe',\n    },\n    cascader: {\n      noMatch: 'Li hembere ve agahî tune',\n      loading: 'Bardibe',\n      placeholder: 'Bibijêre',\n      noData: 'Agahî tune',\n    },\n    pagination: {\n      goto: 'Biçe',\n      pagesize: '/rupel',\n      total: 'Tevahî {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Peyam',\n      confirm: 'Temam',\n      cancel: 'Betal bike',\n      error: 'Beyana çewt',\n    },\n    upload: {\n      deleteTip: 'ji bo rake pêl \"delete\" bike',\n      delete: 'Rake',\n      preview: 'Pêşdîtin',\n      continue: 'Berdewam',\n    },\n    table: {\n      emptyText: 'Agahî tune',\n      confirmFilter: 'Piştrast bike',\n      resetFilter: 'Jê bibe',\n      clearFilter: 'Hemû',\n      sumText: 'Kom',\n    },\n    tree: {\n      emptyText: 'Agahî tune',\n    },\n    transfer: {\n      noMatch: 'Li hembere ve agahî tune',\n      noData: 'Agahî tune',\n      titles: ['Lîste 1', 'Lîste 2'],\n      filterPlaceholder: 'Binivîse',\n      noCheckedFormat: '{total} lib',\n      hasCheckedFormat: '{checked}/{total} bijartin',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,0BAA0B,CAAC,UAAU,CAAC,qBAAqB,CAAC,SAAS,CAAC,0BAA0B,CAAC,SAAS,CAAC,qBAAqB,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,6BAA6B,CAAC,MAAM,CAAC,eAAe,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,6BAA6B,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,iBAAiB,CAAC,aAAa,CAAC,eAAe,CAAC,aAAa,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}