import{_ as e,r as a}from"./index-bcbc0702.js";/* empty css                *//* empty css                    *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                     *//* empty css                       *//* empty css                  */import{r as l,d as t,A as s,B as d,C as i,F as o,G as n,D as u,u as m,I as c,J as _,ae as r,af as p,al as f,H as b,am as h,E as v,K as V,L as g,bt as y,aj as x,b3 as U,b7 as w,ai as k,ak as j,bu as M,bA as Q,au as H,av as C,bv as Y,X as O,az as D,O as E}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const $={class:"system-config"},z={class:"card-header"},S={class:"header-actions"},A={class:"config-section"},B={style:{"margin-bottom":"10px"}},I={key:0,style:{"margin-left":"20px"}},K={class:"config-section"},P={class:"config-section"},q={class:"config-section"},F={class:"config-section"},G=e({__name:"SystemConfig",setup(e){const G=l("auth"),J=l(!1),L=l(""),N=t({auth:{buaa_enabled:!0,buaa_sso_enabled:!0,buaa_email_enabled:!0,freshman_enabled:!0,freshman_available:[],freshman_maintenance_windows:[],external_enabled:!0,invite_enabled:!0,id6_verify_enabled:!0},mail:{from:"<EMAIL>",from_name:"北航QQ身份认证系统",code_expire:5,rate_limit:60},sms:{code_expire:5,rate_limit:60,daily_limit:10},security:{max_attempts:5,lockout_duration:30,session_timeout:120,captcha_type:"image"},system:{site_name:"北航QQ身份认证系统",site_description:"北京航空航天大学QQ身份认证系统",contact_email:"<EMAIL>",maintenance_mode:!1,maintenance_message:"系统正在维护中，请稍后再试"}}),X=async()=>{try{const e=await a.get("/api/config");if(e.success){const a=e.data.configs||e.data;Object.keys(a).forEach(e=>{const l=e.split(".");if(2===l.length){const[t,s]=l;if(N[t]&&N[t].hasOwnProperty(s)){const l=a[e];N[t][s]=void 0!==l.value?l.value:l}}})}}catch(e){v.error("加载配置失败")}},R=async()=>{try{const e=[];Object.keys(N).forEach(a=>{Object.keys(N[a]).forEach(l=>{const t=`${a}.${l}`;e.push({key:t,value:N[a][l],description:`${a}配置项: ${l}`})})});(await a.post("/api/config/batch-update",{configs:e})).success&&(v.success("保存配置成功"),X())}catch(e){v.error("保存配置失败")}},T=()=>{if(L.value.trim()){/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]-([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(L.value.trim())?(N.auth.freshman_maintenance_windows.push(L.value.trim()),L.value="",J.value=!1):v.error("时间格式不正确，请使用 HH:MM-HH:MM 格式")}else J.value=!1};return s(()=>{X()}),(e,a)=>{const l=V,t=g,s=y,v=x,W=U,Z=w,ee=k,ae=j,le=M,te=Q,se=H,de=C,ie=Y,oe=O;return d(),i("div",$,[o(oe,null,{header:n(()=>[u("div",z,[a[29]||(a[29]=u("span",null,"系统配置",-1)),u("div",S,[o(t,{type:"primary",onClick:R},{default:n(()=>[o(l,null,{default:n(()=>[o(m(D))]),_:1}),a[27]||(a[27]=c(" 保存所有配置 ",-1))]),_:1,__:[27]}),o(t,{onClick:X},{default:n(()=>[o(l,null,{default:n(()=>[o(m(E))]),_:1}),a[28]||(a[28]=c(" 刷新 ",-1))]),_:1,__:[28]})])])]),default:n(()=>[o(ie,{modelValue:G.value,"onUpdate:modelValue":a[26]||(a[26]=e=>G.value=e),type:"border-card"},{default:n(()=>[o(le,{label:"认证配置",name:"auth"},{default:n(()=>[u("div",A,[a[41]||(a[41]=u("h3",null,"认证入口控制",-1)),o(ae,{model:N.auth,"label-width":"150px"},{default:n(()=>[o(v,{label:"本校学生认证"},{default:n(()=>[u("div",B,[o(s,{modelValue:N.auth.buaa_enabled,"onUpdate:modelValue":a[0]||(a[0]=e=>N.auth.buaa_enabled=e),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),a[30]||(a[30]=u("span",{style:{"margin-left":"10px"}},"总开关",-1))]),N.auth.buaa_enabled?(d(),i("div",I,[o(v,{label:"SSO认证","label-width":"100px"},{default:n(()=>[o(s,{modelValue:N.auth.buaa_sso_enabled,"onUpdate:modelValue":a[1]||(a[1]=e=>N.auth.buaa_sso_enabled=e),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),a[31]||(a[31]=u("div",{class:"config-desc"},"北航统一身份认证",-1))]),_:1,__:[31]}),o(v,{label:"邮箱认证","label-width":"100px"},{default:n(()=>[o(s,{modelValue:N.auth.buaa_email_enabled,"onUpdate:modelValue":a[2]||(a[2]=e=>N.auth.buaa_email_enabled=e),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),a[32]||(a[32]=u("div",{class:"config-desc"},"北航邮箱认证",-1))]),_:1,__:[32]})])):_("",!0),a[33]||(a[33]=u("div",{class:"config-desc"},"控制本校学生认证入口是否显示",-1))]),_:1,__:[33]}),o(v,{label:"新生认证"},{default:n(()=>[o(s,{modelValue:N.auth.freshman_enabled,"onUpdate:modelValue":a[3]||(a[3]=e=>N.auth.freshman_enabled=e),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),a[34]||(a[34]=u("div",{class:"config-desc"},"控制新生认证入口是否显示",-1))]),_:1,__:[34]}),o(v,{label:"外校学生认证"},{default:n(()=>[o(s,{modelValue:N.auth.external_enabled,"onUpdate:modelValue":a[4]||(a[4]=e=>N.auth.external_enabled=e),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),a[35]||(a[35]=u("div",{class:"config-desc"},"控制外校学生认证入口是否显示",-1))]),_:1,__:[35]}),o(v,{label:"邀请码认证"},{default:n(()=>[o(s,{modelValue:N.auth.invite_enabled,"onUpdate:modelValue":a[5]||(a[5]=e=>N.auth.invite_enabled=e),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),a[36]||(a[36]=u("div",{class:"config-desc"},"控制邀请码认证入口是否显示",-1))]),_:1,__:[36]}),o(v,{label:"新生认证时间段"},{default:n(()=>[o(W,{modelValue:N.auth.freshman_available,"onUpdate:modelValue":a[6]||(a[6]=e=>N.auth.freshman_available=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"]),a[37]||(a[37]=u("div",{class:"config-desc"},"设置新生认证功能的开放时间段",-1))]),_:1,__:[37]}),o(v,{label:"新生认证维护窗口"},{default:n(()=>[(d(!0),i(r,null,p(N.auth.freshman_maintenance_windows,(e,a)=>(d(),f(Z,{key:a,closable:"",onClose:e=>(e=>{N.auth.freshman_maintenance_windows.splice(e,1)})(a),style:{"margin-right":"10px","margin-bottom":"10px"}},{default:n(()=>[c(b(e),1)]),_:2},1032,["onClose"]))),128)),J.value?(d(),f(ee,{key:0,modelValue:L.value,"onUpdate:modelValue":a[7]||(a[7]=e=>L.value=e),size:"small",style:{width:"150px","margin-right":"10px"},placeholder:"如: 01:00-07:00",onKeyup:h(T,["enter"]),onBlur:T},null,8,["modelValue"])):(d(),f(t,{key:1,size:"small",onClick:a[8]||(a[8]=e=>J.value=!0)},{default:n(()=>a[38]||(a[38]=[c(" + 添加维护窗口 ",-1)])),_:1,__:[38]})),a[39]||(a[39]=u("div",{class:"config-desc"},"设置每日系统维护时间段，格式：HH:MM-HH:MM",-1))]),_:1,__:[39]}),o(v,{label:"身份证验证"},{default:n(()=>[o(s,{modelValue:N.auth.id6_verify_enabled,"onUpdate:modelValue":a[9]||(a[9]=e=>N.auth.id6_verify_enabled=e),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),a[40]||(a[40]=u("div",{class:"config-desc"},"是否启用身份证后6位验证",-1))]),_:1,__:[40]})]),_:1},8,["model"])])]),_:1}),o(le,{label:"邮件配置",name:"mail"},{default:n(()=>[u("div",K,[a[48]||(a[48]=u("h3",null,"邮件服务配置",-1)),o(ae,{model:N.mail,"label-width":"150px"},{default:n(()=>[o(v,{label:"发件人地址"},{default:n(()=>[o(ee,{modelValue:N.mail.from,"onUpdate:modelValue":a[10]||(a[10]=e=>N.mail.from=e),placeholder:"<EMAIL>"},null,8,["modelValue"]),a[42]||(a[42]=u("div",{class:"config-desc"},"系统发送邮件时使用的发件人地址",-1))]),_:1,__:[42]}),o(v,{label:"发件人名称"},{default:n(()=>[o(ee,{modelValue:N.mail.from_name,"onUpdate:modelValue":a[11]||(a[11]=e=>N.mail.from_name=e),placeholder:"北航QQ身份认证系统"},null,8,["modelValue"]),a[43]||(a[43]=u("div",{class:"config-desc"},"邮件中显示的发件人名称",-1))]),_:1,__:[43]}),o(v,{label:"验证码有效期"},{default:n(()=>[o(te,{modelValue:N.mail.code_expire,"onUpdate:modelValue":a[12]||(a[12]=e=>N.mail.code_expire=e),min:1,max:30,"controls-position":"right"},null,8,["modelValue"]),a[44]||(a[44]=u("span",{style:{"margin-left":"10px"}},"分钟",-1)),a[45]||(a[45]=u("div",{class:"config-desc"},"邮箱验证码的有效时间",-1))]),_:1,__:[44,45]}),o(v,{label:"发送频率限制"},{default:n(()=>[o(te,{modelValue:N.mail.rate_limit,"onUpdate:modelValue":a[13]||(a[13]=e=>N.mail.rate_limit=e),min:30,max:300,"controls-position":"right"},null,8,["modelValue"]),a[46]||(a[46]=u("span",{style:{"margin-left":"10px"}},"秒",-1)),a[47]||(a[47]=u("div",{class:"config-desc"},"同一邮箱两次发送验证码的最小间隔",-1))]),_:1,__:[46,47]})]),_:1},8,["model"])])]),_:1}),o(le,{label:"短信配置",name:"sms"},{default:n(()=>[u("div",P,[a[55]||(a[55]=u("h3",null,"短信服务配置",-1)),o(ae,{model:N.sms,"label-width":"150px"},{default:n(()=>[o(v,{label:"验证码有效期"},{default:n(()=>[o(te,{modelValue:N.sms.code_expire,"onUpdate:modelValue":a[14]||(a[14]=e=>N.sms.code_expire=e),min:1,max:30,"controls-position":"right"},null,8,["modelValue"]),a[49]||(a[49]=u("span",{style:{"margin-left":"10px"}},"分钟",-1)),a[50]||(a[50]=u("div",{class:"config-desc"},"短信验证码的有效时间",-1))]),_:1,__:[49,50]}),o(v,{label:"发送频率限制"},{default:n(()=>[o(te,{modelValue:N.sms.rate_limit,"onUpdate:modelValue":a[15]||(a[15]=e=>N.sms.rate_limit=e),min:30,max:300,"controls-position":"right"},null,8,["modelValue"]),a[51]||(a[51]=u("span",{style:{"margin-left":"10px"}},"秒",-1)),a[52]||(a[52]=u("div",{class:"config-desc"},"同一手机号两次发送验证码的最小间隔",-1))]),_:1,__:[51,52]}),o(v,{label:"每日发送限制"},{default:n(()=>[o(te,{modelValue:N.sms.daily_limit,"onUpdate:modelValue":a[16]||(a[16]=e=>N.sms.daily_limit=e),min:1,max:50,"controls-position":"right"},null,8,["modelValue"]),a[53]||(a[53]=u("span",{style:{"margin-left":"10px"}},"条",-1)),a[54]||(a[54]=u("div",{class:"config-desc"},"同一手机号每日最大发送次数",-1))]),_:1,__:[53,54]})]),_:1},8,["model"])])]),_:1}),o(le,{label:"安全配置",name:"security"},{default:n(()=>[u("div",q,[a[63]||(a[63]=u("h3",null,"安全防护配置",-1)),o(ae,{model:N.security,"label-width":"150px"},{default:n(()=>[o(v,{label:"登录失败限制"},{default:n(()=>[o(te,{modelValue:N.security.max_attempts,"onUpdate:modelValue":a[17]||(a[17]=e=>N.security.max_attempts=e),min:3,max:20,"controls-position":"right"},null,8,["modelValue"]),a[56]||(a[56]=u("span",{style:{"margin-left":"10px"}},"次",-1)),a[57]||(a[57]=u("div",{class:"config-desc"},"连续登录失败后锁定账户的次数",-1))]),_:1,__:[56,57]}),o(v,{label:"锁定时间"},{default:n(()=>[o(te,{modelValue:N.security.lockout_duration,"onUpdate:modelValue":a[18]||(a[18]=e=>N.security.lockout_duration=e),min:5,max:1440,"controls-position":"right"},null,8,["modelValue"]),a[58]||(a[58]=u("span",{style:{"margin-left":"10px"}},"分钟",-1)),a[59]||(a[59]=u("div",{class:"config-desc"},"账户锁定后的解锁时间",-1))]),_:1,__:[58,59]}),o(v,{label:"会话超时"},{default:n(()=>[o(te,{modelValue:N.security.session_timeout,"onUpdate:modelValue":a[19]||(a[19]=e=>N.security.session_timeout=e),min:30,max:1440,"controls-position":"right"},null,8,["modelValue"]),a[60]||(a[60]=u("span",{style:{"margin-left":"10px"}},"分钟",-1)),a[61]||(a[61]=u("div",{class:"config-desc"},"用户会话的超时时间",-1))]),_:1,__:[60,61]}),o(v,{label:"验证码类型"},{default:n(()=>[o(de,{modelValue:N.security.captcha_type,"onUpdate:modelValue":a[20]||(a[20]=e=>N.security.captcha_type=e),placeholder:"请选择"},{default:n(()=>[o(se,{label:"图形验证码",value:"image"}),o(se,{label:"滑动验证",value:"slide"}),o(se,{label:"点击验证",value:"click"})]),_:1},8,["modelValue"]),a[62]||(a[62]=u("div",{class:"config-desc"},"人机验证的类型",-1))]),_:1,__:[62]})]),_:1},8,["model"])])]),_:1}),o(le,{label:"系统配置",name:"system"},{default:n(()=>[u("div",F,[a[69]||(a[69]=u("h3",null,"系统基础配置",-1)),o(ae,{model:N.system,"label-width":"150px"},{default:n(()=>[o(v,{label:"系统名称"},{default:n(()=>[o(ee,{modelValue:N.system.site_name,"onUpdate:modelValue":a[21]||(a[21]=e=>N.system.site_name=e),placeholder:"北航QQ身份认证系统"},null,8,["modelValue"]),a[64]||(a[64]=u("div",{class:"config-desc"},"系统显示的名称",-1))]),_:1,__:[64]}),o(v,{label:"系统描述"},{default:n(()=>[o(ee,{modelValue:N.system.site_description,"onUpdate:modelValue":a[22]||(a[22]=e=>N.system.site_description=e),type:"textarea",rows:3,placeholder:"北京航空航天大学QQ身份认证系统"},null,8,["modelValue"]),a[65]||(a[65]=u("div",{class:"config-desc"},"系统的描述信息",-1))]),_:1,__:[65]}),o(v,{label:"联系邮箱"},{default:n(()=>[o(ee,{modelValue:N.system.contact_email,"onUpdate:modelValue":a[23]||(a[23]=e=>N.system.contact_email=e),placeholder:"<EMAIL>"},null,8,["modelValue"]),a[66]||(a[66]=u("div",{class:"config-desc"},"用户遇到问题时的联系邮箱",-1))]),_:1,__:[66]}),o(v,{label:"维护模式"},{default:n(()=>[o(s,{modelValue:N.system.maintenance_mode,"onUpdate:modelValue":a[24]||(a[24]=e=>N.system.maintenance_mode=e),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),a[67]||(a[67]=u("div",{class:"config-desc"},"开启后系统将显示维护页面",-1))]),_:1,__:[67]}),o(v,{label:"维护提示"},{default:n(()=>[o(ee,{modelValue:N.system.maintenance_message,"onUpdate:modelValue":a[25]||(a[25]=e=>N.system.maintenance_message=e),type:"textarea",rows:3,placeholder:"系统正在维护中，请稍后再试"},null,8,["modelValue"]),a[68]||(a[68]=u("div",{class:"config-desc"},"维护模式下显示的提示信息",-1))]),_:1,__:[68]})]),_:1},8,["model"])])]),_:1})]),_:1},8,["modelValue"])]),_:1})])}}},[["__scopeId","data-v-f2276950"]]);export{G as default};
