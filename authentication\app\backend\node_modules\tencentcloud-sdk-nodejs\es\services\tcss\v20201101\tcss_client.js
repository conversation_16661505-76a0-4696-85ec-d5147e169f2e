import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("tcss.tencentcloudapi.com", "2020-11-01", clientConfig);
    }
    async DescribeImageAuthorizedInfo(req, cb) {
        return this.request("DescribeImageAuthorizedInfo", req, cb);
    }
    async CreateK8sApiAbnormalEventExportJob(req, cb) {
        return this.request("CreateK8sApiAbnormalEventExportJob", req, cb);
    }
    async UpdateAndPublishNetworkFirewallPolicyYamlDetail(req, cb) {
        return this.request("UpdateAndPublishNetworkFirewallPolicyYamlDetail", req, cb);
    }
    async DescribeVirusScanTimeoutSetting(req, cb) {
        return this.request("DescribeVirusScanTimeoutSetting", req, cb);
    }
    async DescribeVirusSummary(req, cb) {
        return this.request("DescribeVirusSummary", req, cb);
    }
    async ModifyVulDefenceSetting(req, cb) {
        return this.request("ModifyVulDefenceSetting", req, cb);
    }
    async CreateAbnormalProcessRulesExportJob(req, cb) {
        return this.request("CreateAbnormalProcessRulesExportJob", req, cb);
    }
    async DescribeAssetHostList(req, cb) {
        return this.request("DescribeAssetHostList", req, cb);
    }
    async ScanComplianceScanFailedAssets(req, cb) {
        return this.request("ScanComplianceScanFailedAssets", req, cb);
    }
    async DescribeIndexList(req, cb) {
        return this.request("DescribeIndexList", req, cb);
    }
    async DescribePublicKey(req, cb) {
        return this.request("DescribePublicKey", req, cb);
    }
    async DescribeAccessControlDetail(req, cb) {
        return this.request("DescribeAccessControlDetail", req, cb);
    }
    async DescribeVirusAutoIsolateSampleDetail(req, cb) {
        return this.request("DescribeVirusAutoIsolateSampleDetail", req, cb);
    }
    async DescribeRiskSyscallEventsExport(req, cb) {
        return this.request("DescribeRiskSyscallEventsExport", req, cb);
    }
    async DescribeNetworkFirewallPolicyStatus(req, cb) {
        return this.request("DescribeNetworkFirewallPolicyStatus", req, cb);
    }
    async DescribeK8sApiAbnormalSummary(req, cb) {
        return this.request("DescribeK8sApiAbnormalSummary", req, cb);
    }
    async CreateVulDefenceEventExportJob(req, cb) {
        return this.request("CreateVulDefenceEventExportJob", req, cb);
    }
    async DescribeAssetAppServiceList(req, cb) {
        return this.request("DescribeAssetAppServiceList", req, cb);
    }
    async DeleteSearchTemplate(req, cb) {
        return this.request("DeleteSearchTemplate", req, cb);
    }
    async CreateVulImageExportJob(req, cb) {
        return this.request("CreateVulImageExportJob", req, cb);
    }
    async DescribeVulScanAuthorizedImageSummary(req, cb) {
        return this.request("DescribeVulScanAuthorizedImageSummary", req, cb);
    }
    async RemoveAssetImageRegistryRegistryDetail(req, cb) {
        return this.request("RemoveAssetImageRegistryRegistryDetail", req, cb);
    }
    async CreateRefreshTask(req, cb) {
        return this.request("CreateRefreshTask", req, cb);
    }
    async DescribeSearchExportList(req, cb) {
        return this.request("DescribeSearchExportList", req, cb);
    }
    async ModifySecLogKafkaUIN(req, cb) {
        return this.request("ModifySecLogKafkaUIN", req, cb);
    }
    async ModifyAssetImageRegistryScanStopOneKey(req, cb) {
        return this.request("ModifyAssetImageRegistryScanStopOneKey", req, cb);
    }
    async DescribeReverseShellWhiteLists(req, cb) {
        return this.request("DescribeReverseShellWhiteLists", req, cb);
    }
    async CreateComponentExportJob(req, cb) {
        return this.request("CreateComponentExportJob", req, cb);
    }
    async DescribeVirusAutoIsolateSampleDownloadURL(req, cb) {
        return this.request("DescribeVirusAutoIsolateSampleDownloadURL", req, cb);
    }
    async DescribeImageRegistryNamespaceList(req, cb) {
        return this.request("DescribeImageRegistryNamespaceList", req, cb);
    }
    async DescribeScanIgnoreVulList(req, cb) {
        return this.request("DescribeScanIgnoreVulList", req, cb);
    }
    async DescribeSecLogJoinObjectList(req, cb) {
        return this.request("DescribeSecLogJoinObjectList", req, cb);
    }
    async DescribeAssetComponentList(req, cb) {
        return this.request("DescribeAssetComponentList", req, cb);
    }
    async DescribeAssetImageScanStatus(req, cb) {
        return this.request("DescribeAssetImageScanStatus", req, cb);
    }
    async DescribeAgentDaemonSetCmd(req, cb) {
        return this.request("DescribeAgentDaemonSetCmd", req, cb);
    }
    async CreateK8sApiAbnormalRuleExportJob(req, cb) {
        return this.request("CreateK8sApiAbnormalRuleExportJob", req, cb);
    }
    async CreateNetworkFirewallPolicyDiscover(req, cb) {
        return this.request("CreateNetworkFirewallPolicyDiscover", req, cb);
    }
    async CreateClusterCheckTask(req, cb) {
        return this.request("CreateClusterCheckTask", req, cb);
    }
    async DescribeAccessControlEvents(req, cb) {
        return this.request("DescribeAccessControlEvents", req, cb);
    }
    async DescribeRiskDnsList(req, cb) {
        return this.request("DescribeRiskDnsList", req, cb);
    }
    async ModifyVirusScanSetting(req, cb) {
        return this.request("ModifyVirusScanSetting", req, cb);
    }
    async DescribeSecLogDeliveryKafkaOptions(req, cb) {
        return this.request("DescribeSecLogDeliveryKafkaOptions", req, cb);
    }
    async DescribeAssetImageRegistryVirusListExport(req, cb) {
        return this.request("DescribeAssetImageRegistryVirusListExport", req, cb);
    }
    async ConfirmNetworkFirewallPolicy(req, cb) {
        return this.request("ConfirmNetworkFirewallPolicy", req, cb);
    }
    async DeleteComplianceAssetPolicySetFromWhitelist(req, cb) {
        return this.request("DeleteComplianceAssetPolicySetFromWhitelist", req, cb);
    }
    async DescribeImageComponentList(req, cb) {
        return this.request("DescribeImageComponentList", req, cb);
    }
    async DescribeAssetImageRegistrySummary(req, cb) {
        return this.request("DescribeAssetImageRegistrySummary", req, cb);
    }
    async UpdateNetworkFirewallPolicyDetail(req, cb) {
        return this.request("UpdateNetworkFirewallPolicyDetail", req, cb);
    }
    async DescribeAssetImageRegistryAssetStatus(req, cb) {
        return this.request("DescribeAssetImageRegistryAssetStatus", req, cb);
    }
    async DescribeAssetImageRegistryRegistryList(req, cb) {
        return this.request("DescribeAssetImageRegistryRegistryList", req, cb);
    }
    async ExportVirusList(req, cb) {
        return this.request("ExportVirusList", req, cb);
    }
    async DescribeClusterDetail(req, cb) {
        return this.request("DescribeClusterDetail", req, cb);
    }
    async DescribeAssetImageRegistryVirusList(req, cb) {
        return this.request("DescribeAssetImageRegistryVirusList", req, cb);
    }
    async UpdateImageRegistryTimingScanTask(req, cb) {
        return this.request("UpdateImageRegistryTimingScanTask", req, cb);
    }
    async CreateAssetImageRegistryScanTaskOneKey(req, cb) {
        return this.request("CreateAssetImageRegistryScanTaskOneKey", req, cb);
    }
    async ModifyAccessControlRuleStatus(req, cb) {
        return this.request("ModifyAccessControlRuleStatus", req, cb);
    }
    async DescribeNetworkFirewallPolicyYamlDetail(req, cb) {
        return this.request("DescribeNetworkFirewallPolicyYamlDetail", req, cb);
    }
    async CheckNetworkFirewallPolicyYaml(req, cb) {
        return this.request("CheckNetworkFirewallPolicyYaml", req, cb);
    }
    async UpdateNetworkFirewallPolicyYamlDetail(req, cb) {
        return this.request("UpdateNetworkFirewallPolicyYamlDetail", req, cb);
    }
    async DescribeNetworkFirewallNamespaceList(req, cb) {
        return this.request("DescribeNetworkFirewallNamespaceList", req, cb);
    }
    async DescribeAssetImageScanSetting(req, cb) {
        return this.request("DescribeAssetImageScanSetting", req, cb);
    }
    async CreateCheckComponent(req, cb) {
        return this.request("CreateCheckComponent", req, cb);
    }
    async SetCheckMode(req, cb) {
        return this.request("SetCheckMode", req, cb);
    }
    async DescribeNewestVul(req, cb) {
        return this.request("DescribeNewestVul", req, cb);
    }
    async DescribeRiskSyscallNames(req, cb) {
        return this.request("DescribeRiskSyscallNames", req, cb);
    }
    async ModifyK8sApiAbnormalRuleInfo(req, cb) {
        return this.request("ModifyK8sApiAbnormalRuleInfo", req, cb);
    }
    async DescribeCompliancePeriodTaskList(req, cb) {
        return this.request("DescribeCompliancePeriodTaskList", req, cb);
    }
    async DeleteAccessControlRules(req, cb) {
        return this.request("DeleteAccessControlRules", req, cb);
    }
    async CreateVulDefenceHostExportJob(req, cb) {
        return this.request("CreateVulDefenceHostExportJob", req, cb);
    }
    async ModifyEscapeEventStatus(req, cb) {
        return this.request("ModifyEscapeEventStatus", req, cb);
    }
    async DescribeAbnormalProcessLevelSummary(req, cb) {
        return this.request("DescribeAbnormalProcessLevelSummary", req, cb);
    }
    async DescribePurchaseStateInfo(req, cb) {
        return this.request("DescribePurchaseStateInfo", req, cb);
    }
    async DescribeRiskSyscallWhiteListDetail(req, cb) {
        return this.request("DescribeRiskSyscallWhiteListDetail", req, cb);
    }
    async DescribeAssetImageRegistryDetail(req, cb) {
        return this.request("DescribeAssetImageRegistryDetail", req, cb);
    }
    async ModifyEscapeWhiteList(req, cb) {
        return this.request("ModifyEscapeWhiteList", req, cb);
    }
    async DescribeVirusManualScanEstimateTimeout(req, cb) {
        return this.request("DescribeVirusManualScanEstimateTimeout", req, cb);
    }
    async ModifySecLogDeliveryClsSetting(req, cb) {
        return this.request("ModifySecLogDeliveryClsSetting", req, cb);
    }
    async DescribeImageAutoAuthorizedRule(req, cb) {
        return this.request("DescribeImageAutoAuthorizedRule", req, cb);
    }
    async DescribeAbnormalProcessRuleDetail(req, cb) {
        return this.request("DescribeAbnormalProcessRuleDetail", req, cb);
    }
    async CreateDefenceVulExportJob(req, cb) {
        return this.request("CreateDefenceVulExportJob", req, cb);
    }
    async DescribeValueAddedSrvInfo(req, cb) {
        return this.request("DescribeValueAddedSrvInfo", req, cb);
    }
    async ModifyVirusFileStatus(req, cb) {
        return this.request("ModifyVirusFileStatus", req, cb);
    }
    async AddEditReverseShellWhiteList(req, cb) {
        return this.request("AddEditReverseShellWhiteList", req, cb);
    }
    async DescribeReverseShellDetail(req, cb) {
        return this.request("DescribeReverseShellDetail", req, cb);
    }
    async DescribeESAggregations(req, cb) {
        return this.request("DescribeESAggregations", req, cb);
    }
    async DescribeAssetImageBindRuleInfo(req, cb) {
        return this.request("DescribeAssetImageBindRuleInfo", req, cb);
    }
    async DeleteIgnoreVul(req, cb) {
        return this.request("DeleteIgnoreVul", req, cb);
    }
    async CreateAssetImageVirusExportJob(req, cb) {
        return this.request("CreateAssetImageVirusExportJob", req, cb);
    }
    async DescribeNetworkFirewallPolicyDiscover(req, cb) {
        return this.request("DescribeNetworkFirewallPolicyDiscover", req, cb);
    }
    async DescribeSecLogDeliveryClsOptions(req, cb) {
        return this.request("DescribeSecLogDeliveryClsOptions", req, cb);
    }
    async DescribeUnauthorizedCoresTendency(req, cb) {
        return this.request("DescribeUnauthorizedCoresTendency", req, cb);
    }
    async DescribeTaskResultSummary(req, cb) {
        return this.request("DescribeTaskResultSummary", req, cb);
    }
    async DescribeAssetContainerDetail(req, cb) {
        return this.request("DescribeAssetContainerDetail", req, cb);
    }
    async DescribeAssetImageRegistryRiskInfoList(req, cb) {
        return this.request("DescribeAssetImageRegistryRiskInfoList", req, cb);
    }
    async ModifyEscapeRule(req, cb) {
        return this.request("ModifyEscapeRule", req, cb);
    }
    async DescribeCompliancePolicyItemAffectedSummary(req, cb) {
        return this.request("DescribeCompliancePolicyItemAffectedSummary", req, cb);
    }
    async DescribeRefreshTask(req, cb) {
        return this.request("DescribeRefreshTask", req, cb);
    }
    async CreateNetworkFirewallClusterRefresh(req, cb) {
        return this.request("CreateNetworkFirewallClusterRefresh", req, cb);
    }
    async DescribeRiskSyscallEvents(req, cb) {
        return this.request("DescribeRiskSyscallEvents", req, cb);
    }
    async AddComplianceAssetPolicySetToWhitelist(req, cb) {
        return this.request("AddComplianceAssetPolicySetToWhitelist", req, cb);
    }
    async ModifySecLogJoinObjects(req, cb) {
        return this.request("ModifySecLogJoinObjects", req, cb);
    }
    async DescribeContainerAssetSummary(req, cb) {
        return this.request("DescribeContainerAssetSummary", req, cb);
    }
    async DescribeVirusScanSetting(req, cb) {
        return this.request("DescribeVirusScanSetting", req, cb);
    }
    async DescribeAssetSuperNodeList(req, cb) {
        return this.request("DescribeAssetSuperNodeList", req, cb);
    }
    async DescribeImageRegistryTimingScanTask(req, cb) {
        return this.request("DescribeImageRegistryTimingScanTask", req, cb);
    }
    async DescribeReverseShellEventsExport(req, cb) {
        return this.request("DescribeReverseShellEventsExport", req, cb);
    }
    async DescribeAssetDBServiceList(req, cb) {
        return this.request("DescribeAssetDBServiceList", req, cb);
    }
    async DescribeVulImageList(req, cb) {
        return this.request("DescribeVulImageList", req, cb);
    }
    async ModifyAbnormalProcessRuleStatus(req, cb) {
        return this.request("ModifyAbnormalProcessRuleStatus", req, cb);
    }
    async DescribeVulDefenceEvent(req, cb) {
        return this.request("DescribeVulDefenceEvent", req, cb);
    }
    async DescribeVulIgnoreRegistryImageList(req, cb) {
        return this.request("DescribeVulIgnoreRegistryImageList", req, cb);
    }
    async DescribeVulScanInfo(req, cb) {
        return this.request("DescribeVulScanInfo", req, cb);
    }
    async DescribeRaspRules(req, cb) {
        return this.request("DescribeRaspRules", req, cb);
    }
    async CreateWebVulExportJob(req, cb) {
        return this.request("CreateWebVulExportJob", req, cb);
    }
    async UpdateAndPublishNetworkFirewallPolicyDetail(req, cb) {
        return this.request("UpdateAndPublishNetworkFirewallPolicyDetail", req, cb);
    }
    async ModifyContainerNetStatus(req, cb) {
        return this.request("ModifyContainerNetStatus", req, cb);
    }
    async DescribeImageRiskTendency(req, cb) {
        return this.request("DescribeImageRiskTendency", req, cb);
    }
    async DescribeAgentInstallCommand(req, cb) {
        return this.request("DescribeAgentInstallCommand", req, cb);
    }
    async DescribeExportJobResult(req, cb) {
        return this.request("DescribeExportJobResult", req, cb);
    }
    async CreateAssetImageScanTask(req, cb) {
        return this.request("CreateAssetImageScanTask", req, cb);
    }
    async CreateEmergencyVulExportJob(req, cb) {
        return this.request("CreateEmergencyVulExportJob", req, cb);
    }
    async DescribeAssetImageScanTask(req, cb) {
        return this.request("DescribeAssetImageScanTask", req, cb);
    }
    async DescribeAssetImageRegistryListExport(req, cb) {
        return this.request("DescribeAssetImageRegistryListExport", req, cb);
    }
    async DescribeVulDefencePlugin(req, cb) {
        return this.request("DescribeVulDefencePlugin", req, cb);
    }
    async DescribeK8sApiAbnormalEventList(req, cb) {
        return this.request("DescribeK8sApiAbnormalEventList", req, cb);
    }
    async DescribeComplianceTaskAssetSummary(req, cb) {
        return this.request("DescribeComplianceTaskAssetSummary", req, cb);
    }
    async ModifyVirusMonitorSetting(req, cb) {
        return this.request("ModifyVirusMonitorSetting", req, cb);
    }
    async DescribeAccessControlRules(req, cb) {
        return this.request("DescribeAccessControlRules", req, cb);
    }
    async DescribeAssetImageVirusListExport(req, cb) {
        return this.request("DescribeAssetImageVirusListExport", req, cb);
    }
    async DescribeAssetContainerList(req, cb) {
        return this.request("DescribeAssetContainerList", req, cb);
    }
    async CreateSearchTemplate(req, cb) {
        return this.request("CreateSearchTemplate", req, cb);
    }
    async DescribePostPayDetail(req, cb) {
        return this.request("DescribePostPayDetail", req, cb);
    }
    async DescribeSuperNodePodList(req, cb) {
        return this.request("DescribeSuperNodePodList", req, cb);
    }
    async AddNetworkFirewallPolicyYamlDetail(req, cb) {
        return this.request("AddNetworkFirewallPolicyYamlDetail", req, cb);
    }
    async DeleteReverseShellEvents(req, cb) {
        return this.request("DeleteReverseShellEvents", req, cb);
    }
    async DescribeAssetImageRegistryRegistryDetail(req, cb) {
        return this.request("DescribeAssetImageRegistryRegistryDetail", req, cb);
    }
    async AddEscapeWhiteList(req, cb) {
        return this.request("AddEscapeWhiteList", req, cb);
    }
    async CreateImageExportJob(req, cb) {
        return this.request("CreateImageExportJob", req, cb);
    }
    async DeleteRaspRules(req, cb) {
        return this.request("DeleteRaspRules", req, cb);
    }
    async DescribeSecLogDeliveryClsSetting(req, cb) {
        return this.request("DescribeSecLogDeliveryClsSetting", req, cb);
    }
    async DescribeVirusList(req, cb) {
        return this.request("DescribeVirusList", req, cb);
    }
    async ModifyAssetImageScanStop(req, cb) {
        return this.request("ModifyAssetImageScanStop", req, cb);
    }
    async ScanComplianceAssets(req, cb) {
        return this.request("ScanComplianceAssets", req, cb);
    }
    async ModifyK8sApiAbnormalEventStatus(req, cb) {
        return this.request("ModifyK8sApiAbnormalEventStatus", req, cb);
    }
    async AddIgnoreVul(req, cb) {
        return this.request("AddIgnoreVul", req, cb);
    }
    async DescribeCheckItemList(req, cb) {
        return this.request("DescribeCheckItemList", req, cb);
    }
    async CreateSystemVulExportJob(req, cb) {
        return this.request("CreateSystemVulExportJob", req, cb);
    }
    async DescribeVirusDetail(req, cb) {
        return this.request("DescribeVirusDetail", req, cb);
    }
    async CreateVulExportJob(req, cb) {
        return this.request("CreateVulExportJob", req, cb);
    }
    async DescribeComplianceWhitelistItemList(req, cb) {
        return this.request("DescribeComplianceWhitelistItemList", req, cb);
    }
    async DescribeNetworkFirewallPolicyList(req, cb) {
        return this.request("DescribeNetworkFirewallPolicyList", req, cb);
    }
    async SyncAssetImageRegistryAsset(req, cb) {
        return this.request("SyncAssetImageRegistryAsset", req, cb);
    }
    async CreateComplianceTask(req, cb) {
        return this.request("CreateComplianceTask", req, cb);
    }
    async DescribeComplianceScanFailedAssetList(req, cb) {
        return this.request("DescribeComplianceScanFailedAssetList", req, cb);
    }
    async DescribeK8sApiAbnormalRuleScopeList(req, cb) {
        return this.request("DescribeK8sApiAbnormalRuleScopeList", req, cb);
    }
    async DescribeAssetClusterList(req, cb) {
        return this.request("DescribeAssetClusterList", req, cb);
    }
    async DescribeSupportDefenceVul(req, cb) {
        return this.request("DescribeSupportDefenceVul", req, cb);
    }
    async DeleteRiskSyscallEvents(req, cb) {
        return this.request("DeleteRiskSyscallEvents", req, cb);
    }
    async DescribeAffectedWorkloadList(req, cb) {
        return this.request("DescribeAffectedWorkloadList", req, cb);
    }
    async AddCompliancePolicyAssetSetToWhitelist(req, cb) {
        return this.request("AddCompliancePolicyAssetSetToWhitelist", req, cb);
    }
    async DeleteAbnormalProcessRules(req, cb) {
        return this.request("DeleteAbnormalProcessRules", req, cb);
    }
    async DescribeVulDetail(req, cb) {
        return this.request("DescribeVulDetail", req, cb);
    }
    async DescribeExportJobManageList(req, cb) {
        return this.request("DescribeExportJobManageList", req, cb);
    }
    async CreateVulScanTask(req, cb) {
        return this.request("CreateVulScanTask", req, cb);
    }
    async DescribeNetworkFirewallClusterList(req, cb) {
        return this.request("DescribeNetworkFirewallClusterList", req, cb);
    }
    async StopVulScanTask(req, cb) {
        return this.request("StopVulScanTask", req, cb);
    }
    async DescribeAssetImageRegistryRiskListExport(req, cb) {
        return this.request("DescribeAssetImageRegistryRiskListExport", req, cb);
    }
    async ModifyVirusScanTimeoutSetting(req, cb) {
        return this.request("ModifyVirusScanTimeoutSetting", req, cb);
    }
    async CreateVirusScanTask(req, cb) {
        return this.request("CreateVirusScanTask", req, cb);
    }
    async DescribeVulDefenceHost(req, cb) {
        return this.request("DescribeVulDefenceHost", req, cb);
    }
    async DescribeVulIgnoreLocalImageList(req, cb) {
        return this.request("DescribeVulIgnoreLocalImageList", req, cb);
    }
    async DescribeEscapeEventDetail(req, cb) {
        return this.request("DescribeEscapeEventDetail", req, cb);
    }
    async ModifyRiskSyscallStatus(req, cb) {
        return this.request("ModifyRiskSyscallStatus", req, cb);
    }
    async CreateRiskDnsEventExportJob(req, cb) {
        return this.request("CreateRiskDnsEventExportJob", req, cb);
    }
    async DescribeVulDefenceEventTendency(req, cb) {
        return this.request("DescribeVulDefenceEventTendency", req, cb);
    }
    async DescribeEscapeWhiteList(req, cb) {
        return this.request("DescribeEscapeWhiteList", req, cb);
    }
    async ScanCompliancePolicyItems(req, cb) {
        return this.request("ScanCompliancePolicyItems", req, cb);
    }
    async DeleteReverseShellWhiteLists(req, cb) {
        return this.request("DeleteReverseShellWhiteLists", req, cb);
    }
    async DescribeComplianceAssetDetailInfo(req, cb) {
        return this.request("DescribeComplianceAssetDetailInfo", req, cb);
    }
    async DescribeAccessControlRuleDetail(req, cb) {
        return this.request("DescribeAccessControlRuleDetail", req, cb);
    }
    async ModifyImageAuthorized(req, cb) {
        return this.request("ModifyImageAuthorized", req, cb);
    }
    async ModifyAbnormalProcessStatus(req, cb) {
        return this.request("ModifyAbnormalProcessStatus", req, cb);
    }
    async CreateExportComplianceStatusListJob(req, cb) {
        return this.request("CreateExportComplianceStatusListJob", req, cb);
    }
    async DescribeAssetImageSimpleList(req, cb) {
        return this.request("DescribeAssetImageSimpleList", req, cb);
    }
    async DescribeImageSimpleList(req, cb) {
        return this.request("DescribeImageSimpleList", req, cb);
    }
    async ModifyAccessControlStatus(req, cb) {
        return this.request("ModifyAccessControlStatus", req, cb);
    }
    async CreateEscapeWhiteListExportJob(req, cb) {
        return this.request("CreateEscapeWhiteListExportJob", req, cb);
    }
    async DescribeEscapeRuleInfo(req, cb) {
        return this.request("DescribeEscapeRuleInfo", req, cb);
    }
    async DescribeAssetImageRiskList(req, cb) {
        return this.request("DescribeAssetImageRiskList", req, cb);
    }
    async DescribeAssetImageHostList(req, cb) {
        return this.request("DescribeAssetImageHostList", req, cb);
    }
    async DescribeClusterSummary(req, cb) {
        return this.request("DescribeClusterSummary", req, cb);
    }
    async DescribeVulDefenceEventDetail(req, cb) {
        return this.request("DescribeVulDefenceEventDetail", req, cb);
    }
    async DescribeEscapeSafeState(req, cb) {
        return this.request("DescribeEscapeSafeState", req, cb);
    }
    async DescribeVirusTaskList(req, cb) {
        return this.request("DescribeVirusTaskList", req, cb);
    }
    async DescribeSearchLogs(req, cb) {
        return this.request("DescribeSearchLogs", req, cb);
    }
    async AddAssetImageRegistryRegistryDetail(req, cb) {
        return this.request("AddAssetImageRegistryRegistryDetail", req, cb);
    }
    async DescribeAssetPortList(req, cb) {
        return this.request("DescribeAssetPortList", req, cb);
    }
    async AddEditImageAutoAuthorizedRule(req, cb) {
        return this.request("AddEditImageAutoAuthorizedRule", req, cb);
    }
    async DescribeTcssSummary(req, cb) {
        return this.request("DescribeTcssSummary", req, cb);
    }
    async DescribeImageRiskSummary(req, cb) {
        return this.request("DescribeImageRiskSummary", req, cb);
    }
    async DescribeVulRegistryImageList(req, cb) {
        return this.request("DescribeVulRegistryImageList", req, cb);
    }
    async DescribeEmergencyVulList(req, cb) {
        return this.request("DescribeEmergencyVulList", req, cb);
    }
    async DescribeVulLevelSummary(req, cb) {
        return this.request("DescribeVulLevelSummary", req, cb);
    }
    async DescribeNetworkFirewallNamespaceLabelList(req, cb) {
        return this.request("DescribeNetworkFirewallNamespaceLabelList", req, cb);
    }
    async ModifyVirusAutoIsolateExampleSwitch(req, cb) {
        return this.request("ModifyVirusAutoIsolateExampleSwitch", req, cb);
    }
    async DescribeNetworkFirewallClusterRefreshStatus(req, cb) {
        return this.request("DescribeNetworkFirewallClusterRefreshStatus", req, cb);
    }
    async DescribeVirusMonitorSetting(req, cb) {
        return this.request("DescribeVirusMonitorSetting", req, cb);
    }
    async DescribeVirusAutoIsolateSetting(req, cb) {
        return this.request("DescribeVirusAutoIsolateSetting", req, cb);
    }
    async DescribeEscapeEventTendency(req, cb) {
        return this.request("DescribeEscapeEventTendency", req, cb);
    }
    async ModifyCompliancePeriodTask(req, cb) {
        return this.request("ModifyCompliancePeriodTask", req, cb);
    }
    async DescribeSecLogCleanSettingInfo(req, cb) {
        return this.request("DescribeSecLogCleanSettingInfo", req, cb);
    }
    async DescribeSystemVulList(req, cb) {
        return this.request("DescribeSystemVulList", req, cb);
    }
    async DescribeVulScanLocalImageList(req, cb) {
        return this.request("DescribeVulScanLocalImageList", req, cb);
    }
    async StopVirusScanTask(req, cb) {
        return this.request("StopVirusScanTask", req, cb);
    }
    async AddEditAbnormalProcessRule(req, cb) {
        return this.request("AddEditAbnormalProcessRule", req, cb);
    }
    async ModifyVulDefenceEventStatus(req, cb) {
        return this.request("ModifyVulDefenceEventStatus", req, cb);
    }
    async ResetSecLogTopicConfig(req, cb) {
        return this.request("ResetSecLogTopicConfig", req, cb);
    }
    async CreateVirusScanAgain(req, cb) {
        return this.request("CreateVirusScanAgain", req, cb);
    }
    async DescribeNetworkFirewallAuditRecord(req, cb) {
        return this.request("DescribeNetworkFirewallAuditRecord", req, cb);
    }
    async ModifySecLogJoinState(req, cb) {
        return this.request("ModifySecLogJoinState", req, cb);
    }
    async DescribeContainerSecEventSummary(req, cb) {
        return this.request("DescribeContainerSecEventSummary", req, cb);
    }
    async DescribeImageAutoAuthorizedTaskList(req, cb) {
        return this.request("DescribeImageAutoAuthorizedTaskList", req, cb);
    }
    async DescribeComplianceTaskPolicyItemSummaryList(req, cb) {
        return this.request("DescribeComplianceTaskPolicyItemSummaryList", req, cb);
    }
    async DescribeAssetImageList(req, cb) {
        return this.request("DescribeAssetImageList", req, cb);
    }
    async DeleteK8sApiAbnormalRule(req, cb) {
        return this.request("DeleteK8sApiAbnormalRule", req, cb);
    }
    async DescribeReverseShellEvents(req, cb) {
        return this.request("DescribeReverseShellEvents", req, cb);
    }
    async CreateProcessEventsExportJob(req, cb) {
        return this.request("CreateProcessEventsExportJob", req, cb);
    }
    async DescribeNetworkFirewallPolicyDetail(req, cb) {
        return this.request("DescribeNetworkFirewallPolicyDetail", req, cb);
    }
    async CreateAssetImageRegistryScanTask(req, cb) {
        return this.request("CreateAssetImageRegistryScanTask", req, cb);
    }
    async DescribeSecLogAlertMsg(req, cb) {
        return this.request("DescribeSecLogAlertMsg", req, cb);
    }
    async DescribeUnfinishRefreshTask(req, cb) {
        return this.request("DescribeUnfinishRefreshTask", req, cb);
    }
    async AddEditRiskSyscallWhiteList(req, cb) {
        return this.request("AddEditRiskSyscallWhiteList", req, cb);
    }
    async DescribeAssetImageRegistryScanStatusOneKey(req, cb) {
        return this.request("DescribeAssetImageRegistryScanStatusOneKey", req, cb);
    }
    async AddAndPublishNetworkFirewallPolicyDetail(req, cb) {
        return this.request("AddAndPublishNetworkFirewallPolicyDetail", req, cb);
    }
    async DescribeAssetImageVulListExport(req, cb) {
        return this.request("DescribeAssetImageVulListExport", req, cb);
    }
    async DescribeAssetImageRegistryList(req, cb) {
        return this.request("DescribeAssetImageRegistryList", req, cb);
    }
    async DescribeAssetProcessList(req, cb) {
        return this.request("DescribeAssetProcessList", req, cb);
    }
    async DescribeNetworkFirewallPodLabelsList(req, cb) {
        return this.request("DescribeNetworkFirewallPodLabelsList", req, cb);
    }
    async DescribeK8sApiAbnormalTendency(req, cb) {
        return this.request("DescribeK8sApiAbnormalTendency", req, cb);
    }
    async DescribeAbnormalProcessEvents(req, cb) {
        return this.request("DescribeAbnormalProcessEvents", req, cb);
    }
    async UpdateAssetImageRegistryRegistryDetail(req, cb) {
        return this.request("UpdateAssetImageRegistryRegistryDetail", req, cb);
    }
    async DescribeProVersionInfo(req, cb) {
        return this.request("DescribeProVersionInfo", req, cb);
    }
    async DescribeComplianceAssetPolicyItemList(req, cb) {
        return this.request("DescribeComplianceAssetPolicyItemList", req, cb);
    }
    async DescribeClusterNodes(req, cb) {
        return this.request("DescribeClusterNodes", req, cb);
    }
    async DescribeESHits(req, cb) {
        return this.request("DescribeESHits", req, cb);
    }
    async DescribeVulDefenceSetting(req, cb) {
        return this.request("DescribeVulDefenceSetting", req, cb);
    }
    async DescribeAssetSyncLastTime(req, cb) {
        return this.request("DescribeAssetSyncLastTime", req, cb);
    }
    async DescribeK8sApiAbnormalRuleInfo(req, cb) {
        return this.request("DescribeK8sApiAbnormalRuleInfo", req, cb);
    }
    async DescribeRiskDnsEventDetail(req, cb) {
        return this.request("DescribeRiskDnsEventDetail", req, cb);
    }
    async DeleteCompliancePolicyItemFromWhitelist(req, cb) {
        return this.request("DeleteCompliancePolicyItemFromWhitelist", req, cb);
    }
    async DescribeRaspRuleVuls(req, cb) {
        return this.request("DescribeRaspRuleVuls", req, cb);
    }
    async DescribePromotionActivity(req, cb) {
        return this.request("DescribePromotionActivity", req, cb);
    }
    async DescribeComplianceAssetList(req, cb) {
        return this.request("DescribeComplianceAssetList", req, cb);
    }
    async DescribeCompliancePolicyItemAffectedAssetList(req, cb) {
        return this.request("DescribeCompliancePolicyItemAffectedAssetList", req, cb);
    }
    async DescribeLogStorageStatistic(req, cb) {
        return this.request("DescribeLogStorageStatistic", req, cb);
    }
    async ModifyAsset(req, cb) {
        return this.request("ModifyAsset", req, cb);
    }
    async CreateOrModifyPostPayCores(req, cb) {
        return this.request("CreateOrModifyPostPayCores", req, cb);
    }
    async ModifyVirusAutoIsolateSetting(req, cb) {
        return this.request("ModifyVirusAutoIsolateSetting", req, cb);
    }
    async DescribeSecLogKafkaUIN(req, cb) {
        return this.request("DescribeSecLogKafkaUIN", req, cb);
    }
    async SwitchImageAutoAuthorizedRule(req, cb) {
        return this.request("SwitchImageAutoAuthorizedRule", req, cb);
    }
    async DescribeAbnormalProcessDetail(req, cb) {
        return this.request("DescribeAbnormalProcessDetail", req, cb);
    }
    async RenewImageAuthorizeState(req, cb) {
        return this.request("RenewImageAuthorizeState", req, cb);
    }
    async DescribeAffectedClusterCount(req, cb) {
        return this.request("DescribeAffectedClusterCount", req, cb);
    }
    async CreateVulContainerExportJob(req, cb) {
        return this.request("CreateVulContainerExportJob", req, cb);
    }
    async DescribeAffectedNodeList(req, cb) {
        return this.request("DescribeAffectedNodeList", req, cb);
    }
    async DescribeAccessControlEventsExport(req, cb) {
        return this.request("DescribeAccessControlEventsExport", req, cb);
    }
    async DescribeVirusEventTendency(req, cb) {
        return this.request("DescribeVirusEventTendency", req, cb);
    }
    async DescribeImageAutoAuthorizedLogList(req, cb) {
        return this.request("DescribeImageAutoAuthorizedLogList", req, cb);
    }
    async DescribeVulLevelImageSummary(req, cb) {
        return this.request("DescribeVulLevelImageSummary", req, cb);
    }
    async ModifyReverseShellStatus(req, cb) {
        return this.request("ModifyReverseShellStatus", req, cb);
    }
    async ModifyRiskDnsEventStatus(req, cb) {
        return this.request("ModifyRiskDnsEventStatus", req, cb);
    }
    async DescribeSecLogJoinTypeList(req, cb) {
        return this.request("DescribeSecLogJoinTypeList", req, cb);
    }
    async AddEditWarningRules(req, cb) {
        return this.request("AddEditWarningRules", req, cb);
    }
    async DescribeABTestConfig(req, cb) {
        return this.request("DescribeABTestConfig", req, cb);
    }
    async DescribeAssetImageRiskListExport(req, cb) {
        return this.request("DescribeAssetImageRiskListExport", req, cb);
    }
    async ModifySecLogCleanSettingInfo(req, cb) {
        return this.request("ModifySecLogCleanSettingInfo", req, cb);
    }
    async DescribeExportJobDownloadURL(req, cb) {
        return this.request("DescribeExportJobDownloadURL", req, cb);
    }
    async DescribeAssetImageRegistryVulListExport(req, cb) {
        return this.request("DescribeAssetImageRegistryVulListExport", req, cb);
    }
    async DescribeRiskList(req, cb) {
        return this.request("DescribeRiskList", req, cb);
    }
    async DescribeEscapeEventInfo(req, cb) {
        return this.request("DescribeEscapeEventInfo", req, cb);
    }
    async DescribeVulSummary(req, cb) {
        return this.request("DescribeVulSummary", req, cb);
    }
    async DescribeAutoAuthorizedRuleHost(req, cb) {
        return this.request("DescribeAutoAuthorizedRuleHost", req, cb);
    }
    async AddAndPublishNetworkFirewallPolicyYamlDetail(req, cb) {
        return this.request("AddAndPublishNetworkFirewallPolicyYamlDetail", req, cb);
    }
    async DescribeEscapeEventTypeSummary(req, cb) {
        return this.request("DescribeEscapeEventTypeSummary", req, cb);
    }
    async CreateEscapeEventsExportJob(req, cb) {
        return this.request("CreateEscapeEventsExportJob", req, cb);
    }
    async DescribeAssetImageVulList(req, cb) {
        return this.request("DescribeAssetImageVulList", req, cb);
    }
    async DescribeVulTopRanking(req, cb) {
        return this.request("DescribeVulTopRanking", req, cb);
    }
    async DescribeWarningRules(req, cb) {
        return this.request("DescribeWarningRules", req, cb);
    }
    async DescribeSearchTemplates(req, cb) {
        return this.request("DescribeSearchTemplates", req, cb);
    }
    async AddCompliancePolicyItemToWhitelist(req, cb) {
        return this.request("AddCompliancePolicyItemToWhitelist", req, cb);
    }
    async InitializeUserComplianceEnvironment(req, cb) {
        return this.request("InitializeUserComplianceEnvironment", req, cb);
    }
    async DescribeUserCluster(req, cb) {
        return this.request("DescribeUserCluster", req, cb);
    }
    async DeleteMachine(req, cb) {
        return this.request("DeleteMachine", req, cb);
    }
    async CreateHostExportJob(req, cb) {
        return this.request("CreateHostExportJob", req, cb);
    }
    async OpenTcssTrial(req, cb) {
        return this.request("OpenTcssTrial", req, cb);
    }
    async DescribeAbnormalProcessRules(req, cb) {
        return this.request("DescribeAbnormalProcessRules", req, cb);
    }
    async DescribeInspectionReport(req, cb) {
        return this.request("DescribeInspectionReport", req, cb);
    }
    async ScanComplianceAssetsByPolicyItem(req, cb) {
        return this.request("ScanComplianceAssetsByPolicyItem", req, cb);
    }
    async DeleteEscapeWhiteList(req, cb) {
        return this.request("DeleteEscapeWhiteList", req, cb);
    }
    async CreateNetworkFirewallPublish(req, cb) {
        return this.request("CreateNetworkFirewallPublish", req, cb);
    }
    async CreateNetworkFirewallUndoPublish(req, cb) {
        return this.request("CreateNetworkFirewallUndoPublish", req, cb);
    }
    async DeleteRiskSyscallWhiteLists(req, cb) {
        return this.request("DeleteRiskSyscallWhiteLists", req, cb);
    }
    async AddEditAccessControlRule(req, cb) {
        return this.request("AddEditAccessControlRule", req, cb);
    }
    async DescribeVulTendency(req, cb) {
        return this.request("DescribeVulTendency", req, cb);
    }
    async DescribeReverseShellWhiteListDetail(req, cb) {
        return this.request("DescribeReverseShellWhiteListDetail", req, cb);
    }
    async CreateAssetImageScanSetting(req, cb) {
        return this.request("CreateAssetImageScanSetting", req, cb);
    }
    async DescribeAssetImageVirusList(req, cb) {
        return this.request("DescribeAssetImageVirusList", req, cb);
    }
    async CreateK8sApiAbnormalRuleInfo(req, cb) {
        return this.request("CreateK8sApiAbnormalRuleInfo", req, cb);
    }
    async DescribeAssetSummary(req, cb) {
        return this.request("DescribeAssetSummary", req, cb);
    }
    async DescribeSecLogDeliveryKafkaSetting(req, cb) {
        return this.request("DescribeSecLogDeliveryKafkaSetting", req, cb);
    }
    async DescribeEventEscapeImageList(req, cb) {
        return this.request("DescribeEventEscapeImageList", req, cb);
    }
    async DeleteNetworkFirewallPolicyDetail(req, cb) {
        return this.request("DeleteNetworkFirewallPolicyDetail", req, cb);
    }
    async DescribeAssetWebServiceList(req, cb) {
        return this.request("DescribeAssetWebServiceList", req, cb);
    }
    async CheckRepeatAssetImageRegistry(req, cb) {
        return this.request("CheckRepeatAssetImageRegistry", req, cb);
    }
    async AddNetworkFirewallPolicyDetail(req, cb) {
        return this.request("AddNetworkFirewallPolicyDetail", req, cb);
    }
    async DescribeK8sApiAbnormalEventInfo(req, cb) {
        return this.request("DescribeK8sApiAbnormalEventInfo", req, cb);
    }
    async DescribeAbnormalProcessEventTendency(req, cb) {
        return this.request("DescribeAbnormalProcessEventTendency", req, cb);
    }
    async DescribeVulContainerList(req, cb) {
        return this.request("DescribeVulContainerList", req, cb);
    }
    async ModifyAssetImageRegistryScanStop(req, cb) {
        return this.request("ModifyAssetImageRegistryScanStop", req, cb);
    }
    async DescribeSecLogVasInfo(req, cb) {
        return this.request("DescribeSecLogVasInfo", req, cb);
    }
    async DescribeUserPodList(req, cb) {
        return this.request("DescribeUserPodList", req, cb);
    }
    async DescribeVirusSampleDownloadUrl(req, cb) {
        return this.request("DescribeVirusSampleDownloadUrl", req, cb);
    }
    async DescribeAssetHostDetail(req, cb) {
        return this.request("DescribeAssetHostDetail", req, cb);
    }
    async DescribeWebVulList(req, cb) {
        return this.request("DescribeWebVulList", req, cb);
    }
    async DescribeRiskSyscallWhiteLists(req, cb) {
        return this.request("DescribeRiskSyscallWhiteLists", req, cb);
    }
    async DescribeAssetImageRegistryVulList(req, cb) {
        return this.request("DescribeAssetImageRegistryVulList", req, cb);
    }
    async DescribeRiskSyscallDetail(req, cb) {
        return this.request("DescribeRiskSyscallDetail", req, cb);
    }
    async DescribeVirusAutoIsolateSampleList(req, cb) {
        return this.request("DescribeVirusAutoIsolateSampleList", req, cb);
    }
    async DescribeVirusScanTaskStatus(req, cb) {
        return this.request("DescribeVirusScanTaskStatus", req, cb);
    }
    async ModifyK8sApiAbnormalRuleStatus(req, cb) {
        return this.request("ModifyK8sApiAbnormalRuleStatus", req, cb);
    }
    async DescribeSecEventsTendency(req, cb) {
        return this.request("DescribeSecEventsTendency", req, cb);
    }
    async ModifyRaspRules(req, cb) {
        return this.request("ModifyRaspRules", req, cb);
    }
    async DescribeAssetImageDetail(req, cb) {
        return this.request("DescribeAssetImageDetail", req, cb);
    }
    async ModifySecLogDeliveryKafkaSetting(req, cb) {
        return this.request("ModifySecLogDeliveryKafkaSetting", req, cb);
    }
    async CreateAccessControlsRuleExportJob(req, cb) {
        return this.request("CreateAccessControlsRuleExportJob", req, cb);
    }
    async DescribeVulImageSummary(req, cb) {
        return this.request("DescribeVulImageSummary", req, cb);
    }
    async DeleteCompliancePolicyAssetSetFromWhitelist(req, cb) {
        return this.request("DeleteCompliancePolicyAssetSetFromWhitelist", req, cb);
    }
    async DescribeK8sApiAbnormalRuleList(req, cb) {
        return this.request("DescribeK8sApiAbnormalRuleList", req, cb);
    }
}
