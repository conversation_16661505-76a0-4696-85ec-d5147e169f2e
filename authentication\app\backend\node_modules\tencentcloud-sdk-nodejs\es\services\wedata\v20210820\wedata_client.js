import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("wedata.tencentcloudapi.com", "2021-08-20", clientConfig);
    }
    async CreateTask(req, cb) {
        return this.request("CreateTask", req, cb);
    }
    async GetBatchDetailErrorLog(req, cb) {
        return this.request("GetBatchDetailErrorLog", req, cb);
    }
    async CreateTaskAlarmRegular(req, cb) {
        return this.request("CreateTaskAlarmRegular", req, cb);
    }
    async DescribeIntegrationStatisticsRecordsTrend(req, cb) {
        return this.request("DescribeIntegrationStatisticsRecordsTrend", req, cb);
    }
    async DeleteIntegrationTask(req, cb) {
        return this.request("DeleteIntegrationTask", req, cb);
    }
    async KillScheduleInstances(req, cb) {
        return this.request("KillScheduleInstances", req, cb);
    }
    async GetFileInfo(req, cb) {
        return this.request("GetFileInfo", req, cb);
    }
    async DescribeTaskScript(req, cb) {
        return this.request("DescribeTaskScript", req, cb);
    }
    async DescribeRealViewSchemaPage(req, cb) {
        return this.request("DescribeRealViewSchemaPage", req, cb);
    }
    async CreateRuleTemplate(req, cb) {
        return this.request("CreateRuleTemplate", req, cb);
    }
    async DiagnosePro(req, cb) {
        return this.request("DiagnosePro", req, cb);
    }
    async DescribeRealTimeTaskMetricOverview(req, cb) {
        return this.request("DescribeRealTimeTaskMetricOverview", req, cb);
    }
    async DescribeScheduleInstances(req, cb) {
        return this.request("DescribeScheduleInstances", req, cb);
    }
    async BatchStopOpsTasks(req, cb) {
        return this.request("BatchStopOpsTasks", req, cb);
    }
    async DownloadLogByLine(req, cb) {
        return this.request("DownloadLogByLine", req, cb);
    }
    async DescribeDataCheckStat(req, cb) {
        return this.request("DescribeDataCheckStat", req, cb);
    }
    async DescribeOpsWorkflows(req, cb) {
        return this.request("DescribeOpsWorkflows", req, cb);
    }
    async BatchKillIntegrationTaskInstances(req, cb) {
        return this.request("BatchKillIntegrationTaskInstances", req, cb);
    }
    async DescribeTaskParamDs(req, cb) {
        return this.request("DescribeTaskParamDs", req, cb);
    }
    async ModifyExecStrategy(req, cb) {
        return this.request("ModifyExecStrategy", req, cb);
    }
    async DescribeIntegrationStatisticsTaskStatusTrend(req, cb) {
        return this.request("DescribeIntegrationStatisticsTaskStatusTrend", req, cb);
    }
    async CreateHiveTable(req, cb) {
        return this.request("CreateHiveTable", req, cb);
    }
    async DescribeOperateOpsTasks(req, cb) {
        return this.request("DescribeOperateOpsTasks", req, cb);
    }
    async DescribeInstanceByCycle(req, cb) {
        return this.request("DescribeInstanceByCycle", req, cb);
    }
    async DescribeIntegrationNode(req, cb) {
        return this.request("DescribeIntegrationNode", req, cb);
    }
    async UploadResource(req, cb) {
        return this.request("UploadResource", req, cb);
    }
    async DescribeTaskByCycle(req, cb) {
        return this.request("DescribeTaskByCycle", req, cb);
    }
    async BatchSuspendIntegrationTasks(req, cb) {
        return this.request("BatchSuspendIntegrationTasks", req, cb);
    }
    async SubmitWorkflow(req, cb) {
        return this.request("SubmitWorkflow", req, cb);
    }
    async DescribeFieldBasicInfo(req, cb) {
        return this.request("DescribeFieldBasicInfo", req, cb);
    }
    async ReportTaskLineage(req, cb) {
        return this.request("ReportTaskLineage", req, cb);
    }
    async DescribeTableScoreTrend(req, cb) {
        return this.request("DescribeTableScoreTrend", req, cb);
    }
    async DeleteCodeTemplate(req, cb) {
        return this.request("DeleteCodeTemplate", req, cb);
    }
    async RemoveWorkflowDs(req, cb) {
        return this.request("RemoveWorkflowDs", req, cb);
    }
    async DescribeThirdTaskRunLog(req, cb) {
        return this.request("DescribeThirdTaskRunLog", req, cb);
    }
    async DescribeDsParentFolderTree(req, cb) {
        return this.request("DescribeDsParentFolderTree", req, cb);
    }
    async ResumeIntegrationTask(req, cb) {
        return this.request("ResumeIntegrationTask", req, cb);
    }
    async ModifyTaskName(req, cb) {
        return this.request("ModifyTaskName", req, cb);
    }
    async DescribeInstanceLogList(req, cb) {
        return this.request("DescribeInstanceLogList", req, cb);
    }
    async CreateTaskVersionDs(req, cb) {
        return this.request("CreateTaskVersionDs", req, cb);
    }
    async BatchModifyOpsOwners(req, cb) {
        return this.request("BatchModifyOpsOwners", req, cb);
    }
    async CreateCustomFunction(req, cb) {
        return this.request("CreateCustomFunction", req, cb);
    }
    async UnlockIntegrationTask(req, cb) {
        return this.request("UnlockIntegrationTask", req, cb);
    }
    async BatchStopIntegrationTasks(req, cb) {
        return this.request("BatchStopIntegrationTasks", req, cb);
    }
    async CreateCodeTemplateVersion(req, cb) {
        return this.request("CreateCodeTemplateVersion", req, cb);
    }
    async DescribeSchedulerTaskTypeCnt(req, cb) {
        return this.request("DescribeSchedulerTaskTypeCnt", req, cb);
    }
    async CreateCodeTemplate(req, cb) {
        return this.request("CreateCodeTemplate", req, cb);
    }
    async DeleteProjectParamDs(req, cb) {
        return this.request("DeleteProjectParamDs", req, cb);
    }
    async MoveTasksToFolder(req, cb) {
        return this.request("MoveTasksToFolder", req, cb);
    }
    async DescribeWorkflowListByProjectId(req, cb) {
        return this.request("DescribeWorkflowListByProjectId", req, cb);
    }
    async CreateDataSource(req, cb) {
        return this.request("CreateDataSource", req, cb);
    }
    async DescribeOpsInstanceLogList(req, cb) {
        return this.request("DescribeOpsInstanceLogList", req, cb);
    }
    async SetTaskAlarmNew(req, cb) {
        return this.request("SetTaskAlarmNew", req, cb);
    }
    async DescribeEventConsumeTasks(req, cb) {
        return this.request("DescribeEventConsumeTasks", req, cb);
    }
    async RenewWorkflowSchedulerInfoDs(req, cb) {
        return this.request("RenewWorkflowSchedulerInfoDs", req, cb);
    }
    async DescribeOfflineTaskToken(req, cb) {
        return this.request("DescribeOfflineTaskToken", req, cb);
    }
    async DeleteRuleTemplate(req, cb) {
        return this.request("DeleteRuleTemplate", req, cb);
    }
    async UpdateDataModelRegistryInfo(req, cb) {
        return this.request("UpdateDataModelRegistryInfo", req, cb);
    }
    async DescribeApproveList(req, cb) {
        return this.request("DescribeApproveList", req, cb);
    }
    async AddProjectUserRole(req, cb) {
        return this.request("AddProjectUserRole", req, cb);
    }
    async CheckIntegrationNodeNameExists(req, cb) {
        return this.request("CheckIntegrationNodeNameExists", req, cb);
    }
    async DescribeAlarmEvents(req, cb) {
        return this.request("DescribeAlarmEvents", req, cb);
    }
    async RunTasksByMultiWorkflow(req, cb) {
        return this.request("RunTasksByMultiWorkflow", req, cb);
    }
    async DescribeQualityScore(req, cb) {
        return this.request("DescribeQualityScore", req, cb);
    }
    async DescribeExecutorGroupMetric(req, cb) {
        return this.request("DescribeExecutorGroupMetric", req, cb);
    }
    async GetCosToken(req, cb) {
        return this.request("GetCosToken", req, cb);
    }
    async DescribeRuleDimStat(req, cb) {
        return this.request("DescribeRuleDimStat", req, cb);
    }
    async GenHiveTableDDLSql(req, cb) {
        return this.request("GenHiveTableDDLSql", req, cb);
    }
    async DescribeTaskRunHistory(req, cb) {
        return this.request("DescribeTaskRunHistory", req, cb);
    }
    async DescribeOpsMakePlanTasks(req, cb) {
        return this.request("DescribeOpsMakePlanTasks", req, cb);
    }
    async UpdateWorkflowInfo(req, cb) {
        return this.request("UpdateWorkflowInfo", req, cb);
    }
    async DescribeTopTableStat(req, cb) {
        return this.request("DescribeTopTableStat", req, cb);
    }
    async DescribeOrganizationalFunctions(req, cb) {
        return this.request("DescribeOrganizationalFunctions", req, cb);
    }
    async RunForceSucScheduleInstances(req, cb) {
        return this.request("RunForceSucScheduleInstances", req, cb);
    }
    async CreateTaskNew(req, cb) {
        return this.request("CreateTaskNew", req, cb);
    }
    async UnboundProjectExecutorResource(req, cb) {
        return this.request("UnboundProjectExecutorResource", req, cb);
    }
    async DescribeDsFolderTree(req, cb) {
        return this.request("DescribeDsFolderTree", req, cb);
    }
    async DescribeTaskTableMetricOverview(req, cb) {
        return this.request("DescribeTaskTableMetricOverview", req, cb);
    }
    async TriggerManualTasks(req, cb) {
        return this.request("TriggerManualTasks", req, cb);
    }
    async SubmitTaskTestRun(req, cb) {
        return this.request("SubmitTaskTestRun", req, cb);
    }
    async DescribeApproveTypeList(req, cb) {
        return this.request("DescribeApproveTypeList", req, cb);
    }
    async DescribeRuleExecLog(req, cb) {
        return this.request("DescribeRuleExecLog", req, cb);
    }
    async DeleteFilePath(req, cb) {
        return this.request("DeleteFilePath", req, cb);
    }
    async CreateDsFolder(req, cb) {
        return this.request("CreateDsFolder", req, cb);
    }
    async DescribeSuccessorOpsTaskInfos(req, cb) {
        return this.request("DescribeSuccessorOpsTaskInfos", req, cb);
    }
    async DryRunDIOfflineTask(req, cb) {
        return this.request("DryRunDIOfflineTask", req, cb);
    }
    async DescribeDimensionScore(req, cb) {
        return this.request("DescribeDimensionScore", req, cb);
    }
    async DescribeRuleGroupTable(req, cb) {
        return this.request("DescribeRuleGroupTable", req, cb);
    }
    async DescribeRoleList(req, cb) {
        return this.request("DescribeRoleList", req, cb);
    }
    async CreateIntegrationTask(req, cb) {
        return this.request("CreateIntegrationTask", req, cb);
    }
    async DescribeRule(req, cb) {
        return this.request("DescribeRule", req, cb);
    }
    async KillOpsMakePlanInstances(req, cb) {
        return this.request("KillOpsMakePlanInstances", req, cb);
    }
    async DescribeDataSourceList(req, cb) {
        return this.request("DescribeDataSourceList", req, cb);
    }
    async GetInstanceLog(req, cb) {
        return this.request("GetInstanceLog", req, cb);
    }
    async DescribeFormVersionParam(req, cb) {
        return this.request("DescribeFormVersionParam", req, cb);
    }
    async TriggerDsEvent(req, cb) {
        return this.request("TriggerDsEvent", req, cb);
    }
    async DescribeSchedulerRunTimeInstanceCntByStatus(req, cb) {
        return this.request("DescribeSchedulerRunTimeInstanceCntByStatus", req, cb);
    }
    async DescribeSchedulerTaskCntByStatus(req, cb) {
        return this.request("DescribeSchedulerTaskCntByStatus", req, cb);
    }
    async ModifyIntegrationTask(req, cb) {
        return this.request("ModifyIntegrationTask", req, cb);
    }
    async DescribeDutyScheduleDetails(req, cb) {
        return this.request("DescribeDutyScheduleDetails", req, cb);
    }
    async DescribeIntegrationStatisticsTaskStatus(req, cb) {
        return this.request("DescribeIntegrationStatisticsTaskStatus", req, cb);
    }
    async BatchStartIntegrationTasks(req, cb) {
        return this.request("BatchStartIntegrationTasks", req, cb);
    }
    async DisableProject(req, cb) {
        return this.request("DisableProject", req, cb);
    }
    async ModifyDimensionWeight(req, cb) {
        return this.request("ModifyDimensionWeight", req, cb);
    }
    async DescribeRuleExecDetail(req, cb) {
        return this.request("DescribeRuleExecDetail", req, cb);
    }
    async CheckTaskNameExist(req, cb) {
        return this.request("CheckTaskNameExist", req, cb);
    }
    async DescribeOpsMakePlanInstances(req, cb) {
        return this.request("DescribeOpsMakePlanInstances", req, cb);
    }
    async DescribeDatasource(req, cb) {
        return this.request("DescribeDatasource", req, cb);
    }
    async ModifyDsFolder(req, cb) {
        return this.request("ModifyDsFolder", req, cb);
    }
    async DescribeWorkflowInfoById(req, cb) {
        return this.request("DescribeWorkflowInfoById", req, cb);
    }
    async DescribeTaskByStatusReport(req, cb) {
        return this.request("DescribeTaskByStatusReport", req, cb);
    }
    async DescribeTaskTemplates(req, cb) {
        return this.request("DescribeTaskTemplates", req, cb);
    }
    async GetOfflineInstanceList(req, cb) {
        return this.request("GetOfflineInstanceList", req, cb);
    }
    async CreateOfflineTask(req, cb) {
        return this.request("CreateOfflineTask", req, cb);
    }
    async BindProjectExecutorResource(req, cb) {
        return this.request("BindProjectExecutorResource", req, cb);
    }
    async ModifyWorkflowSchedule(req, cb) {
        return this.request("ModifyWorkflowSchedule", req, cb);
    }
    async DescribeTableSchemaInfo(req, cb) {
        return this.request("DescribeTableSchemaInfo", req, cb);
    }
    async ModifyRule(req, cb) {
        return this.request("ModifyRule", req, cb);
    }
    async DescribeFunctionTypes(req, cb) {
        return this.request("DescribeFunctionTypes", req, cb);
    }
    async EnableProject(req, cb) {
        return this.request("EnableProject", req, cb);
    }
    async DeleteDataSources(req, cb) {
        return this.request("DeleteDataSources", req, cb);
    }
    async DescribeWorkflowByFordIds(req, cb) {
        return this.request("DescribeWorkflowByFordIds", req, cb);
    }
    async DeleteIntegrationNode(req, cb) {
        return this.request("DeleteIntegrationNode", req, cb);
    }
    async DescribeParentTask(req, cb) {
        return this.request("DescribeParentTask", req, cb);
    }
    async StopIntegrationTask(req, cb) {
        return this.request("StopIntegrationTask", req, cb);
    }
    async DescribeTableMetas(req, cb) {
        return this.request("DescribeTableMetas", req, cb);
    }
    async DescribeRealTimeTaskSpeed(req, cb) {
        return this.request("DescribeRealTimeTaskSpeed", req, cb);
    }
    async DescribeInstanceList(req, cb) {
        return this.request("DescribeInstanceList", req, cb);
    }
    async DescribeTableBasicInfo(req, cb) {
        return this.request("DescribeTableBasicInfo", req, cb);
    }
    async DescribeAlarmReceiver(req, cb) {
        return this.request("DescribeAlarmReceiver", req, cb);
    }
    async DescribeRelatedTasksByTaskId(req, cb) {
        return this.request("DescribeRelatedTasksByTaskId", req, cb);
    }
    async DescribeSchedulerInstanceStatus(req, cb) {
        return this.request("DescribeSchedulerInstanceStatus", req, cb);
    }
    async DescribeInstanceLog(req, cb) {
        return this.request("DescribeInstanceLog", req, cb);
    }
    async BatchUpdateIntegrationTasks(req, cb) {
        return this.request("BatchUpdateIntegrationTasks", req, cb);
    }
    async DeleteResourceFiles(req, cb) {
        return this.request("DeleteResourceFiles", req, cb);
    }
    async CreateOpsMakePlan(req, cb) {
        return this.request("CreateOpsMakePlan", req, cb);
    }
    async GetTaskInstance(req, cb) {
        return this.request("GetTaskInstance", req, cb);
    }
    async FreezeOpsTasks(req, cb) {
        return this.request("FreezeOpsTasks", req, cb);
    }
    async DescribeRuleGroup(req, cb) {
        return this.request("DescribeRuleGroup", req, cb);
    }
    async ModifyTaskScript(req, cb) {
        return this.request("ModifyTaskScript", req, cb);
    }
    async DescribeInstanceLogDetail(req, cb) {
        return this.request("DescribeInstanceLogDetail", req, cb);
    }
    async FindAllFolder(req, cb) {
        return this.request("FindAllFolder", req, cb);
    }
    async DescribeDatabaseInfoList(req, cb) {
        return this.request("DescribeDatabaseInfoList", req, cb);
    }
    async ModifyRuleTemplate(req, cb) {
        return this.request("ModifyRuleTemplate", req, cb);
    }
    async DescribeDependOpsTasks(req, cb) {
        return this.request("DescribeDependOpsTasks", req, cb);
    }
    async JudgeResourceFile(req, cb) {
        return this.request("JudgeResourceFile", req, cb);
    }
    async RemoveDatabase(req, cb) {
        return this.request("RemoveDatabase", req, cb);
    }
    async CommitIntegrationTask(req, cb) {
        return this.request("CommitIntegrationTask", req, cb);
    }
    async DeleteOfflineTask(req, cb) {
        return this.request("DeleteOfflineTask", req, cb);
    }
    async CreateHiveTableByDDL(req, cb) {
        return this.request("CreateHiveTableByDDL", req, cb);
    }
    async DeleteDsFolder(req, cb) {
        return this.request("DeleteDsFolder", req, cb);
    }
    async DescribeDutyScheduleList(req, cb) {
        return this.request("DescribeDutyScheduleList", req, cb);
    }
    async ReportSchema(req, cb) {
        return this.request("ReportSchema", req, cb);
    }
    async DescribeTaskLineage(req, cb) {
        return this.request("DescribeTaskLineage", req, cb);
    }
    async DescribeResourceManagePathTrees(req, cb) {
        return this.request("DescribeResourceManagePathTrees", req, cb);
    }
    async BatchForceSuccessIntegrationTaskInstances(req, cb) {
        return this.request("BatchForceSuccessIntegrationTaskInstances", req, cb);
    }
    async DescribeTasksForCodeTemplate(req, cb) {
        return this.request("DescribeTasksForCodeTemplate", req, cb);
    }
    async DescribeStreamTaskLogList(req, cb) {
        return this.request("DescribeStreamTaskLogList", req, cb);
    }
    async CreateRule(req, cb) {
        return this.request("CreateRule", req, cb);
    }
    async DescribeRuleGroupSubscription(req, cb) {
        return this.request("DescribeRuleGroupSubscription", req, cb);
    }
    async DescribeEvent(req, cb) {
        return this.request("DescribeEvent", req, cb);
    }
    async DescribeAllByFolderNew(req, cb) {
        return this.request("DescribeAllByFolderNew", req, cb);
    }
    async CreateBaseProject(req, cb) {
        return this.request("CreateBaseProject", req, cb);
    }
    async DescribeTableQualityDetails(req, cb) {
        return this.request("DescribeTableQualityDetails", req, cb);
    }
    async TriggerEvent(req, cb) {
        return this.request("TriggerEvent", req, cb);
    }
    async DescribeRuleGroupsByPage(req, cb) {
        return this.request("DescribeRuleGroupsByPage", req, cb);
    }
    async DescribeColumnsMeta(req, cb) {
        return this.request("DescribeColumnsMeta", req, cb);
    }
    async RegisterEventListener(req, cb) {
        return this.request("RegisterEventListener", req, cb);
    }
    async DescribeInstanceDetailInfo(req, cb) {
        return this.request("DescribeInstanceDetailInfo", req, cb);
    }
    async DescribeBatchOperateTask(req, cb) {
        return this.request("DescribeBatchOperateTask", req, cb);
    }
    async DeleteRule(req, cb) {
        return this.request("DeleteRule", req, cb);
    }
    async CheckAlarmRegularNameExist(req, cb) {
        return this.request("CheckAlarmRegularNameExist", req, cb);
    }
    async CheckIntegrationTaskNameExists(req, cb) {
        return this.request("CheckIntegrationTaskNameExists", req, cb);
    }
    async DescribeTablePartitions(req, cb) {
        return this.request("DescribeTablePartitions", req, cb);
    }
    async DescribeRulesByPage(req, cb) {
        return this.request("DescribeRulesByPage", req, cb);
    }
    async ModifyProject(req, cb) {
        return this.request("ModifyProject", req, cb);
    }
    async ModifyMonitorStatus(req, cb) {
        return this.request("ModifyMonitorStatus", req, cb);
    }
    async DeleteProjectUsers(req, cb) {
        return this.request("DeleteProjectUsers", req, cb);
    }
    async GetJobStatus(req, cb) {
        return this.request("GetJobStatus", req, cb);
    }
    async DeleteFile(req, cb) {
        return this.request("DeleteFile", req, cb);
    }
    async BatchMakeUpIntegrationTasks(req, cb) {
        return this.request("BatchMakeUpIntegrationTasks", req, cb);
    }
    async UploadContent(req, cb) {
        return this.request("UploadContent", req, cb);
    }
    async DescribeDsTaskVersionList(req, cb) {
        return this.request("DescribeDsTaskVersionList", req, cb);
    }
    async SubmitCustomFunction(req, cb) {
        return this.request("SubmitCustomFunction", req, cb);
    }
    async DescribeCodeTemplateDetail(req, cb) {
        return this.request("DescribeCodeTemplateDetail", req, cb);
    }
    async BatchRunOpsTask(req, cb) {
        return this.request("BatchRunOpsTask", req, cb);
    }
    async DescribeRuleTemplatesByPage(req, cb) {
        return this.request("DescribeRuleTemplatesByPage", req, cb);
    }
    async ModifyWorkflowInfo(req, cb) {
        return this.request("ModifyWorkflowInfo", req, cb);
    }
    async DescribeRuleExecStat(req, cb) {
        return this.request("DescribeRuleExecStat", req, cb);
    }
    async RunRerunScheduleInstances(req, cb) {
        return this.request("RunRerunScheduleInstances", req, cb);
    }
    async DescribeRuleGroupExecResultsByPage(req, cb) {
        return this.request("DescribeRuleGroupExecResultsByPage", req, cb);
    }
    async DeleteResourceFile(req, cb) {
        return this.request("DeleteResourceFile", req, cb);
    }
    async BatchCreateIntegrationTaskAlarms(req, cb) {
        return this.request("BatchCreateIntegrationTaskAlarms", req, cb);
    }
    async DagInstances(req, cb) {
        return this.request("DagInstances", req, cb);
    }
    async DescribeDataServicePublishedApiList(req, cb) {
        return this.request("DescribeDataServicePublishedApiList", req, cb);
    }
    async ModifyApproveStatus(req, cb) {
        return this.request("ModifyApproveStatus", req, cb);
    }
    async DescribeInstanceLogFile(req, cb) {
        return this.request("DescribeInstanceLogFile", req, cb);
    }
    async BatchCreateTaskVersionAsync(req, cb) {
        return this.request("BatchCreateTaskVersionAsync", req, cb);
    }
    async DescribeTaskLockStatus(req, cb) {
        return this.request("DescribeTaskLockStatus", req, cb);
    }
    async ReportTable(req, cb) {
        return this.request("ReportTable", req, cb);
    }
    async DescribeExecStrategy(req, cb) {
        return this.request("DescribeExecStrategy", req, cb);
    }
    async FreezeTasksByWorkflowIds(req, cb) {
        return this.request("FreezeTasksByWorkflowIds", req, cb);
    }
    async DescribeTableLineage(req, cb) {
        return this.request("DescribeTableLineage", req, cb);
    }
    async ModifyTaskLinksDs(req, cb) {
        return this.request("ModifyTaskLinksDs", req, cb);
    }
    async SuspendIntegrationTask(req, cb) {
        return this.request("SuspendIntegrationTask", req, cb);
    }
    async CreateWorkflowDs(req, cb) {
        return this.request("CreateWorkflowDs", req, cb);
    }
    async DescribeEventCases(req, cb) {
        return this.request("DescribeEventCases", req, cb);
    }
    async DeleteLink(req, cb) {
        return this.request("DeleteLink", req, cb);
    }
    async DeleteWorkflowById(req, cb) {
        return this.request("DeleteWorkflowById", req, cb);
    }
    async DescribeRules(req, cb) {
        return this.request("DescribeRules", req, cb);
    }
    async RegisterDsEvent(req, cb) {
        return this.request("RegisterDsEvent", req, cb);
    }
    async StartIntegrationTask(req, cb) {
        return this.request("StartIntegrationTask", req, cb);
    }
    async LockIntegrationTask(req, cb) {
        return this.request("LockIntegrationTask", req, cb);
    }
    async DescribeReportTaskList(req, cb) {
        return this.request("DescribeReportTaskList", req, cb);
    }
    async ListBatchDetail(req, cb) {
        return this.request("ListBatchDetail", req, cb);
    }
    async BatchDeleteIntegrationTasks(req, cb) {
        return this.request("BatchDeleteIntegrationTasks", req, cb);
    }
    async DescribeIntegrationTasks(req, cb) {
        return this.request("DescribeIntegrationTasks", req, cb);
    }
    async DescribeStatisticInstanceStatusTrendOps(req, cb) {
        return this.request("DescribeStatisticInstanceStatusTrendOps", req, cb);
    }
    async DescribeColumnLineage(req, cb) {
        return this.request("DescribeColumnLineage", req, cb);
    }
    async DescribeTableInfoList(req, cb) {
        return this.request("DescribeTableInfoList", req, cb);
    }
    async RegisterDsEventListener(req, cb) {
        return this.request("RegisterDsEventListener", req, cb);
    }
    async DescribeDsTaskVersionInfo(req, cb) {
        return this.request("DescribeDsTaskVersionInfo", req, cb);
    }
    async DeleteTaskDs(req, cb) {
        return this.request("DeleteTaskDs", req, cb);
    }
    async DescribeTestRunningRecord(req, cb) {
        return this.request("DescribeTestRunningRecord", req, cb);
    }
    async DeleteTaskAlarmRegular(req, cb) {
        return this.request("DeleteTaskAlarmRegular", req, cb);
    }
    async DescribePendingSubmitTaskList(req, cb) {
        return this.request("DescribePendingSubmitTaskList", req, cb);
    }
    async DescribeRuleExecResults(req, cb) {
        return this.request("DescribeRuleExecResults", req, cb);
    }
    async DeleteTaskLineage(req, cb) {
        return this.request("DeleteTaskLineage", req, cb);
    }
    async DescribeSuccessorTaskInfoList(req, cb) {
        return this.request("DescribeSuccessorTaskInfoList", req, cb);
    }
    async DeleteResource(req, cb) {
        return this.request("DeleteResource", req, cb);
    }
    async DescribeQualityScoreTrend(req, cb) {
        return this.request("DescribeQualityScoreTrend", req, cb);
    }
    async DescribeDependTaskLists(req, cb) {
        return this.request("DescribeDependTaskLists", req, cb);
    }
    async SaveCustomFunction(req, cb) {
        return this.request("SaveCustomFunction", req, cb);
    }
    async RemoveTable(req, cb) {
        return this.request("RemoveTable", req, cb);
    }
    async ModifyDataSource(req, cb) {
        return this.request("ModifyDataSource", req, cb);
    }
    async DescribeTaskAlarmRegulations(req, cb) {
        return this.request("DescribeTaskAlarmRegulations", req, cb);
    }
    async DescribeIntegrationVersionNodesInfo(req, cb) {
        return this.request("DescribeIntegrationVersionNodesInfo", req, cb);
    }
    async RenewWorkflowOwnerDs(req, cb) {
        return this.request("RenewWorkflowOwnerDs", req, cb);
    }
    async DescribeProjectUsers(req, cb) {
        return this.request("DescribeProjectUsers", req, cb);
    }
    async DescribeWorkflowSchedulerInfoDs(req, cb) {
        return this.request("DescribeWorkflowSchedulerInfoDs", req, cb);
    }
    async DeleteCustomFunction(req, cb) {
        return this.request("DeleteCustomFunction", req, cb);
    }
    async DescribeTemplateDimCount(req, cb) {
        return this.request("DescribeTemplateDimCount", req, cb);
    }
    async DescribeFolderWorkflowList(req, cb) {
        return this.request("DescribeFolderWorkflowList", req, cb);
    }
    async DescribeIntegrationTask(req, cb) {
        return this.request("DescribeIntegrationTask", req, cb);
    }
    async DescribeDataServicePublishedApiDetail(req, cb) {
        return this.request("DescribeDataServicePublishedApiDetail", req, cb);
    }
    async DescribeWorkflowTaskCount(req, cb) {
        return this.request("DescribeWorkflowTaskCount", req, cb);
    }
    async ModifyTaskInfoDs(req, cb) {
        return this.request("ModifyTaskInfoDs", req, cb);
    }
    async CountOpsInstanceState(req, cb) {
        return this.request("CountOpsInstanceState", req, cb);
    }
    async DescribeIntegrationStatisticsInstanceTrend(req, cb) {
        return this.request("DescribeIntegrationStatisticsInstanceTrend", req, cb);
    }
    async DescribeWorkflowExecuteById(req, cb) {
        return this.request("DescribeWorkflowExecuteById", req, cb);
    }
    async CreateIntegrationNode(req, cb) {
        return this.request("CreateIntegrationNode", req, cb);
    }
    async BatchDeleteOpsTasks(req, cb) {
        return this.request("BatchDeleteOpsTasks", req, cb);
    }
    async ListInstances(req, cb) {
        return this.request("ListInstances", req, cb);
    }
    async CommitRuleGroupTask(req, cb) {
        return this.request("CommitRuleGroupTask", req, cb);
    }
    async DescribeInstanceLastLog(req, cb) {
        return this.request("DescribeInstanceLastLog", req, cb);
    }
    async ModifyTaskLinks(req, cb) {
        return this.request("ModifyTaskLinks", req, cb);
    }
    async RegisterEvent(req, cb) {
        return this.request("RegisterEvent", req, cb);
    }
    async DescribeOpsMakePlans(req, cb) {
        return this.request("DescribeOpsMakePlans", req, cb);
    }
    async DescribeTenantProjects(req, cb) {
        return this.request("DescribeTenantProjects", req, cb);
    }
    async ModifyRuleGroupSubscription(req, cb) {
        return this.request("ModifyRuleGroupSubscription", req, cb);
    }
    async DescribeWorkflowCanvasInfo(req, cb) {
        return this.request("DescribeWorkflowCanvasInfo", req, cb);
    }
    async DescribeDatabaseMetas(req, cb) {
        return this.request("DescribeDatabaseMetas", req, cb);
    }
    async RemoveSchema(req, cb) {
        return this.request("RemoveSchema", req, cb);
    }
    async CreateTaskFolder(req, cb) {
        return this.request("CreateTaskFolder", req, cb);
    }
    async ModifyTaskAlarmRegular(req, cb) {
        return this.request("ModifyTaskAlarmRegular", req, cb);
    }
    async BatchResumeIntegrationTasks(req, cb) {
        return this.request("BatchResumeIntegrationTasks", req, cb);
    }
    async ModifyTaskInfo(req, cb) {
        return this.request("ModifyTaskInfo", req, cb);
    }
    async DescribeRuleTemplate(req, cb) {
        return this.request("DescribeRuleTemplate", req, cb);
    }
    async SubmitTask(req, cb) {
        return this.request("SubmitTask", req, cb);
    }
    async SubmitSqlTask(req, cb) {
        return this.request("SubmitSqlTask", req, cb);
    }
    async DescribeDataSourceInfoList(req, cb) {
        return this.request("DescribeDataSourceInfoList", req, cb);
    }
    async GetPaginationTaskScript(req, cb) {
        return this.request("GetPaginationTaskScript", req, cb);
    }
    async DescribeFunctionKinds(req, cb) {
        return this.request("DescribeFunctionKinds", req, cb);
    }
    async UpdateProjectUserRole(req, cb) {
        return this.request("UpdateProjectUserRole", req, cb);
    }
    async DescribeTaskDetailDs(req, cb) {
        return this.request("DescribeTaskDetailDs", req, cb);
    }
    async TaskLog(req, cb) {
        return this.request("TaskLog", req, cb);
    }
    async DescribeTrendStat(req, cb) {
        return this.request("DescribeTrendStat", req, cb);
    }
    async UpdateCodeTemplate(req, cb) {
        return this.request("UpdateCodeTemplate", req, cb);
    }
    async UpdateWorkflowOwner(req, cb) {
        return this.request("UpdateWorkflowOwner", req, cb);
    }
    async GetOfflineDIInstanceList(req, cb) {
        return this.request("GetOfflineDIInstanceList", req, cb);
    }
    async ModifyIntegrationNode(req, cb) {
        return this.request("ModifyIntegrationNode", req, cb);
    }
    async RobAndLockIntegrationTask(req, cb) {
        return this.request("RobAndLockIntegrationTask", req, cb);
    }
    async DescribeProject(req, cb) {
        return this.request("DescribeProject", req, cb);
    }
    async DescribeTableMeta(req, cb) {
        return this.request("DescribeTableMeta", req, cb);
    }
    async CreateDataModel(req, cb) {
        return this.request("CreateDataModel", req, cb);
    }
    async DescribeManualTriggerRecordPage(req, cb) {
        return this.request("DescribeManualTriggerRecordPage", req, cb);
    }
    async GetIntegrationNodeColumnSchema(req, cb) {
        return this.request("GetIntegrationNodeColumnSchema", req, cb);
    }
    async DescribeBaseBizCatalogs(req, cb) {
        return this.request("DescribeBaseBizCatalogs", req, cb);
    }
    async BatchRerunIntegrationTaskInstances(req, cb) {
        return this.request("BatchRerunIntegrationTaskInstances", req, cb);
    }
    async DescribeTableLineageInfo(req, cb) {
        return this.request("DescribeTableLineageInfo", req, cb);
    }
    async ReportDatabase(req, cb) {
        return this.request("ReportDatabase", req, cb);
    }
    async DescribeRealTimeTaskInstanceNodeInfo(req, cb) {
        return this.request("DescribeRealTimeTaskInstanceNodeInfo", req, cb);
    }
    async DescribeIntegrationStatistics(req, cb) {
        return this.request("DescribeIntegrationStatistics", req, cb);
    }
    async DescribeReportTaskDetail(req, cb) {
        return this.request("DescribeReportTaskDetail", req, cb);
    }
    async BatchStopWorkflowsByIds(req, cb) {
        return this.request("BatchStopWorkflowsByIds", req, cb);
    }
    async DescribeRuleTemplates(req, cb) {
        return this.request("DescribeRuleTemplates", req, cb);
    }
    async DeleteDataModel(req, cb) {
        return this.request("DeleteDataModel", req, cb);
    }
}
