{"version": 3, "file": "form-label-wrap.js", "sources": ["../../../../../../packages/components/form/src/form-label-wrap.tsx"], "sourcesContent": ["import {\n  Fragment,\n  computed,\n  defineComponent,\n  inject,\n  nextTick,\n  onBeforeUnmount,\n  onMounted,\n  onUpdated,\n  ref,\n  watch,\n} from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\nimport { throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { formContextKey, formItemContextKey } from './constants'\n\nimport type { CSSProperties } from 'vue'\n\nconst COMPONENT_NAME = 'ElLabelWrap'\nexport default defineComponent({\n  name: COMPONENT_NAME,\n  props: {\n    isAutoWidth: Boolean,\n    updateAll: Boolean,\n  },\n\n  setup(props, { slots }) {\n    const formContext = inject(formContextKey, undefined)\n    const formItemContext = inject(formItemContextKey)\n    if (!formItemContext)\n      throwError(\n        COMPONENT_NAME,\n        'usage: <el-form-item><label-wrap /></el-form-item>'\n      )\n\n    const ns = useNamespace('form')\n\n    const el = ref<HTMLElement>()\n    const computedWidth = ref(0)\n\n    const getLabelWidth = () => {\n      if (el.value?.firstElementChild) {\n        const width = window.getComputedStyle(el.value.firstElementChild).width\n        return Math.ceil(Number.parseFloat(width))\n      } else {\n        return 0\n      }\n    }\n\n    const updateLabelWidth = (action: 'update' | 'remove' = 'update') => {\n      nextTick(() => {\n        if (slots.default && props.isAutoWidth) {\n          if (action === 'update') {\n            computedWidth.value = getLabelWidth()\n          } else if (action === 'remove') {\n            formContext?.deregisterLabelWidth(computedWidth.value)\n          }\n        }\n      })\n    }\n    const updateLabelWidthFn = () => updateLabelWidth('update')\n\n    onMounted(() => {\n      updateLabelWidthFn()\n    })\n    onBeforeUnmount(() => {\n      updateLabelWidth('remove')\n    })\n    onUpdated(() => updateLabelWidthFn())\n\n    watch(computedWidth, (val, oldVal) => {\n      if (props.updateAll) {\n        formContext?.registerLabelWidth(val, oldVal)\n      }\n    })\n\n    useResizeObserver(\n      computed(\n        () => (el.value?.firstElementChild ?? null) as HTMLElement | null\n      ),\n      updateLabelWidthFn\n    )\n\n    return () => {\n      if (!slots) return null\n\n      const { isAutoWidth } = props\n      if (isAutoWidth) {\n        const autoLabelWidth = formContext?.autoLabelWidth\n        const hasLabel = formItemContext?.hasLabel\n        const style: CSSProperties = {}\n        if (hasLabel && autoLabelWidth && autoLabelWidth !== 'auto') {\n          const marginWidth = Math.max(\n            0,\n            Number.parseInt(autoLabelWidth, 10) - computedWidth.value\n          )\n          const labelPosition =\n            formItemContext.labelPosition || formContext.labelPosition\n\n          const marginPosition =\n            labelPosition === 'left' ? 'marginRight' : 'marginLeft'\n\n          if (marginWidth) {\n            style[marginPosition] = `${marginWidth}px`\n          }\n        }\n        return (\n          <div ref={el} class={[ns.be('item', 'label-wrap')]} style={style}>\n            {slots.default?.()}\n          </div>\n        )\n      } else {\n        return <Fragment ref={el}>{slots.default?.()}</Fragment>\n      }\n    }\n  },\n})\n"], "names": ["COMPONENT_NAME", "defineComponent", "name", "props", "isAutoWidth", "Boolean", "updateAll", "slots", "inject", "formContextKey", "formContext", "formItemContextKey", "throwError", "ns", "useNamespace", "el", "ref", "computedWidth", "<PERSON><PERSON><PERSON><PERSON>", "value", "width", "window", "getComputedStyle", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "ceil", "Number", "parseFloat", "nextTick", "default", "action", "onMounted", "updateLabelWidthFn", "onBeforeUnmount", "onUpdated", "watch", "registerLabel<PERSON>th", "useResizeObserver", "computed", "<PERSON><PERSON><PERSON><PERSON>", "style", "autoLabel<PERSON>idth", "marginWid<PERSON>", "max", "parseInt", "labelPosition", "formItemContext", "marginPosition", "_createVNode", "be", "_Fragment"], "mappings": ";;;;;;;;;;AAmBA,MAAMA,cAAc,GAAG,aAAvB,CAAA;AACA,oBAAeC,mBAAe,CAAC;AAC7BC,EAAAA,IAAI,EAAEF,cADuB;AAE7BG,EAAAA,KAAK,EAAE;AACLC,IAAAA,WAAW,EAAEC,OADR;AAELC,IAAAA,SAAS,EAAED,OAAAA;GAJgB;;IAOxB;AAAUE,GAAAA,EAAAA;AAAF,IAAW,MAAA,WAAA,GAAAC,UAAA,CAAAC,wBAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AACtB,IAAA,MAAMC,eAAcF,GAAAA,UAAM,CAAAG;AAC1B,IAAA,IAAA,CAAA,eAAqB;MACjBC,+BACFA,EAAAA;AAKF,IAAA,MAAMC,EAAE,GAAGC,kBAAY,CAAC,MAAD,CAAvB,CAAA;IAEA,MAAMC,EAAE,GAAGC,OAAG,EAAd,CAAA;AACA,IAAA,MAAMC,aAAa,GAAGD,OAAG,CAAC,CAAD,CAAzB,CAAA;;MAEME,IAAAA,EAAAA,CAAAA;AACJ,MAAA,IAAIH,CAAE,EAACI,GAAH,EAAA,CAAA,sBAA6B,CAAA,GAAA,EAAA,CAAA,iBAAA,EAAA;AAC/B,QAAA,MAAMC,KAAK,GAAGC,MAAM,CAACC,gBAAP,CAAwBP,EAAE,CAACI,KAAH,CAASI,iBAAjC,CAAA,CAAoDH,KAAlE,CAAA;QACA,OAAOI,IAAI,CAACC,IAAL,CAAUC,MAAM,CAACC,UAAP,CAAkBP,KAAlB,CAAV,CAAP,CAAA;AACD,OAHD,MAGO;AACL,QAAA,OAAO,CAAP,CAAA;AACD,OAAA;KANH,CAAA;;AASA,MAAAQ;AACEA,QAAAA,IAAAA,KAAe,CAAA,OAAA,IAAA,KAAA,CAAA,WAAA,EAAA;AACb,UAAA,UAAUC,KAAN,QAAsB,EAACzB;YACrB0B,aAAW,CAAA,KAAA,GAAU,aAAA,EAAA,CAAA;AACvBb,WAAAA,MAAAA,IAAAA,MAAcE,KAAQD,QAAAA,EAAAA;AACvB,YAAM,WAAU,IAAA,IAAA,SAAV,GAAyB,WAAA,CAAA,oBAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA;AAC9BR,WAAAA;AACD,SAAA;AACF,OAAA,CAAA,CAAA;AACF,KAAA,CAAA;IACF,MAVD,kBAAA,GAAA,MAAA,gBAAA,CAAA,QAAA,CAAA,CAAA;;AAWA,MAAA;;AAEAqB,IAAAA,mBAAgB,CAAA,MAAA;MACdC,gBAAkB,CAAA,QAAA,CAAA,CAAA;AACnB,KAFQ,CAAT,CAAA;AAGAC,IAAAA,aAAAA,CAAAA,MAAgB,kBAAM,EAAA,CAAA,CAAA;aACJ,CAAA,aAAC,QAAD,MAAhB,KAAA;AACD,MAFD,IAAA,KAAA,CAAA,SAAA,EAAA;AAGAC,QAAAA,WAAU,IAAwB,IAAA,GAAA,KAAA,CAAA,GAAA,WAAlC,CAAA,kBAAA,CAAA,GAAA,EAAA,MAAA,CAAA,CAAA;AAEAC,OAAAA;MACE,CAAIhC;AACFO,IAAAA,sBAAa0B,CAAAA,YAAAA,CAAAA,MAAAA;AACd,MAAA,IAAA,EAAA,EAAA,EAAA,CAAA;AACF,MAJD,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,iBAAA,KAAA,IAAA,GAAA,EAAA,GAAA,IAAA,CAAA;AAMAC,KAAAA,CAAAA,EAAAA,kBACEC,CAAAA,CAAAA;AAMF,IAAA,OAAO,MAAM;AACX,MAAA,IAAI,EAAC/B,EAAAA,EAAL,CAAY;MAEZ,IAAM,CAAA,KAAA;AAAEH,QAAAA,OAAAA,IAAAA,CAAAA;AAAF,MAAA,MAAN;;AACA,OAAA,GAAIA;AACF,MAAA,IAAA,WAAoB,EAAA;AACpB,QAAA,MAAMmC,cAA0B,GAAA,WAAEA,IAAlC,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,cAAA,CAAA;QACA,MAAMC,QAAuB,GAA7B,eAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,eAAA,CAAA,QAAA,CAAA;;AACA,QAAA,IAAID,QAAQ,IAAIE,cAAZ,IAA8BA,cAAc,KAAK,MAArD,EAA6D;AAC3D,UAAA,MAAMC,WAAW,GAAGlB,IAAI,CAACmB,GAAL,CAClB,CADkB,EAElBjB,MAAM,CAACkB,QAAP,CAAgBH,cAAhB,EAAgC,EAAhC,IAAsCxB,aAAa,CAACE,KAFlC,CAApB,CAAA;UAIA,MAAM0B,aAAa,GACjBC,eAAe,CAACD,aAAhB,IAAiCnC,WAAW,CAACmC,aAD/C,CAAA;UAGA,MAAME,cAAc,GAClBF,aAAa,KAAK,MAAlB,GAA2B,aAA3B,GAA2C,YAD7C,CAAA;;AAGA,YAAA,oBAAiB,CAAA,GAAA,CAAA,EAAA,WAAA,CAAA,EAAA,CAAA,CAAA;AACfL,WAAAA;AACD,SAAA;AACF,QAAA,OAAAQ,eAAA,CAAA,KAAA,EAAA;;AACD,UAAA,OAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,YAAA,CAAA,CAAA;AAAA,UAAA,OACYjC,EADZ,KAAA;WACuB,CAAA,CAAA,EAAA,GAAA,KAAIkC,CAAAA,OAAH,KAAA,IAAA,GAAA,KADxB,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;aAC6DT;QAD7D,OAEKjC,eAFL,CAAA2C,YAAA,EAAA;AAKD,UAAM,KAAA,EAAA,EAAA;AACL,SAAA,EAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;;;AACD,GAAA;;;;;"}