{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-calc-input-width/index.ts"], "sourcesContent": ["import { computed, ref, shallowRef } from 'vue'\nimport { useResizeObserver } from '@vueuse/core'\n\nexport function useCalcInputWidth() {\n  const calculatorRef = shallowRef<HTMLElement>()\n  const calculatorWidth = ref(0)\n  const MINIMUM_INPUT_WIDTH = 11\n\n  const inputStyle = computed(() => ({\n    minWidth: `${Math.max(calculatorWidth.value, MINIMUM_INPUT_WIDTH)}px`,\n  }))\n\n  const resetCalculatorWidth = () => {\n    calculatorWidth.value =\n      calculatorRef.value?.getBoundingClientRect().width ?? 0\n  }\n\n  useResizeObserver(calculatorRef, resetCalculatorWidth)\n\n  return {\n    calculatorRef,\n    calculatorWidth,\n    inputStyle,\n  }\n}\n"], "names": ["shallowRef", "ref", "computed", "useResizeObserver"], "mappings": ";;;;;;;AAEO,SAAS,iBAAiB,GAAG;AACpC,EAAE,MAAM,aAAa,GAAGA,cAAU,EAAE,CAAC;AACrC,EAAE,MAAM,eAAe,GAAGC,OAAG,CAAC,CAAC,CAAC,CAAC;AACjC,EAAE,MAAM,mBAAmB,GAAG,EAAE,CAAC;AACjC,EAAE,MAAM,UAAU,GAAGC,YAAQ,CAAC,OAAO;AACrC,IAAI,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC;AACzE,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,MAAM,oBAAoB,GAAG,MAAM;AACrC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,eAAe,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,qBAAqB,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;AACnI,GAAG,CAAC;AACJ,EAAEC,sBAAiB,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAAC;AACzD,EAAE,OAAO;AACT,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,UAAU;AACd,GAAG,CAAC;AACJ;;;;"}