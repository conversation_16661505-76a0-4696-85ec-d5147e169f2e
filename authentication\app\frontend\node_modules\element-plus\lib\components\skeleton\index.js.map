{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/skeleton/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\nimport Skeleton from './src/skeleton.vue'\nimport SkeletonItem from './src/skeleton-item.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSkeleton: SFCWithInstall<typeof Skeleton> & {\n  SkeletonItem: typeof SkeletonItem\n} = withInstall(Skeleton, {\n  SkeletonItem,\n})\nexport const ElSkeletonItem: SFCWithInstall<typeof SkeletonItem> =\n  withNoopInstall(SkeletonItem)\nexport default ElSkeleton\n\nexport * from './src/skeleton'\nexport * from './src/skeleton-item'\n"], "names": ["withInstall", "Skeleton", "SkeletonItem", "withNoopInstall"], "mappings": ";;;;;;;;;;AAGY,MAAC,UAAU,GAAGA,mBAAW,CAACC,qBAAQ,EAAE;AAChD,gBAAEC,yBAAY;AACd,CAAC,EAAE;AACS,MAAC,cAAc,GAAGC,uBAAe,CAACD,yBAAY;;;;;;;;"}