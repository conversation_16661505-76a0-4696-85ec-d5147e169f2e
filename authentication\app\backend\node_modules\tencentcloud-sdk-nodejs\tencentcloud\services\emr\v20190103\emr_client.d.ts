import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { AddNodeResourceConfigRequest, TerminateTasksRequest, DescribeEmrApplicationStaticsRequest, DescribeTrinoQueryInfoResponse, ResetYarnConfigRequest, DescribeServiceNodeInfosResponse, AttachDisksRequest, AddUsersForUserManagerResponse, DeleteNodeResourceConfigRequest, TerminateTasksResponse, DescribeSparkQueriesResponse, DescribeInspectionTaskResultRequest, InquiryPriceCreateInstanceResponse, DescribeHDFSStorageInfoRequest, DescribeGlobalConfigResponse, ModifyResourceRequest, RunJobFlowRequest, ScaleOutClusterRequest, DescribeHiveQueriesRequest, CreateClusterResponse, DescribeCvmQuotaResponse, AddMetricScaleStrategyResponse, DescribeNodeResourceConfigFastRequest, SyncPodStateRequest, DescribeSLInstanceListResponse, DescribeServiceConfGroupInfosRequest, InquiryPriceUpdateInstanceRequest, AttachDisksResponse, DescribeSparkQueriesRequest, DescribeInstancesResponse, DescribeUsersForUserManagerResponse, DescribeResourceScheduleDiffDetailRequest, InquirePriceRenewEmrResponse, DescribeSLInstanceRequest, ModifyAutoRenewFlagRequest, AddNodeResourceConfigResponse, ModifyResourceScheduleConfigRequest, DeleteAutoScaleStrategyResponse, DescribeStarRocksQueryInfoResponse, ModifyResourceSchedulerResponse, DescribeAutoScaleRecordsRequest, DescribeJobFlowResponse, ModifyResourceResponse, ModifyUserGroupResponse, DescribeHDFSStorageInfoResponse, ModifyPodNumResponse, ModifyGlobalConfigResponse, CreateGroupsSTDResponse, ModifySLInstanceBasicRequest, CreateCloudInstanceResponse, ScaleOutInstanceRequest, ModifyAutoScaleStrategyRequest, DescribeGroupsSTDRequest, DescribeKyuubiQueryInfoResponse, DescribeServiceConfGroupInfosResponse, ModifyYarnDeployRequest, DescribeClusterFlowStatusDetailRequest, DescribeDAGInfoResponse, RunJobFlowResponse, DescribeHiveQueriesResponse, DescribeInstanceRenewNodesResponse, CreateSLInstanceResponse, DescribeClusterNodesResponse, DescribeYarnApplicationsRequest, DeleteUserManagerUserListRequest, DescribeResourceScheduleResponse, DescribeKyuubiQueryInfoRequest, DescribeNodeResourceConfigFastResponse, DescribeInsightListResponse, DescribeAutoScaleRecordsResponse, DescribeInspectionTaskResultResponse, DescribeCvmQuotaRequest, CreateInstanceRequest, ModifyUsersOfGroupSTDRequest, DeleteAutoScaleStrategyRequest, DescribeInstancesListResponse, ModifyResourcePoolsResponse, DescribeInstanceRenewNodesRequest, DescribeYarnApplicationsResponse, InquiryPriceCreateInstanceRequest, DescribeSLInstanceResponse, ModifySLInstanceResponse, ModifyGlobalConfigRequest, DescribeImpalaQueriesResponse, SetNodeResourceConfigDefaultResponse, ModifyInstanceBasicResponse, DescribeTrinoQueryInfoRequest, ModifyResourcePoolsRequest, TerminateInstanceResponse, DescribeSparkApplicationsResponse, ScaleOutInstanceResponse, ModifyUserManagerPwdResponse, CreateInstanceResponse, DescribeServiceNodeInfosRequest, DescribeAutoScaleGroupGlobalConfRequest, ModifyResourceScheduleConfigResponse, DescribeResourceScheduleDiffDetailResponse, DescribeYarnQueueResponse, CreateGroupsSTDRequest, ConvertPreToPostClusterRequest, DescribeNodeDataDisksRequest, DescribeJobFlowRequest, ModifyYarnDeployResponse, DescribeUsersForUserManagerRequest, DescribeInsightListRequest, DescribeYarnScheduleHistoryRequest, StartStopServiceOrMonitorResponse, DescribeYarnQueueRequest, ModifyAutoRenewFlagResponse, DescribeYarnScheduleHistoryResponse, DescribeNodeSpecRequest, TerminateInstanceRequest, ModifyResourcesTagsRequest, TerminateClusterNodesResponse, DescribeSLInstanceListRequest, DeleteGroupsSTDResponse, DescribeImpalaQueriesRequest, ModifyPodNumRequest, ModifyUserGroupRequest, DescribeInstancesRequest, DeployYarnConfRequest, DescribeGroupsSTDResponse, DescribeNodeDataDisksResponse, DescribeInstancesListRequest, ModifyInstanceBasicRequest, AddUsersForUserManagerRequest, DescribeEmrOverviewMetricsRequest, TerminateSLInstanceRequest, DescribeNodeSpecResponse, DescribeStarRocksQueryInfoRequest, ResizeDataDisksResponse, ModifySLInstanceBasicResponse, ResizeDataDisksRequest, ScaleOutClusterResponse, CreateSLInstanceRequest, DescribeGlobalConfigRequest, DescribeHBaseTableOverviewRequest, DescribeClusterFlowStatusDetailResponse, DescribeSparkApplicationsRequest, SyncPodStateResponse, ModifyUserManagerPwdRequest, ModifyInspectionSettingsResponse, DescribeHBaseTableOverviewResponse, AddMetricScaleStrategyRequest, ModifyUsersOfGroupSTDResponse, ResetYarnConfigResponse, DescribeAutoScaleStrategiesResponse, InquiryPriceRenewInstanceResponse, StartStopServiceOrMonitorRequest, DescribeResourceScheduleRequest, DeleteNodeResourceConfigResponse, ConvertPreToPostClusterResponse, ModifyResourcesTagsResponse, SetNodeResourceConfigDefaultRequest, DescribeDAGInfoRequest, DescribeAutoScaleGroupGlobalConfResponse, InquiryPriceUpdateInstanceResponse, ModifyYarnQueueV2Response, ModifyInspectionSettingsRequest, ModifyResourceSchedulerRequest, ModifySLInstanceRequest, CreateClusterRequest, CreateCloudInstanceRequest, ModifyYarnQueueV2Request, DescribeClusterNodesRequest, DescribeEmrOverviewMetricsResponse, TerminateSLInstanceResponse, DescribeAutoScaleStrategiesRequest, DeployYarnConfResponse, DeleteUserManagerUserListResponse, InquiryPriceRenewInstanceRequest, DeleteGroupsSTDRequest, ModifyAutoScaleStrategyResponse, InquiryPriceScaleOutInstanceRequest, DescribeEmrApplicationStaticsResponse, InquirePriceRenewEmrRequest, InquiryPriceScaleOutInstanceResponse, TerminateClusterNodesRequest } from "./emr_models";
/**
 * emr client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 修改用户密码（用户管理）
     */
    ModifyUserManagerPwd(req: ModifyUserManagerPwdRequest, cb?: (error: string, rep: ModifyUserManagerPwdResponse) => void): Promise<ModifyUserManagerPwdResponse>;
    /**
     * 创建EMR集群实例
     */
    CreateCluster(req: CreateClusterRequest, cb?: (error: string, rep: CreateClusterResponse) => void): Promise<CreateClusterResponse>;
    /**
     * 修改资源调度中队列信息
     */
    ModifyYarnQueueV2(req: ModifyYarnQueueV2Request, cb?: (error: string, rep: ModifyYarnQueueV2Response) => void): Promise<ModifyYarnQueueV2Response>;
    /**
     * yarn资源调度-部署生效
     */
    DeployYarnConf(req: DeployYarnConfRequest, cb?: (error: string, rep: DeployYarnConfResponse) => void): Promise<DeployYarnConfResponse>;
    /**
     * 获取账户的CVM配额
     */
    DescribeCvmQuota(req: DescribeCvmQuotaRequest, cb?: (error: string, rep: DescribeCvmQuotaResponse) => void): Promise<DescribeCvmQuotaResponse>;
    /**
     * 销毁EMR实例。此接口仅支持弹性MapReduce正式计费版本。
     */
    TerminateInstance(req: TerminateInstanceRequest, cb?: (error: string, rep: TerminateInstanceResponse) => void): Promise<TerminateInstanceResponse>;
    /**
     * 获取Hbase表级监控数据概览接口
     */
    DescribeHBaseTableOverview(req: DescribeHBaseTableOverviewRequest, cb?: (error: string, rep: DescribeHBaseTableOverviewResponse) => void): Promise<DescribeHBaseTableOverviewResponse>;
    /**
     * 查询服务进程信息
     */
    DescribeServiceNodeInfos(req: DescribeServiceNodeInfosRequest, cb?: (error: string, rep: DescribeServiceNodeInfosResponse) => void): Promise<DescribeServiceNodeInfosResponse>;
    /**
     * 该接口已废弃，请使用DeployYarnConf完成部署生效

部署生效。已废弃，请使用`DeployYarnConf`接口进行部署生效
     */
    ModifyYarnDeploy(req: ModifyYarnDeployRequest, cb?: (error: string, rep: ModifyYarnDeployResponse) => void): Promise<ModifyYarnDeployResponse>;
    /**
     * 查询节点规格
     */
    DescribeNodeSpec(req: DescribeNodeSpecRequest, cb?: (error: string, rep: DescribeNodeSpecResponse) => void): Promise<DescribeNodeSpecResponse>;
    /**
     * 查询Kyuubi查询信息
     */
    DescribeKyuubiQueryInfo(req: DescribeKyuubiQueryInfoRequest, cb?: (error: string, rep: DescribeKyuubiQueryInfoResponse) => void): Promise<DescribeKyuubiQueryInfoResponse>;
    /**
     * 删除自动扩缩容规则，后台销毁根据该规则扩缩容出来的节点
     */
    DeleteAutoScaleStrategy(req: DeleteAutoScaleStrategyRequest, cb?: (error: string, rep: DeleteAutoScaleStrategyResponse) => void): Promise<DeleteAutoScaleStrategyResponse>;
    /**
     * 扩容集群节点
     */
    ScaleOutCluster(req: ScaleOutClusterRequest, cb?: (error: string, rep: ScaleOutClusterResponse) => void): Promise<ScaleOutClusterResponse>;
    /**
     * 查询待续费节点信息
     */
    DescribeInstanceRenewNodes(req: DescribeInstanceRenewNodesRequest, cb?: (error: string, rep: DescribeInstanceRenewNodesResponse) => void): Promise<DescribeInstanceRenewNodesResponse>;
    /**
     * YARN资源调度-变更详情
     */
    DescribeResourceScheduleDiffDetail(req: DescribeResourceScheduleDiffDetailRequest, cb?: (error: string, rep: DescribeResourceScheduleDiffDetailResponse) => void): Promise<DescribeResourceScheduleDiffDetailResponse>;
    /**
     * 查询流程任务
     */
    DescribeJobFlow(req: DescribeJobFlowRequest, cb?: (error: string, rep: DescribeJobFlowResponse) => void): Promise<DescribeJobFlowResponse>;
    /**
     * 获取hive查询信息
     */
    DescribeHiveQueries(req: DescribeHiveQueriesRequest, cb?: (error: string, rep: DescribeHiveQueriesResponse) => void): Promise<DescribeHiveQueriesResponse>;
    /**
     * 查询Trino(PrestoSQL)查询信息
     */
    DescribeTrinoQueryInfo(req: DescribeTrinoQueryInfoRequest, cb?: (error: string, rep: DescribeTrinoQueryInfoResponse) => void): Promise<DescribeTrinoQueryInfoResponse>;
    /**
     * 查询集群列表
     */
    DescribeInstancesList(req: DescribeInstancesListRequest, cb?: (error: string, rep: DescribeInstancesListResponse) => void): Promise<DescribeInstancesListResponse>;
    /**
     * 变更用户组用户信息
     */
    ModifyUsersOfGroupSTD(req: ModifyUsersOfGroupSTDRequest, cb?: (error: string, rep: ModifyUsersOfGroupSTDResponse) => void): Promise<ModifyUsersOfGroupSTDResponse>;
    /**
     * 强制修改标签
     */
    ModifyResourcesTags(req: ModifyResourcesTagsRequest, cb?: (error: string, rep: ModifyResourcesTagsResponse) => void): Promise<ModifyResourcesTagsResponse>;
    /**
     * 创建流程作业
     */
    RunJobFlow(req: RunJobFlowRequest, cb?: (error: string, rep: RunJobFlowResponse) => void): Promise<RunJobFlowResponse>;
    /**
     * DescribeImpalaQueries
     */
    DescribeImpalaQueries(req: DescribeImpalaQueriesRequest, cb?: (error: string, rep: DescribeImpalaQueriesResponse) => void): Promise<DescribeImpalaQueriesResponse>;
    /**
     * 获取资源调度中的队列信息
     */
    DescribeYarnQueue(req: DescribeYarnQueueRequest, cb?: (error: string, rep: DescribeYarnQueueResponse) => void): Promise<DescribeYarnQueueResponse>;
    /**
     * 获取集群的自动扩缩容的详细记录
     */
    DescribeAutoScaleRecords(req: DescribeAutoScaleRecordsRequest, cb?: (error: string, rep: DescribeAutoScaleRecordsResponse) => void): Promise<DescribeAutoScaleRecordsResponse>;
    /**
     * 扩容节点
     */
    ScaleOutInstance(req: ScaleOutInstanceRequest, cb?: (error: string, rep: ScaleOutInstanceResponse) => void): Promise<ScaleOutInstanceResponse>;
    /**
     * 云盘挂载
     */
    AttachDisks(req: AttachDisksRequest, cb?: (error: string, rep: AttachDisksResponse) => void): Promise<AttachDisksResponse>;
    /**
     * 设置当前集群的某个节点规格配置为默认或取消默认
     */
    SetNodeResourceConfigDefault(req: SetNodeResourceConfigDefaultRequest, cb?: (error: string, rep: SetNodeResourceConfigDefaultResponse) => void): Promise<SetNodeResourceConfigDefaultResponse>;
    /**
     * 查询StarRocks查询信息
     */
    DescribeStarRocksQueryInfo(req: DescribeStarRocksQueryInfoRequest, cb?: (error: string, rep: DescribeStarRocksQueryInfoResponse) => void): Promise<DescribeStarRocksQueryInfoResponse>;
    /**
     * 快速获取当前集群的节点规格配置
     */
    DescribeNodeResourceConfigFast(req: DescribeNodeResourceConfigFastRequest, cb?: (error: string, rep: DescribeNodeResourceConfigFastResponse) => void): Promise<DescribeNodeResourceConfigFastResponse>;
    /**
     * 查询DAG信息
     */
    DescribeDAGInfo(req: DescribeDAGInfoRequest, cb?: (error: string, rep: DescribeDAGInfoResponse) => void): Promise<DescribeDAGInfoResponse>;
    /**
     * 删除用户列表（用户管理）
     */
    DeleteUserManagerUserList(req: DeleteUserManagerUserListRequest, cb?: (error: string, rep: DeleteUserManagerUserListResponse) => void): Promise<DeleteUserManagerUserListResponse>;
    /**
     * 包月集群转按量集群（不含cdb）
     */
    ConvertPreToPostCluster(req: ConvertPreToPostClusterRequest, cb?: (error: string, rep: ConvertPreToPostClusterResponse) => void): Promise<ConvertPreToPostClusterResponse>;
    /**
     * 本接口（TerminateSLInstance）用于销毁Serverless HBase实例
     */
    TerminateSLInstance(req: TerminateSLInstanceRequest, cb?: (error: string, rep: TerminateSLInstanceResponse) => void): Promise<TerminateSLInstanceResponse>;
    /**
     * 调整Pod数量
     */
    ModifyPodNum(req: ModifyPodNumRequest, cb?: (error: string, rep: ModifyPodNumResponse) => void): Promise<ModifyPodNumResponse>;
    /**
     * 本接口（CreateSLInstance）用于创建Serverless HBase实例
- 接口调用成功，会创建Serverless HBase实例，创建实例请求成功会返回创建实例的InstaceId和请求的 RequestID。
- 接口为异步接口，接口返回时操作并未立即完成，实例操作结果可以通过调用DescribeInstancesList查看当前实例的StatusDesc状态。
     */
    CreateSLInstance(req: CreateSLInstanceRequest, cb?: (error: string, rep: CreateSLInstanceResponse) => void): Promise<CreateSLInstanceResponse>;
    /**
     * 本接口（DescribeSLInstanceList）用于查询Serverless HBase实例列表详细信息
     */
    DescribeSLInstanceList(req: DescribeSLInstanceListRequest, cb?: (error: string, rep: DescribeSLInstanceListResponse) => void): Promise<DescribeSLInstanceListResponse>;
    /**
     * 修改自动扩缩容规则
     */
    ModifyAutoScaleStrategy(req: ModifyAutoScaleStrategyRequest, cb?: (error: string, rep: ModifyAutoScaleStrategyResponse) => void): Promise<ModifyAutoScaleStrategyResponse>;
    /**
     * EMR同步TKE中POD状态
     */
    SyncPodState(req: SyncPodStateRequest, cb?: (error: string, rep: SyncPodStateResponse) => void): Promise<SyncPodStateResponse>;
    /**
     * 本接口（ModifySLInstance）用于Serverless HBase变配实例。
- 接口调用成功，会创建Serverless HBase实例，创建实例请求成功会返回请求的 RequestID。
- 接口为异步接口，接口返回时操作并未立即完成，实例操作结果可以通过调用DescribeInstancesList查看当前实例的StatusDesc状态。
     */
    ModifySLInstance(req: ModifySLInstanceRequest, cb?: (error: string, rep: ModifySLInstanceResponse) => void): Promise<ModifySLInstanceResponse>;
    /**
     * 创建EMR集群实例
     */
    CreateInstance(req: CreateInstanceRequest, cb?: (error: string, rep: CreateInstanceResponse) => void): Promise<CreateInstanceResponse>;
    /**
     * 创建实例询价
     */
    InquiryPriceCreateInstance(req: InquiryPriceCreateInstanceRequest, cb?: (error: string, rep: InquiryPriceCreateInstanceResponse) => void): Promise<InquiryPriceCreateInstanceResponse>;
    /**
     * 添加扩缩容规则，按负载和时间
     */
    AddMetricScaleStrategy(req: AddMetricScaleStrategyRequest, cb?: (error: string, rep: AddMetricScaleStrategyResponse) => void): Promise<AddMetricScaleStrategyResponse>;
    /**
     * 云数据盘扩容
     */
    ResizeDataDisks(req: ResizeDataDisksRequest, cb?: (error: string, rep: ResizeDataDisksResponse) => void): Promise<ResizeDataDisksResponse>;
    /**
     * 用于启停服务 重启服务等功能
     */
    StartStopServiceOrMonitor(req: StartStopServiceOrMonitorRequest, cb?: (error: string, rep: StartStopServiceOrMonitorResponse) => void): Promise<StartStopServiceOrMonitorResponse>;
    /**
     * 增加当前集群的节点规格配置
     */
    AddNodeResourceConfig(req: AddNodeResourceConfigRequest, cb?: (error: string, rep: AddNodeResourceConfigResponse) => void): Promise<AddNodeResourceConfigResponse>;
    /**
     * 缩容Task节点
     */
    TerminateTasks(req: TerminateTasksRequest, cb?: (error: string, rep: TerminateTasksResponse) => void): Promise<TerminateTasksResponse>;
    /**
     * 查询节点数据盘信息
     */
    DescribeNodeDataDisks(req: DescribeNodeDataDisksRequest, cb?: (error: string, rep: DescribeNodeDataDisksResponse) => void): Promise<DescribeNodeDataDisksResponse>;
    /**
     * 查询YARN资源调度的全局配置
     */
    DescribeGlobalConfig(req: DescribeGlobalConfigRequest, cb?: (error: string, rep: DescribeGlobalConfigResponse) => void): Promise<DescribeGlobalConfigResponse>;
    /**
     * 查询集群节点信息
     */
    DescribeClusterNodes(req: DescribeClusterNodesRequest, cb?: (error: string, rep: DescribeClusterNodesResponse) => void): Promise<DescribeClusterNodesResponse>;
    /**
     * 查看yarn资源调度的调度历史。废弃，请使用流程中心查看历史记录。
     */
    DescribeYarnScheduleHistory(req: DescribeYarnScheduleHistoryRequest, cb?: (error: string, rep: DescribeYarnScheduleHistoryResponse) => void): Promise<DescribeYarnScheduleHistoryResponse>;
    /**
     * 获取洞察结果信息
     */
    DescribeInsightList(req: DescribeInsightListRequest, cb?: (error: string, rep: DescribeInsightListResponse) => void): Promise<DescribeInsightListResponse>;
    /**
     * 设置巡检任务配置
     */
    ModifyInspectionSettings(req: ModifyInspectionSettingsRequest, cb?: (error: string, rep: ModifyInspectionSettingsResponse) => void): Promise<ModifyInspectionSettingsResponse>;
    /**
     * 变配询价
     */
    InquiryPriceUpdateInstance(req: InquiryPriceUpdateInstanceRequest, cb?: (error: string, rep: InquiryPriceUpdateInstanceResponse) => void): Promise<InquiryPriceUpdateInstanceResponse>;
    /**
     * 创建EMR容器集群实例
     */
    CreateCloudInstance(req: CreateCloudInstanceRequest, cb?: (error: string, rep: CreateCloudInstanceResponse) => void): Promise<CreateCloudInstanceResponse>;
    /**
     * 扩容询价. 当扩容时候，请通过该接口查询价格。
     */
    InquiryPriceScaleOutInstance(req: InquiryPriceScaleOutInstanceRequest, cb?: (error: string, rep: InquiryPriceScaleOutInstanceResponse) => void): Promise<InquiryPriceScaleOutInstanceResponse>;
    /**
     * 查询监控概览页指标数据
     */
    DescribeEmrOverviewMetrics(req: DescribeEmrOverviewMetricsRequest, cb?: (error: string, rep: DescribeEmrOverviewMetricsResponse) => void): Promise<DescribeEmrOverviewMetricsResponse>;
    /**
     * serverless hbase修改实例名称
     */
    ModifySLInstanceBasic(req: ModifySLInstanceBasicRequest, cb?: (error: string, rep: ModifySLInstanceBasicResponse) => void): Promise<ModifySLInstanceBasicResponse>;
    /**
     * 销毁集群节点
     */
    TerminateClusterNodes(req: TerminateClusterNodesRequest, cb?: (error: string, rep: TerminateClusterNodesResponse) => void): Promise<TerminateClusterNodesResponse>;
    /**
     * 修改YARN资源调度的资源配置
     */
    ResetYarnConfig(req: ResetYarnConfigRequest, cb?: (error: string, rep: ResetYarnConfigResponse) => void): Promise<ResetYarnConfigResponse>;
    /**
     * 用户管理-修改用户组
     */
    ModifyUserGroup(req: ModifyUserGroupRequest, cb?: (error: string, rep: ModifyUserGroupResponse) => void): Promise<ModifyUserGroupResponse>;
    /**
     * yarn application 统计接口查询
     */
    DescribeEmrApplicationStatics(req: DescribeEmrApplicationStaticsRequest, cb?: (error: string, rep: DescribeEmrApplicationStaticsResponse) => void): Promise<DescribeEmrApplicationStaticsResponse>;
    /**
     * 本接口（DescribeSLInstance）用于查询 Serverless HBase实例基本信息
     */
    DescribeSLInstance(req: DescribeSLInstanceRequest, cb?: (error: string, rep: DescribeSLInstanceResponse) => void): Promise<DescribeSLInstanceResponse>;
    /**
     * 查询Spark查询信息列表
     */
    DescribeSparkQueries(req: DescribeSparkQueriesRequest, cb?: (error: string, rep: DescribeSparkQueriesResponse) => void): Promise<DescribeSparkQueriesResponse>;
    /**
     * 修改YARN资源调度的全局配置
     */
    ModifyGlobalConfig(req: ModifyGlobalConfigRequest, cb?: (error: string, rep: ModifyGlobalConfigResponse) => void): Promise<ModifyGlobalConfigResponse>;
    /**
     * 获取自动扩缩容规则
     */
    DescribeAutoScaleStrategies(req: DescribeAutoScaleStrategiesRequest, cb?: (error: string, rep: DescribeAutoScaleStrategiesResponse) => void): Promise<DescribeAutoScaleStrategiesResponse>;
    /**
     * 获取自动扩缩容全局配置
     */
    DescribeAutoScaleGroupGlobalConf(req: DescribeAutoScaleGroupGlobalConfRequest, cb?: (error: string, rep: DescribeAutoScaleGroupGlobalConfResponse) => void): Promise<DescribeAutoScaleGroupGlobalConfResponse>;
    /**
     * 描述服务配置组信息
     */
    DescribeServiceConfGroupInfos(req: DescribeServiceConfGroupInfosRequest, cb?: (error: string, rep: DescribeServiceConfGroupInfosResponse) => void): Promise<DescribeServiceConfGroupInfosResponse>;
    /**
     * 获取巡检任务结果列表
     */
    DescribeInspectionTaskResult(req: DescribeInspectionTaskResultRequest, cb?: (error: string, rep: DescribeInspectionTaskResultResponse) => void): Promise<DescribeInspectionTaskResultResponse>;
    /**
     * DescribeYarnApplications
     */
    DescribeYarnApplications(req: DescribeYarnApplicationsRequest, cb?: (error: string, rep: DescribeYarnApplicationsResponse) => void): Promise<DescribeYarnApplicationsResponse>;
    /**
     * 该接口支持安装了OpenLdap组件的集群。
批量导出用户。对于kerberos集群，如果需要kertab文件下载地址，可以将NeedKeytabInfo设置为true；注意SupportDownLoadKeyTab为true，但是DownLoadKeyTabUrl为空字符串，表示keytab文件在后台没有准备好（正在生成）。
     */
    DescribeUsersForUserManager(req: DescribeUsersForUserManagerRequest, cb?: (error: string, rep: DescribeUsersForUserManagerResponse) => void): Promise<DescribeUsersForUserManagerResponse>;
    /**
     * 续费询价。
     */
    InquiryPriceRenewInstance(req: InquiryPriceRenewInstanceRequest, cb?: (error: string, rep: InquiryPriceRenewInstanceResponse) => void): Promise<InquiryPriceRenewInstanceResponse>;
    /**
     * 查询用户组
     */
    DescribeGroupsSTD(req: DescribeGroupsSTDRequest, cb?: (error: string, rep: DescribeGroupsSTDResponse) => void): Promise<DescribeGroupsSTDResponse>;
    /**
     * 查询HDFS存储文件信息
     */
    DescribeHDFSStorageInfo(req: DescribeHDFSStorageInfoRequest, cb?: (error: string, rep: DescribeHDFSStorageInfoResponse) => void): Promise<DescribeHDFSStorageInfoResponse>;
    /**
     * 集群续费询价。
     */
    InquirePriceRenewEmr(req: InquirePriceRenewEmrRequest, cb?: (error: string, rep: InquirePriceRenewEmrResponse) => void): Promise<InquirePriceRenewEmrResponse>;
    /**
     * 查询EMR任务运行详情状态
     */
    DescribeClusterFlowStatusDetail(req: DescribeClusterFlowStatusDetailRequest, cb?: (error: string, rep: DescribeClusterFlowStatusDetailResponse) => void): Promise<DescribeClusterFlowStatusDetailResponse>;
    /**
     * 查询集群实例信息
     */
    DescribeInstances(req: DescribeInstancesRequest, cb?: (error: string, rep: DescribeInstancesResponse) => void): Promise<DescribeInstancesResponse>;
    /**
     * 用户管理-批量创建用户组
     */
    CreateGroupsSTD(req: CreateGroupsSTDRequest, cb?: (error: string, rep: CreateGroupsSTDResponse) => void): Promise<CreateGroupsSTDResponse>;
    /**
     * 该接口支持安装了OpenLdap组件的集群。
新增用户列表（用户管理）。
     */
    AddUsersForUserManager(req: AddUsersForUserManagerRequest, cb?: (error: string, rep: AddUsersForUserManagerResponse) => void): Promise<AddUsersForUserManagerResponse>;
    /**
     * 获取spark应用列表
     */
    DescribeSparkApplications(req: DescribeSparkApplicationsRequest, cb?: (error: string, rep: DescribeSparkApplicationsResponse) => void): Promise<DescribeSparkApplicationsResponse>;
    /**
     * 前提：预付费集群
资源级别开启或关闭自动续费
     */
    ModifyAutoRenewFlag(req: ModifyAutoRenewFlagRequest, cb?: (error: string, rep: ModifyAutoRenewFlagResponse) => void): Promise<ModifyAutoRenewFlagResponse>;
    /**
     * 修改了yarn的资源调度器，点击部署生效。
     */
    ModifyResourceScheduler(req: ModifyResourceSchedulerRequest, cb?: (error: string, rep: ModifyResourceSchedulerResponse) => void): Promise<ModifyResourceSchedulerResponse>;
    /**
     * 查询YARN资源调度数据信息。已废弃，请使用`DescribeYarnQueue`去查询队列信息。
     */
    DescribeResourceSchedule(req: DescribeResourceScheduleRequest, cb?: (error: string, rep: DescribeResourceScheduleResponse) => void): Promise<DescribeResourceScheduleResponse>;
    /**
     * 已废弃，请使用ModifyYarnQueueV2来修改队列配置，近一年无相关日志

修改YARN资源调度的资源配置。已废弃，请使用`ModifyYarnQueueV2`来修改队列配置
     */
    ModifyResourceScheduleConfig(req: ModifyResourceScheduleConfigRequest, cb?: (error: string, rep: ModifyResourceScheduleConfigResponse) => void): Promise<ModifyResourceScheduleConfigResponse>;
    /**
     * 变配实例
     */
    ModifyResource(req: ModifyResourceRequest, cb?: (error: string, rep: ModifyResourceResponse) => void): Promise<ModifyResourceResponse>;
    /**
     * 批量删除用户组
     */
    DeleteGroupsSTD(req: DeleteGroupsSTDRequest, cb?: (error: string, rep: DeleteGroupsSTDResponse) => void): Promise<DeleteGroupsSTDResponse>;
    /**
     * 已废弃，请使用DeployYarnConf\\n，近一年未被调用

刷新YARN的动态资源池。已废弃，请使用`DeployYarnConf`
     */
    ModifyResourcePools(req: ModifyResourcePoolsRequest, cb?: (error: string, rep: ModifyResourcePoolsResponse) => void): Promise<ModifyResourcePoolsResponse>;
    /**
     * 删除当前集群的节点规格配置
     */
    DeleteNodeResourceConfig(req: DeleteNodeResourceConfigRequest, cb?: (error: string, rep: DeleteNodeResourceConfigResponse) => void): Promise<DeleteNodeResourceConfigResponse>;
    /**
     * 修改集群名称
     */
    ModifyInstanceBasic(req: ModifyInstanceBasicRequest, cb?: (error: string, rep: ModifyInstanceBasicResponse) => void): Promise<ModifyInstanceBasicResponse>;
}
