{"name": "ag-grid-community", "version": "30.2.1", "description": "Advanced Data Grid / Data Table supporting Javascript / Typescript / React / Angular / Vue", "main": "./dist/ag-grid-community.cjs.js", "module": "./dist/ag-grid-community.auto.esm.js", "scripts": {"clean": "rimraf dist main.d.ts src/styles .hash", "build": "npm run customise && npx gulp build && npm run rollup", "build-prod": "npm run build", "package": "npx gulp package", "customise": "node customise.js", "rollup": "mkdir -p dist && node build.js"}, "types": "./main.d.ts", "repository": {"type": "git", "url": "https://github.com/ag-grid/ag-grid.git"}, "keywords": ["ag", "ag-grid", "datagrid", "data-grid", "datatable", "data-table", "grid", "table", "react", "table", "angular", "angular-component", "react", "react-component", "reactjs", "vue", "v<PERSON><PERSON><PERSON>"], "jspm": {"main": "jspm-main"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ag-grid/ag-grid/issues"}, "browserslist": ["> 1%", "last 2 versions", "not ie >= 0", "not ie_mob >= 0", "not blackberry > 0"], "homepage": "https://www.ag-grid.com/", "dependencies": {}, "devDependencies": {"@ag-grid-community/all-modules": "~30.2.1", "@ag-grid-community/core": "~30.2.1", "@ag-grid-community/client-side-row-model": "~30.2.1", "@ag-grid-community/csv-export": "~30.2.1", "@ag-grid-community/infinite-row-model": "~30.2.1", "@ag-grid-community/styles": "~30.2.1", "@types/node": "12.20.20", "gulp": "4.0.2", "gulp-clean": "0.4.0", "gulp-concat": "2.6.1", "gulp-header": "2.0.9", "gulp-rename": "^1.4.0", "gulp-replace": "1.1.2", "gulp-sourcemaps": "2.6.0", "gulp-tsd": "0.1.0", "gulp-typescript": "5.0.0", "merge2": "1.2.0", "rollup": "1.21.2", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-typescript2": "0.24.3", "typescript": "~4.3.5", "rimraf": "3.0.2"}}