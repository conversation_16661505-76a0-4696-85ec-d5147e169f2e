{"name": "ag-grid-vue3", "description": "AG Grid Vue 3 Component", "version": "30.2.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "main": "./lib/AgGridVue.js", "typings": "./lib/AgGridVue.d.ts", "repository": {"type": "git", "url": "https://github.com/ag-grid/ag-grid.git"}, "bugs": {"url": "https://github.com/ag-grid/ag-grid/issues"}, "homepage": "https://www.ag-grid.com/", "keywords": ["grid", "data", "table", "vue", "v<PERSON><PERSON><PERSON>"], "scripts": {"clean": "rimraf lib dist .hash", "build": "npx gulp copy-from-module-source && NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build --target lib src/AgGridVue.ts && npx tsc && rm ./dist/demo.html", "build-docs-initial": "npm run build", "build-prod": "NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build --target lib src/AgGridVue.ts && npx tsc --sourceMap false && rm ./dist/demo.html", "test:unit": "vue-cli-service test:unit", "test:e2ex": "vue-cli-service test:e2e"}, "peerDependencies": {"ag-grid-community": "~30.2.1", "vue": "^3.0.0"}, "devDependencies": {"ag-grid-community": "~30.2.1", "vue": "^3.0.0", "@types/jest": "^24.0.19", "@vue/cli-plugin-e2e-cypress": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-unit-jest": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/test-utils": "^2.0.0-0", "typescript": "~4.3.5", "vue-jest": "^5.0.0-0", "gulp": "^4.0.0", "gulp-replace": "^1.0.0", "rimraf": "3.0.2"}, "publishConfig": {"access": "public"}}