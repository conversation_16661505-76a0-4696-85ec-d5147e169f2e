{"version": 3, "file": "link2.js", "sources": ["../../../../../../packages/components/link/src/link.vue"], "sourcesContent": ["<template>\n  <a\n    :class=\"linkKls\"\n    :href=\"disabled || !href ? undefined : href\"\n    :target=\"disabled || !href ? undefined : target\"\n    @click=\"handleClick\"\n  >\n    <el-icon v-if=\"icon\"><component :is=\"icon\" /></el-icon>\n    <span v-if=\"$slots.default\" :class=\"ns.e('inner')\">\n      <slot />\n    </span>\n\n    <slot v-if=\"$slots.icon\" name=\"icon\" />\n  </a>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useGlobalConfig } from '@element-plus/components/config-provider'\nimport { useDeprecated, useNamespace } from '@element-plus/hooks'\nimport { isBoolean } from '@element-plus/utils'\nimport { linkEmits, linkProps } from './link'\n\ndefineOptions({\n  name: 'ElLink',\n})\nconst props = defineProps(linkProps)\nconst emit = defineEmits(linkEmits)\nconst globalConfig = useGlobalConfig('link')\n\nuseDeprecated(\n  {\n    scope: 'el-link',\n    from: 'The underline option (boolean)',\n    replacement: \"'always' | 'hover' | 'never'\",\n    version: '3.0.0',\n    ref: 'https://element-plus.org/en-US/component/link.html#underline',\n  },\n  computed(() => isBoolean(props.underline))\n)\n\nconst ns = useNamespace('link')\n\nconst linkKls = computed(() => [\n  ns.b(),\n  ns.m(props.type ?? globalConfig.value?.type ?? 'default'),\n  ns.is('disabled', props.disabled),\n  ns.is('underline', underline.value === 'always'),\n  ns.is('hover-underline', underline.value === 'hover' && !props.disabled),\n])\n\n// Boolean compatibility\nconst underline = computed(() => {\n  if (isBoolean(props.underline)) {\n    return props.underline ? 'hover' : 'never'\n  } else return props.underline ?? globalConfig.value?.underline ?? 'hover'\n})\n\nfunction handleClick(event: MouseEvent) {\n  if (!props.disabled) emit('click', event)\n}\n</script>\n"], "names": ["useGlobalConfig", "useDeprecated", "computed", "isBoolean", "useNamespace"], "mappings": ";;;;;;;;;;;;;uCAwBc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAGA,IAAM,MAAA,YAAA,GAAeA,gCAAgB,MAAM,CAAA,CAAA;AAE3C,IAAAC,mBAAA,CAAA;AAAA,MACE,KAAA,EAAA,SAAA;AAAA,MAAA,IACS,EAAA,gCAAA;AAAA,MAAA,WACD,EAAA,8BAAA;AAAA,MAAA,OACO,EAAA,OAAA;AAAA,MAAA,GACJ,EAAA,8DAAA;AAAA,KAAA,EAAAC,YACJ,CAAA,MAAAC,eAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IACP,MAAA,EAAA,GAAAC,oBAAA,CAAA,MAAA,CAAA,CAAA;AAAA,IAAA,MACS,OAAA,GAAAF,YAAgB,CAAA,MAAA;AAAgB,MAC3C,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AAEA,MAAM,OAAA;AAEN,QAAM,EAAA,CAAA,CAAA,EAAA;AAAyB,UACxB,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,EAAA,GAAA,YAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,SAAA,CAAA;AAAA,UACA,CAAA,EAAA,CAAA,YAA2B,KAAA,CAAA,QAAA,CAAA;AAAwB,QACrD,EAAA,CAAA,EAAe,CAAA,WAAA,EAAA,SAAc,CAAA,KAAA,KAAA,QAAA,CAAA;AAAA,QAC7B,EAAA,CAAA,EAAgB,CAAA,iBAAA,EAAA,eAA4B,KAAA,OAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA;AAAA,OAC/C,CAAA;AAAuE,KACxE,CAAA,CAAA;AAGD,IAAM,MAAA,SAAA,GAAYA,aAAS,MAAM;AAC/B,MAAI,IAAA,EAAA,EAAA,EAAA,EAAA,EAAU,CAAM;AAClB,MAAO,IAAAC,eAAA,CAAA,eAA4B,CAAA,EAAA;AAAA,eACvB,KAAA,CAAA,SAAmB,GAAA,OAAA,GAAA,OAAA,CAAA;AAAiC,OACnE;AAED,QAAA,mBAAwC,KAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,EAAA,GAAA,YAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,OAAA,CAAA;AACtC,KAAA,CAAA,CAAA;AAAwC,IAC1C,SAAA,WAAA,CAAA,KAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}