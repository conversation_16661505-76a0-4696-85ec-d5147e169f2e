var qs=Object.defineProperty;var tn=n=>{throw TypeError(n)};var nn=(n,e)=>{for(var t in e)qs(n,t,{get:e[t],enumerable:!0})};var dt=(n,e,t)=>e.has(n)||tn("Cannot "+t);var L=(n,e,t)=>(dt(n,e,"read from private field"),t?t.call(n):e.get(n)),V=(n,e,t)=>e.has(n)?tn("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(n):e.set(n,t),te=(n,e,t,s)=>(dt(n,e,"write to private field"),s?s.call(n,t):e.set(n,t),t),c=(n,e,t)=>(dt(n,e,"access private method"),t);var Zt={};nn(Zt,{parsers:()=>Kt});var Kt={};nn(Kt,{__ng_action:()=>Jr,__ng_binding:()=>Yr,__ng_directive:()=>Kr,__ng_interpolation:()=>Qr});var ei=new RegExp(`(\\:not\\()|(([\\.\\#]?)[-\\w]+)|(?:\\[([-.\\w*\\\\$]+)(?:=(["']?)([^\\]"']*)\\5)?\\])|(\\))|(\\s*,\\s*)`,"g");var sn;(function(n){n[n.Emulated=0]="Emulated",n[n.None=2]="None",n[n.ShadowDom=3]="ShadowDom"})(sn||(sn={}));var rn;(function(n){n[n.OnPush=0]="OnPush",n[n.Default=1]="Default"})(rn||(rn={}));var on;(function(n){n[n.None=0]="None",n[n.SignalBased=1]="SignalBased",n[n.HasDecoratorInputTransform=2]="HasDecoratorInputTransform"})(on||(on={}));var D;(function(n){n[n.NONE=0]="NONE",n[n.HTML=1]="HTML",n[n.STYLE=2]="STYLE",n[n.SCRIPT=3]="SCRIPT",n[n.URL=4]="URL",n[n.RESOURCE_URL=5]="RESOURCE_URL"})(D||(D={}));var an;(function(n){n[n.Error=0]="Error",n[n.Warning=1]="Warning",n[n.Ignore=2]="Ignore"})(an||(an={}));var ln;(function(n){n[n.Directive=0]="Directive",n[n.Component=1]="Component",n[n.Injectable=2]="Injectable",n[n.Pipe=3]="Pipe",n[n.NgModule=4]="NgModule"})(ln||(ln={}));var cn;(function(n){n[n.Directive=0]="Directive",n[n.Pipe=1]="Pipe",n[n.NgModule=2]="NgModule"})(cn||(cn={}));var un;(function(n){n[n.Emulated=0]="Emulated",n[n.None=2]="None",n[n.ShadowDom=3]="ShadowDom"})(un||(un={}));var pn;(function(n){n[n.Little=0]="Little",n[n.Big=1]="Big"})(pn||(pn={}));var hn;(function(n){n[n.None=0]="None",n[n.Const=1]="Const"})(hn||(hn={}));var fn;(function(n){n[n.Dynamic=0]="Dynamic",n[n.Bool=1]="Bool",n[n.String=2]="String",n[n.Int=3]="Int",n[n.Number=4]="Number",n[n.Function=5]="Function",n[n.Inferred=6]="Inferred",n[n.None=7]="None"})(fn||(fn={}));var js=void 0;var dn;(function(n){n[n.Minus=0]="Minus",n[n.Plus=1]="Plus"})(dn||(dn={}));var _;(function(n){n[n.Equals=0]="Equals",n[n.NotEquals=1]="NotEquals",n[n.Identical=2]="Identical",n[n.NotIdentical=3]="NotIdentical",n[n.Minus=4]="Minus",n[n.Plus=5]="Plus",n[n.Divide=6]="Divide",n[n.Multiply=7]="Multiply",n[n.Modulo=8]="Modulo",n[n.And=9]="And",n[n.Or=10]="Or",n[n.BitwiseOr=11]="BitwiseOr",n[n.BitwiseAnd=12]="BitwiseAnd",n[n.Lower=13]="Lower",n[n.LowerEquals=14]="LowerEquals",n[n.Bigger=15]="Bigger",n[n.BiggerEquals=16]="BiggerEquals",n[n.NullishCoalesce=17]="NullishCoalesce",n[n.Exponentiation=18]="Exponentiation",n[n.In=19]="In"})(_||(_={}));function zs(n,e){return n==null||e==null?n==e:n.isEquivalent(e)}function Gs(n,e,t){let s=n.length;if(s!==e.length)return!1;for(let r=0;r<s;r++)if(!t(n[r],e[r]))return!1;return!0}function ct(n,e){return Gs(n,e,(t,s)=>t.isEquivalent(s))}var b=class{type;sourceSpan;constructor(e,t){this.type=e||null,this.sourceSpan=t||null}prop(e,t){return new _t(this,e,null,t)}key(e,t,s){return new Ct(this,e,t,s)}callFn(e,t,s){return new xt(this,e,null,t,s)}instantiate(e,t,s){return new St(this,e,t,s)}conditional(e,t=null,s){return new yt(this,e,t,null,s)}equals(e,t){return new C(_.Equals,this,e,null,t)}notEquals(e,t){return new C(_.NotEquals,this,e,null,t)}identical(e,t){return new C(_.Identical,this,e,null,t)}notIdentical(e,t){return new C(_.NotIdentical,this,e,null,t)}minus(e,t){return new C(_.Minus,this,e,null,t)}plus(e,t){return new C(_.Plus,this,e,null,t)}divide(e,t){return new C(_.Divide,this,e,null,t)}multiply(e,t){return new C(_.Multiply,this,e,null,t)}modulo(e,t){return new C(_.Modulo,this,e,null,t)}power(e,t){return new C(_.Exponentiation,this,e,null,t)}and(e,t){return new C(_.And,this,e,null,t)}bitwiseOr(e,t){return new C(_.BitwiseOr,this,e,null,t)}bitwiseAnd(e,t){return new C(_.BitwiseAnd,this,e,null,t)}or(e,t){return new C(_.Or,this,e,null,t)}lower(e,t){return new C(_.Lower,this,e,null,t)}lowerEquals(e,t){return new C(_.LowerEquals,this,e,null,t)}bigger(e,t){return new C(_.Bigger,this,e,null,t)}biggerEquals(e,t){return new C(_.BiggerEquals,this,e,null,t)}isBlank(e){return this.equals(TYPED_NULL_EXPR,e)}nullishCoalesce(e,t){return new C(_.NullishCoalesce,this,e,null,t)}toStmt(){return new It(this,null)}},st=class n extends b{name;constructor(e,t,s){super(t,s),this.name=e}isEquivalent(e){return e instanceof n&&this.name===e.name}isConstant(){return!1}visitExpression(e,t){return e.visitReadVarExpr(this,t)}clone(){return new n(this.name,this.type,this.sourceSpan)}set(e){return new gt(this.name,e,null,this.sourceSpan)}},mt=class n extends b{expr;constructor(e,t,s){super(t,s),this.expr=e}visitExpression(e,t){return e.visitTypeofExpr(this,t)}isEquivalent(e){return e instanceof n&&e.expr.isEquivalent(this.expr)}isConstant(){return this.expr.isConstant()}clone(){return new n(this.expr.clone())}};var gt=class n extends b{name;value;constructor(e,t,s,r){super(s||t.type,r),this.name=e,this.value=t}isEquivalent(e){return e instanceof n&&this.name===e.name&&this.value.isEquivalent(e.value)}isConstant(){return!1}visitExpression(e,t){return e.visitWriteVarExpr(this,t)}clone(){return new n(this.name,this.value.clone(),this.type,this.sourceSpan)}toDeclStmt(e,t){return new bt(this.name,this.value,e,t,this.sourceSpan)}toConstDecl(){return this.toDeclStmt(js,Ae.Final)}},vt=class n extends b{receiver;index;value;constructor(e,t,s,r,i){super(r||s.type,i),this.receiver=e,this.index=t,this.value=s}isEquivalent(e){return e instanceof n&&this.receiver.isEquivalent(e.receiver)&&this.index.isEquivalent(e.index)&&this.value.isEquivalent(e.value)}isConstant(){return!1}visitExpression(e,t){return e.visitWriteKeyExpr(this,t)}clone(){return new n(this.receiver.clone(),this.index.clone(),this.value.clone(),this.type,this.sourceSpan)}},wt=class n extends b{receiver;name;value;constructor(e,t,s,r,i){super(r||s.type,i),this.receiver=e,this.name=t,this.value=s}isEquivalent(e){return e instanceof n&&this.receiver.isEquivalent(e.receiver)&&this.name===e.name&&this.value.isEquivalent(e.value)}isConstant(){return!1}visitExpression(e,t){return e.visitWritePropExpr(this,t)}clone(){return new n(this.receiver.clone(),this.name,this.value.clone(),this.type,this.sourceSpan)}},xt=class n extends b{fn;args;pure;constructor(e,t,s,r,i=!1){super(s,r),this.fn=e,this.args=t,this.pure=i}get receiver(){return this.fn}isEquivalent(e){return e instanceof n&&this.fn.isEquivalent(e.fn)&&ct(this.args,e.args)&&this.pure===e.pure}isConstant(){return!1}visitExpression(e,t){return e.visitInvokeFunctionExpr(this,t)}clone(){return new n(this.fn.clone(),this.args.map(e=>e.clone()),this.type,this.sourceSpan,this.pure)}};var St=class n extends b{classExpr;args;constructor(e,t,s,r){super(s,r),this.classExpr=e,this.args=t}isEquivalent(e){return e instanceof n&&this.classExpr.isEquivalent(e.classExpr)&&ct(this.args,e.args)}isConstant(){return!1}visitExpression(e,t){return e.visitInstantiateExpr(this,t)}clone(){return new n(this.classExpr.clone(),this.args.map(e=>e.clone()),this.type,this.sourceSpan)}},rt=class n extends b{value;constructor(e,t,s){super(t,s),this.value=e}isEquivalent(e){return e instanceof n&&this.value===e.value}isConstant(){return!0}visitExpression(e,t){return e.visitLiteralExpr(this,t)}clone(){return new n(this.value,this.type,this.sourceSpan)}};var Et=class n extends b{value;typeParams;constructor(e,t,s=null,r){super(t,r),this.value=e,this.typeParams=s}isEquivalent(e){return e instanceof n&&this.value.name===e.value.name&&this.value.moduleName===e.value.moduleName}isConstant(){return!1}visitExpression(e,t){return e.visitExternalExpr(this,t)}clone(){return new n(this.value,this.type,this.typeParams,this.sourceSpan)}};var yt=class n extends b{condition;falseCase;trueCase;constructor(e,t,s=null,r,i){super(r||t.type,i),this.condition=e,this.falseCase=s,this.trueCase=t}isEquivalent(e){return e instanceof n&&this.condition.isEquivalent(e.condition)&&this.trueCase.isEquivalent(e.trueCase)&&zs(this.falseCase,e.falseCase)}isConstant(){return!1}visitExpression(e,t){return e.visitConditionalExpr(this,t)}clone(){var e;return new n(this.condition.clone(),this.trueCase.clone(),(e=this.falseCase)==null?void 0:e.clone(),this.type,this.sourceSpan)}};var C=class n extends b{operator;rhs;lhs;constructor(e,t,s,r,i){super(r||t.type,i),this.operator=e,this.rhs=s,this.lhs=t}isEquivalent(e){return e instanceof n&&this.operator===e.operator&&this.lhs.isEquivalent(e.lhs)&&this.rhs.isEquivalent(e.rhs)}isConstant(){return!1}visitExpression(e,t){return e.visitBinaryOperatorExpr(this,t)}clone(){return new n(this.operator,this.lhs.clone(),this.rhs.clone(),this.type,this.sourceSpan)}},_t=class n extends b{receiver;name;constructor(e,t,s,r){super(s,r),this.receiver=e,this.name=t}get index(){return this.name}isEquivalent(e){return e instanceof n&&this.receiver.isEquivalent(e.receiver)&&this.name===e.name}isConstant(){return!1}visitExpression(e,t){return e.visitReadPropExpr(this,t)}set(e){return new wt(this.receiver,this.name,e,null,this.sourceSpan)}clone(){return new n(this.receiver.clone(),this.name,this.type,this.sourceSpan)}},Ct=class n extends b{receiver;index;constructor(e,t,s,r){super(s,r),this.receiver=e,this.index=t}isEquivalent(e){return e instanceof n&&this.receiver.isEquivalent(e.receiver)&&this.index.isEquivalent(e.index)}isConstant(){return!1}visitExpression(e,t){return e.visitReadKeyExpr(this,t)}set(e){return new vt(this.receiver,this.index,e,null,this.sourceSpan)}clone(){return new n(this.receiver.clone(),this.index.clone(),this.type,this.sourceSpan)}},Tt=class n extends b{entries;constructor(e,t,s){super(t,s),this.entries=e}isConstant(){return this.entries.every(e=>e.isConstant())}isEquivalent(e){return e instanceof n&&ct(this.entries,e.entries)}visitExpression(e,t){return e.visitLiteralArrayExpr(this,t)}clone(){return new n(this.entries.map(e=>e.clone()),this.type,this.sourceSpan)}};var kt=class n extends b{entries;valueType=null;constructor(e,t,s){super(t,s),this.entries=e,t&&(this.valueType=t.valueType)}isEquivalent(e){return e instanceof n&&ct(this.entries,e.entries)}isConstant(){return this.entries.every(e=>e.value.isConstant())}visitExpression(e,t){return e.visitLiteralMapExpr(this,t)}clone(){let e=this.entries.map(t=>t.clone());return new n(e,this.type,this.sourceSpan)}};var Ae;(function(n){n[n.None=0]="None",n[n.Final=1]="Final",n[n.Private=2]="Private",n[n.Exported=4]="Exported",n[n.Static=8]="Static"})(Ae||(Ae={}));var it=class{modifiers;sourceSpan;leadingComments;constructor(e=Ae.None,t=null,s){this.modifiers=e,this.sourceSpan=t,this.leadingComments=s}hasModifier(e){return(this.modifiers&e)!==0}addLeadingComment(e){this.leadingComments=this.leadingComments??[],this.leadingComments.push(e)}},bt=class n extends it{name;value;type;constructor(e,t,s,r,i,a){super(r,i,a),this.name=e,this.value=t,this.type=s||t&&t.type||null}isEquivalent(e){return e instanceof n&&this.name===e.name&&(this.value?!!e.value&&this.value.isEquivalent(e.value):!e.value)}visitStatement(e,t){return e.visitDeclareVarStmt(this,t)}};var It=class n extends it{expr;constructor(e,t,s){super(Ae.None,t,s),this.expr=e}isEquivalent(e){return e instanceof n&&this.expr.isEquivalent(e.expr)}visitStatement(e,t){return e.visitExpressionStmt(this,t)}};function Xs(n,e,t){return new st(n,e,t)}var ti=Xs("<unknown>");var mn=class n{static INSTANCE=new n;keyOf(e){if(e instanceof rt&&typeof e.value=="string")return`"${e.value}"`;if(e instanceof rt)return String(e.value);if(e instanceof Tt){let t=[];for(let s of e.entries)t.push(this.keyOf(s));return`[${t.join(",")}]`}else if(e instanceof kt){let t=[];for(let s of e.entries){let r=s.key;s.quoted&&(r=`"${r}"`),t.push(r+":"+this.keyOf(s.value))}return`{${t.join(",")}}`}else{if(e instanceof Et)return`import("${e.value.moduleName}", ${e.value.name})`;if(e instanceof st)return`read(${e.name})`;if(e instanceof mt)return`typeof(${this.keyOf(e.expr)})`;throw new Error(`${this.constructor.name} does not handle expressions of type ${e.constructor.name}`)}}};var o="@angular/core",p=class{static NEW_METHOD="factory";static TRANSFORM_METHOD="transform";static PATCH_DEPS="patchedDeps";static core={name:null,moduleName:o};static namespaceHTML={name:"\u0275\u0275namespaceHTML",moduleName:o};static namespaceMathML={name:"\u0275\u0275namespaceMathML",moduleName:o};static namespaceSVG={name:"\u0275\u0275namespaceSVG",moduleName:o};static element={name:"\u0275\u0275element",moduleName:o};static elementStart={name:"\u0275\u0275elementStart",moduleName:o};static elementEnd={name:"\u0275\u0275elementEnd",moduleName:o};static advance={name:"\u0275\u0275advance",moduleName:o};static syntheticHostProperty={name:"\u0275\u0275syntheticHostProperty",moduleName:o};static syntheticHostListener={name:"\u0275\u0275syntheticHostListener",moduleName:o};static attribute={name:"\u0275\u0275attribute",moduleName:o};static classProp={name:"\u0275\u0275classProp",moduleName:o};static elementContainerStart={name:"\u0275\u0275elementContainerStart",moduleName:o};static elementContainerEnd={name:"\u0275\u0275elementContainerEnd",moduleName:o};static elementContainer={name:"\u0275\u0275elementContainer",moduleName:o};static styleMap={name:"\u0275\u0275styleMap",moduleName:o};static classMap={name:"\u0275\u0275classMap",moduleName:o};static styleProp={name:"\u0275\u0275styleProp",moduleName:o};static interpolate={name:"\u0275\u0275interpolate",moduleName:o};static interpolate1={name:"\u0275\u0275interpolate1",moduleName:o};static interpolate2={name:"\u0275\u0275interpolate2",moduleName:o};static interpolate3={name:"\u0275\u0275interpolate3",moduleName:o};static interpolate4={name:"\u0275\u0275interpolate4",moduleName:o};static interpolate5={name:"\u0275\u0275interpolate5",moduleName:o};static interpolate6={name:"\u0275\u0275interpolate6",moduleName:o};static interpolate7={name:"\u0275\u0275interpolate7",moduleName:o};static interpolate8={name:"\u0275\u0275interpolate8",moduleName:o};static interpolateV={name:"\u0275\u0275interpolateV",moduleName:o};static nextContext={name:"\u0275\u0275nextContext",moduleName:o};static resetView={name:"\u0275\u0275resetView",moduleName:o};static templateCreate={name:"\u0275\u0275template",moduleName:o};static defer={name:"\u0275\u0275defer",moduleName:o};static deferWhen={name:"\u0275\u0275deferWhen",moduleName:o};static deferOnIdle={name:"\u0275\u0275deferOnIdle",moduleName:o};static deferOnImmediate={name:"\u0275\u0275deferOnImmediate",moduleName:o};static deferOnTimer={name:"\u0275\u0275deferOnTimer",moduleName:o};static deferOnHover={name:"\u0275\u0275deferOnHover",moduleName:o};static deferOnInteraction={name:"\u0275\u0275deferOnInteraction",moduleName:o};static deferOnViewport={name:"\u0275\u0275deferOnViewport",moduleName:o};static deferPrefetchWhen={name:"\u0275\u0275deferPrefetchWhen",moduleName:o};static deferPrefetchOnIdle={name:"\u0275\u0275deferPrefetchOnIdle",moduleName:o};static deferPrefetchOnImmediate={name:"\u0275\u0275deferPrefetchOnImmediate",moduleName:o};static deferPrefetchOnTimer={name:"\u0275\u0275deferPrefetchOnTimer",moduleName:o};static deferPrefetchOnHover={name:"\u0275\u0275deferPrefetchOnHover",moduleName:o};static deferPrefetchOnInteraction={name:"\u0275\u0275deferPrefetchOnInteraction",moduleName:o};static deferPrefetchOnViewport={name:"\u0275\u0275deferPrefetchOnViewport",moduleName:o};static deferHydrateWhen={name:"\u0275\u0275deferHydrateWhen",moduleName:o};static deferHydrateNever={name:"\u0275\u0275deferHydrateNever",moduleName:o};static deferHydrateOnIdle={name:"\u0275\u0275deferHydrateOnIdle",moduleName:o};static deferHydrateOnImmediate={name:"\u0275\u0275deferHydrateOnImmediate",moduleName:o};static deferHydrateOnTimer={name:"\u0275\u0275deferHydrateOnTimer",moduleName:o};static deferHydrateOnHover={name:"\u0275\u0275deferHydrateOnHover",moduleName:o};static deferHydrateOnInteraction={name:"\u0275\u0275deferHydrateOnInteraction",moduleName:o};static deferHydrateOnViewport={name:"\u0275\u0275deferHydrateOnViewport",moduleName:o};static deferEnableTimerScheduling={name:"\u0275\u0275deferEnableTimerScheduling",moduleName:o};static conditionalCreate={name:"\u0275\u0275conditionalCreate",moduleName:o};static conditionalBranchCreate={name:"\u0275\u0275conditionalBranchCreate",moduleName:o};static conditional={name:"\u0275\u0275conditional",moduleName:o};static repeater={name:"\u0275\u0275repeater",moduleName:o};static repeaterCreate={name:"\u0275\u0275repeaterCreate",moduleName:o};static repeaterTrackByIndex={name:"\u0275\u0275repeaterTrackByIndex",moduleName:o};static repeaterTrackByIdentity={name:"\u0275\u0275repeaterTrackByIdentity",moduleName:o};static componentInstance={name:"\u0275\u0275componentInstance",moduleName:o};static text={name:"\u0275\u0275text",moduleName:o};static enableBindings={name:"\u0275\u0275enableBindings",moduleName:o};static disableBindings={name:"\u0275\u0275disableBindings",moduleName:o};static getCurrentView={name:"\u0275\u0275getCurrentView",moduleName:o};static textInterpolate={name:"\u0275\u0275textInterpolate",moduleName:o};static textInterpolate1={name:"\u0275\u0275textInterpolate1",moduleName:o};static textInterpolate2={name:"\u0275\u0275textInterpolate2",moduleName:o};static textInterpolate3={name:"\u0275\u0275textInterpolate3",moduleName:o};static textInterpolate4={name:"\u0275\u0275textInterpolate4",moduleName:o};static textInterpolate5={name:"\u0275\u0275textInterpolate5",moduleName:o};static textInterpolate6={name:"\u0275\u0275textInterpolate6",moduleName:o};static textInterpolate7={name:"\u0275\u0275textInterpolate7",moduleName:o};static textInterpolate8={name:"\u0275\u0275textInterpolate8",moduleName:o};static textInterpolateV={name:"\u0275\u0275textInterpolateV",moduleName:o};static restoreView={name:"\u0275\u0275restoreView",moduleName:o};static pureFunction0={name:"\u0275\u0275pureFunction0",moduleName:o};static pureFunction1={name:"\u0275\u0275pureFunction1",moduleName:o};static pureFunction2={name:"\u0275\u0275pureFunction2",moduleName:o};static pureFunction3={name:"\u0275\u0275pureFunction3",moduleName:o};static pureFunction4={name:"\u0275\u0275pureFunction4",moduleName:o};static pureFunction5={name:"\u0275\u0275pureFunction5",moduleName:o};static pureFunction6={name:"\u0275\u0275pureFunction6",moduleName:o};static pureFunction7={name:"\u0275\u0275pureFunction7",moduleName:o};static pureFunction8={name:"\u0275\u0275pureFunction8",moduleName:o};static pureFunctionV={name:"\u0275\u0275pureFunctionV",moduleName:o};static pipeBind1={name:"\u0275\u0275pipeBind1",moduleName:o};static pipeBind2={name:"\u0275\u0275pipeBind2",moduleName:o};static pipeBind3={name:"\u0275\u0275pipeBind3",moduleName:o};static pipeBind4={name:"\u0275\u0275pipeBind4",moduleName:o};static pipeBindV={name:"\u0275\u0275pipeBindV",moduleName:o};static domProperty={name:"\u0275\u0275domProperty",moduleName:o};static property={name:"\u0275\u0275property",moduleName:o};static i18n={name:"\u0275\u0275i18n",moduleName:o};static i18nAttributes={name:"\u0275\u0275i18nAttributes",moduleName:o};static i18nExp={name:"\u0275\u0275i18nExp",moduleName:o};static i18nStart={name:"\u0275\u0275i18nStart",moduleName:o};static i18nEnd={name:"\u0275\u0275i18nEnd",moduleName:o};static i18nApply={name:"\u0275\u0275i18nApply",moduleName:o};static i18nPostprocess={name:"\u0275\u0275i18nPostprocess",moduleName:o};static pipe={name:"\u0275\u0275pipe",moduleName:o};static projection={name:"\u0275\u0275projection",moduleName:o};static projectionDef={name:"\u0275\u0275projectionDef",moduleName:o};static reference={name:"\u0275\u0275reference",moduleName:o};static inject={name:"\u0275\u0275inject",moduleName:o};static injectAttribute={name:"\u0275\u0275injectAttribute",moduleName:o};static directiveInject={name:"\u0275\u0275directiveInject",moduleName:o};static invalidFactory={name:"\u0275\u0275invalidFactory",moduleName:o};static invalidFactoryDep={name:"\u0275\u0275invalidFactoryDep",moduleName:o};static templateRefExtractor={name:"\u0275\u0275templateRefExtractor",moduleName:o};static forwardRef={name:"forwardRef",moduleName:o};static resolveForwardRef={name:"resolveForwardRef",moduleName:o};static replaceMetadata={name:"\u0275\u0275replaceMetadata",moduleName:o};static getReplaceMetadataURL={name:"\u0275\u0275getReplaceMetadataURL",moduleName:o};static \u0275\u0275defineInjectable={name:"\u0275\u0275defineInjectable",moduleName:o};static declareInjectable={name:"\u0275\u0275ngDeclareInjectable",moduleName:o};static InjectableDeclaration={name:"\u0275\u0275InjectableDeclaration",moduleName:o};static resolveWindow={name:"\u0275\u0275resolveWindow",moduleName:o};static resolveDocument={name:"\u0275\u0275resolveDocument",moduleName:o};static resolveBody={name:"\u0275\u0275resolveBody",moduleName:o};static getComponentDepsFactory={name:"\u0275\u0275getComponentDepsFactory",moduleName:o};static defineComponent={name:"\u0275\u0275defineComponent",moduleName:o};static declareComponent={name:"\u0275\u0275ngDeclareComponent",moduleName:o};static setComponentScope={name:"\u0275\u0275setComponentScope",moduleName:o};static ChangeDetectionStrategy={name:"ChangeDetectionStrategy",moduleName:o};static ViewEncapsulation={name:"ViewEncapsulation",moduleName:o};static ComponentDeclaration={name:"\u0275\u0275ComponentDeclaration",moduleName:o};static FactoryDeclaration={name:"\u0275\u0275FactoryDeclaration",moduleName:o};static declareFactory={name:"\u0275\u0275ngDeclareFactory",moduleName:o};static FactoryTarget={name:"\u0275\u0275FactoryTarget",moduleName:o};static defineDirective={name:"\u0275\u0275defineDirective",moduleName:o};static declareDirective={name:"\u0275\u0275ngDeclareDirective",moduleName:o};static DirectiveDeclaration={name:"\u0275\u0275DirectiveDeclaration",moduleName:o};static InjectorDef={name:"\u0275\u0275InjectorDef",moduleName:o};static InjectorDeclaration={name:"\u0275\u0275InjectorDeclaration",moduleName:o};static defineInjector={name:"\u0275\u0275defineInjector",moduleName:o};static declareInjector={name:"\u0275\u0275ngDeclareInjector",moduleName:o};static NgModuleDeclaration={name:"\u0275\u0275NgModuleDeclaration",moduleName:o};static ModuleWithProviders={name:"ModuleWithProviders",moduleName:o};static defineNgModule={name:"\u0275\u0275defineNgModule",moduleName:o};static declareNgModule={name:"\u0275\u0275ngDeclareNgModule",moduleName:o};static setNgModuleScope={name:"\u0275\u0275setNgModuleScope",moduleName:o};static registerNgModuleType={name:"\u0275\u0275registerNgModuleType",moduleName:o};static PipeDeclaration={name:"\u0275\u0275PipeDeclaration",moduleName:o};static definePipe={name:"\u0275\u0275definePipe",moduleName:o};static declarePipe={name:"\u0275\u0275ngDeclarePipe",moduleName:o};static declareClassMetadata={name:"\u0275\u0275ngDeclareClassMetadata",moduleName:o};static declareClassMetadataAsync={name:"\u0275\u0275ngDeclareClassMetadataAsync",moduleName:o};static setClassMetadata={name:"\u0275setClassMetadata",moduleName:o};static setClassMetadataAsync={name:"\u0275setClassMetadataAsync",moduleName:o};static setClassDebugInfo={name:"\u0275setClassDebugInfo",moduleName:o};static queryRefresh={name:"\u0275\u0275queryRefresh",moduleName:o};static viewQuery={name:"\u0275\u0275viewQuery",moduleName:o};static loadQuery={name:"\u0275\u0275loadQuery",moduleName:o};static contentQuery={name:"\u0275\u0275contentQuery",moduleName:o};static viewQuerySignal={name:"\u0275\u0275viewQuerySignal",moduleName:o};static contentQuerySignal={name:"\u0275\u0275contentQuerySignal",moduleName:o};static queryAdvance={name:"\u0275\u0275queryAdvance",moduleName:o};static twoWayProperty={name:"\u0275\u0275twoWayProperty",moduleName:o};static twoWayBindingSet={name:"\u0275\u0275twoWayBindingSet",moduleName:o};static twoWayListener={name:"\u0275\u0275twoWayListener",moduleName:o};static declareLet={name:"\u0275\u0275declareLet",moduleName:o};static storeLet={name:"\u0275\u0275storeLet",moduleName:o};static readContextLet={name:"\u0275\u0275readContextLet",moduleName:o};static attachSourceLocations={name:"\u0275\u0275attachSourceLocations",moduleName:o};static NgOnChangesFeature={name:"\u0275\u0275NgOnChangesFeature",moduleName:o};static InheritDefinitionFeature={name:"\u0275\u0275InheritDefinitionFeature",moduleName:o};static CopyDefinitionFeature={name:"\u0275\u0275CopyDefinitionFeature",moduleName:o};static ProvidersFeature={name:"\u0275\u0275ProvidersFeature",moduleName:o};static HostDirectivesFeature={name:"\u0275\u0275HostDirectivesFeature",moduleName:o};static ExternalStylesFeature={name:"\u0275\u0275ExternalStylesFeature",moduleName:o};static listener={name:"\u0275\u0275listener",moduleName:o};static getInheritedFactory={name:"\u0275\u0275getInheritedFactory",moduleName:o};static sanitizeHtml={name:"\u0275\u0275sanitizeHtml",moduleName:o};static sanitizeStyle={name:"\u0275\u0275sanitizeStyle",moduleName:o};static sanitizeResourceUrl={name:"\u0275\u0275sanitizeResourceUrl",moduleName:o};static sanitizeScript={name:"\u0275\u0275sanitizeScript",moduleName:o};static sanitizeUrl={name:"\u0275\u0275sanitizeUrl",moduleName:o};static sanitizeUrlOrResourceUrl={name:"\u0275\u0275sanitizeUrlOrResourceUrl",moduleName:o};static trustConstantHtml={name:"\u0275\u0275trustConstantHtml",moduleName:o};static trustConstantResourceUrl={name:"\u0275\u0275trustConstantResourceUrl",moduleName:o};static validateIframeAttribute={name:"\u0275\u0275validateIframeAttribute",moduleName:o};static InputSignalBrandWriteType={name:"\u0275INPUT_SIGNAL_BRAND_WRITE_TYPE",moduleName:o};static UnwrapDirectiveSignalInputs={name:"\u0275UnwrapDirectiveSignalInputs",moduleName:o};static unwrapWritableSignal={name:"\u0275unwrapWritableSignal",moduleName:o}};var Nt=class{full;major;minor;patch;constructor(e){this.full=e;let t=e.split(".");this.major=t[0],this.minor=t[1],this.patch=t.slice(2).join(".")}};var gn;(function(n){n[n.Class=0]="Class",n[n.Function=1]="Function"})(gn||(gn={}));var Pe=class{input;errLocation;ctxLocation;message;constructor(e,t,s,r){this.input=t,this.errLocation=s,this.ctxLocation=r,this.message=`Parser Error: ${e} ${s} [${t}] in ${r}`}},J=class{start;end;constructor(e,t){this.start=e,this.end=t}toAbsolute(e){return new O(e+this.start,e+this.end)}},S=class{span;sourceSpan;constructor(e,t){this.span=e,this.sourceSpan=t}toString(){return"AST"}},ae=class extends S{nameSpan;constructor(e,t,s){super(e,t),this.nameSpan=s}},P=class extends S{visit(e,t=null){}},Y=class extends S{visit(e,t=null){return e.visitImplicitReceiver(this,t)}},At=class extends Y{visit(e,t=null){var s;return(s=e.visitThisReceiver)==null?void 0:s.call(e,this,t)}},Le=class extends S{expressions;constructor(e,t,s){super(e,t),this.expressions=s}visit(e,t=null){return e.visitChain(this,t)}},Me=class extends S{condition;trueExp;falseExp;constructor(e,t,s,r,i){super(e,t),this.condition=s,this.trueExp=r,this.falseExp=i}visit(e,t=null){return e.visitConditional(this,t)}},le=class extends ae{receiver;name;constructor(e,t,s,r,i){super(e,t,s),this.receiver=r,this.name=i}visit(e,t=null){return e.visitPropertyRead(this,t)}},$e=class extends ae{receiver;name;value;constructor(e,t,s,r,i,a){super(e,t,s),this.receiver=r,this.name=i,this.value=a}visit(e,t=null){return e.visitPropertyWrite(this,t)}},ce=class extends ae{receiver;name;constructor(e,t,s,r,i){super(e,t,s),this.receiver=r,this.name=i}visit(e,t=null){return e.visitSafePropertyRead(this,t)}},Re=class extends S{receiver;key;constructor(e,t,s,r){super(e,t),this.receiver=s,this.key=r}visit(e,t=null){return e.visitKeyedRead(this,t)}},ue=class extends S{receiver;key;constructor(e,t,s,r){super(e,t),this.receiver=s,this.key=r}visit(e,t=null){return e.visitSafeKeyedRead(this,t)}},De=class extends S{receiver;key;value;constructor(e,t,s,r,i){super(e,t),this.receiver=s,this.key=r,this.value=i}visit(e,t=null){return e.visitKeyedWrite(this,t)}},Be=class extends ae{exp;name;args;constructor(e,t,s,r,i,a){super(e,t,a),this.exp=s,this.name=r,this.args=i}visit(e,t=null){return e.visitPipe(this,t)}},I=class extends S{value;constructor(e,t,s){super(e,t),this.value=s}visit(e,t=null){return e.visitLiteralPrimitive(this,t)}},Oe=class extends S{expressions;constructor(e,t,s){super(e,t),this.expressions=s}visit(e,t=null){return e.visitLiteralArray(this,t)}},Fe=class extends S{keys;values;constructor(e,t,s,r){super(e,t),this.keys=s,this.values=r}visit(e,t=null){return e.visitLiteralMap(this,t)}},Vt=class extends S{strings;expressions;constructor(e,t,s,r){super(e,t),this.strings=s,this.expressions=r}visit(e,t=null){return e.visitInterpolation(this,t)}},A=class extends S{operation;left;right;constructor(e,t,s,r,i){super(e,t),this.operation=s,this.left=r,this.right=i}visit(e,t=null){return e.visitBinary(this,t)}},X=class n extends A{operator;expr;left=null;right=null;operation=null;static createMinus(e,t,s){return new n(e,t,"-",s,"-",new I(e,t,0),s)}static createPlus(e,t,s){return new n(e,t,"+",s,"-",s,new I(e,t,0))}constructor(e,t,s,r,i,a,l){super(e,t,i,a,l),this.operator=s,this.expr=r}visit(e,t=null){return e.visitUnary!==void 0?e.visitUnary(this,t):e.visitBinary(this,t)}},Q=class extends S{expression;constructor(e,t,s){super(e,t),this.expression=s}visit(e,t=null){return e.visitPrefixNot(this,t)}},K=class extends S{expression;constructor(e,t,s){super(e,t),this.expression=s}visit(e,t=null){return e.visitTypeofExpression(this,t)}},Z=class extends S{expression;constructor(e,t,s){super(e,t),this.expression=s}visit(e,t=null){return e.visitVoidExpression(this,t)}},Ve=class extends S{expression;constructor(e,t,s){super(e,t),this.expression=s}visit(e,t=null){return e.visitNonNullAssert(this,t)}},Ue=class extends S{receiver;args;argumentSpan;constructor(e,t,s,r,i){super(e,t),this.receiver=s,this.args=r,this.argumentSpan=i}visit(e,t=null){return e.visitCall(this,t)}},pe=class extends S{receiver;args;argumentSpan;constructor(e,t,s,r,i){super(e,t),this.receiver=s,this.args=r,this.argumentSpan=i}visit(e,t=null){return e.visitSafeCall(this,t)}},he=class extends S{tag;template;constructor(e,t,s,r){super(e,t),this.tag=s,this.template=r}visit(e,t){return e.visitTaggedTemplateLiteral(this,t)}},fe=class extends S{elements;expressions;constructor(e,t,s,r){super(e,t),this.elements=s,this.expressions=r}visit(e,t){return e.visitTemplateLiteral(this,t)}},de=class extends S{text;constructor(e,t,s){super(e,t),this.text=s}visit(e,t){return e.visitTemplateLiteralElement(this,t)}},He=class extends S{expression;constructor(e,t,s){super(e,t),this.expression=s}visit(e,t){return e.visitParenthesizedExpression(this,t)}},O=class{start;end;constructor(e,t){this.start=e,this.end=t}},W=class extends S{ast;source;location;errors;constructor(e,t,s,r,i){super(new J(0,t===null?0:t.length),new O(r,t===null?r:r+t.length)),this.ast=e,this.source=t,this.location=s,this.errors=i}visit(e,t=null){return e.visitASTWithSource?e.visitASTWithSource(this,t):this.ast.visit(e,t)}toString(){return`${this.source} in ${this.location}`}},me=class{sourceSpan;key;value;constructor(e,t,s){this.sourceSpan=e,this.key=t,this.value=s}},We=class{sourceSpan;key;value;constructor(e,t,s){this.sourceSpan=e,this.key=t,this.value=s}},Pt=class{visit(e,t){e.visit(this,t)}visitUnary(e,t){this.visit(e.expr,t)}visitBinary(e,t){this.visit(e.left,t),this.visit(e.right,t)}visitChain(e,t){this.visitAll(e.expressions,t)}visitConditional(e,t){this.visit(e.condition,t),this.visit(e.trueExp,t),this.visit(e.falseExp,t)}visitPipe(e,t){this.visit(e.exp,t),this.visitAll(e.args,t)}visitImplicitReceiver(e,t){}visitThisReceiver(e,t){}visitInterpolation(e,t){this.visitAll(e.expressions,t)}visitKeyedRead(e,t){this.visit(e.receiver,t),this.visit(e.key,t)}visitKeyedWrite(e,t){this.visit(e.receiver,t),this.visit(e.key,t),this.visit(e.value,t)}visitLiteralArray(e,t){this.visitAll(e.expressions,t)}visitLiteralMap(e,t){this.visitAll(e.values,t)}visitLiteralPrimitive(e,t){}visitPrefixNot(e,t){this.visit(e.expression,t)}visitTypeofExpression(e,t){this.visit(e.expression,t)}visitVoidExpression(e,t){this.visit(e.expression,t)}visitNonNullAssert(e,t){this.visit(e.expression,t)}visitPropertyRead(e,t){this.visit(e.receiver,t)}visitPropertyWrite(e,t){this.visit(e.receiver,t),this.visit(e.value,t)}visitSafePropertyRead(e,t){this.visit(e.receiver,t)}visitSafeKeyedRead(e,t){this.visit(e.receiver,t),this.visit(e.key,t)}visitCall(e,t){this.visit(e.receiver,t),this.visitAll(e.args,t)}visitSafeCall(e,t){this.visit(e.receiver,t),this.visitAll(e.args,t)}visitTemplateLiteral(e,t){for(let s=0;s<e.elements.length;s++){this.visit(e.elements[s],t);let r=s<e.expressions.length?e.expressions[s]:null;r!==null&&this.visit(r,t)}}visitTemplateLiteralElement(e,t){}visitTaggedTemplateLiteral(e,t){this.visit(e.tag,t),this.visit(e.template,t)}visitParenthesizedExpression(e,t){this.visit(e.expression,t)}visitAll(e,t){for(let s of e)this.visit(s,t)}};var vn;(function(n){n[n.DEFAULT=0]="DEFAULT",n[n.LITERAL_ATTR=1]="LITERAL_ATTR",n[n.ANIMATION=2]="ANIMATION",n[n.TWO_WAY=3]="TWO_WAY"})(vn||(vn={}));var wn;(function(n){n[n.Regular=0]="Regular",n[n.Animation=1]="Animation",n[n.TwoWay=2]="TwoWay"})(wn||(wn={}));var U;(function(n){n[n.Property=0]="Property",n[n.Attribute=1]="Attribute",n[n.Class=2]="Class",n[n.Style=3]="Style",n[n.Animation=4]="Animation",n[n.TwoWay=5]="TwoWay"})(U||(U={}));var xn;(function(n){n[n.RAW_TEXT=0]="RAW_TEXT",n[n.ESCAPABLE_RAW_TEXT=1]="ESCAPABLE_RAW_TEXT",n[n.PARSABLE_DATA=2]="PARSABLE_DATA"})(xn||(xn={}));var Js=[/@/,/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//];function Ys(n,e){if(e!=null&&!(Array.isArray(e)&&e.length==2))throw new Error(`Expected '${n}' to be an array, [start, end].`);if(e!=null){let t=e[0],s=e[1];Js.forEach(r=>{if(r.test(t)||r.test(s))throw new Error(`['${t}', '${s}'] contains unusable interpolation symbol.`)})}}var Lt=class n{start;end;static fromArray(e){return e?(Ys("interpolation",e),new n(e[0],e[1])):ne}constructor(e,t){this.start=e,this.end=t}},ne=new Lt("{{","}}");var Qe=0;var Kn=9,Qs=10,Ks=11,Zs=12,er=13,Zn=32,tr=33,es=34,nr=35,Ut=36,sr=37,Sn=38,ts=39,tt=40,se=41,En=42,ns=43,Ce=44,ss=45,re=46,Mt=47,ie=58,Te=59,rr=60,Ke=61,ir=62,yn=63,or=48;var ar=57,rs=65,lr=69;var is=90,nt=91,_n=92,ke=93,cr=94,Ht=95,os=97;var ur=101,pr=102,hr=110,fr=114,dr=116,mr=117,gr=118;var as=122,ot=123,Cn=124,be=125,ls=160;var $t=96;function vr(n){return n>=Kn&&n<=Zn||n==ls}function z(n){return or<=n&&n<=ar}function wr(n){return n>=os&&n<=as||n>=rs&&n<=is}function Tn(n){return n===ts||n===es||n===$t}var kn;(function(n){n[n.WARNING=0]="WARNING",n[n.ERROR=1]="ERROR"})(kn||(kn={}));var bn;(function(n){n[n.Inline=0]="Inline",n[n.SideEffect=1]="SideEffect",n[n.Omit=2]="Omit"})(bn||(bn={}));var In;(function(n){n[n.Global=0]="Global",n[n.Local=1]="Local"})(In||(In={}));var Nn;(function(n){n[n.Directive=0]="Directive",n[n.Pipe=1]="Pipe",n[n.NgModule=2]="NgModule"})(Nn||(Nn={}));var xr="(:(where|is)\\()?";var cs="-shadowcsshost",Sr="-shadowcsscontext",us="(?:\\(((?:\\([^)(]*\\)|[^)(]*)+?)\\))",ri=new RegExp(cs+us+"?([^,{]*)","gim"),ps=Sr+us+"?([^{]*)",ii=new RegExp(`${xr}(${ps})`,"gim"),oi=new RegExp(ps,"im"),Er=cs+"-no-combinator",ai=new RegExp(`${Er}(?![^(]*\\))`,"g");var hs="%COMMENT%",li=new RegExp(hs,"g");var ci=new RegExp(`(\\s*(?:${hs}\\s*)*)([^;\\{\\}]+?)(\\s*)((?:{%BLOCK%}?\\s*;?)|(?:\\s*;))`,"g");var yr="%COMMA_IN_PLACEHOLDER%",_r="%SEMI_IN_PLACEHOLDER%",Cr="%COLON_IN_PLACEHOLDER%",ui=new RegExp(yr,"g"),pi=new RegExp(_r,"g"),hi=new RegExp(Cr,"g");var d;(function(n){n[n.ListEnd=0]="ListEnd",n[n.Statement=1]="Statement",n[n.Variable=2]="Variable",n[n.ElementStart=3]="ElementStart",n[n.Element=4]="Element",n[n.Template=5]="Template",n[n.ElementEnd=6]="ElementEnd",n[n.ContainerStart=7]="ContainerStart",n[n.Container=8]="Container",n[n.ContainerEnd=9]="ContainerEnd",n[n.DisableBindings=10]="DisableBindings",n[n.ConditionalCreate=11]="ConditionalCreate",n[n.ConditionalBranchCreate=12]="ConditionalBranchCreate",n[n.Conditional=13]="Conditional",n[n.EnableBindings=14]="EnableBindings",n[n.Text=15]="Text",n[n.Listener=16]="Listener",n[n.InterpolateText=17]="InterpolateText",n[n.Binding=18]="Binding",n[n.Property=19]="Property",n[n.StyleProp=20]="StyleProp",n[n.ClassProp=21]="ClassProp",n[n.StyleMap=22]="StyleMap",n[n.ClassMap=23]="ClassMap",n[n.Advance=24]="Advance",n[n.Pipe=25]="Pipe",n[n.Attribute=26]="Attribute",n[n.ExtractedAttribute=27]="ExtractedAttribute",n[n.Defer=28]="Defer",n[n.DeferOn=29]="DeferOn",n[n.DeferWhen=30]="DeferWhen",n[n.I18nMessage=31]="I18nMessage",n[n.DomProperty=32]="DomProperty",n[n.Namespace=33]="Namespace",n[n.ProjectionDef=34]="ProjectionDef",n[n.Projection=35]="Projection",n[n.RepeaterCreate=36]="RepeaterCreate",n[n.Repeater=37]="Repeater",n[n.TwoWayProperty=38]="TwoWayProperty",n[n.TwoWayListener=39]="TwoWayListener",n[n.DeclareLet=40]="DeclareLet",n[n.StoreLet=41]="StoreLet",n[n.I18nStart=42]="I18nStart",n[n.I18n=43]="I18n",n[n.I18nEnd=44]="I18nEnd",n[n.I18nExpression=45]="I18nExpression",n[n.I18nApply=46]="I18nApply",n[n.IcuStart=47]="IcuStart",n[n.IcuEnd=48]="IcuEnd",n[n.IcuPlaceholder=49]="IcuPlaceholder",n[n.I18nContext=50]="I18nContext",n[n.I18nAttributes=51]="I18nAttributes",n[n.SourceLocation=52]="SourceLocation"})(d||(d={}));var ge;(function(n){n[n.LexicalRead=0]="LexicalRead",n[n.Context=1]="Context",n[n.TrackContext=2]="TrackContext",n[n.ReadVariable=3]="ReadVariable",n[n.NextContext=4]="NextContext",n[n.Reference=5]="Reference",n[n.StoreLet=6]="StoreLet",n[n.ContextLetReference=7]="ContextLetReference",n[n.GetCurrentView=8]="GetCurrentView",n[n.RestoreView=9]="RestoreView",n[n.ResetView=10]="ResetView",n[n.PureFunctionExpr=11]="PureFunctionExpr",n[n.PureFunctionParameterExpr=12]="PureFunctionParameterExpr",n[n.PipeBinding=13]="PipeBinding",n[n.PipeBindingVariadic=14]="PipeBindingVariadic",n[n.SafePropertyRead=15]="SafePropertyRead",n[n.SafeKeyedRead=16]="SafeKeyedRead",n[n.SafeInvokeFunction=17]="SafeInvokeFunction",n[n.SafeTernaryExpr=18]="SafeTernaryExpr",n[n.EmptyExpr=19]="EmptyExpr",n[n.AssignTemporaryExpr=20]="AssignTemporaryExpr",n[n.ReadTemporaryExpr=21]="ReadTemporaryExpr",n[n.SlotLiteralExpr=22]="SlotLiteralExpr",n[n.ConditionalCase=23]="ConditionalCase",n[n.ConstCollected=24]="ConstCollected",n[n.TwoWayBindingSet=25]="TwoWayBindingSet"})(ge||(ge={}));var An;(function(n){n[n.None=0]="None",n[n.AlwaysInline=1]="AlwaysInline"})(An||(An={}));var Pn;(function(n){n[n.Context=0]="Context",n[n.Identifier=1]="Identifier",n[n.SavedView=2]="SavedView",n[n.Alias=3]="Alias"})(Pn||(Pn={}));var Ln;(function(n){n[n.Normal=0]="Normal",n[n.TemplateDefinitionBuilder=1]="TemplateDefinitionBuilder"})(Ln||(Ln={}));var H;(function(n){n[n.Attribute=0]="Attribute",n[n.ClassName=1]="ClassName",n[n.StyleProperty=2]="StyleProperty",n[n.Property=3]="Property",n[n.Template=4]="Template",n[n.I18n=5]="I18n",n[n.Animation=6]="Animation",n[n.TwoWayProperty=7]="TwoWayProperty"})(H||(H={}));var Mn;(function(n){n[n.Creation=0]="Creation",n[n.Postproccessing=1]="Postproccessing"})(Mn||(Mn={}));var $n;(function(n){n[n.I18nText=0]="I18nText",n[n.I18nAttribute=1]="I18nAttribute"})($n||($n={}));var Rn;(function(n){n[n.None=0]="None",n[n.ElementTag=1]="ElementTag",n[n.TemplateTag=2]="TemplateTag",n[n.OpenTag=4]="OpenTag",n[n.CloseTag=8]="CloseTag",n[n.ExpressionIndex=16]="ExpressionIndex"})(Rn||(Rn={}));var Dn;(function(n){n[n.HTML=0]="HTML",n[n.SVG=1]="SVG",n[n.Math=2]="Math"})(Dn||(Dn={}));var Bn;(function(n){n[n.Idle=0]="Idle",n[n.Immediate=1]="Immediate",n[n.Timer=2]="Timer",n[n.Hover=3]="Hover",n[n.Interaction=4]="Interaction",n[n.Viewport=5]="Viewport",n[n.Never=6]="Never"})(Bn||(Bn={}));var On;(function(n){n[n.RootI18n=0]="RootI18n",n[n.Icu=1]="Icu",n[n.Attr=2]="Attr"})(On||(On={}));var Fn;(function(n){n[n.NgTemplate=0]="NgTemplate",n[n.Structural=1]="Structural",n[n.Block=2]="Block"})(Fn||(Fn={}));var Tr=Symbol("ConsumesSlot"),fs=Symbol("DependsOnSlotContext"),je=Symbol("ConsumesVars"),Wt=Symbol("UsesVarOffset"),fi={[Tr]:!0,numSlotsUsed:1},di={[fs]:!0},mi={[je]:!0};var at=class{strings;expressions;i18nPlaceholders;constructor(e,t,s){if(this.strings=e,this.expressions=t,this.i18nPlaceholders=s,s.length!==0&&s.length!==t.length)throw new Error(`Expected ${t.length} placeholders to match interpolation expression count, but got ${s.length}`)}};var ve=class extends b{constructor(e=null){super(null,e)}};var Vn=class n extends ve{target;value;sourceSpan;kind=ge.StoreLet;[je]=!0;[fs]=!0;constructor(e,t,s){super(),this.target=e,this.value=t,this.sourceSpan=s}visitExpression(){}isEquivalent(e){return e instanceof n&&e.target===this.target&&e.value.isEquivalent(this.value)}isConstant(){return!1}transformInternalExpressions(e,t){this.value=(this.value,void 0)}clone(){return new n(this.target,this.value,this.sourceSpan)}};var Un=class n extends ve{kind=ge.PureFunctionExpr;[je]=!0;[Wt]=!0;varOffset=null;body;args;fn=null;constructor(e,t){super(),this.body=e,this.args=t}visitExpression(e,t){var s;(s=this.body)==null||s.visitExpression(e,t);for(let r of this.args)r.visitExpression(e,t)}isEquivalent(e){return!(e instanceof n)||e.args.length!==this.args.length?!1:e.body!==null&&this.body!==null&&e.body.isEquivalent(this.body)&&e.args.every((t,s)=>t.isEquivalent(this.args[s]))}isConstant(){return!1}transformInternalExpressions(e,t){this.body!==null?this.body=(this.body,t|Rt.InChildOperation,void 0):this.fn!==null&&(this.fn=(this.fn,void 0));for(let s=0;s<this.args.length;s++)this.args[s]=(this.args[s],void 0)}clone(){var t,s;let e=new n(((t=this.body)==null?void 0:t.clone())??null,this.args.map(r=>r.clone()));return e.fn=((s=this.fn)==null?void 0:s.clone())??null,e.varOffset=this.varOffset,e}};var Hn=class n extends ve{target;targetSlot;name;args;kind=ge.PipeBinding;[je]=!0;[Wt]=!0;varOffset=null;constructor(e,t,s,r){super(),this.target=e,this.targetSlot=t,this.name=s,this.args=r}visitExpression(e,t){for(let s of this.args)s.visitExpression(e,t)}isEquivalent(){return!1}isConstant(){return!1}transformInternalExpressions(e,t){for(let s=0;s<this.args.length;s++)this.args[s]=(this.args[s],void 0)}clone(){let e=new n(this.target,this.targetSlot,this.name,this.args.map(t=>t.clone()));return e.varOffset=this.varOffset,e}},Wn=class n extends ve{target;targetSlot;name;args;numArgs;kind=ge.PipeBindingVariadic;[je]=!0;[Wt]=!0;varOffset=null;constructor(e,t,s,r,i){super(),this.target=e,this.targetSlot=t,this.name=s,this.args=r,this.numArgs=i}visitExpression(e,t){this.args.visitExpression(e,t)}isEquivalent(){return!1}isConstant(){return!1}transformInternalExpressions(e,t){this.args=(this.args,void 0)}clone(){let e=new n(this.target,this.targetSlot,this.name,this.args.clone(),this.numArgs);return e.varOffset=this.varOffset,e}};var Rt;(function(n){n[n.None=0]="None",n[n.InChildOperation=1]="InChildOperation"})(Rt||(Rt={}));var gi=new Set([d.Element,d.ElementStart,d.Container,d.ContainerStart,d.Template,d.RepeaterCreate,d.ConditionalCreate,d.ConditionalBranchCreate]);var qn;(function(n){n[n.Tmpl=0]="Tmpl",n[n.Host=1]="Host",n[n.Both=2]="Both"})(qn||(qn={}));var vi=new Map([[p.attribute,p.attribute],[p.classProp,p.classProp],[p.element,p.element],[p.elementContainer,p.elementContainer],[p.elementContainerEnd,p.elementContainerEnd],[p.elementContainerStart,p.elementContainerStart],[p.elementEnd,p.elementEnd],[p.elementStart,p.elementStart],[p.domProperty,p.domProperty],[p.i18nExp,p.i18nExp],[p.listener,p.listener],[p.listener,p.listener],[p.property,p.property],[p.styleProp,p.styleProp],[p.syntheticHostListener,p.syntheticHostListener],[p.syntheticHostProperty,p.syntheticHostProperty],[p.templateCreate,p.templateCreate],[p.twoWayProperty,p.twoWayProperty],[p.twoWayListener,p.twoWayListener],[p.declareLet,p.declareLet],[p.conditionalCreate,p.conditionalBranchCreate],[p.conditionalBranchCreate,p.conditionalBranchCreate]]);var wi=Object.freeze([]);var xi=new Map([[d.ElementEnd,[d.ElementStart,d.Element]],[d.ContainerEnd,[d.ContainerStart,d.Container]],[d.I18nEnd,[d.I18nStart,d.I18n]]]),Si=new Set([d.Pipe]);var kr={},br="\uE500";kr.ngsp=br;var jn;(function(n){n.HEX="hexadecimal",n.DEC="decimal"})(jn||(jn={}));var ds=` \f
\r	\v\u1680\u180E\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF`,Ei=new RegExp(`[^${ds}]`),yi=new RegExp(`[${ds}]{2,}`,"g");var m;(function(n){n[n.Character=0]="Character",n[n.Identifier=1]="Identifier",n[n.PrivateIdentifier=2]="PrivateIdentifier",n[n.Keyword=3]="Keyword",n[n.String=4]="String",n[n.Operator=5]="Operator",n[n.Number=6]="Number",n[n.Error=7]="Error"})(m||(m={}));var q;(function(n){n[n.Plain=0]="Plain",n[n.TemplateLiteralPart=1]="TemplateLiteralPart",n[n.TemplateLiteralEnd=2]="TemplateLiteralEnd"})(q||(q={}));var Ir=["var","let","as","null","undefined","true","false","if","else","this","typeof","void","in"],qe=class{tokenize(e){return new Dt(e).scan()}},M=class{index;end;type;numValue;strValue;constructor(e,t,s,r,i){this.index=e,this.end=t,this.type=s,this.numValue=r,this.strValue=i}isCharacter(e){return this.type===m.Character&&this.numValue===e}isNumber(){return this.type===m.Number}isString(){return this.type===m.String}isOperator(e){return this.type===m.Operator&&this.strValue===e}isIdentifier(){return this.type===m.Identifier}isPrivateIdentifier(){return this.type===m.PrivateIdentifier}isKeyword(){return this.type===m.Keyword}isKeywordLet(){return this.type===m.Keyword&&this.strValue==="let"}isKeywordAs(){return this.type===m.Keyword&&this.strValue==="as"}isKeywordNull(){return this.type===m.Keyword&&this.strValue==="null"}isKeywordUndefined(){return this.type===m.Keyword&&this.strValue==="undefined"}isKeywordTrue(){return this.type===m.Keyword&&this.strValue==="true"}isKeywordFalse(){return this.type===m.Keyword&&this.strValue==="false"}isKeywordThis(){return this.type===m.Keyword&&this.strValue==="this"}isKeywordTypeof(){return this.type===m.Keyword&&this.strValue==="typeof"}isKeywordVoid(){return this.type===m.Keyword&&this.strValue==="void"}isKeywordIn(){return this.type===m.Keyword&&this.strValue==="in"}isError(){return this.type===m.Error}toNumber(){return this.type===m.Number?this.numValue:-1}isTemplateLiteralPart(){return this.isString()&&this.kind===q.TemplateLiteralPart}isTemplateLiteralEnd(){return this.isString()&&this.kind===q.TemplateLiteralEnd}isTemplateLiteralInterpolationStart(){return this.isOperator("${")}isTemplateLiteralInterpolationEnd(){return this.isOperator("}")}toString(){switch(this.type){case m.Character:case m.Identifier:case m.Keyword:case m.Operator:case m.PrivateIdentifier:case m.String:case m.Error:return this.strValue;case m.Number:return this.numValue.toString();default:return null}}},Ie=class extends M{kind;constructor(e,t,s,r){super(e,t,m.String,0,s),this.kind=r}};function Ze(n,e,t){return new M(n,e,m.Character,t,String.fromCharCode(t))}function Nr(n,e,t){return new M(n,e,m.Identifier,0,t)}function Ar(n,e,t){return new M(n,e,m.PrivateIdentifier,0,t)}function Pr(n,e,t){return new M(n,e,m.Keyword,0,t)}function _e(n,e,t){return new M(n,e,m.Operator,0,t)}function Lr(n,e,t){return new M(n,e,m.Number,t,"")}function Mr(n,e,t){return new M(n,e,m.Error,0,t)}var et=new M(-1,-1,m.Character,0,""),Dt=class{input;tokens=[];length;peek=0;index=-1;braceStack=[];constructor(e){this.input=e,this.length=e.length,this.advance()}scan(){let e=this.scanToken();for(;e!==null;)this.tokens.push(e),e=this.scanToken();return this.tokens}advance(){this.peek=++this.index>=this.length?Qe:this.input.charCodeAt(this.index)}scanToken(){let e=this.input,t=this.length,s=this.peek,r=this.index;for(;s<=Zn;)if(++r>=t){s=Qe;break}else s=e.charCodeAt(r);if(this.peek=s,this.index=r,r>=t)return null;if(zn(s))return this.scanIdentifier();if(z(s))return this.scanNumber(r);let i=r;switch(s){case re:return this.advance(),z(this.peek)?this.scanNumber(i):Ze(i,this.index,re);case tt:case se:case nt:case ke:case Ce:case ie:case Te:return this.scanCharacter(i,s);case ot:return this.scanOpenBrace(i,s);case be:return this.scanCloseBrace(i,s);case ts:case es:return this.scanString();case $t:return this.advance(),this.scanTemplateLiteralPart(i);case nr:return this.scanPrivateIdentifier();case ns:case ss:case Mt:case sr:case cr:return this.scanOperator(i,String.fromCharCode(s));case En:return this.scanComplexOperator(i,"*",En,"*");case yn:return this.scanQuestion(i);case rr:case ir:return this.scanComplexOperator(i,String.fromCharCode(s),Ke,"=");case tr:case Ke:return this.scanComplexOperator(i,String.fromCharCode(s),Ke,"=",Ke,"=");case Sn:return this.scanComplexOperator(i,"&",Sn,"&");case Cn:return this.scanComplexOperator(i,"|",Cn,"|");case ls:for(;vr(this.peek);)this.advance();return this.scanToken()}return this.advance(),this.error(`Unexpected character [${String.fromCharCode(s)}]`,0)}scanCharacter(e,t){return this.advance(),Ze(e,this.index,t)}scanOperator(e,t){return this.advance(),_e(e,this.index,t)}scanOpenBrace(e,t){return this.braceStack.push("expression"),this.advance(),Ze(e,this.index,t)}scanCloseBrace(e,t){return this.advance(),this.braceStack.pop()==="interpolation"?(this.tokens.push(_e(e,this.index,"}")),this.scanTemplateLiteralPart(this.index)):Ze(e,this.index,t)}scanComplexOperator(e,t,s,r,i,a){this.advance();let l=t;return this.peek==s&&(this.advance(),l+=r),i!=null&&this.peek==i&&(this.advance(),l+=a),_e(e,this.index,l)}scanIdentifier(){let e=this.index;for(this.advance();Gn(this.peek);)this.advance();let t=this.input.substring(e,this.index);return Ir.indexOf(t)>-1?Pr(e,this.index,t):Nr(e,this.index,t)}scanPrivateIdentifier(){let e=this.index;if(this.advance(),!zn(this.peek))return this.error("Invalid character [#]",-1);for(;Gn(this.peek);)this.advance();let t=this.input.substring(e,this.index);return Ar(e,this.index,t)}scanNumber(e){let t=this.index===e,s=!1;for(this.advance();;){if(!z(this.peek))if(this.peek===Ht){if(!z(this.input.charCodeAt(this.index-1))||!z(this.input.charCodeAt(this.index+1)))return this.error("Invalid numeric separator",0);s=!0}else if(this.peek===re)t=!1;else if($r(this.peek)){if(this.advance(),Rr(this.peek)&&this.advance(),!z(this.peek))return this.error("Invalid exponent",-1);t=!1}else break;this.advance()}let r=this.input.substring(e,this.index);s&&(r=r.replace(/_/g,""));let i=t?Br(r):parseFloat(r);return Lr(e,this.index,i)}scanString(){let e=this.index,t=this.peek;this.advance();let s="",r=this.index,i=this.input;for(;this.peek!=t;)if(this.peek==_n){let l=this.scanStringBackslash(s,r);if(typeof l!="string")return l;s=l,r=this.index}else{if(this.peek==Qe)return this.error("Unterminated quote",0);this.advance()}let a=i.substring(r,this.index);return this.advance(),new Ie(e,this.index,s+a,q.Plain)}scanQuestion(e){this.advance();let t="?";return(this.peek===yn||this.peek===re)&&(t+=this.peek===re?".":"?",this.advance()),_e(e,this.index,t)}scanTemplateLiteralPart(e){let t="",s=this.index;for(;this.peek!==$t;)if(this.peek===_n){let i=this.scanStringBackslash(t,s);if(typeof i!="string")return i;t=i,s=this.index}else if(this.peek===Ut){let i=this.index;if(this.advance(),this.peek===ot)return this.braceStack.push("interpolation"),this.tokens.push(new Ie(e,i,t+this.input.substring(s,i),q.TemplateLiteralPart)),this.advance(),_e(i,this.index,this.input.substring(i,this.index))}else{if(this.peek===Qe)return this.error("Unterminated template literal",0);this.advance()}let r=this.input.substring(s,this.index);return this.advance(),new Ie(e,this.index,t+r,q.TemplateLiteralEnd)}error(e,t){let s=this.index+t;return Mr(s,this.index,`Lexer Error: ${e} at column ${s} in expression [${this.input}]`)}scanStringBackslash(e,t){e+=this.input.substring(t,this.index);let s;if(this.advance(),this.peek===mr){let r=this.input.substring(this.index+1,this.index+5);if(/^[0-9a-f]+$/i.test(r))s=parseInt(r,16);else return this.error(`Invalid unicode escape [\\u${r}]`,0);for(let i=0;i<5;i++)this.advance()}else s=Dr(this.peek),this.advance();return e+=String.fromCharCode(s),e}};function zn(n){return os<=n&&n<=as||rs<=n&&n<=is||n==Ht||n==Ut}function Gn(n){return wr(n)||z(n)||n==Ht||n==Ut}function $r(n){return n==ur||n==lr}function Rr(n){return n==ss||n==ns}function Dr(n){switch(n){case hr:return Qs;case pr:return Zs;case fr:return er;case dr:return Kn;case gr:return Ks;default:return n}}function Br(n){let e=parseInt(n);if(isNaN(e))throw new Error("Invalid integer literal when parsing "+n);return e}var Bt=class{strings;expressions;offsets;constructor(e,t,s){this.strings=e,this.expressions=t,this.offsets=s}},Ot=class{templateBindings;warnings;errors;constructor(e,t,s){this.templateBindings=e,this.warnings=t,this.errors=s}},we=class{_lexer;errors=[];constructor(e){this._lexer=e}parseAction(e,t,s,r=ne){this._checkNoInterpolation(e,t,r);let i=this._stripComments(e),a=this._lexer.tokenize(i),l=new G(e,t,s,a,1,this.errors,0).parseChain();return new W(l,e,t,s,this.errors)}parseBinding(e,t,s,r=ne){let i=this._parseBindingAst(e,t,s,r);return new W(i,e,t,s,this.errors)}checkSimpleExpression(e){let t=new Ft;return e.visit(t),t.errors}parseSimpleBinding(e,t,s,r=ne){let i=this._parseBindingAst(e,t,s,r),a=this.checkSimpleExpression(i);return a.length>0&&this._reportError(`Host binding expression cannot contain ${a.join(" ")}`,e,t),new W(i,e,t,s,this.errors)}_reportError(e,t,s,r){this.errors.push(new Pe(e,t,s,r))}_parseBindingAst(e,t,s,r){this._checkNoInterpolation(e,t,r);let i=this._stripComments(e),a=this._lexer.tokenize(i);return new G(e,t,s,a,0,this.errors,0).parseChain()}parseTemplateBindings(e,t,s,r,i){let a=this._lexer.tokenize(t);return new G(t,s,i,a,0,this.errors,0).parseTemplateBindings({source:e,span:new O(r,r+e.length)})}parseInterpolation(e,t,s,r,i=ne){let{strings:a,expressions:l,offsets:h}=this.splitInterpolation(e,t,r,i);if(l.length===0)return null;let f=[];for(let v=0;v<l.length;++v){let E=l[v].text,y=this._stripComments(E),T=this._lexer.tokenize(y),k=new G(e,t,s,T,0,this.errors,h[v]).parseChain();f.push(k)}return this.createInterpolationAst(a.map(v=>v.text),f,e,t,s)}parseInterpolationExpression(e,t,s){let r=this._stripComments(e),i=this._lexer.tokenize(r),a=new G(e,t,s,i,0,this.errors,0).parseChain(),l=["",""];return this.createInterpolationAst(l,[a],e,t,s)}createInterpolationAst(e,t,s,r,i){let a=new J(0,s.length),l=new Vt(a,a.toAbsolute(i),e,t);return new W(l,s,r,i,this.errors)}splitInterpolation(e,t,s,r=ne){let i=[],a=[],l=[],h=s?Or(s):null,f=0,v=!1,E=!1,{start:y,end:T}=r;for(;f<e.length;)if(v){let k=f,F=k+y.length,Ee=this._getInterpolationEndIndex(e,T,F);if(Ee===-1){v=!1,E=!0;break}let ye=Ee+T.length,j=e.substring(F,Ee);j.trim().length===0&&this._reportError("Blank expressions are not allowed in interpolated strings",e,`at column ${f} in`,t),a.push({text:j,start:k,end:ye});let Ws=((h==null?void 0:h.get(k))??k)+y.length;l.push(Ws),f=ye,v=!1}else{let k=f;f=e.indexOf(y,f),f===-1&&(f=e.length);let F=e.substring(k,f);i.push({text:F,start:k,end:f}),v=!0}if(!v)if(E){let k=i[i.length-1];k.text+=e.substring(f),k.end=e.length}else i.push({text:e.substring(f),start:f,end:e.length});return new Bt(i,a,l)}wrapLiteralPrimitive(e,t,s){let r=new J(0,e==null?0:e.length);return new W(new I(r,r.toAbsolute(s),e),e,t,s,this.errors)}_stripComments(e){let t=this._commentStart(e);return t!=null?e.substring(0,t):e}_commentStart(e){let t=null;for(let s=0;s<e.length-1;s++){let r=e.charCodeAt(s),i=e.charCodeAt(s+1);if(r===Mt&&i==Mt&&t==null)return s;t===r?t=null:t==null&&Tn(r)&&(t=r)}return null}_checkNoInterpolation(e,t,{start:s,end:r}){let i=-1,a=-1;for(let l of this._forEachUnquotedChar(e,0))if(i===-1)e.startsWith(s)&&(i=l);else if(a=this._getInterpolationEndIndex(e,r,l),a>-1)break;i>-1&&a>-1&&this._reportError(`Got interpolation (${s}${r}) where expression was expected`,e,`at column ${i} in`,t)}_getInterpolationEndIndex(e,t,s){for(let r of this._forEachUnquotedChar(e,s)){if(e.startsWith(t,r))return r;if(e.startsWith("//",r))return e.indexOf(t,r)}return-1}*_forEachUnquotedChar(e,t){let s=null,r=0;for(let i=t;i<e.length;i++){let a=e[i];Tn(e.charCodeAt(i))&&(s===null||s===a)&&r%2===0?s=s===null?a:null:s===null&&(yield i),r=a==="\\"?r+1:0}}},oe;(function(n){n[n.None=0]="None",n[n.Writable=1]="Writable"})(oe||(oe={}));var G=class{input;location;absoluteOffset;tokens;parseFlags;errors;offset;rparensExpected=0;rbracketsExpected=0;rbracesExpected=0;context=oe.None;sourceSpanCache=new Map;index=0;constructor(e,t,s,r,i,a,l){this.input=e,this.location=t,this.absoluteOffset=s,this.tokens=r,this.parseFlags=i,this.errors=a,this.offset=l}peek(e){let t=this.index+e;return t<this.tokens.length?this.tokens[t]:et}get next(){return this.peek(0)}get atEOF(){return this.index>=this.tokens.length}get inputIndex(){return this.atEOF?this.currentEndIndex:this.next.index+this.offset}get currentEndIndex(){return this.index>0?this.peek(-1).end+this.offset:this.tokens.length===0?this.input.length+this.offset:this.next.index+this.offset}get currentAbsoluteOffset(){return this.absoluteOffset+this.inputIndex}span(e,t){let s=this.currentEndIndex;if(t!==void 0&&t>this.currentEndIndex&&(s=t),e>s){let r=s;s=e,e=r}return new J(e,s)}sourceSpan(e,t){let s=`${e}@${this.inputIndex}:${t}`;return this.sourceSpanCache.has(s)||this.sourceSpanCache.set(s,this.span(e,t).toAbsolute(this.absoluteOffset)),this.sourceSpanCache.get(s)}advance(){this.index++}withContext(e,t){this.context|=e;let s=t();return this.context^=e,s}consumeOptionalCharacter(e){return this.next.isCharacter(e)?(this.advance(),!0):!1}peekKeywordLet(){return this.next.isKeywordLet()}peekKeywordAs(){return this.next.isKeywordAs()}expectCharacter(e){this.consumeOptionalCharacter(e)||this.error(`Missing expected ${String.fromCharCode(e)}`)}consumeOptionalOperator(e){return this.next.isOperator(e)?(this.advance(),!0):!1}expectOperator(e){this.consumeOptionalOperator(e)||this.error(`Missing expected operator ${e}`)}prettyPrintToken(e){return e===et?"end of input":`token ${e}`}expectIdentifierOrKeyword(){let e=this.next;return!e.isIdentifier()&&!e.isKeyword()?(e.isPrivateIdentifier()?this._reportErrorForPrivateIdentifier(e,"expected identifier or keyword"):this.error(`Unexpected ${this.prettyPrintToken(e)}, expected identifier or keyword`),null):(this.advance(),e.toString())}expectIdentifierOrKeywordOrString(){let e=this.next;return!e.isIdentifier()&&!e.isKeyword()&&!e.isString()?(e.isPrivateIdentifier()?this._reportErrorForPrivateIdentifier(e,"expected identifier, keyword or string"):this.error(`Unexpected ${this.prettyPrintToken(e)}, expected identifier, keyword, or string`),""):(this.advance(),e.toString())}parseChain(){let e=[],t=this.inputIndex;for(;this.index<this.tokens.length;){let s=this.parsePipe();if(e.push(s),this.consumeOptionalCharacter(Te))for(this.parseFlags&1||this.error("Binding expression cannot contain chained expression");this.consumeOptionalCharacter(Te););else if(this.index<this.tokens.length){let r=this.index;if(this.error(`Unexpected token '${this.next}'`),this.index===r)break}}if(e.length===0){let s=this.offset,r=this.offset+this.input.length;return new P(this.span(s,r),this.sourceSpan(s,r))}return e.length==1?e[0]:new Le(this.span(t),this.sourceSpan(t),e)}parsePipe(){let e=this.inputIndex,t=this.parseExpression();if(this.consumeOptionalOperator("|")){this.parseFlags&1&&this.error("Cannot have a pipe in an action expression");do{let s=this.inputIndex,r=this.expectIdentifierOrKeyword(),i,a;r!==null?i=this.sourceSpan(s):(r="",a=this.next.index!==-1?this.next.index:this.input.length+this.offset,i=new J(a,a).toAbsolute(this.absoluteOffset));let l=[];for(;this.consumeOptionalCharacter(ie);)l.push(this.parseExpression());t=new Be(this.span(e),this.sourceSpan(e,a),t,r,l,i)}while(this.consumeOptionalOperator("|"))}return t}parseExpression(){return this.parseConditional()}parseConditional(){let e=this.inputIndex,t=this.parseLogicalOr();if(this.consumeOptionalOperator("?")){let s=this.parsePipe(),r;if(this.consumeOptionalCharacter(ie))r=this.parsePipe();else{let i=this.inputIndex,a=this.input.substring(e,i);this.error(`Conditional expression ${a} requires all 3 expressions`),r=new P(this.span(e),this.sourceSpan(e))}return new Me(this.span(e),this.sourceSpan(e),t,s,r)}else return t}parseLogicalOr(){let e=this.inputIndex,t=this.parseLogicalAnd();for(;this.consumeOptionalOperator("||");){let s=this.parseLogicalAnd();t=new A(this.span(e),this.sourceSpan(e),"||",t,s)}return t}parseLogicalAnd(){let e=this.inputIndex,t=this.parseNullishCoalescing();for(;this.consumeOptionalOperator("&&");){let s=this.parseNullishCoalescing();t=new A(this.span(e),this.sourceSpan(e),"&&",t,s)}return t}parseNullishCoalescing(){let e=this.inputIndex,t=this.parseEquality();for(;this.consumeOptionalOperator("??");){let s=this.parseEquality();t=new A(this.span(e),this.sourceSpan(e),"??",t,s)}return t}parseEquality(){let e=this.inputIndex,t=this.parseRelational();for(;this.next.type==m.Operator;){let s=this.next.strValue;switch(s){case"==":case"===":case"!=":case"!==":this.advance();let r=this.parseRelational();t=new A(this.span(e),this.sourceSpan(e),s,t,r);continue}break}return t}parseRelational(){let e=this.inputIndex,t=this.parseAdditive();for(;this.next.type==m.Operator||this.next.isKeywordIn;){let s=this.next.strValue;switch(s){case"<":case">":case"<=":case">=":case"in":this.advance();let r=this.parseAdditive();t=new A(this.span(e),this.sourceSpan(e),s,t,r);continue}break}return t}parseAdditive(){let e=this.inputIndex,t=this.parseMultiplicative();for(;this.next.type==m.Operator;){let s=this.next.strValue;switch(s){case"+":case"-":this.advance();let r=this.parseMultiplicative();t=new A(this.span(e),this.sourceSpan(e),s,t,r);continue}break}return t}parseMultiplicative(){let e=this.inputIndex,t=this.parseExponentiation();for(;this.next.type==m.Operator;){let s=this.next.strValue;switch(s){case"*":case"%":case"/":this.advance();let r=this.parseExponentiation();t=new A(this.span(e),this.sourceSpan(e),s,t,r);continue}break}return t}parseExponentiation(){let e=this.inputIndex,t=this.parsePrefix();for(;this.next.type==m.Operator&&this.next.strValue==="**";){(t instanceof X||t instanceof Q||t instanceof K||t instanceof Z)&&this.error("Unary operator used immediately before exponentiation expression. Parenthesis must be used to disambiguate operator precedence"),this.advance();let s=this.parseExponentiation();t=new A(this.span(e),this.sourceSpan(e),"**",t,s)}return t}parsePrefix(){if(this.next.type==m.Operator){let e=this.inputIndex,t=this.next.strValue,s;switch(t){case"+":return this.advance(),s=this.parsePrefix(),X.createPlus(this.span(e),this.sourceSpan(e),s);case"-":return this.advance(),s=this.parsePrefix(),X.createMinus(this.span(e),this.sourceSpan(e),s);case"!":return this.advance(),s=this.parsePrefix(),new Q(this.span(e),this.sourceSpan(e),s)}}else if(this.next.isKeywordTypeof()){this.advance();let e=this.inputIndex,t=this.parsePrefix();return new K(this.span(e),this.sourceSpan(e),t)}else if(this.next.isKeywordVoid()){this.advance();let e=this.inputIndex,t=this.parsePrefix();return new Z(this.span(e),this.sourceSpan(e),t)}return this.parseCallChain()}parseCallChain(){let e=this.inputIndex,t=this.parsePrimary();for(;;)if(this.consumeOptionalCharacter(re))t=this.parseAccessMember(t,e,!1);else if(this.consumeOptionalOperator("?."))this.consumeOptionalCharacter(tt)?t=this.parseCall(t,e,!0):t=this.consumeOptionalCharacter(nt)?this.parseKeyedReadOrWrite(t,e,!0):this.parseAccessMember(t,e,!0);else if(this.consumeOptionalCharacter(nt))t=this.parseKeyedReadOrWrite(t,e,!1);else if(this.consumeOptionalCharacter(tt))t=this.parseCall(t,e,!1);else if(this.consumeOptionalOperator("!"))t=new Ve(this.span(e),this.sourceSpan(e),t);else if(this.next.isTemplateLiteralEnd())t=this.parseNoInterpolationTaggedTemplateLiteral(t,e);else if(this.next.isTemplateLiteralPart())t=this.parseTaggedTemplateLiteral(t,e);else return t}parsePrimary(){let e=this.inputIndex;if(this.consumeOptionalCharacter(tt)){this.rparensExpected++;let t=this.parsePipe();return this.consumeOptionalCharacter(se)||(this.error("Missing closing parentheses"),this.consumeOptionalCharacter(se)),this.rparensExpected--,new He(this.span(e),this.sourceSpan(e),t)}else{if(this.next.isKeywordNull())return this.advance(),new I(this.span(e),this.sourceSpan(e),null);if(this.next.isKeywordUndefined())return this.advance(),new I(this.span(e),this.sourceSpan(e),void 0);if(this.next.isKeywordTrue())return this.advance(),new I(this.span(e),this.sourceSpan(e),!0);if(this.next.isKeywordFalse())return this.advance(),new I(this.span(e),this.sourceSpan(e),!1);if(this.next.isKeywordIn())return this.advance(),new I(this.span(e),this.sourceSpan(e),"in");if(this.next.isKeywordThis())return this.advance(),new At(this.span(e),this.sourceSpan(e));if(this.consumeOptionalCharacter(nt)){this.rbracketsExpected++;let t=this.parseExpressionList(ke);return this.rbracketsExpected--,this.expectCharacter(ke),new Oe(this.span(e),this.sourceSpan(e),t)}else{if(this.next.isCharacter(ot))return this.parseLiteralMap();if(this.next.isIdentifier())return this.parseAccessMember(new Y(this.span(e),this.sourceSpan(e)),e,!1);if(this.next.isNumber()){let t=this.next.toNumber();return this.advance(),new I(this.span(e),this.sourceSpan(e),t)}else{if(this.next.isTemplateLiteralEnd())return this.parseNoInterpolationTemplateLiteral();if(this.next.isTemplateLiteralPart())return this.parseTemplateLiteral();if(this.next.isString()&&this.next.kind===q.Plain){let t=this.next.toString();return this.advance(),new I(this.span(e),this.sourceSpan(e),t)}else return this.next.isPrivateIdentifier()?(this._reportErrorForPrivateIdentifier(this.next,null),new P(this.span(e),this.sourceSpan(e))):this.index>=this.tokens.length?(this.error(`Unexpected end of expression: ${this.input}`),new P(this.span(e),this.sourceSpan(e))):(this.error(`Unexpected token ${this.next}`),new P(this.span(e),this.sourceSpan(e)))}}}}parseExpressionList(e){let t=[];do if(!this.next.isCharacter(e))t.push(this.parsePipe());else break;while(this.consumeOptionalCharacter(Ce));return t}parseLiteralMap(){let e=[],t=[],s=this.inputIndex;if(this.expectCharacter(ot),!this.consumeOptionalCharacter(be)){this.rbracesExpected++;do{let r=this.inputIndex,i=this.next.isString(),a=this.expectIdentifierOrKeywordOrString(),l={key:a,quoted:i};if(e.push(l),i)this.expectCharacter(ie),t.push(this.parsePipe());else if(this.consumeOptionalCharacter(ie))t.push(this.parsePipe());else{l.isShorthandInitialized=!0;let h=this.span(r),f=this.sourceSpan(r);t.push(new le(h,f,f,new Y(h,f),a))}}while(this.consumeOptionalCharacter(Ce)&&!this.next.isCharacter(be));this.rbracesExpected--,this.expectCharacter(be)}return new Fe(this.span(s),this.sourceSpan(s),e,t)}parseAccessMember(e,t,s){let r=this.inputIndex,i=this.withContext(oe.Writable,()=>{let h=this.expectIdentifierOrKeyword()??"";return h.length===0&&this.error("Expected identifier for property access",e.span.end),h}),a=this.sourceSpan(r),l;if(s)this.consumeOptionalOperator("=")?(this.error("The '?.' operator cannot be used in the assignment"),l=new P(this.span(t),this.sourceSpan(t))):l=new ce(this.span(t),this.sourceSpan(t),a,e,i);else if(this.consumeOptionalOperator("=")){if(!(this.parseFlags&1))return this.error("Bindings cannot contain assignments"),new P(this.span(t),this.sourceSpan(t));let h=this.parseConditional();l=new $e(this.span(t),this.sourceSpan(t),a,e,i,h)}else l=new le(this.span(t),this.sourceSpan(t),a,e,i);return l}parseCall(e,t,s){let r=this.inputIndex;this.rparensExpected++;let i=this.parseCallArguments(),a=this.span(r,this.inputIndex).toAbsolute(this.absoluteOffset);this.expectCharacter(se),this.rparensExpected--;let l=this.span(t),h=this.sourceSpan(t);return s?new pe(l,h,e,i,a):new Ue(l,h,e,i,a)}parseCallArguments(){if(this.next.isCharacter(se))return[];let e=[];do e.push(this.parsePipe());while(this.consumeOptionalCharacter(Ce));return e}expectTemplateBindingKey(){let e="",t=!1,s=this.currentAbsoluteOffset;do e+=this.expectIdentifierOrKeywordOrString(),t=this.consumeOptionalOperator("-"),t&&(e+="-");while(t);return{source:e,span:new O(s,s+e.length)}}parseTemplateBindings(e){let t=[];for(t.push(...this.parseDirectiveKeywordBindings(e));this.index<this.tokens.length;){let s=this.parseLetBinding();if(s)t.push(s);else{let r=this.expectTemplateBindingKey(),i=this.parseAsBinding(r);i?t.push(i):(r.source=e.source+r.source.charAt(0).toUpperCase()+r.source.substring(1),t.push(...this.parseDirectiveKeywordBindings(r)))}this.consumeStatementTerminator()}return new Ot(t,[],this.errors)}parseKeyedReadOrWrite(e,t,s){return this.withContext(oe.Writable,()=>{this.rbracketsExpected++;let r=this.parsePipe();if(r instanceof P&&this.error("Key access cannot be empty"),this.rbracketsExpected--,this.expectCharacter(ke),this.consumeOptionalOperator("="))if(s)this.error("The '?.' operator cannot be used in the assignment");else{let i=this.parseConditional();return new De(this.span(t),this.sourceSpan(t),e,r,i)}else return s?new ue(this.span(t),this.sourceSpan(t),e,r):new Re(this.span(t),this.sourceSpan(t),e,r);return new P(this.span(t),this.sourceSpan(t))})}parseDirectiveKeywordBindings(e){let t=[];this.consumeOptionalCharacter(ie);let s=this.getDirectiveBoundTarget(),r=this.currentAbsoluteOffset,i=this.parseAsBinding(e);i||(this.consumeStatementTerminator(),r=this.currentAbsoluteOffset);let a=new O(e.span.start,r);return t.push(new We(a,e,s)),i&&t.push(i),t}getDirectiveBoundTarget(){if(this.next===et||this.peekKeywordAs()||this.peekKeywordLet())return null;let e=this.parsePipe(),{start:t,end:s}=e.span,r=this.input.substring(t,s);return new W(e,r,this.location,this.absoluteOffset+t,this.errors)}parseAsBinding(e){if(!this.peekKeywordAs())return null;this.advance();let t=this.expectTemplateBindingKey();this.consumeStatementTerminator();let s=new O(e.span.start,this.currentAbsoluteOffset);return new me(s,t,e)}parseLetBinding(){if(!this.peekKeywordLet())return null;let e=this.currentAbsoluteOffset;this.advance();let t=this.expectTemplateBindingKey(),s=null;this.consumeOptionalOperator("=")&&(s=this.expectTemplateBindingKey()),this.consumeStatementTerminator();let r=new O(e,this.currentAbsoluteOffset);return new me(r,t,s)}parseNoInterpolationTaggedTemplateLiteral(e,t){let s=this.parseNoInterpolationTemplateLiteral();return new he(this.span(t),this.sourceSpan(t),e,s)}parseNoInterpolationTemplateLiteral(){let e=this.next.strValue,t=this.inputIndex;this.advance();let s=this.span(t),r=this.sourceSpan(t);return new fe(s,r,[new de(s,r,e)],[])}parseTaggedTemplateLiteral(e,t){let s=this.parseTemplateLiteral();return new he(this.span(t),this.sourceSpan(t),e,s)}parseTemplateLiteral(){let e=[],t=[],s=this.inputIndex;for(;this.next!==et;){let r=this.next;if(r.isTemplateLiteralPart()||r.isTemplateLiteralEnd()){let i=this.inputIndex;if(this.advance(),e.push(new de(this.span(i),this.sourceSpan(i),r.strValue)),r.isTemplateLiteralEnd())break}else if(r.isTemplateLiteralInterpolationStart()){this.advance();let i=this.parsePipe();i instanceof P?this.error("Template literal interpolation cannot be empty"):t.push(i)}else this.advance()}return new fe(this.span(s),this.sourceSpan(s),e,t)}consumeStatementTerminator(){this.consumeOptionalCharacter(Te)||this.consumeOptionalCharacter(Ce)}error(e,t=null){this.errors.push(new Pe(e,this.input,this.locationText(t),this.location)),this.skip()}locationText(e=null){return e==null&&(e=this.index),e<this.tokens.length?`at column ${this.tokens[e].index+1} in`:"at the end of the expression"}_reportErrorForPrivateIdentifier(e,t){let s=`Private identifiers are not supported. Unexpected private identifier: ${e}`;t!==null&&(s+=`, ${t}`),this.error(s)}skip(){let e=this.next;for(;this.index<this.tokens.length&&!e.isCharacter(Te)&&!e.isOperator("|")&&(this.rparensExpected<=0||!e.isCharacter(se))&&(this.rbracesExpected<=0||!e.isCharacter(be))&&(this.rbracketsExpected<=0||!e.isCharacter(ke))&&(!(this.context&oe.Writable)||!e.isOperator("="));)this.next.isError()&&this.errors.push(new Pe(this.next.toString(),this.input,this.locationText(),this.location)),this.advance(),e=this.next}},Ft=class extends Pt{errors=[];visitPipe(){this.errors.push("pipes")}};function Or(n){let e=new Map,t=0,s=0,r=0;for(;r<n.length;){let i=n[r];if(i.type===9){let[a,l]=i.parts;t+=l.length,s+=a.length}else{let a=i.parts.reduce((l,h)=>l+h.length,0);s+=a,t+=a}e.set(s,t),r++}return e}var Fr=new Map(Object.entries({class:"className",for:"htmlFor",formaction:"formAction",innerHtml:"innerHTML",readonly:"readOnly",tabindex:"tabIndex"})),_i=Array.from(Fr).reduce((n,[e,t])=>(n.set(e,t),n),new Map);var Ci=new we(new qe);function B(n){return e=>e.kind===n}function Ne(n,e){return t=>t.kind===n&&e===t.expression instanceof at}function Vr(n){return(n.kind===d.Property||n.kind===d.TwoWayProperty)&&!(n.expression instanceof at)}var Ti=[{test:B(d.StyleMap),transform:lt},{test:B(d.ClassMap),transform:lt},{test:B(d.StyleProp)},{test:B(d.ClassProp)},{test:Ne(d.Attribute,!0)},{test:Ne(d.Property,!0)},{test:Vr},{test:Ne(d.Attribute,!1)}],ki=[{test:Ne(d.DomProperty,!0)},{test:Ne(d.DomProperty,!1)},{test:B(d.Attribute)},{test:B(d.StyleMap),transform:lt},{test:B(d.ClassMap),transform:lt},{test:B(d.StyleProp)},{test:B(d.ClassProp)}],bi=new Set([d.Listener,d.TwoWayListener,d.StyleMap,d.ClassMap,d.StyleProp,d.ClassProp,d.Property,d.TwoWayProperty,d.DomProperty,d.Attribute]);function lt(n){return n.slice(n.length-1)}var Ii={constant:[p.interpolate,p.interpolate1,p.interpolate2,p.interpolate3,p.interpolate4,p.interpolate5,p.interpolate6,p.interpolate7,p.interpolate8],variable:p.interpolateV,mapping:n=>{if(n%2===0)throw new Error("Expected odd number of arguments");return(n-1)/2}};var Ni=new Map([["window",p.resolveWindow],["document",p.resolveDocument],["body",p.resolveBody]]);var Ai=new Map([[D.HTML,p.sanitizeHtml],[D.RESOURCE_URL,p.sanitizeResourceUrl],[D.SCRIPT,p.sanitizeScript],[D.STYLE,p.sanitizeStyle],[D.URL,p.sanitizeUrl]]),Pi=new Map([[D.HTML,p.trustConstantHtml],[D.RESOURCE_URL,p.trustConstantResourceUrl]]);var Xn;(function(n){n[n.None=0]="None",n[n.ViewContextRead=1]="ViewContextRead",n[n.ViewContextWrite=2]="ViewContextWrite",n[n.SideEffectful=4]="SideEffectful"})(Xn||(Xn={}));var Li=new Map([[U.Property,H.Property],[U.TwoWay,H.TwoWayProperty],[U.Attribute,H.Attribute],[U.Class,H.ClassName],[U.Style,H.StyleProperty],[U.Animation,H.Animation]]);var Mi=Symbol("queryAdvancePlaceholder");var Jn;(function(n){n[n.NG_CONTENT=0]="NG_CONTENT",n[n.STYLE=1]="STYLE",n[n.STYLESHEET=2]="STYLESHEET",n[n.SCRIPT=3]="SCRIPT",n[n.OTHER=4]="OTHER"})(Jn||(Jn={}));var Yn;(function(n){n.IDLE="idle",n.TIMER="timer",n.INTERACTION="interaction",n.IMMEDIATE="immediate",n.HOVER="hover",n.VIEWPORT="viewport",n.NEVER="never"})(Yn||(Yn={}));var ms="%COMP%",$i=`_nghost-${ms}`,Ri=`_ngcontent-${ms}`;var Qn;(function(n){n[n.Extract=0]="Extract",n[n.Merge=1]="Merge"})(Qn||(Qn={}));var Di=new Nt("20.0.5");function gs({start:n,end:e},t){let s=n,r=e;for(;r!==s&&/\s/.test(t[r-1]);)r--;for(;s!==r&&/\s/.test(t[s]);)s++;return{start:s,end:r}}function Hr({start:n,end:e},t){let s=n,r=e;for(;r!==t.length&&/\s/.test(t[r]);)r++;for(;s!==0&&/\s/.test(t[s-1]);)s--;return{start:s,end:r}}function Wr(n,e){return e[n.start-1]==="("&&e[n.end]===")"?{start:n.start-1,end:n.end+1}:n}function vs(n,e,t){let s=0,r={start:n.start,end:n.end};for(;;){let i=Hr(r,e),a=Wr(i,e);if(i.start===a.start&&i.end===a.end)break;r.start=a.start,r.end=a.end,s++}return{hasParens:(t?s-1:s)!==0,outerSpan:gs(t?{start:r.start+1,end:r.end-1}:r,e),innerSpan:gs(n,e)}}function ws(n){return typeof n=="string"?e=>e===n:e=>n.test(e)}function xs(n,e,t){let s=ws(e);for(let r=t;r>=0;r--){let i=n[r];if(s(i))return r}throw new Error(`Cannot find front char ${e} from index ${t} in ${JSON.stringify(n)}`)}function Ss(n,e,t){let s=ws(e);for(let r=t;r<n.length;r++){let i=n[r];if(s(i))return r}throw new Error(`Cannot find character ${e} from index ${t} in ${JSON.stringify(n)}`)}function Es(n){return n.slice(0,1).toLowerCase()+n.slice(1)}function ze(n){let{start:e,end:t}=n;return{start:e,end:t,range:[e,t]}}var qr=n=>we.prototype._commentStart(n);function jr(n,e){let t=e?qr(n):null;if(t===null)return{text:n,comments:[]};let s={type:"CommentLine",value:n.slice(t+2),...ze({start:t,end:n.length})};return{text:n.slice(0,t),comments:[s]}}function Ge(n,e=!0){return t=>{let s=new qe,r=new we(s),{text:i,comments:a}=jr(t,e),l=n(i,r);if(l.errors.length!==0){let[{message:h}]=l.errors;throw new SyntaxError(h.replace(/^Parser Error: | at column \d+ in [^]*$/g,""))}return{result:l,comments:a,text:i}}}var ys=Ge((n,e)=>e.parseBinding(n,"",0)),zr=Ge((n,e)=>e.parseSimpleBinding(n,"",0)),_s=Ge((n,e)=>e.parseAction(n,"",0)),Cs=Ge((n,e)=>e.parseInterpolationExpression(n,"",0)),Ts=Ge((n,e)=>e.parseTemplateBindings("",n,"",0,0),!1);var Xr=(n,e,t)=>{if(!(n&&e==null))return Array.isArray(e)||typeof e=="string"?e[t<0?e.length+t:t]:e.at(t)},ut=Xr;var qt=class{text;constructor(e){this.text=e}getCharacterIndex(e,t){return Ss(this.text,e,t)}getCharacterLastIndex(e,t){return xs(this.text,e,t)}transformSpan(e,{stripSpaces:t=!1,hasParentParens:s=!1}={}){if(!t)return ze(e);let{outerSpan:r,innerSpan:i,hasParens:a}=vs(e,this.text,s),l=ze(i);return a&&(l.extra={parenthesized:!0,parenStart:r.start,parenEnd:r.end}),l}createNode(e,{stripSpaces:t=!0,hasParentParens:s=!1}={}){let{type:r,start:i,end:a}=e,l={...e,...this.transformSpan({start:i,end:a},{stripSpaces:t,hasParentParens:s})};switch(r){case"NumericLiteral":case"StringLiteral":{let h=this.text.slice(l.start,l.end),{value:f}=l;l.extra={...l.extra,raw:h,rawValue:f};break}case"ObjectProperty":{let{shorthand:h}=l;h&&(l.extra={...l.extra,shorthand:h});break}}return l}},ks=qt;function jt(n){var e;return!!((e=n.extra)!=null&&e.parenthesized)}function $(n){return jt(n)?n.extra.parenStart:n.start}function R(n){return jt(n)?n.extra.parenEnd:n.end}function bs(n){return(n.type==="OptionalCallExpression"||n.type==="OptionalMemberExpression")&&!jt(n)}function Is(n,e){let{start:t,end:s}=n.sourceSpan;return t>=s||/^\s+$/.test(e.slice(t,s))}var Ye,xe,u,w,Xe,x,pt,Je=class extends ks{constructor(t,s){super(s);V(this,u);V(this,Ye);V(this,xe);te(this,Ye,t),te(this,xe,s)}get node(){return c(this,u,x).call(this,L(this,Ye))}transformNode(t){return c(this,u,pt).call(this,t)}};Ye=new WeakMap,xe=new WeakMap,u=new WeakSet,w=function(t,{stripSpaces:s=!0,hasParentParens:r=!1}={}){return this.createNode(t,{stripSpaces:s,hasParentParens:r})},Xe=function(t,s,{computed:r,optional:i,end:a=R(s),hasParentParens:l=!1}){if(Is(t,L(this,xe))||t.sourceSpan.start===s.start)return s;let h=c(this,u,x).call(this,t),f=bs(h);return c(this,u,w).call(this,{type:i||f?"OptionalMemberExpression":"MemberExpression",object:h,property:s,computed:r,...i?{optional:!0}:f?{optional:!1}:void 0,start:$(h),end:a},{hasParentParens:l})},x=function(t,s){return c(this,u,pt).call(this,t,s)},pt=function(t,s){let{isInParentParens:r}={isInParentParens:!1,...s};if(t instanceof Vt){let{expressions:i}=t;if(i.length!==1)throw new Error("Unexpected 'Interpolation'");return c(this,u,x).call(this,i[0])}if(t instanceof X)return c(this,u,w).call(this,{type:"UnaryExpression",prefix:!0,argument:c(this,u,x).call(this,t.expr),operator:t.operator,...t.sourceSpan},{hasParentParens:r});if(t instanceof A){let{left:i,operation:a,right:l}=t,h=c(this,u,x).call(this,i),f=c(this,u,x).call(this,l),v=$(h),E=R(f),y={left:h,right:f,start:v,end:E};return a==="&&"||a==="||"||a==="??"?c(this,u,w).call(this,{...y,type:"LogicalExpression",operator:a},{hasParentParens:r}):c(this,u,w).call(this,{...y,type:"BinaryExpression",operator:a},{hasParentParens:r})}if(t instanceof Be){let{exp:i,name:a,args:l}=t,h=c(this,u,x).call(this,i),f=$(h),v=R(h),E=this.getCharacterIndex(/\S/,this.getCharacterIndex("|",v)+1),y=c(this,u,w).call(this,{type:"Identifier",name:a,start:E,end:E+a.length}),T=l.map(k=>c(this,u,x).call(this,k));return c(this,u,w).call(this,{type:"NGPipeExpression",left:h,right:y,arguments:T,start:f,end:R(T.length===0?y:ut(!1,T,-1))},{hasParentParens:r})}if(t instanceof Le)return c(this,u,w).call(this,{type:"NGChainedExpression",expressions:t.expressions.map(i=>c(this,u,x).call(this,i)),...t.sourceSpan},{hasParentParens:r});if(t instanceof Me){let{condition:i,trueExp:a,falseExp:l}=t,h=c(this,u,x).call(this,i),f=c(this,u,x).call(this,a),v=c(this,u,x).call(this,l);return c(this,u,w).call(this,{type:"ConditionalExpression",test:h,consequent:f,alternate:v,start:$(h),end:R(v)},{hasParentParens:r})}if(t instanceof P)return c(this,u,w).call(this,{type:"NGEmptyExpression",...t.sourceSpan},{hasParentParens:r});if(t instanceof Y)return c(this,u,w).call(this,{type:"ThisExpression",...t.sourceSpan},{hasParentParens:r});if(t instanceof Re||t instanceof ue)return c(this,u,Xe).call(this,t.receiver,c(this,u,x).call(this,t.key),{computed:!0,optional:t instanceof ue,end:t.sourceSpan.end,hasParentParens:r});if(t instanceof Oe)return c(this,u,w).call(this,{type:"ArrayExpression",elements:t.expressions.map(i=>c(this,u,x).call(this,i)),...t.sourceSpan},{hasParentParens:r});if(t instanceof Fe){let{keys:i,values:a}=t,l=a.map(f=>c(this,u,x).call(this,f)),h=i.map(({key:f,quoted:v},E)=>{let y=l[E],T=$(y),k=R(y),F=this.getCharacterIndex(/\S/,E===0?t.sourceSpan.start+1:this.getCharacterIndex(",",R(l[E-1]))+1),Ee=T===F?k:this.getCharacterLastIndex(/\S/,this.getCharacterLastIndex(":",T-1)-1)+1,ye={start:F,end:Ee},j=v?c(this,u,w).call(this,{type:"StringLiteral",value:f,...ye}):c(this,u,w).call(this,{type:"Identifier",name:f,...ye}),en=j.end<j.start||F===T;return c(this,u,w).call(this,{type:"ObjectProperty",key:j,value:y,shorthand:en,computed:!1,start:$(j),end:k})});return c(this,u,w).call(this,{type:"ObjectExpression",properties:h,...t.sourceSpan},{hasParentParens:r})}if(t instanceof I){let{value:i}=t;switch(typeof i){case"boolean":return c(this,u,w).call(this,{type:"BooleanLiteral",value:i,...t.sourceSpan},{hasParentParens:r});case"number":return c(this,u,w).call(this,{type:"NumericLiteral",value:i,...t.sourceSpan},{hasParentParens:r});case"object":return c(this,u,w).call(this,{type:"NullLiteral",...t.sourceSpan},{hasParentParens:r});case"string":return c(this,u,w).call(this,{type:"StringLiteral",value:i,...t.sourceSpan},{hasParentParens:r});case"undefined":return c(this,u,w).call(this,{type:"Identifier",name:"undefined",...t.sourceSpan},{hasParentParens:r});default:throw new Error(`Unexpected LiteralPrimitive value type ${typeof i}`)}}if(t instanceof Ue||t instanceof pe){let i=t instanceof pe,{receiver:a,args:l}=t,h=l.length===1?[c(this,u,x).call(this,l[0],{isInParentParens:!0})]:l.map(y=>c(this,u,x).call(this,y)),f=c(this,u,x).call(this,a),v=bs(f),E=i||v?"OptionalCallExpression":"CallExpression";return c(this,u,w).call(this,{type:E,callee:f,arguments:h,...E==="OptionalCallExpression"?{optional:i}:void 0,start:$(f),end:t.sourceSpan.end},{hasParentParens:r})}if(t instanceof Ve){let i=c(this,u,x).call(this,t.expression);return c(this,u,w).call(this,{type:"TSNonNullExpression",expression:i,start:$(i),end:t.sourceSpan.end},{hasParentParens:r})}if(t instanceof Q||t instanceof K||t instanceof Z){let i=t instanceof Q?"!":t instanceof K?"typeof":t instanceof Z?"void":void 0;if(!i)throw new Error("Unexpected expression.");let{start:a}=t.sourceSpan;if(i==="typeof"||i==="void"){let h=this.text.lastIndexOf(i,a);if(h===-1)throw new Error(`Cannot find operator '${i}' from index ${a} in ${JSON.stringify(this.text)}`);a=h}let l=c(this,u,x).call(this,t.expression);return c(this,u,w).call(this,{type:"UnaryExpression",prefix:!0,operator:i,argument:l,start:a,end:R(l)},{hasParentParens:r})}if(t instanceof le||t instanceof ce){let{receiver:i,name:a}=t,l=this.getCharacterLastIndex(/\S/,t.sourceSpan.end-1)+1,h=c(this,u,w).call(this,{type:"Identifier",name:a,start:l-a.length,end:l},Is(i,L(this,xe))?{hasParentParens:r}:{});return c(this,u,Xe).call(this,i,h,{computed:!1,optional:t instanceof ce,hasParentParens:r})}if(t instanceof De){let i=c(this,u,x).call(this,t.key),a=c(this,u,x).call(this,t.value),l=c(this,u,Xe).call(this,t.receiver,i,{computed:!0,optional:!1,end:this.getCharacterIndex("]",R(i))+1});return c(this,u,w).call(this,{type:"AssignmentExpression",left:l,operator:"=",right:a,start:$(l),end:R(a)},{hasParentParens:r})}if(t instanceof $e){let{receiver:i,name:a,value:l}=t,h=c(this,u,x).call(this,l),f=this.getCharacterLastIndex(/\S/,this.getCharacterLastIndex("=",$(h)-1)-1)+1,v=c(this,u,w).call(this,{type:"Identifier",name:a,start:f-a.length,end:f}),E=c(this,u,Xe).call(this,i,v,{computed:!1,optional:!1});return c(this,u,w).call(this,{type:"AssignmentExpression",left:E,operator:"=",right:h,start:$(E),end:R(h)},{hasParentParens:r})}if(t instanceof he)return c(this,u,w).call(this,{type:"TaggedTemplateExpression",tag:c(this,u,x).call(this,t.tag),quasi:c(this,u,x).call(this,t.template),...t.sourceSpan});if(t instanceof fe){let{elements:i,expressions:a}=t;return c(this,u,w).call(this,{type:"TemplateLiteral",quasis:i.map(l=>c(this,u,x).call(this,l,{parent:t})),expressions:a.map(l=>c(this,u,x).call(this,l)),...t.sourceSpan})}if(t instanceof de){let{elements:i}=s.parent,a=i.indexOf(t),l=a===0,h=a===i.length-1,f=t.sourceSpan.end-(h?1:0),v=t.sourceSpan.start+(l?1:0),E=this.text.slice(v,f);return c(this,u,w).call(this,{type:"TemplateElement",value:{cooked:t.text,raw:E},start:v,end:f,tail:h},{stripSpaces:!1})}if(t instanceof He)return c(this,u,pt).call(this,t.expression);throw new Error(`Unexpected node type '${t.constructor.name}'`)};function Ns(n,e){return new Je(n,e).node}function As(n){return n instanceof We}function Ps(n){return n instanceof me}var Se,ee,g,Ls,N,Gt,Xt,Jt,Ms,$s,Rs,Ds,zt=class extends Je{constructor(t,s){super(void 0,s);V(this,g);V(this,Se);V(this,ee);te(this,Se,t),te(this,ee,s);for(let r of t)c(this,g,Ms).call(this,r)}get expressions(){return c(this,g,Rs).call(this)}};Se=new WeakMap,ee=new WeakMap,g=new WeakSet,Ls=function(){return L(this,Se)[0].key},N=function(t,{stripSpaces:s=!0}={}){return this.createNode(t,{stripSpaces:s})},Gt=function(t){return this.transformNode(t)},Xt=function(t){return Es(t.slice(L(this,g,Ls).source.length))},Jt=function(t){let s=L(this,ee);if(s[t.start]!=='"'&&s[t.start]!=="'")return;let r=s[t.start],i=!1;for(let a=t.start+1;a<s.length;a++)switch(s[a]){case r:if(!i){t.end=a+1;return}default:i=!1;break;case"\\":i=!i;break}},Ms=function(t){c(this,g,Jt).call(this,t.key.span),Ps(t)&&t.value&&c(this,g,Jt).call(this,t.value.span)},$s=function(t){if(!t.value||t.value.source)return t.value;let s=this.getCharacterIndex(/\S/,t.sourceSpan.start);return{source:"$implicit",span:{start:s,end:s}}},Rs=function(){let t=L(this,Se),[s]=t,r=L(this,ee).slice(s.sourceSpan.start,s.sourceSpan.end).trim().length===0?t.slice(1):t,i=[],a=null;for(let[l,h]of r.entries()){if(a&&As(a)&&Ps(h)&&h.value&&h.value.source===a.key.source){let f=c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:h.key.source,...h.key.span}),v=(T,k)=>({...T,...this.transformSpan({start:T.start,end:k})}),E=T=>({...v(T,f.end),alias:f}),y=i.pop();if(y.type==="NGMicrosyntaxExpression")i.push(E(y));else if(y.type==="NGMicrosyntaxKeyedExpression"){let T=E(y.expression);i.push(v({...y,expression:T},T.end))}else throw new Error(`Unexpected type ${y.type}`)}else i.push(c(this,g,Ds).call(this,h,l));a=h}return c(this,g,N).call(this,{type:"NGMicrosyntax",body:i,...i.length===0?t[0].sourceSpan:{start:i[0].start,end:ut(!1,i,-1).end}})},Ds=function(t,s){if(As(t)){let{key:r,value:i}=t;return i?s===0?c(this,g,N).call(this,{type:"NGMicrosyntaxExpression",expression:c(this,g,Gt).call(this,i.ast),alias:null,...i.sourceSpan}):c(this,g,N).call(this,{type:"NGMicrosyntaxKeyedExpression",key:c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:c(this,g,Xt).call(this,r.source),...r.span}),expression:c(this,g,N).call(this,{type:"NGMicrosyntaxExpression",expression:c(this,g,Gt).call(this,i.ast),alias:null,...i.sourceSpan}),start:r.span.start,end:i.sourceSpan.end}):c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:c(this,g,Xt).call(this,r.source),...r.span})}else{let{key:r,sourceSpan:i}=t;if(/^let\s$/.test(L(this,ee).slice(i.start,i.start+4))){let{value:l}=t;return c(this,g,N).call(this,{type:"NGMicrosyntaxLet",key:c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:r.source,...r.span}),value:l?c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:l.source,...l.span}):null,start:i.start,end:l?l.span.end:r.span.end})}else{let l=c(this,g,$s).call(this,t);return c(this,g,N).call(this,{type:"NGMicrosyntaxAs",key:c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:l.source,...l.span}),alias:c(this,g,N).call(this,{type:"NGMicrosyntaxKey",name:r.source,...r.span}),start:l.span.start,end:r.span.end})}}};function Bs(n,e){return new zt(n,e).expressions}function ht({result:{ast:n},text:e,comments:t}){return Object.assign(Ns(n,e),{comments:t})}function Os({result:{templateBindings:n},text:e}){return Bs(n,e)}var Fs=n=>ht(ys(n));var Vs=n=>ht(Cs(n)),Yt=n=>ht(_s(n)),Us=n=>Os(Ts(n));function Qt(n){var s,r,i;let e=((s=n.range)==null?void 0:s[0])??n.start,t=(i=((r=n.declaration)==null?void 0:r.decorators)??n.decorators)==null?void 0:i[0];return t?Math.min(Qt(t),e):e}function Hs(n){var t;return((t=n.range)==null?void 0:t[1])??n.end}function ft(n){return{astFormat:"estree",parse(e){let t=n(e);return{type:"NGRoot",node:n===Yt&&t.type!=="NGChainedExpression"?{...t,type:"NGChainedExpression",expressions:[t]}:t}},locStart:Qt,locEnd:Hs}}var Jr=ft(Yt),Yr=ft(Fs),Qr=ft(Vs),Kr=ft(Us);var ho=Zt;export{ho as default,Kt as parsers};
