{"version": 3, "file": "sk.min.js", "sources": ["../../../../packages/locale/lang/sk.ts"], "sourcesContent": ["export default {\n  name: 'sk',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON><PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: '<PERSON><PERSON><PERSON>ť dátum',\n      selectTime: 'V<PERSON>brať čas',\n      startDate: '<PERSON><PERSON><PERSON> začiatku',\n      startTime: '<PERSON>as začiatku',\n      endDate: 'D<PERSON>tum konca',\n      endTime: 'Čas konca',\n      prevYear: 'Predošlý rok',\n      nextYear: 'Ďalší rok',\n      prevMonth: 'Predošlý mesiac',\n      nextMonth: 'Ďalší mesiac',\n      day: 'Deň',\n      week: 'Týždeň',\n      month: 'Mesiac',\n      year: 'Rok',\n      month1: 'Január',\n      month2: 'Február',\n      month3: '<PERSON><PERSON>',\n      month4: 'Apríl',\n      month5: '<PERSON>á<PERSON>',\n      month6: 'J<PERSON>',\n      month7: 'J<PERSON>',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Október',\n      month11: 'November',\n      month12: 'December',\n      weeks: {\n        sun: 'Ne',\n        mon: 'Po',\n        tue: 'Ut',\n        wed: 'St',\n        thu: 'Št',\n        fri: 'Pi',\n        sat: 'So',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Máj',\n        jun: 'Jún',\n        jul: 'Júl',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Načítavanie',\n      noMatch: 'Žiadna zhoda',\n      noData: 'Žiadne dáta',\n      placeholder: 'Vybrať',\n    },\n    mention: {\n      loading: 'Načítavanie',\n    },\n    cascader: {\n      noMatch: 'Žiadna zhoda',\n      loading: 'Načítavanie',\n      placeholder: 'Vybrať',\n      noData: 'Žiadne dáta',\n    },\n    pagination: {\n      goto: 'Choď na',\n      pagesize: 'na stranu',\n      total: 'Všetko {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Správa',\n      confirm: 'OK',\n      cancel: 'Zrušiť',\n      error: 'Neplatný vstup',\n    },\n    upload: {\n      deleteTip: 'pre odstránenie stisni klávesu Delete',\n      delete: 'Vymazať',\n      preview: 'Prehliadať',\n      continue: 'Pokračovať',\n    },\n    table: {\n      emptyText: 'Žiadne dáta',\n      confirmFilter: 'Potvrdiť',\n      resetFilter: 'Zresetovať',\n      clearFilter: 'Všetko',\n      sumText: 'Spolu',\n    },\n    tree: {\n      emptyText: 'Žiadne dáta',\n    },\n    transfer: {\n      noMatch: 'Žiadna zhoda',\n      noData: 'Žiadne dáta',\n      titles: ['Zoznam 1', 'Zoznam 2'],\n      filterPlaceholder: 'Filtrovať podľa',\n      noCheckedFormat: '{total} položiek',\n      hasCheckedFormat: '{checked}/{total} označených',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,wBAAwB,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,sBAAsB,CAAC,QAAQ,CAAC,wBAAwB,CAAC,SAAS,CAAC,yBAAyB,CAAC,SAAS,CAAC,2BAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,qBAAqB,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,OAAO,CAAC,qBAAqB,CAAC,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,qBAAqB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,6CAA6C,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,iBAAiB,CAAC,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,2BAA2B,CAAC,eAAe,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,sCAAsC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}