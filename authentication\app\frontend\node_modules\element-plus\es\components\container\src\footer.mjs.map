{"version": 3, "file": "footer.mjs", "sources": ["../../../../../../packages/components/container/src/footer.vue"], "sourcesContent": ["<template>\n  <footer :class=\"ns.b()\" :style=\"style\">\n    <slot />\n  </footer>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: '<PERSON>Footer',\n})\nconst props = defineProps({\n  /**\n   * @description height of the footer\n   */\n  height: {\n    type: String,\n    default: null,\n  },\n})\n\nconst ns = useNamespace('footer')\n\nconst style = computed(\n  () =>\n    (props.height\n      ? ns.cssVarBlock({ height: props.height })\n      : {}) as CSSProperties\n)\n</script>\n"], "names": ["_openBlock", "_normalizeClass", "_unref", "_normalizeStyle"], "mappings": ";;;;mCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;AAWA,MAAM,OAAAA,+BAA0B,CAAA,QAAA,EAAA;AAEhC,QAAA,KAAc,EAAAC,cAAA,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AAAA,QACZ,KACS,EAAAC,cACA,CAAAD,KAAA,CAAA,KAAc,CAAA,CAAA;AAChB,OACT,EAAA;;;;;;;;;;"}