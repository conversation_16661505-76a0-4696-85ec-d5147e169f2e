"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Client = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * Copyright (c) 2018 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
const abstract_client_1 = require("../../../common/abstract_client");
/**
 * chc client
 * @class
 */
class Client extends abstract_client_1.AbstractClient {
    constructor(clientConfig) {
        super("chc.tencentcloudapi.com", "2023-04-18", clientConfig);
    }
    /**
     * 创建设备退出工单
     */
    async CreateQuitWorkOrder(req, cb) {
        return this.request("CreateQuitWorkOrder", req, cb);
    }
    /**
     * 获取用户可用的工单类型
     */
    async DescribeWorkOrderTypes(req, cb) {
        return this.request("DescribeWorkOrderTypes", req, cb);
    }
    /**
     * 查询设备型号详情
     */
    async DescribeModel(req, cb) {
        return this.request("DescribeModel", req, cb);
    }
    /**
     * 获取机架列表
     */
    async DescribeRacks(req, cb) {
        return this.request("DescribeRacks", req, cb);
    }
    /**
     * 创建设备型号评估工单
     */
    async CreateModelEvaluationWorkOrder(req, cb) {
        return this.request("CreateModelEvaluationWorkOrder", req, cb);
    }
    /**
     * 新增服务器设备型号
     */
    async CreateServerModel(req, cb) {
        return this.request("CreateServerModel", req, cb);
    }
    /**
     * 创建设备下架工单
     */
    async CreateRackOffWorkOrder(req, cb) {
        return this.request("CreateRackOffWorkOrder", req, cb);
    }
    /**
     * 获取用户的型号和对应的版本数量
     */
    async DescribeModelVersionList(req, cb) {
        return this.request("DescribeModelVersionList", req, cb);
    }
    /**
     * 查询通用服务工单详情
     */
    async DescribeCommonServiceWorkOrderDetail(req, cb) {
        return this.request("DescribeCommonServiceWorkOrderDetail", req, cb);
    }
    /**
     * 如果当前该工单类型是收藏状态，调用接口后变成未收藏状态，如果是未收藏状态，调用该接口变为收藏状态
     */
    async ModifyWorkOrderTypeCollectFlag(req, cb) {
        return this.request("ModifyWorkOrderTypeCollectFlag", req, cb);
    }
    /**
     * 查询设备型号评估工单详情
     */
    async DescribeModelEvaluationWorkOrderDetail(req, cb) {
        return this.request("DescribeModelEvaluationWorkOrderDetail", req, cb);
    }
    /**
     * 查询客户信息
     */
    async DescribeCustomerInfo(req, cb) {
        return this.request("DescribeCustomerInfo", req, cb);
    }
    /**
     * 获取用户可操作的园区列表
     */
    async DescribeCampusList(req, cb) {
        return this.request("DescribeCampusList", req, cb);
    }
    /**
     * 获取机房管理单元的机位分布
     */
    async DescribeRacksDistribution(req, cb) {
        return this.request("DescribeRacksDistribution", req, cb);
    }
    /**
     * 获取机位列表
     */
    async DescribePositions(req, cb) {
        return this.request("DescribePositions", req, cb);
    }
    /**
     * 创建通用工单
     */
    async CreateCommonServiceWorkOrder(req, cb) {
        return this.request("CreateCommonServiceWorkOrder", req, cb);
    }
    /**
     * 用于查询设备类工单的工单详情，如：'receiving', 'rackOn', 'powerOn', 'powerOff', 'rackOff', 'quit'
     */
    async DescribeDeviceWorkOrderDetail(req, cb) {
        return this.request("DescribeDeviceWorkOrderDetail", req, cb);
    }
    /**
     * 获取机房内可用的型号列表
     */
    async DescribeAvailableModelList(req, cb) {
        return this.request("DescribeAvailableModelList", req, cb);
    }
    /**
     * 获取设备列表
     */
    async DescribeDeviceList(req, cb) {
        return this.request("DescribeDeviceList", req, cb);
    }
    /**
     * 创建设备开电工单
     */
    async CreatePowerOnWorkOrder(req, cb) {
        return this.request("CreatePowerOnWorkOrder", req, cb);
    }
    /**
     * 获取机架总数及各状态对应的数量汇总
     */
    async DescribePositionStatusSummary(req, cb) {
        return this.request("DescribePositionStatusSummary", req, cb);
    }
    /**
     * 导出工单详情
     */
    async ExportCustomerWorkOrderDetail(req, cb) {
        return this.request("ExportCustomerWorkOrderDetail", req, cb);
    }
    /**
     * 创建临时设备退出工单
     */
    async CreateSpeciallyQuitWorkOrder(req, cb) {
        return this.request("CreateSpeciallyQuitWorkOrder", req, cb);
    }
    /**
     * 创建设备关电工单
     */
    async CreatePowerOffWorkOrder(req, cb) {
        return this.request("CreatePowerOffWorkOrder", req, cb);
    }
    /**
     * 创建人员到访工单
     */
    async CreatePersonnelVisitWorkOrder(req, cb) {
        return this.request("CreatePersonnelVisitWorkOrder", req, cb);
    }
    /**
     * 查询人员到访工单详情
     */
    async DescribePersonnelVisitWorkOrderDetail(req, cb) {
        return this.request("DescribePersonnelVisitWorkOrderDetail", req, cb);
    }
    /**
     * 查询机房管理单元详情
     */
    async DescribeIdcUnitDetail(req, cb) {
        return this.request("DescribeIdcUnitDetail", req, cb);
    }
    /**
     * 确认通用服务工单
     */
    async ConfirmCommonServiceWorkOrder(req, cb) {
        return this.request("ConfirmCommonServiceWorkOrder", req, cb);
    }
    /**
     * 创建设备搬迁工单
     */
    async CreateMovingWorkOrder(req, cb) {
        return this.request("CreateMovingWorkOrder", req, cb);
    }
    /**
     * 创建新的网络设备型号
     */
    async CreateNetDeviceModel(req, cb) {
        return this.request("CreateNetDeviceModel", req, cb);
    }
    /**
     * 工单统计数据查询
     */
    async DescribeWorkOrderStatistics(req, cb) {
        return this.request("DescribeWorkOrderStatistics", req, cb);
    }
    /**
     * 查询资源汇总
     */
    async DescribeResourceUsage(req, cb) {
        return this.request("DescribeResourceUsage", req, cb);
    }
    /**
     * 查询机房管理单元资产详情
     */
    async DescribeIdcUnitAssetDetail(req, cb) {
        return this.request("DescribeIdcUnitAssetDetail", req, cb);
    }
    /**
     * 创建设备上架工单
     */
    async CreateRackOnWorkOrder(req, cb) {
        return this.request("CreateRackOnWorkOrder", req, cb);
    }
    /**
     * 获取机房和机房管理单元信息
     */
    async DescribeIdcs(req, cb) {
        return this.request("DescribeIdcs", req, cb);
    }
    /**
     * 创建设备收货工单
     */
    async CreateReceivingWorkOrder(req, cb) {
        return this.request("CreateReceivingWorkOrder", req, cb);
    }
    /**
     * 获取型号的填写模板
     */
    async DescribeModelTemplate(req, cb) {
        return this.request("DescribeModelTemplate", req, cb);
    }
    /**
     * 查询工单列表
     */
    async DescribeWorkOrderList(req, cb) {
        return this.request("DescribeWorkOrderList", req, cb);
    }
}
exports.Client = Client;
