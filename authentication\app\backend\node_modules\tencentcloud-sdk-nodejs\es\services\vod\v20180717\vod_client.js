import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("vod.tencentcloudapi.com", "2018-07-17", clientConfig);
    }
    async CreateImageProcessingTemplate(req, cb) {
        return this.request("CreateImageProcessingTemplate", req, cb);
    }
    async DescribeCLSLogsets(req, cb) {
        return this.request("DescribeCLSLogsets", req, cb);
    }
    async CreateRoundPlay(req, cb) {
        return this.request("CreateRoundPlay", req, cb);
    }
    async ModifyDefaultStorageRegion(req, cb) {
        return this.request("ModifyDefaultStorageRegion", req, cb);
    }
    async ExtractTraceWatermark(req, cb) {
        return this.request("ExtractTraceWatermark", req, cb);
    }
    async DescribeFileAttributes(req, cb) {
        return this.request("DescribeFileAttributes", req, cb);
    }
    async EditMedia(req, cb) {
        return this.request("EditMedia", req, cb);
    }
    async ModifyJustInTimeTranscodeTemplate(req, cb) {
        return this.request("ModifyJustInTimeTranscodeTemplate", req, cb);
    }
    async DescribeVodDomains(req, cb) {
        return this.request("DescribeVodDomains", req, cb);
    }
    async ApplyUpload(req, cb) {
        return this.request("ApplyUpload", req, cb);
    }
    async CreateSnapshotByTimeOffsetTemplate(req, cb) {
        return this.request("CreateSnapshotByTimeOffsetTemplate", req, cb);
    }
    async DescribeAIAnalysisTemplates(req, cb) {
        return this.request("DescribeAIAnalysisTemplates", req, cb);
    }
    async PullEvents(req, cb) {
        return this.request("PullEvents", req, cb);
    }
    async ProcessMediaByProcedure(req, cb) {
        return this.request("ProcessMediaByProcedure", req, cb);
    }
    async CreateQualityInspectTemplate(req, cb) {
        return this.request("CreateQualityInspectTemplate", req, cb);
    }
    async ModifyRebuildMediaTemplate(req, cb) {
        return this.request("ModifyRebuildMediaTemplate", req, cb);
    }
    async DeleteTranscodeTemplate(req, cb) {
        return this.request("DeleteTranscodeTemplate", req, cb);
    }
    async DescribeTaskDetail(req, cb) {
        return this.request("DescribeTaskDetail", req, cb);
    }
    async DescribeReviewDetails(req, cb) {
        return this.request("DescribeReviewDetails", req, cb);
    }
    async DescribeWordSamples(req, cb) {
        return this.request("DescribeWordSamples", req, cb);
    }
    async DescribeStorageData(req, cb) {
        return this.request("DescribeStorageData", req, cb);
    }
    async SplitMedia(req, cb) {
        return this.request("SplitMedia", req, cb);
    }
    async DescribeImageProcessingTemplates(req, cb) {
        return this.request("DescribeImageProcessingTemplates", req, cb);
    }
    async DeleteProcedureTemplate(req, cb) {
        return this.request("DeleteProcedureTemplate", req, cb);
    }
    async DeleteAdaptiveDynamicStreamingTemplate(req, cb) {
        return this.request("DeleteAdaptiveDynamicStreamingTemplate", req, cb);
    }
    async CreateAdaptiveDynamicStreamingTemplate(req, cb) {
        return this.request("CreateAdaptiveDynamicStreamingTemplate", req, cb);
    }
    async DescribeDrmDataKey(req, cb) {
        return this.request("DescribeDrmDataKey", req, cb);
    }
    async DeleteSnapshotByTimeOffsetTemplate(req, cb) {
        return this.request("DeleteSnapshotByTimeOffsetTemplate", req, cb);
    }
    async RemoveWatermark(req, cb) {
        return this.request("RemoveWatermark", req, cb);
    }
    async ModifyDefaultDistributionConfig(req, cb) {
        return this.request("ModifyDefaultDistributionConfig", req, cb);
    }
    async DescribeCdnLogs(req, cb) {
        return this.request("DescribeCdnLogs", req, cb);
    }
    async CreateCLSLogset(req, cb) {
        return this.request("CreateCLSLogset", req, cb);
    }
    async SetCLSPushTarget(req, cb) {
        return this.request("SetCLSPushTarget", req, cb);
    }
    async DescribeTasks(req, cb) {
        return this.request("DescribeTasks", req, cb);
    }
    async CreateStorageRegion(req, cb) {
        return this.request("CreateStorageRegion", req, cb);
    }
    async DeleteCLSTopic(req, cb) {
        return this.request("DeleteCLSTopic", req, cb);
    }
    async ResetProcedureTemplate(req, cb) {
        return this.request("ResetProcedureTemplate", req, cb);
    }
    async DescribeCDNUsageData(req, cb) {
        return this.request("DescribeCDNUsageData", req, cb);
    }
    async CreateTranscodeTemplate(req, cb) {
        return this.request("CreateTranscodeTemplate", req, cb);
    }
    async DescribeCDNStatDetails(req, cb) {
        return this.request("DescribeCDNStatDetails", req, cb);
    }
    async DescribeMediaPlayStatDetails(req, cb) {
        return this.request("DescribeMediaPlayStatDetails", req, cb);
    }
    async ModifyReviewTemplate(req, cb) {
        return this.request("ModifyReviewTemplate", req, cb);
    }
    async DescribeLicenseUsageData(req, cb) {
        return this.request("DescribeLicenseUsageData", req, cb);
    }
    async ModifyImageSpriteTemplate(req, cb) {
        return this.request("ModifyImageSpriteTemplate", req, cb);
    }
    async DeleteClass(req, cb) {
        return this.request("DeleteClass", req, cb);
    }
    async ExecuteFunction(req, cb) {
        return this.request("ExecuteFunction", req, cb);
    }
    async DeleteReviewTemplate(req, cb) {
        return this.request("DeleteReviewTemplate", req, cb);
    }
    async DescribeMediaProcessUsageData(req, cb) {
        return this.request("DescribeMediaProcessUsageData", req, cb);
    }
    async DescribeTranscodeTemplates(req, cb) {
        return this.request("DescribeTranscodeTemplates", req, cb);
    }
    async InspectMediaQuality(req, cb) {
        return this.request("InspectMediaQuality", req, cb);
    }
    async DescribePrepaidProducts(req, cb) {
        return this.request("DescribePrepaidProducts", req, cb);
    }
    async CreateDomainVerifyRecord(req, cb) {
        return this.request("CreateDomainVerifyRecord", req, cb);
    }
    async DescribeSnapshotByTimeOffsetTemplates(req, cb) {
        return this.request("DescribeSnapshotByTimeOffsetTemplates", req, cb);
    }
    async ComposeMedia(req, cb) {
        return this.request("ComposeMedia", req, cb);
    }
    async CreateContentReviewTemplate(req, cb) {
        return this.request("CreateContentReviewTemplate", req, cb);
    }
    async DescribeMPSTemplates(req, cb) {
        return this.request("DescribeMPSTemplates", req, cb);
    }
    async RebuildMedia(req, cb) {
        return this.request("RebuildMedia", req, cb);
    }
    async CreateSampleSnapshotTemplate(req, cb) {
        return this.request("CreateSampleSnapshotTemplate", req, cb);
    }
    async DeleteAIAnalysisTemplate(req, cb) {
        return this.request("DeleteAIAnalysisTemplate", req, cb);
    }
    async DescribeDrmKeyProviderInfo(req, cb) {
        return this.request("DescribeDrmKeyProviderInfo", req, cb);
    }
    async DeleteRebuildMediaTemplate(req, cb) {
        return this.request("DeleteRebuildMediaTemplate", req, cb);
    }
    async LiveRealTimeClip(req, cb) {
        return this.request("LiveRealTimeClip", req, cb);
    }
    async ModifyMediaStorageClass(req, cb) {
        return this.request("ModifyMediaStorageClass", req, cb);
    }
    async PullUpload(req, cb) {
        return this.request("PullUpload", req, cb);
    }
    async ModifySampleSnapshotTemplate(req, cb) {
        return this.request("ModifySampleSnapshotTemplate", req, cb);
    }
    async ModifyVodDomainAccelerateConfig(req, cb) {
        return this.request("ModifyVodDomainAccelerateConfig", req, cb);
    }
    async DeleteSuperPlayerConfig(req, cb) {
        return this.request("DeleteSuperPlayerConfig", req, cb);
    }
    async ReviewAudioVideo(req, cb) {
        return this.request("ReviewAudioVideo", req, cb);
    }
    async DescribeProcedureTemplates(req, cb) {
        return this.request("DescribeProcedureTemplates", req, cb);
    }
    async DescribeClientUploadAccelerationUsageData(req, cb) {
        return this.request("DescribeClientUploadAccelerationUsageData", req, cb);
    }
    async ParseStreamingManifest(req, cb) {
        return this.request("ParseStreamingManifest", req, cb);
    }
    async ProcessImage(req, cb) {
        return this.request("ProcessImage", req, cb);
    }
    async EnhanceMediaQuality(req, cb) {
        return this.request("EnhanceMediaQuality", req, cb);
    }
    async CreateProcedureTemplate(req, cb) {
        return this.request("CreateProcedureTemplate", req, cb);
    }
    async PushUrlCache(req, cb) {
        return this.request("PushUrlCache", req, cb);
    }
    async DeleteMedia(req, cb) {
        return this.request("DeleteMedia", req, cb);
    }
    async HandleCurrentPlaylist(req, cb) {
        return this.request("HandleCurrentPlaylist", req, cb);
    }
    async CreateSuperPlayerConfig(req, cb) {
        return this.request("CreateSuperPlayerConfig", req, cb);
    }
    async DescribeCLSPushTargets(req, cb) {
        return this.request("DescribeCLSPushTargets", req, cb);
    }
    async EnhanceMediaByTemplate(req, cb) {
        return this.request("EnhanceMediaByTemplate", req, cb);
    }
    async ModifyPersonSample(req, cb) {
        return this.request("ModifyPersonSample", req, cb);
    }
    async CreateEnhanceMediaTemplate(req, cb) {
        return this.request("CreateEnhanceMediaTemplate", req, cb);
    }
    async CreateRebuildMediaTemplate(req, cb) {
        return this.request("CreateRebuildMediaTemplate", req, cb);
    }
    async DescribeCurrentPlaylist(req, cb) {
        return this.request("DescribeCurrentPlaylist", req, cb);
    }
    async DescribeDailyMediaPlayStat(req, cb) {
        return this.request("DescribeDailyMediaPlayStat", req, cb);
    }
    async DeleteAnimatedGraphicsTemplate(req, cb) {
        return this.request("DeleteAnimatedGraphicsTemplate", req, cb);
    }
    async DescribeCLSTopics(req, cb) {
        return this.request("DescribeCLSTopics", req, cb);
    }
    async CreateCLSTopic(req, cb) {
        return this.request("CreateCLSTopic", req, cb);
    }
    async DeleteContentReviewTemplate(req, cb) {
        return this.request("DeleteContentReviewTemplate", req, cb);
    }
    async CreateAIAnalysisTemplate(req, cb) {
        return this.request("CreateAIAnalysisTemplate", req, cb);
    }
    async DescribeDailyMostPlayedStat(req, cb) {
        return this.request("DescribeDailyMostPlayedStat", req, cb);
    }
    async ExtractCopyRightWatermark(req, cb) {
        return this.request("ExtractCopyRightWatermark", req, cb);
    }
    async DescribeStorageRegions(req, cb) {
        return this.request("DescribeStorageRegions", req, cb);
    }
    async RefreshUrlCache(req, cb) {
        return this.request("RefreshUrlCache", req, cb);
    }
    async DescribeEventsState(req, cb) {
        return this.request("DescribeEventsState", req, cb);
    }
    async ModifyQualityInspectTemplate(req, cb) {
        return this.request("ModifyQualityInspectTemplate", req, cb);
    }
    async VerifyDomainRecord(req, cb) {
        return this.request("VerifyDomainRecord", req, cb);
    }
    async DescribeImageReviewUsageData(req, cb) {
        return this.request("DescribeImageReviewUsageData", req, cb);
    }
    async ProcessMediaByMPS(req, cb) {
        return this.request("ProcessMediaByMPS", req, cb);
    }
    async ModifyVodDomainConfig(req, cb) {
        return this.request("ModifyVodDomainConfig", req, cb);
    }
    async ConfirmEvents(req, cb) {
        return this.request("ConfirmEvents", req, cb);
    }
    async DescribeRoundPlays(req, cb) {
        return this.request("DescribeRoundPlays", req, cb);
    }
    async ProcessMediaByUrl(req, cb) {
        return this.request("ProcessMediaByUrl", req, cb);
    }
    async CreateComplexAdaptiveDynamicStreamingTask(req, cb) {
        return this.request("CreateComplexAdaptiveDynamicStreamingTask", req, cb);
    }
    async ModifyTranscodeTemplate(req, cb) {
        return this.request("ModifyTranscodeTemplate", req, cb);
    }
    async DescribeContentReviewTemplates(req, cb) {
        return this.request("DescribeContentReviewTemplates", req, cb);
    }
    async ModifyHeadTailTemplate(req, cb) {
        return this.request("ModifyHeadTailTemplate", req, cb);
    }
    async DeleteImageProcessingTemplate(req, cb) {
        return this.request("DeleteImageProcessingTemplate", req, cb);
    }
    async ProcessMedia(req, cb) {
        return this.request("ProcessMedia", req, cb);
    }
    async CreateHeadTailTemplate(req, cb) {
        return this.request("CreateHeadTailTemplate", req, cb);
    }
    async ModifyWatermarkTemplate(req, cb) {
        return this.request("ModifyWatermarkTemplate", req, cb);
    }
    async DescribeStorageDetails(req, cb) {
        return this.request("DescribeStorageDetails", req, cb);
    }
    async DeleteWordSamples(req, cb) {
        return this.request("DeleteWordSamples", req, cb);
    }
    async CreateMPSTemplate(req, cb) {
        return this.request("CreateMPSTemplate", req, cb);
    }
    async FastEditMedia(req, cb) {
        return this.request("FastEditMedia", req, cb);
    }
    async CreateVodDomain(req, cb) {
        return this.request("CreateVodDomain", req, cb);
    }
    async CreateImageSpriteTemplate(req, cb) {
        return this.request("CreateImageSpriteTemplate", req, cb);
    }
    async DescribePersonSamples(req, cb) {
        return this.request("DescribePersonSamples", req, cb);
    }
    async DeleteAIRecognitionTemplate(req, cb) {
        return this.request("DeleteAIRecognitionTemplate", req, cb);
    }
    async CreateSubAppId(req, cb) {
        return this.request("CreateSubAppId", req, cb);
    }
    async DescribeAnimatedGraphicsTemplates(req, cb) {
        return this.request("DescribeAnimatedGraphicsTemplates", req, cb);
    }
    async ForbidMediaDistribution(req, cb) {
        return this.request("ForbidMediaDistribution", req, cb);
    }
    async ManageTask(req, cb) {
        return this.request("ManageTask", req, cb);
    }
    async ModifyAIAnalysisTemplate(req, cb) {
        return this.request("ModifyAIAnalysisTemplate", req, cb);
    }
    async ModifySnapshotByTimeOffsetTemplate(req, cb) {
        return this.request("ModifySnapshotByTimeOffsetTemplate", req, cb);
    }
    async ModifySuperPlayerConfig(req, cb) {
        return this.request("ModifySuperPlayerConfig", req, cb);
    }
    async CreateClass(req, cb) {
        return this.request("CreateClass", req, cb);
    }
    async CreateWordSamples(req, cb) {
        return this.request("CreateWordSamples", req, cb);
    }
    async DescribeEventConfig(req, cb) {
        return this.request("DescribeEventConfig", req, cb);
    }
    async ModifySubAppIdInfo(req, cb) {
        return this.request("ModifySubAppIdInfo", req, cb);
    }
    async ModifyRoundPlay(req, cb) {
        return this.request("ModifyRoundPlay", req, cb);
    }
    async DescribeAdaptiveDynamicStreamingTemplates(req, cb) {
        return this.request("DescribeAdaptiveDynamicStreamingTemplates", req, cb);
    }
    async DescribeSampleSnapshotTemplates(req, cb) {
        return this.request("DescribeSampleSnapshotTemplates", req, cb);
    }
    async ModifyWordSample(req, cb) {
        return this.request("ModifyWordSample", req, cb);
    }
    async ModifyEnhanceMediaTemplate(req, cb) {
        return this.request("ModifyEnhanceMediaTemplate", req, cb);
    }
    async DescribeImageSpriteTemplates(req, cb) {
        return this.request("DescribeImageSpriteTemplates", req, cb);
    }
    async DescribeAllClass(req, cb) {
        return this.request("DescribeAllClass", req, cb);
    }
    async DescribeWatermarkTemplates(req, cb) {
        return this.request("DescribeWatermarkTemplates", req, cb);
    }
    async CreateWatermarkTemplate(req, cb) {
        return this.request("CreateWatermarkTemplate", req, cb);
    }
    async SetDrmKeyProviderInfo(req, cb) {
        return this.request("SetDrmKeyProviderInfo", req, cb);
    }
    async DescribeJustInTimeTranscodeTemplates(req, cb) {
        return this.request("DescribeJustInTimeTranscodeTemplates", req, cb);
    }
    async CreateReviewTemplate(req, cb) {
        return this.request("CreateReviewTemplate", req, cb);
    }
    async DescribeAIRecognitionTemplates(req, cb) {
        return this.request("DescribeAIRecognitionTemplates", req, cb);
    }
    async DeleteQualityInspectTemplate(req, cb) {
        return this.request("DeleteQualityInspectTemplate", req, cb);
    }
    async DescribeQualityInspectTemplates(req, cb) {
        return this.request("DescribeQualityInspectTemplates", req, cb);
    }
    async CreateJustInTimeTranscodeTemplate(req, cb) {
        return this.request("CreateJustInTimeTranscodeTemplate", req, cb);
    }
    async DescribeSuperPlayerConfigs(req, cb) {
        return this.request("DescribeSuperPlayerConfigs", req, cb);
    }
    async DescribeSubAppIds(req, cb) {
        return this.request("DescribeSubAppIds", req, cb);
    }
    async DescribeHeadTailTemplates(req, cb) {
        return this.request("DescribeHeadTailTemplates", req, cb);
    }
    async DescribeDefaultDistributionConfig(req, cb) {
        return this.request("DescribeDefaultDistributionConfig", req, cb);
    }
    async DeleteMPSTemplate(req, cb) {
        return this.request("DeleteMPSTemplate", req, cb);
    }
    async DescribeReviewTemplates(req, cb) {
        return this.request("DescribeReviewTemplates", req, cb);
    }
    async CommitUpload(req, cb) {
        return this.request("CommitUpload", req, cb);
    }
    async ModifyAIRecognitionTemplate(req, cb) {
        return this.request("ModifyAIRecognitionTemplate", req, cb);
    }
    async ModifyAdaptiveDynamicStreamingTemplate(req, cb) {
        return this.request("ModifyAdaptiveDynamicStreamingTemplate", req, cb);
    }
    async SearchMedia(req, cb) {
        return this.request("SearchMedia", req, cb);
    }
    async DeleteWatermarkTemplate(req, cb) {
        return this.request("DeleteWatermarkTemplate", req, cb);
    }
    async DeleteJustInTimeTranscodeTemplate(req, cb) {
        return this.request("DeleteJustInTimeTranscodeTemplate", req, cb);
    }
    async DeletePersonSample(req, cb) {
        return this.request("DeletePersonSample", req, cb);
    }
    async DescribeEnhanceMediaTemplates(req, cb) {
        return this.request("DescribeEnhanceMediaTemplates", req, cb);
    }
    async CreateAnimatedGraphicsTemplate(req, cb) {
        return this.request("CreateAnimatedGraphicsTemplate", req, cb);
    }
    async ModifyAnimatedGraphicsTemplate(req, cb) {
        return this.request("ModifyAnimatedGraphicsTemplate", req, cb);
    }
    async ModifyMPSTemplate(req, cb) {
        return this.request("ModifyMPSTemplate", req, cb);
    }
    async AttachMediaSubtitles(req, cb) {
        return this.request("AttachMediaSubtitles", req, cb);
    }
    async ModifyEventConfig(req, cb) {
        return this.request("ModifyEventConfig", req, cb);
    }
    async ModifyMediaInfo(req, cb) {
        return this.request("ModifyMediaInfo", req, cb);
    }
    async ReviewImage(req, cb) {
        return this.request("ReviewImage", req, cb);
    }
    async DeleteSampleSnapshotTemplate(req, cb) {
        return this.request("DeleteSampleSnapshotTemplate", req, cb);
    }
    async WeChatMiniProgramPublish(req, cb) {
        return this.request("WeChatMiniProgramPublish", req, cb);
    }
    async SimpleHlsClip(req, cb) {
        return this.request("SimpleHlsClip", req, cb);
    }
    async SetVodDomainCertificate(req, cb) {
        return this.request("SetVodDomainCertificate", req, cb);
    }
    async DescribeMediaInfos(req, cb) {
        return this.request("DescribeMediaInfos", req, cb);
    }
    async RebuildMediaByTemplate(req, cb) {
        return this.request("RebuildMediaByTemplate", req, cb);
    }
    async DeleteHeadTailTemplate(req, cb) {
        return this.request("DeleteHeadTailTemplate", req, cb);
    }
    async CreatePersonSample(req, cb) {
        return this.request("CreatePersonSample", req, cb);
    }
    async ModifySubAppIdStatus(req, cb) {
        return this.request("ModifySubAppIdStatus", req, cb);
    }
    async DeleteRoundPlay(req, cb) {
        return this.request("DeleteRoundPlay", req, cb);
    }
    async ModifyClass(req, cb) {
        return this.request("ModifyClass", req, cb);
    }
    async ModifyContentReviewTemplate(req, cb) {
        return this.request("ModifyContentReviewTemplate", req, cb);
    }
    async DeleteVodDomain(req, cb) {
        return this.request("DeleteVodDomain", req, cb);
    }
    async CreateAIRecognitionTemplate(req, cb) {
        return this.request("CreateAIRecognitionTemplate", req, cb);
    }
    async DeleteEnhanceMediaTemplate(req, cb) {
        return this.request("DeleteEnhanceMediaTemplate", req, cb);
    }
    async DescribeRebuildMediaTemplates(req, cb) {
        return this.request("DescribeRebuildMediaTemplates", req, cb);
    }
    async DescribeDailyPlayStatFileList(req, cb) {
        return this.request("DescribeDailyPlayStatFileList", req, cb);
    }
    async DeleteImageSpriteTemplate(req, cb) {
        return this.request("DeleteImageSpriteTemplate", req, cb);
    }
    async RestoreMedia(req, cb) {
        return this.request("RestoreMedia", req, cb);
    }
}
