import{_ as a,r as e}from"./index-bcbc0702.js";/* empty css                    *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                  *//* empty css               *//* empty css                *//* empty css               *//* empty css                        *//* empty css                       *//* empty css                 */import{r as s,d as t,A as l,B as i,C as c,F as d,G as r,D as n,u as o,I as u,H as p,V as v,ae as _,af as f,E as m,K as g,L as h,b3 as b,Y as y,a$ as w,Z as j,X as x,b7 as k,b5 as C,b8 as O,bu as Y,bv as D,bd as z,O as I,a3 as U,a1 as V,a5 as L,b1 as S,aU as R}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const T={class:"reports"},q={class:"card-header"},M={class:"header-actions"},E={class:"time-range-selector"},B={class:"stats-overview"},F={class:"stat-content"},H={class:"stat-icon total-registrations"},$={class:"stat-info"},A={class:"stat-number"},G={class:"stat-content"},K={class:"stat-icon approved-users"},N={class:"stat-info"},X={class:"stat-number"},Z={class:"stat-content"},J={class:"stat-icon pending-users"},P={class:"stat-info"},Q={class:"stat-number"},W={class:"stat-change"},aa={class:"stat-content"},ea={class:"stat-icon active-users"},sa={class:"stat-info"},ta={class:"stat-number"},la={class:"charts-section"},ia={class:"chart-container"},ca={class:"chart-placeholder"},da={class:"mock-chart-data"},ra={class:"date"},na={class:"count"},oa={class:"chart-container"},ua={class:"chart-placeholder"},pa={class:"distribution-data"},va={class:"percentage"},_a={class:"count"},fa={class:"chart-container"},ma={class:"efficiency-stats"},ga={class:"efficiency-item"},ha={class:"efficiency-value"},ba={class:"efficiency-item"},ya={class:"efficiency-value"},wa={class:"efficiency-item"},ja={class:"efficiency-value"},xa={class:"efficiency-item"},ka={class:"efficiency-value"},Ca={class:"chart-container"},Oa={class:"usage-stats"},Ya={class:"usage-item"},Da={class:"usage-value"},za={class:"usage-item"},Ia={class:"usage-value"},Ua={class:"usage-item"},Va={class:"usage-value"},La={class:"usage-item"},Sa={class:"usage-value"},Ra={class:"detailed-data"},Ta=a({__name:"Reports",setup(a){const Ta=s([]),qa=s("30d"),Ma=s("daily"),Ea=t({total_registrations:0,registration_change:0,approved_users:0,approval_rate:0,pending_users:0,avg_processing_time:0,daily_active:0,activity_change:0}),Ba=s([]),Fa=s([]),Ha=t({avg_time:0,min_time:0,max_time:0,approval_rate:0}),$a=t({total_visits:0,unique_visitors:0,avg_response_time:0,uptime:0}),Aa=s([]),Ga=s([]),Ka=async()=>{try{const a={};Ta.value&&2===Ta.value.length&&(a.start_date=Ta.value[0],a.end_date=Ta.value[1]);const s=await e.get("/api/statistics/reports",{params:a});if(s.success){const a=s.data;Object.assign(Ea,a.overview||{}),Ba.value=a.registration_trend||[],Fa.value=a.user_distribution||[],Object.assign(Ha,a.audit_stats||{}),Object.assign($a,a.system_usage||{}),Aa.value=a.daily_stats||[],Ga.value=a.school_stats||[]}}catch(a){m.error("加载统计报告失败")}},Na=a=>{qa.value=a;const e=new Date,s=parseInt(a.replace("d","")),t=new Date(e.getTime()-24*s*60*60*1e3);Ta.value=[t.toISOString().split("T")[0],e.toISOString().split("T")[0]],Ka()},Xa=a=>({buaa:"本校学生",freshman:"新生",external:"外校学生",invite:"邀请码"}[a]||a),Za=async()=>{try{const a={};Ta.value&&2===Ta.value.length&&(a.start_date=Ta.value[0],a.end_date=Ta.value[1]);const s=await e.get("/api/statistics/reports/export",{params:a,responseType:"blob"}),t=new Blob([s],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),l=window.URL.createObjectURL(t),i=document.createElement("a");i.href=l,i.download=`统计报告_${(new Date).toISOString().split("T")[0]}.xlsx`,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(l),m.success("报告导出成功")}catch(a){m.error("导出报告失败")}};return l(()=>{Na("30d")}),(a,e)=>{const s=g,t=h,l=b,m=y,Ja=w,Pa=j,Qa=x,Wa=k,ae=C,ee=O,se=Y,te=D;return i(),c("div",T,[d(Qa,null,{header:r(()=>[n("div",q,[e[7]||(e[7]=n("span",null,"统计报告",-1)),n("div",M,[d(t,{type:"primary",onClick:Za},{default:r(()=>[d(s,null,{default:r(()=>[d(o(z))]),_:1}),e[5]||(e[5]=u(" 导出报告 ",-1))]),_:1,__:[5]}),d(t,{onClick:Ka},{default:r(()=>[d(s,null,{default:r(()=>[d(o(I))]),_:1}),e[6]||(e[6]=u(" 刷新数据 ",-1))]),_:1,__:[6]})])])]),default:r(()=>[n("div",E,[d(Pa,{gutter:20},{default:r(()=>[d(m,{span:8},{default:r(()=>[d(l,{modelValue:Ta.value,"onUpdate:modelValue":e[0]||(e[0]=a=>Ta.value=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:Ka},null,8,["modelValue"])]),_:1}),d(m,{span:4},{default:r(()=>[d(Ja,null,{default:r(()=>[d(t,{type:"7d"===qa.value?"primary":"",onClick:e[1]||(e[1]=a=>Na("7d"))},{default:r(()=>e[8]||(e[8]=[u(" 最近7天 ",-1)])),_:1,__:[8]},8,["type"]),d(t,{type:"30d"===qa.value?"primary":"",onClick:e[2]||(e[2]=a=>Na("30d"))},{default:r(()=>e[9]||(e[9]=[u(" 最近30天 ",-1)])),_:1,__:[9]},8,["type"]),d(t,{type:"90d"===qa.value?"primary":"",onClick:e[3]||(e[3]=a=>Na("90d"))},{default:r(()=>e[10]||(e[10]=[u(" 最近90天 ",-1)])),_:1,__:[10]},8,["type"])]),_:1})]),_:1})]),_:1})]),n("div",B,[d(Pa,{gutter:20},{default:r(()=>[d(m,{span:6},{default:r(()=>[d(Qa,{class:"stat-card"},{default:r(()=>[n("div",F,[n("div",H,[d(s,{size:"32"},{default:r(()=>[d(o(U))]),_:1})]),n("div",$,[n("div",A,p(Ea.total_registrations),1),e[11]||(e[11]=n("div",{class:"stat-label"},"总注册数",-1)),n("div",{class:v(["stat-change",Ea.registration_change>=0?"positive":"negative"])},p(Ea.registration_change>=0?"+":"")+p(Ea.registration_change)+"% ",3)])])]),_:1})]),_:1}),d(m,{span:6},{default:r(()=>[d(Qa,{class:"stat-card"},{default:r(()=>[n("div",G,[n("div",K,[d(s,{size:"32"},{default:r(()=>[d(o(V))]),_:1})]),n("div",N,[n("div",X,p(Ea.approved_users),1),e[12]||(e[12]=n("div",{class:"stat-label"},"审核通过",-1)),n("div",{class:v(["stat-change",Ea.approval_rate>=80?"positive":"negative"])}," 通过率 "+p(Ea.approval_rate)+"% ",3)])])]),_:1})]),_:1}),d(m,{span:6},{default:r(()=>[d(Qa,{class:"stat-card"},{default:r(()=>[n("div",Z,[n("div",J,[d(s,{size:"32"},{default:r(()=>[d(o(L))]),_:1})]),n("div",P,[n("div",Q,p(Ea.pending_users),1),e[13]||(e[13]=n("div",{class:"stat-label"},"待审核",-1)),n("div",W," 平均处理时间 "+p(Ea.avg_processing_time)+"h ",1)])])]),_:1})]),_:1}),d(m,{span:6},{default:r(()=>[d(Qa,{class:"stat-card"},{default:r(()=>[n("div",aa,[n("div",ea,[d(s,{size:"32"},{default:r(()=>[d(o(S))]),_:1})]),n("div",sa,[n("div",ta,p(Ea.daily_active),1),e[14]||(e[14]=n("div",{class:"stat-label"},"日活跃用户",-1)),n("div",{class:v(["stat-change",Ea.activity_change>=0?"positive":"negative"])},p(Ea.activity_change>=0?"+":"")+p(Ea.activity_change)+"% ",3)])])]),_:1})]),_:1})]),_:1})]),n("div",la,[d(Pa,{gutter:20},{default:r(()=>[d(m,{span:12},{default:r(()=>[d(Qa,{class:"chart-card"},{header:r(()=>e[15]||(e[15]=[n("span",null,"注册趋势",-1)])),default:r(()=>[n("div",ia,[n("div",ca,[d(s,{size:"64",color:"#ddd"},{default:r(()=>[d(o(S))]),_:1}),e[16]||(e[16]=n("p",null,"注册趋势图表",-1)),n("div",da,[(i(!0),c(_,null,f(Ba.value,(a,e)=>(i(),c("div",{key:e,class:"trend-item"},[n("span",ra,p(a.date),1),n("span",na,p(a.count)+"人",1)]))),128))])])])]),_:1})]),_:1}),d(m,{span:12},{default:r(()=>[d(Qa,{class:"chart-card"},{header:r(()=>e[17]||(e[17]=[n("span",null,"用户类型分布",-1)])),default:r(()=>[n("div",oa,[n("div",ua,[d(s,{size:"64",color:"#ddd"},{default:r(()=>[d(o(R))]),_:1}),e[18]||(e[18]=n("p",null,"用户类型分布图",-1)),n("div",pa,[(i(!0),c(_,null,f(Fa.value,(a,e)=>{return i(),c("div",{key:e,class:"distribution-item"},[d(Wa,{type:(s=a.category,{buaa:"primary",freshman:"success",external:"warning",invite:"info"}[s]||""),size:"small"},{default:r(()=>[u(p(Xa(a.category)),1)]),_:2},1032,["type"]),n("span",va,p(a.percentage)+"%",1),n("span",_a,"("+p(a.count)+"人)",1)]);var s}),128))])])])]),_:1})]),_:1})]),_:1}),d(Pa,{gutter:20,style:{"margin-top":"20px"}},{default:r(()=>[d(m,{span:12},{default:r(()=>[d(Qa,{class:"chart-card"},{header:r(()=>e[19]||(e[19]=[n("span",null,"审核效率统计",-1)])),default:r(()=>[n("div",fa,[n("div",ma,[n("div",ga,[e[20]||(e[20]=n("div",{class:"efficiency-label"},"平均审核时间",-1)),n("div",ha,p(Ha.avg_time)+"小时",1)]),n("div",ba,[e[21]||(e[21]=n("div",{class:"efficiency-label"},"最快审核时间",-1)),n("div",ya,p(Ha.min_time)+"分钟",1)]),n("div",wa,[e[22]||(e[22]=n("div",{class:"efficiency-label"},"最慢审核时间",-1)),n("div",ja,p(Ha.max_time)+"小时",1)]),n("div",xa,[e[23]||(e[23]=n("div",{class:"efficiency-label"},"审核通过率",-1)),n("div",ka,p(Ha.approval_rate)+"%",1)])])])]),_:1})]),_:1}),d(m,{span:12},{default:r(()=>[d(Qa,{class:"chart-card"},{header:r(()=>e[24]||(e[24]=[n("span",null,"系统使用情况",-1)])),default:r(()=>[n("div",Ca,[n("div",Oa,[n("div",Ya,[e[25]||(e[25]=n("div",{class:"usage-label"},"总访问量",-1)),n("div",Da,p($a.total_visits),1)]),n("div",za,[e[26]||(e[26]=n("div",{class:"usage-label"},"独立访客",-1)),n("div",Ia,p($a.unique_visitors),1)]),n("div",Ua,[e[27]||(e[27]=n("div",{class:"usage-label"},"平均响应时间",-1)),n("div",Va,p($a.avg_response_time)+"ms",1)]),n("div",La,[e[28]||(e[28]=n("div",{class:"usage-label"},"系统可用率",-1)),n("div",Sa,p($a.uptime)+"%",1)])])])]),_:1})]),_:1})]),_:1})]),n("div",Ra,[d(Qa,null,{header:r(()=>e[29]||(e[29]=[n("span",null,"详细数据",-1)])),default:r(()=>[d(te,{modelValue:Ma.value,"onUpdate:modelValue":e[4]||(e[4]=a=>Ma.value=a)},{default:r(()=>[d(se,{label:"每日统计",name:"daily"},{default:r(()=>[d(ee,{data:Aa.value,stripe:""},{default:r(()=>[d(ae,{prop:"date",label:"日期",width:"120"}),d(ae,{prop:"registrations",label:"新注册",width:"100"}),d(ae,{prop:"approvals",label:"审核通过",width:"100"}),d(ae,{prop:"rejections",label:"审核拒绝",width:"100"}),d(ae,{prop:"visits",label:"访问量",width:"100"}),d(ae,{prop:"unique_visitors",label:"独立访客",width:"100"})]),_:1},8,["data"])]),_:1}),d(se,{label:"学校统计",name:"school"},{default:r(()=>[d(ee,{data:Ga.value,stripe:""},{default:r(()=>[d(ae,{prop:"school",label:"学校",width:"200"}),d(ae,{prop:"total_users",label:"总用户数",width:"100"}),d(ae,{prop:"buaa_users",label:"本校学生",width:"100"}),d(ae,{prop:"freshman_users",label:"新生",width:"100"}),d(ae,{prop:"external_users",label:"外校学生",width:"100"}),d(ae,{prop:"approval_rate",label:"通过率",width:"100"},{default:r(({row:a})=>[u(p(a.approval_rate)+"% ",1)]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["modelValue"])]),_:1})])]),_:1})])}}},[["__scopeId","data-v-1625a65f"]]);export{Ta as default};
