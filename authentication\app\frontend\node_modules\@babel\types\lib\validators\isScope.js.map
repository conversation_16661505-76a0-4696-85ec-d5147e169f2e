{"version": 3, "names": ["_index", "require", "isScope", "node", "parent", "isBlockStatement", "isFunction", "isCatchClause", "isPattern", "isScopable"], "sources": ["../../src/validators/isScope.ts"], "sourcesContent": ["import {\n  isFunction,\n  isCatchClause,\n  isBlockStatement,\n  isScopable,\n  isPattern,\n} from \"./generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Check if the input `node` is a scope.\n */\nexport default function isScope(node: t.Node, parent: t.Node): boolean {\n  // If a BlockStatement is an immediate descendent of a Function/CatchClause, it must be in the body.\n  // Hence we skipped the parentKey === \"params\" check\n  if (isBlockStatement(node) && (isFunction(parent) || isCatchClause(parent))) {\n    return false;\n  }\n\n  // If a Pattern is an immediate descendent of a Function/CatchClause, it must be in the params.\n  // Hence we skipped the parentKey === \"params\" check\n  if (isPattern(node) && (isFunction(parent) || isCatchClause(parent))) {\n    return true;\n  }\n\n  return isScopable(node);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAYe,SAASC,OAAOA,CAACC,IAAY,EAAEC,MAAc,EAAW;EAGrE,IAAI,IAAAC,uBAAgB,EAACF,IAAI,CAAC,KAAK,IAAAG,iBAAU,EAACF,MAAM,CAAC,IAAI,IAAAG,oBAAa,EAACH,MAAM,CAAC,CAAC,EAAE;IAC3E,OAAO,KAAK;EACd;EAIA,IAAI,IAAAI,gBAAS,EAACL,IAAI,CAAC,KAAK,IAAAG,iBAAU,EAACF,MAAM,CAAC,IAAI,IAAAG,oBAAa,EAACH,MAAM,CAAC,CAAC,EAAE;IACpE,OAAO,IAAI;EACb;EAEA,OAAO,IAAAK,iBAAU,EAACN,IAAI,CAAC;AACzB", "ignoreList": []}