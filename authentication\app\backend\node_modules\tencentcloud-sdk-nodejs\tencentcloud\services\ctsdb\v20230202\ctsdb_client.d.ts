import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { DescribeClustersRequest, DescribeDatabasesResponse, DescribeDatabasesRequest, DescribeClustersResponse } from "./ctsdb_models";
/**
 * ctsdb client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 查询实例列表及详情
     */
    DescribeClusters(req: DescribeClustersRequest, cb?: (error: string, rep: DescribeClustersResponse) => void): Promise<DescribeClustersResponse>;
    /**
     * 查看数据库/列表

```
  "Database":{
    "ClusterID":"ctsdbi-rebg0ghl",
    "Name":"" //不指定则查询实例下所有db
  }
```
     */
    DescribeDatabases(req: DescribeDatabasesRequest, cb?: (error: string, rep: DescribeDatabasesResponse) => void): Promise<DescribeDatabasesResponse>;
}
