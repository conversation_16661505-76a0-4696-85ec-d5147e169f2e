import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("tse.tencentcloudapi.com", "2020-12-07", clientConfig);
    }
    async DescribeOneCloudNativeAPIGatewayService(req, cb) {
        return this.request("DescribeOneCloudNativeAPIGatewayService", req, cb);
    }
    async UpdateEngineInternetAccess(req, cb) {
        return this.request("UpdateEngineInternetAccess", req, cb);
    }
    async DescribeCloudNativeAPIGatewayServiceRateLimit(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayServiceRateLimit", req, cb);
    }
    async ModifyCloudNativeAPIGatewayCertificate(req, cb) {
        return this.request("ModifyCloudNativeAPIGatewayCertificate", req, cb);
    }
    async DeleteCloudNativeAPIGatewayPublicNetwork(req, cb) {
        return this.request("DeleteCloudNativeAPIGatewayPublicNetwork", req, cb);
    }
    async DescribeGovernanceAliases(req, cb) {
        return this.request("DescribeGovernanceAliases", req, cb);
    }
    async DeleteConfigFileGroup(req, cb) {
        return this.request("DeleteConfigFileGroup", req, cb);
    }
    async CreateConfigFileGroup(req, cb) {
        return this.request("CreateConfigFileGroup", req, cb);
    }
    async DeleteEngine(req, cb) {
        return this.request("DeleteEngine", req, cb);
    }
    async ModifyGovernanceServices(req, cb) {
        return this.request("ModifyGovernanceServices", req, cb);
    }
    async DescribeCloudNativeAPIGateways(req, cb) {
        return this.request("DescribeCloudNativeAPIGateways", req, cb);
    }
    async CreateCloudNativeAPIGatewayCertificate(req, cb) {
        return this.request("CreateCloudNativeAPIGatewayCertificate", req, cb);
    }
    async DescribeCloudNativeAPIGatewayServices(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayServices", req, cb);
    }
    async DescribeCloudNativeAPIGatewayServicesLight(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayServicesLight", req, cb);
    }
    async DeleteGovernanceAliases(req, cb) {
        return this.request("DeleteGovernanceAliases", req, cb);
    }
    async DescribeZookeeperReplicas(req, cb) {
        return this.request("DescribeZookeeperReplicas", req, cb);
    }
    async DeleteGovernanceInstances(req, cb) {
        return this.request("DeleteGovernanceInstances", req, cb);
    }
    async CreateCloudNativeAPIGatewayCanaryRule(req, cb) {
        return this.request("CreateCloudNativeAPIGatewayCanaryRule", req, cb);
    }
    async DeleteNativeGatewayServiceSource(req, cb) {
        return this.request("DeleteNativeGatewayServiceSource", req, cb);
    }
    async DescribeCloudNativeAPIGatewayCertificateDetails(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayCertificateDetails", req, cb);
    }
    async DescribeAutoScalerResourceStrategies(req, cb) {
        return this.request("DescribeAutoScalerResourceStrategies", req, cb);
    }
    async ModifyNativeGatewayServiceSource(req, cb) {
        return this.request("ModifyNativeGatewayServiceSource", req, cb);
    }
    async ModifyNetworkBasicInfo(req, cb) {
        return this.request("ModifyNetworkBasicInfo", req, cb);
    }
    async DeleteCloudNativeAPIGatewayCertificate(req, cb) {
        return this.request("DeleteCloudNativeAPIGatewayCertificate", req, cb);
    }
    async DescribeGovernanceServiceContracts(req, cb) {
        return this.request("DescribeGovernanceServiceContracts", req, cb);
    }
    async UpdateCloudNativeAPIGatewayCertificateInfo(req, cb) {
        return this.request("UpdateCloudNativeAPIGatewayCertificateInfo", req, cb);
    }
    async DescribeAllConfigFileTemplates(req, cb) {
        return this.request("DescribeAllConfigFileTemplates", req, cb);
    }
    async CreateNativeGatewayServerGroup(req, cb) {
        return this.request("CreateNativeGatewayServerGroup", req, cb);
    }
    async DeleteCloudNativeAPIGatewayServiceRateLimit(req, cb) {
        return this.request("DeleteCloudNativeAPIGatewayServiceRateLimit", req, cb);
    }
    async DescribeZookeeperServerInterfaces(req, cb) {
        return this.request("DescribeZookeeperServerInterfaces", req, cb);
    }
    async DescribeWafDomains(req, cb) {
        return this.request("DescribeWafDomains", req, cb);
    }
    async DeleteConfigFiles(req, cb) {
        return this.request("DeleteConfigFiles", req, cb);
    }
    async ModifyAutoScalerResourceStrategy(req, cb) {
        return this.request("ModifyAutoScalerResourceStrategy", req, cb);
    }
    async DescribeInstanceTagInfos(req, cb) {
        return this.request("DescribeInstanceTagInfos", req, cb);
    }
    async PublishConfigFiles(req, cb) {
        return this.request("PublishConfigFiles", req, cb);
    }
    async DeleteGovernanceServices(req, cb) {
        return this.request("DeleteGovernanceServices", req, cb);
    }
    async DescribeConfigFile(req, cb) {
        return this.request("DescribeConfigFile", req, cb);
    }
    async DescribeConfigFileReleases(req, cb) {
        return this.request("DescribeConfigFileReleases", req, cb);
    }
    async CloseWafProtection(req, cb) {
        return this.request("CloseWafProtection", req, cb);
    }
    async CreateCloudNativeAPIGateway(req, cb) {
        return this.request("CreateCloudNativeAPIGateway", req, cb);
    }
    async DescribeCloudNativeAPIGatewayRouteRateLimit(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayRouteRateLimit", req, cb);
    }
    async ModifyCloudNativeAPIGatewayService(req, cb) {
        return this.request("ModifyCloudNativeAPIGatewayService", req, cb);
    }
    async DescribeAutoScalerResourceStrategyBindingGroups(req, cb) {
        return this.request("DescribeAutoScalerResourceStrategyBindingGroups", req, cb);
    }
    async DescribeConfigFileRelease(req, cb) {
        return this.request("DescribeConfigFileRelease", req, cb);
    }
    async DescribeGovernanceNamespaces(req, cb) {
        return this.request("DescribeGovernanceNamespaces", req, cb);
    }
    async UpdateUpstreamTargets(req, cb) {
        return this.request("UpdateUpstreamTargets", req, cb);
    }
    async CreateCloudNativeAPIGatewayRoute(req, cb) {
        return this.request("CreateCloudNativeAPIGatewayRoute", req, cb);
    }
    async CreateGovernanceInstances(req, cb) {
        return this.request("CreateGovernanceInstances", req, cb);
    }
    async DeleteCloudNativeAPIGatewayRouteRateLimit(req, cb) {
        return this.request("DeleteCloudNativeAPIGatewayRouteRateLimit", req, cb);
    }
    async DeleteConfigFileReleases(req, cb) {
        return this.request("DeleteConfigFileReleases", req, cb);
    }
    async DeleteAutoScalerResourceStrategy(req, cb) {
        return this.request("DeleteAutoScalerResourceStrategy", req, cb);
    }
    async ModifyCloudNativeAPIGatewayRoute(req, cb) {
        return this.request("ModifyCloudNativeAPIGatewayRoute", req, cb);
    }
    async DeleteNativeGatewayServerGroup(req, cb) {
        return this.request("DeleteNativeGatewayServerGroup", req, cb);
    }
    async DescribeCloudNativeAPIGatewayPorts(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayPorts", req, cb);
    }
    async DescribeCloudNativeAPIGatewayCanaryRules(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayCanaryRules", req, cb);
    }
    async DescribeSREInstanceAccessAddress(req, cb) {
        return this.request("DescribeSREInstanceAccessAddress", req, cb);
    }
    async ModifyConsoleNetwork(req, cb) {
        return this.request("ModifyConsoleNetwork", req, cb);
    }
    async DescribeNacosReplicas(req, cb) {
        return this.request("DescribeNacosReplicas", req, cb);
    }
    async DescribeUpstreamHealthCheckConfig(req, cb) {
        return this.request("DescribeUpstreamHealthCheckConfig", req, cb);
    }
    async DescribeNativeGatewayServiceSources(req, cb) {
        return this.request("DescribeNativeGatewayServiceSources", req, cb);
    }
    async DescribeCloudNativeAPIGatewayInfoByIp(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayInfoByIp", req, cb);
    }
    async ModifyNativeGatewayServerGroup(req, cb) {
        return this.request("ModifyNativeGatewayServerGroup", req, cb);
    }
    async DescribeCloudNativeAPIGatewayCertificates(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayCertificates", req, cb);
    }
    async ModifyCloudNativeAPIGatewayServiceRateLimit(req, cb) {
        return this.request("ModifyCloudNativeAPIGatewayServiceRateLimit", req, cb);
    }
    async CreateNativeGatewayServiceSource(req, cb) {
        return this.request("CreateNativeGatewayServiceSource", req, cb);
    }
    async RestartSREInstance(req, cb) {
        return this.request("RestartSREInstance", req, cb);
    }
    async CreateGovernanceAlias(req, cb) {
        return this.request("CreateGovernanceAlias", req, cb);
    }
    async DescribeGovernanceInstances(req, cb) {
        return this.request("DescribeGovernanceInstances", req, cb);
    }
    async DescribeGovernanceServices(req, cb) {
        return this.request("DescribeGovernanceServices", req, cb);
    }
    async DeleteCloudNativeAPIGatewayCanaryRule(req, cb) {
        return this.request("DeleteCloudNativeAPIGatewayCanaryRule", req, cb);
    }
    async DescribeCloudNativeAPIGatewayNodes(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayNodes", req, cb);
    }
    async UnbindAutoScalerResourceStrategyFromGroups(req, cb) {
        return this.request("UnbindAutoScalerResourceStrategyFromGroups", req, cb);
    }
    async ModifyCloudNativeAPIGatewayRouteRateLimit(req, cb) {
        return this.request("ModifyCloudNativeAPIGatewayRouteRateLimit", req, cb);
    }
    async DescribeConfigFileReleaseVersions(req, cb) {
        return this.request("DescribeConfigFileReleaseVersions", req, cb);
    }
    async DescribeSREInstances(req, cb) {
        return this.request("DescribeSREInstances", req, cb);
    }
    async DescribeConfigFilesByGroup(req, cb) {
        return this.request("DescribeConfigFilesByGroup", req, cb);
    }
    async DescribeWafProtection(req, cb) {
        return this.request("DescribeWafProtection", req, cb);
    }
    async DescribeCloudNativeAPIGatewayConfig(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayConfig", req, cb);
    }
    async CreateGovernanceNamespaces(req, cb) {
        return this.request("CreateGovernanceNamespaces", req, cb);
    }
    async DescribeGovernanceServiceContractVersions(req, cb) {
        return this.request("DescribeGovernanceServiceContractVersions", req, cb);
    }
    async OpenWafProtection(req, cb) {
        return this.request("OpenWafProtection", req, cb);
    }
    async ModifyCloudNativeAPIGatewayCanaryRule(req, cb) {
        return this.request("ModifyCloudNativeAPIGatewayCanaryRule", req, cb);
    }
    async CreateCloudNativeAPIGatewayService(req, cb) {
        return this.request("CreateCloudNativeAPIGatewayService", req, cb);
    }
    async DeleteCloudNativeAPIGateway(req, cb) {
        return this.request("DeleteCloudNativeAPIGateway", req, cb);
    }
    async ModifyGovernanceNamespaces(req, cb) {
        return this.request("ModifyGovernanceNamespaces", req, cb);
    }
    async DescribePublicNetwork(req, cb) {
        return this.request("DescribePublicNetwork", req, cb);
    }
    async CreateAutoScalerResourceStrategy(req, cb) {
        return this.request("CreateAutoScalerResourceStrategy", req, cb);
    }
    async CreateConfigFile(req, cb) {
        return this.request("CreateConfigFile", req, cb);
    }
    async ModifyNetworkAccessStrategy(req, cb) {
        return this.request("ModifyNetworkAccessStrategy", req, cb);
    }
    async DescribeCloudNativeAPIGatewayUpstream(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayUpstream", req, cb);
    }
    async CreateOrUpdateConfigFileAndRelease(req, cb) {
        return this.request("CreateOrUpdateConfigFileAndRelease", req, cb);
    }
    async ModifyGovernanceAlias(req, cb) {
        return this.request("ModifyGovernanceAlias", req, cb);
    }
    async CreateWafDomains(req, cb) {
        return this.request("CreateWafDomains", req, cb);
    }
    async ModifyConfigFileGroup(req, cb) {
        return this.request("ModifyConfigFileGroup", req, cb);
    }
    async RollbackConfigFileReleases(req, cb) {
        return this.request("RollbackConfigFileReleases", req, cb);
    }
    async CreateEngine(req, cb) {
        return this.request("CreateEngine", req, cb);
    }
    async ModifyUpstreamNodeStatus(req, cb) {
        return this.request("ModifyUpstreamNodeStatus", req, cb);
    }
    async BindAutoScalerResourceStrategyToGroups(req, cb) {
        return this.request("BindAutoScalerResourceStrategyToGroups", req, cb);
    }
    async CreateCloudNativeAPIGatewayRouteRateLimit(req, cb) {
        return this.request("CreateCloudNativeAPIGatewayRouteRateLimit", req, cb);
    }
    async DeleteWafDomains(req, cb) {
        return this.request("DeleteWafDomains", req, cb);
    }
    async DescribePublicAddressConfig(req, cb) {
        return this.request("DescribePublicAddressConfig", req, cb);
    }
    async DeleteCloudNativeAPIGatewayService(req, cb) {
        return this.request("DeleteCloudNativeAPIGatewayService", req, cb);
    }
    async DescribeCloudNativeAPIGatewayRoutes(req, cb) {
        return this.request("DescribeCloudNativeAPIGatewayRoutes", req, cb);
    }
    async DescribeConfigFileGroups(req, cb) {
        return this.request("DescribeConfigFileGroups", req, cb);
    }
    async DescribeCloudNativeAPIGateway(req, cb) {
        return this.request("DescribeCloudNativeAPIGateway", req, cb);
    }
    async DeleteCloudNativeAPIGatewayRoute(req, cb) {
        return this.request("DeleteCloudNativeAPIGatewayRoute", req, cb);
    }
    async CreateCloudNativeAPIGatewayPublicNetwork(req, cb) {
        return this.request("CreateCloudNativeAPIGatewayPublicNetwork", req, cb);
    }
    async DescribeNacosServerInterfaces(req, cb) {
        return this.request("DescribeNacosServerInterfaces", req, cb);
    }
    async ModifyConfigFiles(req, cb) {
        return this.request("ModifyConfigFiles", req, cb);
    }
    async UpdateCloudNativeAPIGatewaySpec(req, cb) {
        return this.request("UpdateCloudNativeAPIGatewaySpec", req, cb);
    }
    async CreateGovernanceServices(req, cb) {
        return this.request("CreateGovernanceServices", req, cb);
    }
    async DeleteGovernanceNamespaces(req, cb) {
        return this.request("DeleteGovernanceNamespaces", req, cb);
    }
    async ModifyGovernanceInstances(req, cb) {
        return this.request("ModifyGovernanceInstances", req, cb);
    }
    async DescribeConfigFiles(req, cb) {
        return this.request("DescribeConfigFiles", req, cb);
    }
    async ModifyCloudNativeAPIGateway(req, cb) {
        return this.request("ModifyCloudNativeAPIGateway", req, cb);
    }
    async CreateCloudNativeAPIGatewayServiceRateLimit(req, cb) {
        return this.request("CreateCloudNativeAPIGatewayServiceRateLimit", req, cb);
    }
    async DeleteGovernanceInstancesByHost(req, cb) {
        return this.request("DeleteGovernanceInstancesByHost", req, cb);
    }
    async DescribeConfigFileReleaseHistories(req, cb) {
        return this.request("DescribeConfigFileReleaseHistories", req, cb);
    }
    async DescribeNativeGatewayServerGroups(req, cb) {
        return this.request("DescribeNativeGatewayServerGroups", req, cb);
    }
    async UpdateUpstreamHealthCheckConfig(req, cb) {
        return this.request("UpdateUpstreamHealthCheckConfig", req, cb);
    }
}
