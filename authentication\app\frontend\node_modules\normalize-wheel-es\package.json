{"name": "normalize-wheel-es", "version": "1.2.0", "description": "Mouse wheel normalization across multiple multiple browsers.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "index.d.ts", "author": "<PERSON><PERSON>", "contributors": [{"name": "三咲智子", "email": "<EMAIL>", "url": "https://github.com/sxzz"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sxzz/normalize-wheel-es/issues"}, "homepage": "https://github.com/sxzz/normalize-wheel-es#readme", "repository": {"type": "git", "url": "git+https://github.com/sxzz/normalize-wheel-es.git"}, "keywords": ["mouse wheel", "normalization", "browser", "esm"], "files": ["dist", "index.d.ts"], "devDependencies": {"@swc/core": "^1.2.218", "bumpp": "^8.2.1", "tsup": "^6.1.3"}, "scripts": {"build": "tsup", "release": "bumpp"}}