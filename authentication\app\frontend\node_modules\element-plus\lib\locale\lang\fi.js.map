{"version": 3, "file": "fi.js", "sources": ["../../../../../packages/locale/lang/fi.ts"], "sourcesContent": ["export default {\n  name: 'fi',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: 'Nyt',\n      today: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n      cancel: '<PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Valitse päivä',\n      selectTime: 'Valitse aika',\n      startDate: 'Aloituspäivä',\n      startTime: 'Aloitusaika',\n      endDate: 'Lopetuspäivä',\n      endTime: 'Lopetusaika',\n      prevYear: 'Edellinen vuosi',\n      nextYear: 'Seuraava vuosi',\n      prevMonth: 'Edellinen kuukausi',\n      nextMonth: '<PERSON><PERSON><PERSON> kuukausi',\n      year: '',\n      month1: 'tammikuu',\n      month2: 'helmikuu',\n      month3: 'maaliskuu',\n      month4: 'huhtikuu',\n      month5: 'toukokuu',\n      month6: 'kesäkuu',\n      month7: 'heinäkuu',\n      month8: 'elokuu',\n      month9: 'syyskuu',\n      month10: 'lokakuu',\n      month11: 'marraskuu',\n      month12: 'joulukuu',\n      // week: 'week',\n      weeks: {\n        sun: 'su',\n        mon: 'ma',\n        tue: 'ti',\n        wed: 'ke',\n        thu: 'to',\n        fri: 'pe',\n        sat: 'la',\n      },\n      months: {\n        jan: 'tammi',\n        feb: 'helmi',\n        mar: 'maalis',\n        apr: 'huhti',\n        may: 'touko',\n        jun: 'kesä',\n        jul: 'heinä',\n        aug: 'elo',\n        sep: 'syys',\n        oct: 'loka',\n        nov: 'marras',\n        dec: 'joulu',\n      },\n    },\n    select: {\n      loading: 'Lataa',\n      noMatch: 'Ei vastaavia tietoja',\n      noData: 'Ei tietoja',\n      placeholder: 'Valitse',\n    },\n    mention: {\n      loading: 'Lataa',\n    },\n    cascader: {\n      noMatch: 'Ei vastaavia tietoja',\n      loading: 'Lataa',\n      placeholder: 'Valitse',\n      noData: 'Ei tietoja',\n    },\n    pagination: {\n      goto: 'Mene',\n      pagesize: '/sivu',\n      total: 'Yhteensä {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Viesti',\n      confirm: 'OK',\n      cancel: 'Peruuta',\n      error: 'Virheellinen syöte',\n    },\n    upload: {\n      deleteTip: 'Poista Delete-näppäimellä',\n      delete: 'Poista',\n      preview: 'Esikatsele',\n      continue: 'Jatka',\n    },\n    table: {\n      emptyText: 'Ei tietoja',\n      confirmFilter: 'Vahvista',\n      resetFilter: 'Tyhjennä',\n      clearFilter: 'Kaikki',\n      sumText: 'Summa',\n    },\n    tree: {\n      emptyText: 'Ei tietoja',\n    },\n    transfer: {\n      noMatch: 'Ei vastaavia tietoja',\n      noData: 'Ei tietoja',\n      titles: ['Luettelo 1', 'Luettelo 2'],\n      filterPlaceholder: 'Syötä hakusana',\n      noCheckedFormat: '{total} kohdetta',\n      hasCheckedFormat: '{checked}/{total} valittu',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,aAAa;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,iBAAiB;AAC9B,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,qBAAqB;AACvC,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,OAAO;AACpB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,WAAW,EAAE,SAAS;AAC5B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,MAAM,EAAE,YAAY;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,qBAAqB;AAClC,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,KAAK,EAAE,uBAAuB;AACpC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,oCAAoC;AACrD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,QAAQ,EAAE,OAAO;AACvB,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,YAAY;AAC7B,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,YAAY;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AAC1C,MAAM,iBAAiB,EAAE,sBAAsB;AAC/C,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,gBAAgB,EAAE,2BAA2B;AACnD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}