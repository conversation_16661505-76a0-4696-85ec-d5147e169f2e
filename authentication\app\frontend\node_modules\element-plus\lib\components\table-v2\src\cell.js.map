{"version": 3, "file": "cell.js", "sources": ["../../../../../../packages/components/table-v2/src/cell.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { column } from './common'\n\nimport type {\n  ExtractPropTypes,\n  StyleValue,\n  __ExtractPublicPropTypes,\n} from 'vue'\n\nexport const tableV2CellProps = buildProps({\n  class: String,\n  cellData: {\n    type: definePropType<any>([String, Boolean, Number, Object]),\n  },\n  column,\n  columnIndex: Number,\n  style: {\n    type: definePropType<StyleValue>([String, Array, Object]),\n  },\n  rowData: {\n    type: definePropType<any>(Object),\n  },\n  rowIndex: Number,\n} as const)\n\nexport type TableV2CellProps = ExtractPropTypes<typeof tableV2CellProps>\nexport type TableV2CellPropsPublic = __ExtractPublicPropTypes<\n  typeof tableV2CellProps\n>\n"], "names": ["buildProps", "definePropType", "column"], "mappings": ";;;;;;;AAEY,MAAC,gBAAgB,GAAGA,kBAAU,CAAC;AAC3C,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAC3D,GAAG;AACH,UAAEC,aAAM;AACR,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,QAAQ,EAAE,MAAM;AAClB,CAAC;;;;"}