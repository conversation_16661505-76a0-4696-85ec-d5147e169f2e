{"version": 3, "file": "loading.js", "sources": ["../../../../../../packages/components/loading/src/loading.ts"], "sourcesContent": ["import {\n  Transition,\n  createApp,\n  createVNode,\n  defineComponent,\n  h,\n  reactive,\n  ref,\n  toRefs,\n  vShow,\n  withCtx,\n  withDirectives,\n} from 'vue'\nimport { removeClass } from '@element-plus/utils'\nimport { useGlobalComponentSettings } from '@element-plus/components/config-provider'\n\nimport type { AppContext } from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { LoadingOptionsResolved } from './types'\n\nexport function createLoadingComponent(\n  options: LoadingOptionsResolved,\n  appContext: AppContext | null\n) {\n  let afterLeaveTimer: ReturnType<typeof setTimeout>\n  // IMPORTANT NOTE: this is only a hacking way to expose the injections on an\n  // instance, DO NOT FOLLOW this pattern in your own code.\n  const afterLeaveFlag = ref(false)\n  const data = reactive({\n    ...options,\n    originalPosition: '',\n    originalOverflow: '',\n    visible: false,\n  })\n\n  function setText(text: string) {\n    data.text = text\n  }\n\n  function destroySelf() {\n    const target = data.parent\n    const ns = (vm as any).ns as UseNamespaceReturn\n    if (!target.vLoadingAddClassList) {\n      let loadingNumber: number | string | null =\n        target.getAttribute('loading-number')\n      loadingNumber = Number.parseInt(loadingNumber as any) - 1\n      if (!loadingNumber) {\n        removeClass(target, ns.bm('parent', 'relative'))\n        target.removeAttribute('loading-number')\n      } else {\n        target.setAttribute('loading-number', loadingNumber.toString())\n      }\n      removeClass(target, ns.bm('parent', 'hidden'))\n    }\n    removeElLoadingChild()\n    loadingInstance.unmount()\n  }\n  function removeElLoadingChild(): void {\n    vm.$el?.parentNode?.removeChild(vm.$el)\n  }\n  function close() {\n    if (options.beforeClose && !options.beforeClose()) return\n\n    afterLeaveFlag.value = true\n    clearTimeout(afterLeaveTimer)\n\n    afterLeaveTimer = setTimeout(handleAfterLeave, 400)\n    data.visible = false\n\n    options.closed?.()\n  }\n\n  function handleAfterLeave() {\n    if (!afterLeaveFlag.value) return\n    const target = data.parent\n    afterLeaveFlag.value = false\n    target.vLoadingAddClassList = undefined\n    destroySelf()\n  }\n\n  const elLoadingComponent = defineComponent({\n    name: 'ElLoading',\n    setup(_, { expose }) {\n      const { ns, zIndex } = useGlobalComponentSettings('loading')\n\n      expose({\n        ns,\n        zIndex,\n      })\n\n      return () => {\n        const svg = data.spinner || data.svg\n        const spinner = h(\n          'svg',\n          {\n            class: 'circular',\n            viewBox: data.svgViewBox ? data.svgViewBox : '0 0 50 50',\n            ...(svg ? { innerHTML: svg } : {}),\n          },\n          [\n            h('circle', {\n              class: 'path',\n              cx: '25',\n              cy: '25',\n              r: '20',\n              fill: 'none',\n            }),\n          ]\n        )\n\n        const spinnerText = data.text\n          ? h('p', { class: ns.b('text') }, [data.text])\n          : undefined\n\n        return h(\n          Transition,\n          {\n            name: ns.b('fade'),\n            onAfterLeave: handleAfterLeave,\n          },\n          {\n            default: withCtx(() => [\n              withDirectives(\n                createVNode(\n                  'div',\n                  {\n                    style: {\n                      backgroundColor: data.background || '',\n                    },\n                    class: [\n                      ns.b('mask'),\n                      data.customClass,\n                      data.fullscreen ? 'is-fullscreen' : '',\n                    ],\n                  },\n                  [\n                    h(\n                      'div',\n                      {\n                        class: ns.b('spinner'),\n                      },\n                      [spinner, spinnerText]\n                    ),\n                  ]\n                ),\n                [[vShow, data.visible]]\n              ),\n            ]),\n          }\n        )\n      }\n    },\n  })\n\n  const loadingInstance = createApp(elLoadingComponent)\n  Object.assign(loadingInstance._context, appContext ?? {})\n  const vm = loadingInstance.mount(document.createElement('div'))\n\n  return {\n    ...toRefs(data),\n    setText,\n    removeElLoadingChild,\n    close,\n    handleAfterLeave,\n    vm,\n    get $el(): HTMLElement {\n      return vm.$el\n    },\n  }\n}\n\nexport type LoadingInstance = ReturnType<typeof createLoadingComponent>\n"], "names": ["ref", "reactive", "removeClass", "defineComponent", "useGlobalComponentSettings", "h", "Transition", "withCtx", "withDirectives", "createVNode", "vShow", "createApp", "toRefs"], "mappings": ";;;;;;;;AAeO,SAAS,sBAAsB,CAAC,OAAO,EAAE,UAAU,EAAE;AAC5D,EAAE,IAAI,eAAe,CAAC;AACtB,EAAE,MAAM,cAAc,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AACpC,EAAE,MAAM,IAAI,GAAGC,YAAQ,CAAC;AACxB,IAAI,GAAG,OAAO;AACd,IAAI,gBAAgB,EAAE,EAAE;AACxB,IAAI,gBAAgB,EAAE,EAAE;AACxB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,OAAO,CAAC,IAAI,EAAE;AACzB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,GAAG;AACH,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;AACtC,MAAM,IAAI,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;AAChE,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACzD,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,QAAQC,iBAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;AACzD,QAAQ,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;AACjD,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,YAAY,CAAC,gBAAgB,EAAE,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC;AACxE,OAAO;AACP,MAAMA,iBAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,oBAAoB,EAAE,CAAC;AAC3B,IAAI,eAAe,CAAC,OAAO,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,SAAS,oBAAoB,GAAG;AAClC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AACpG,GAAG;AACH,EAAE,SAAS,KAAK,GAAG;AACnB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,OAAO,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AACrD,MAAM,OAAO;AACb,IAAI,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC;AAChC,IAAI,YAAY,CAAC,eAAe,CAAC,CAAC;AAClC,IAAI,eAAe,GAAG,UAAU,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;AACxD,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC9D,GAAG;AACH,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK;AAC7B,MAAM,OAAO;AACb,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,IAAI,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;AACjC,IAAI,MAAM,CAAC,oBAAoB,GAAG,KAAK,CAAC,CAAC;AACzC,IAAI,WAAW,EAAE,CAAC;AAClB,GAAG;AACH,EAAE,MAAM,kBAAkB,GAAGC,mBAAe,CAAC;AAC7C,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE;AACzB,MAAM,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAGC,0CAA0B,CAAC,SAAS,CAAC,CAAC;AACnE,MAAM,MAAM,CAAC;AACb,QAAQ,EAAE;AACV,QAAQ,MAAM;AACd,OAAO,CAAC,CAAC;AACT,MAAM,OAAO,MAAM;AACnB,QAAQ,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC;AAC7C,QAAQ,MAAM,OAAO,GAAGC,KAAC,CAAC,KAAK,EAAE;AACjC,UAAU,KAAK,EAAE,UAAU;AAC3B,UAAU,OAAO,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,WAAW;AAClE,UAAU,GAAG,GAAG,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;AAC1C,SAAS,EAAE;AACX,UAAUA,KAAC,CAAC,QAAQ,EAAE;AACtB,YAAY,KAAK,EAAE,MAAM;AACzB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,CAAC,EAAE,IAAI;AACnB,YAAY,IAAI,EAAE,MAAM;AACxB,WAAW,CAAC;AACZ,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,GAAGA,KAAC,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AAC9F,QAAQ,OAAOA,KAAC,CAACC,cAAU,EAAE;AAC7B,UAAU,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;AAC5B,UAAU,YAAY,EAAE,gBAAgB;AACxC,SAAS,EAAE;AACX,UAAU,OAAO,EAAEC,WAAO,CAAC,MAAM;AACjC,YAAYC,kBAAc,CAACC,eAAW,CAAC,KAAK,EAAE;AAC9C,cAAc,KAAK,EAAE;AACrB,gBAAgB,eAAe,EAAE,IAAI,CAAC,UAAU,IAAI,EAAE;AACtD,eAAe;AACf,cAAc,KAAK,EAAE;AACrB,gBAAgB,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;AAC5B,gBAAgB,IAAI,CAAC,WAAW;AAChC,gBAAgB,IAAI,CAAC,UAAU,GAAG,eAAe,GAAG,EAAE;AACtD,eAAe;AACf,aAAa,EAAE;AACf,cAAcJ,KAAC,CAAC,KAAK,EAAE;AACvB,gBAAgB,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;AACtC,eAAe,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AACxC,aAAa,CAAC,EAAE,CAAC,CAACK,SAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AACxC,WAAW,CAAC;AACZ,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAGC,aAAS,CAAC,kBAAkB,CAAC,CAAC;AACxD,EAAE,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,EAAE,UAAU,IAAI,IAAI,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC;AAChF,EAAE,MAAM,EAAE,GAAG,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AAClE,EAAE,OAAO;AACT,IAAI,GAAGC,UAAM,CAAC,IAAI,CAAC;AACnB,IAAI,OAAO;AACX,IAAI,oBAAoB;AACxB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,EAAE;AACN,IAAI,IAAI,GAAG,GAAG;AACd,MAAM,OAAO,EAAE,CAAC,GAAG,CAAC;AACpB,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}