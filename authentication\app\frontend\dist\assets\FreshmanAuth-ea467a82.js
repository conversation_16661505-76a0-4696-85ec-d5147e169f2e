import{_ as e,u as a,r as l,b as s}from"./index-bcbc0702.js";/* empty css                   *//* empty css                  *//* empty css                 *//* empty css                     *//* empty css                 */import{r as i,d as t,A as r,B as d,al as u,G as o,C as n,D as c,F as p,u as m,am as _,J as v,I as f,H as g,E as h,K as y,ai as b,aj as k,L as q,ak as x,an as V,ao as w,ap as z,ah as C,aq as j,ar as A,a2 as F,ag as U,as as E}from"./element-plus-3ab68b46.js";import{V as I}from"./VerificationCode-b10ec6a7.js";import{A as Q}from"./AuthLayout-1aa16efb.js";import"./utils-c6a461b2.js";const T={key:0,class:"step-content"},R={class:"admission-query"},$={class:"captcha-group"},D=["src"],L={class:"form-actions"},S={key:0,class:"admission-info"},B={class:"info-grid"},K={class:"info-item"},O={class:"info-item"},P={class:"info-item"},G={class:"info-item"},H={class:"form-actions"},J={key:1,class:"step-content"},N={class:"identity-verification"},X={key:0,class:"id-verification"},M={class:"form-actions"},W={key:1,class:"identity-success"},Y={key:2,class:"step-content"},Z={class:"email-verification"},ee={class:"form-actions"},ae={key:0,class:"email-verification-code"},le={key:3,class:"step-content"},se={class:"phone-verification"},ie={class:"form-actions"},te={key:0,class:"phone-verification-code"},re={key:4,class:"step-content"},de={class:"success-content"},ue=e({__name:"FreshmanAuth",setup(e){a();const ue=["录取查询","身份验证","邮箱验证","手机验证","认证完成"],oe=i(0),ne=i(!1),ce=i(""),pe=i(""),me=i(null),_e=i(!1),ve=i(!1),fe=i(!1),ge=t({qq:"",candidate_id:"",id_card:"",captcha:""}),he=t({id_last_six:""}),ye=t({email:""}),be=t({phone:""}),ke=i(),qe=i(),xe=i(),Ve=i(),we={qq:[{required:!0,message:"请输入QQ号",trigger:"blur"},{pattern:/^[1-9][0-9]{4,10}$/,message:"QQ号格式不正确",trigger:"blur"}],candidate_id:[{required:!0,message:"请输入考生号",trigger:"blur"},{len:14,message:"考生号为14位数字",trigger:"blur"},{pattern:/^\d{14}$/,message:"考生号只能包含数字",trigger:"blur"}],id_card:[{required:!0,message:"请输入身份证号",trigger:"blur"},{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,message:"身份证号格式不正确",trigger:"blur"}],captcha:[{required:!0,message:"请输入验证码",trigger:"blur"}]},ze={id_last_six:[{required:!0,message:"请输入身份证后6位",trigger:"blur"},{len:6,message:"请输入6位字符",trigger:"blur"}]},Ce={email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"邮箱格式不正确",trigger:"blur"}]},je={phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}]},Ae=async()=>{try{const e=await l.get("/api/freshman/captcha");e.success&&(ce.value=e.data.image,pe.value=e.data.captchaId)}catch(e){}},Fe=()=>{Ae()},Ue=async()=>{if(!ke.value)return;if(await ke.value.validate().catch(()=>!1)){ne.value=!0;try{const e=await l.post("/api/freshman/query",{qq:ge.qq,candidate_id:ge.candidate_id,id_card:ge.id_card,captcha:ge.captcha,captchaId:pe.value});e.success?(me.value=e.data.admission_info,_e.value=e.data.need_id_verification||!1,h.success("录取信息查询成功")):(h.error(e.message||"查询失败"),e.need_captcha&&Ae())}catch(e){h.error("查询失败，请稍后重试"),Ae()}finally{ne.value=!1}}},Ee=async()=>{if(!qe.value)return;if(await qe.value.validate().catch(()=>!1)){ne.value=!0;try{const e=await l.post("/api/freshman/verify-identity",{qq:ge.qq,id_last_six:he.id_last_six});e.success?(h.success("身份验证成功"),_e.value=!1,setTimeout(()=>{$e()},1e3)):h.error(e.message||"身份验证失败")}catch(e){h.error("验证失败，请稍后重试")}finally{ne.value=!1}}},Ie=async()=>{if(!xe.value)return;if(await xe.value.validate().catch(()=>!1)){ne.value=!0;try{const e=await l.post("/api/auth/email",{email:ye.email});e.success?(h.success("验证码发送成功"),ve.value=!0):h.error(e.message||"发送失败")}catch(e){h.error("发送失败，请稍后重试")}finally{ne.value=!1}}},Qe=()=>{h.success("邮箱验证成功"),oe.value=3},Te=async()=>{if(!Ve.value)return;if(await Ve.value.validate().catch(()=>!1)){ne.value=!0;try{const e=await l.post("/api/auth/sms",{phone:be.phone});e.success?(h.success("验证码发送成功"),fe.value=!0):h.error(e.message||"发送失败")}catch(e){h.error("发送失败，请稍后重试")}finally{ne.value=!1}}},Re=async()=>{var e,a;try{h.success("手机验证成功");const l={qq:ge.qq,real_name:(null==(e=me.value)?void 0:e.name)||"",school:"北京航空航天大学",student_id:(null==(a=me.value)?void 0:a.student_id)||"",email:ye.email,phone:be.phone,category:"freshman",admission_info:me.value},{authApi:i}=await s(()=>import("./auth-36869992.js"),["assets/auth-36869992.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css"]),t=await i.submitFreshmanAuth(l);if(t.success){if(t.editToken&&t.editTokenExpiresIn){const{useUserSessionStore:e}=await s(()=>import("./userSession-03354358.js"),["assets/userSession-03354358.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css"]);e().setEditToken(t.editToken,t.editTokenExpiresIn)}oe.value=4}else h.error(t.message||"认证提交失败")}catch(l){h.error("认证提交失败："+l.message)}},$e=()=>{oe.value<4&&oe.value++},De=()=>{oe.value>0&&oe.value--};return r(()=>{Ae()}),(e,a)=>{const l=y,s=b,i=k,t=q,r=x,h=V,pe=w,Ae=z;return d(),u(Q,{title:"新生认证",subtitle:"Freshman Authentication","page-title":"新生认证","page-description":"高考录取后的新生身份认证","icon-component":"UserFilled",features:["录取信息查询","身份快速验证","新生专属通道"],steps:ue,"current-step":oe.value,"show-steps":!0},{default:o(()=>[0===oe.value?(d(),n("div",T,[c("div",R,[a[18]||(a[18]=c("h3",null,"录取信息查询",-1)),p(r,{model:ge,rules:we,ref_key:"queryFormRef",ref:ke,"label-width":"100px"},{default:o(()=>[p(i,{label:"QQ号",prop:"qq"},{default:o(()=>[p(s,{modelValue:ge.qq,"onUpdate:modelValue":a[0]||(a[0]=e=>ge.qq=e),placeholder:"请输入您的QQ号",size:"large",disabled:ne.value},{prefix:o(()=>[p(l,null,{default:o(()=>[p(m(C))]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),p(i,{label:"考生号",prop:"candidate_id"},{default:o(()=>[p(s,{modelValue:ge.candidate_id,"onUpdate:modelValue":a[1]||(a[1]=e=>ge.candidate_id=e),placeholder:"请输入14位考生号",size:"large",disabled:ne.value,maxlength:"14"},{prefix:o(()=>[p(l,null,{default:o(()=>[p(m(j))]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),p(i,{label:"身份证号",prop:"id_card"},{default:o(()=>[p(s,{modelValue:ge.id_card,"onUpdate:modelValue":a[2]||(a[2]=e=>ge.id_card=e),placeholder:"请输入身份证号",size:"large",disabled:ne.value,maxlength:"18"},{prefix:o(()=>[p(l,null,{default:o(()=>[p(m(A))]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),ce.value?(d(),u(i,{key:0,label:"验证码",prop:"captcha"},{default:o(()=>[c("div",$,[p(s,{modelValue:ge.captcha,"onUpdate:modelValue":a[3]||(a[3]=e=>ge.captcha=e),placeholder:"请输入验证码",size:"large",disabled:ne.value,onKeyup:_(Ue,["enter"])},null,8,["modelValue","disabled"]),c("img",{src:ce.value,alt:"验证码",class:"captcha-image",onClick:Fe,title:"点击刷新验证码"},null,8,D)])]),_:1})):v("",!0),c("div",L,[p(t,{type:"primary",size:"large",loading:ne.value,onClick:Ue},{default:o(()=>a[11]||(a[11]=[f(" 查询录取信息 ",-1)])),_:1,__:[11]},8,["loading"]),p(t,{size:"large",onClick:a[4]||(a[4]=a=>e.$router.push("/"))},{default:o(()=>a[12]||(a[12]=[f(" 返回首页 ",-1)])),_:1,__:[12]})])]),_:1},8,["model"]),me.value?(d(),n("div",S,[p(h,{title:"录取信息",type:"success",closable:!1,"show-icon":""},{default:o(()=>{return[c("div",B,[c("div",K,[a[13]||(a[13]=c("label",null,"姓名：",-1)),c("span",null,g(me.value.name),1)]),c("div",O,[a[14]||(a[14]=c("label",null,"录取专业：",-1)),c("span",null,g(me.value.major),1)]),c("div",P,[a[15]||(a[15]=c("label",null,"录取批次：",-1)),c("span",null,g(me.value.batch),1)]),c("div",G,[a[16]||(a[16]=c("label",null,"录取时间：",-1)),c("span",null,g((e=me.value.admission_date,e?new Date(e).toLocaleDateString("zh-CN"):"")),1)])])];var e}),_:1}),c("div",H,[p(t,{type:"primary",size:"large",onClick:$e},{default:o(()=>a[17]||(a[17]=[f(" 确认信息，继续认证 ",-1)])),_:1,__:[17]})])])):v("",!0)])])):v("",!0),1===oe.value?(d(),n("div",J,[c("div",N,[a[23]||(a[23]=c("h3",null,"身份信息验证",-1)),_e.value?(d(),n("div",X,[p(h,{title:"身份验证",type:"warning",closable:!1,"show-icon":""},{default:o(()=>a[19]||(a[19]=[c("p",null,"为确保身份安全，请输入身份证号后6位进行验证",-1)])),_:1}),p(r,{model:he,rules:ze,ref_key:"idFormRef",ref:qe,"label-width":"120px",class:"mt-20"},{default:o(()=>[p(i,{label:"身份证后6位",prop:"id_last_six"},{default:o(()=>[p(s,{modelValue:he.id_last_six,"onUpdate:modelValue":a[5]||(a[5]=e=>he.id_last_six=e),placeholder:"请输入身份证号后6位",size:"large",disabled:ne.value,maxlength:"6",onKeyup:_(Ee,["enter"])},{prefix:o(()=>[p(l,null,{default:o(()=>[p(m(F))]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),c("div",M,[p(t,{type:"primary",size:"large",loading:ne.value,onClick:Ee},{default:o(()=>a[20]||(a[20]=[f(" 验证身份 ",-1)])),_:1,__:[20]},8,["loading"]),p(t,{size:"large",onClick:De},{default:o(()=>a[21]||(a[21]=[f(" 返回上一步 ",-1)])),_:1,__:[21]})])]),_:1},8,["model"])])):(d(),n("div",W,[p(pe,{icon:"success",title:"身份验证成功","sub-title":"您的身份信息已通过验证"},{extra:o(()=>[p(t,{type:"primary",size:"large",onClick:$e},{default:o(()=>a[22]||(a[22]=[f(" 下一步 ",-1)])),_:1,__:[22]})]),_:1})]))])])):v("",!0),2===oe.value?(d(),n("div",Y,[c("div",Z,[a[27]||(a[27]=c("h3",null,"邮箱验证",-1)),p(r,{model:ye,rules:Ce,ref_key:"emailFormRef",ref:xe,"label-width":"100px"},{default:o(()=>[p(i,{label:"邮箱地址",prop:"email"},{default:o(()=>[p(s,{modelValue:ye.email,"onUpdate:modelValue":a[6]||(a[6]=e=>ye.email=e),placeholder:"请输入邮箱地址",size:"large",disabled:ne.value},{prefix:o(()=>[p(l,null,{default:o(()=>[p(m(U))]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),c("div",ee,[p(t,{type:"primary",size:"large",loading:ne.value,onClick:Ie},{default:o(()=>a[24]||(a[24]=[f(" 发送验证码 ",-1)])),_:1,__:[24]},8,["loading"]),p(t,{size:"large",onClick:De},{default:o(()=>a[25]||(a[25]=[f(" 返回上一步 ",-1)])),_:1,__:[25]})])]),_:1},8,["model"]),ve.value?(d(),n("div",ae,[p(Ae,null,{default:o(()=>a[26]||(a[26]=[f("验证码已发送",-1)])),_:1,__:[26]}),p(I,{email:ye.email,onVerified:Qe,onBack:a[7]||(a[7]=e=>ve.value=!1)},null,8,["email"])])):v("",!0)])])):v("",!0),3===oe.value?(d(),n("div",le,[c("div",se,[a[31]||(a[31]=c("h3",null,"手机号验证",-1)),p(r,{model:be,rules:je,ref_key:"phoneFormRef",ref:Ve,"label-width":"100px"},{default:o(()=>[p(i,{label:"手机号",prop:"phone"},{default:o(()=>[p(s,{modelValue:be.phone,"onUpdate:modelValue":a[8]||(a[8]=e=>be.phone=e),placeholder:"请输入手机号",size:"large",disabled:ne.value},{prefix:o(()=>[p(l,null,{default:o(()=>[p(m(E))]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),c("div",ie,[p(t,{type:"primary",size:"large",loading:ne.value,onClick:Te},{default:o(()=>a[28]||(a[28]=[f(" 发送验证码 ",-1)])),_:1,__:[28]},8,["loading"]),p(t,{size:"large",onClick:De},{default:o(()=>a[29]||(a[29]=[f(" 返回上一步 ",-1)])),_:1,__:[29]})])]),_:1},8,["model"]),fe.value?(d(),n("div",te,[p(Ae,null,{default:o(()=>a[30]||(a[30]=[f("验证码已发送",-1)])),_:1,__:[30]}),p(I,{phone:be.phone,onVerified:Re,onBack:a[9]||(a[9]=e=>fe.value=!1)},null,8,["phone"])])):v("",!0)])])):v("",!0),4===oe.value?(d(),n("div",re,[c("div",de,[p(pe,{icon:"success",title:"新生认证成功","sub-title":"您的新生身份已验证成功，欢迎加入北航大家庭！"},{extra:o(()=>[p(t,{type:"primary",size:"large",onClick:a[10]||(a[10]=a=>e.$router.push("/"))},{default:o(()=>a[32]||(a[32]=[f(" 返回首页 ",-1)])),_:1,__:[32]})]),_:1})])])):v("",!0)]),_:1},8,["current-step"])}}},[["__scopeId","data-v-0f83b55c"]]);export{ue as default};
