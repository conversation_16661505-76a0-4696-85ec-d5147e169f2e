import{_ as t,u as e,a as r,s as i,b as n}from"./index-bcbc0702.js";import{C as s}from"./utils-c6a461b2.js";import{A as o}from"./AuthLayout-1aa16efb.js";import{r as a,p as h,w as u,B as c,C as l,D as f,H as p,ad as d,ae as g,af as m,V as v,J as y,I as b,F as S,G as w,u as T,a2 as E,ag as D,K as x,L as A,d as R,ah as B,a0 as V,ac as O,ai as I,aj as N,ak as P,A as _,al as q,E as C}from"./element-plus-3ab68b46.js";/* empty css                     *//* empty css                 */function M(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function j(t,e){return t&e}function L(t,e){return t|e}function H(t,e){return t^e}function U(t,e){return t&~e}function k(t){if(0==t)return-1;var e=0;return 65535&t||(t>>=16,e+=16),255&t||(t>>=8,e+=8),15&t||(t>>=4,e+=4),3&t||(t>>=2,e+=2),1&t||++e,e}function K(t){for(var e=0;0!=t;)t&=t-1,++e;return e}var F,z="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function G(t){var e,r,i="";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),i+=z.charAt(r>>6)+z.charAt(63&r);for(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),i+=z.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),i+=z.charAt(r>>2)+z.charAt((3&r)<<4));(3&i.length)>0;)i+="=";return i}function Z(t){var e,r="",i=0,n=0;for(e=0;e<t.length&&"="!=t.charAt(e);++e){var s=z.indexOf(t.charAt(e));s<0||(0==i?(r+=M(s>>2),n=3&s,i=1):1==i?(r+=M(n<<2|s>>4),n=15&s,i=2):2==i?(r+=M(n),r+=M(s>>2),n=3&s,i=3):(r+=M(n<<2|s>>4),r+=M(15&s),i=0))}return 1==i&&(r+=M(n<<2)),r}var $,Q=function(t){var e;if(void 0===F){var r="0123456789ABCDEF",i=" \f\n\r\t \u2028\u2029";for(F={},e=0;e<16;++e)F[r.charAt(e)]=e;for(r=r.toLowerCase(),e=10;e<16;++e)F[r.charAt(e)]=e;for(e=0;e<8;++e)F[i.charAt(e)]=-1}var n=[],s=0,o=0;for(e=0;e<t.length;++e){var a=t.charAt(e);if("="==a)break;if(-1!=(a=F[a])){if(void 0===a)throw new Error("Illegal character at offset "+e);s|=a,++o>=2?(n[n.length]=s,s=0,o=0):s<<=4}}if(o)throw new Error("Hex encoding incomplete: 4 bits missing");return n},W={decode:function(t){var e;if(void 0===$){var r="= \f\n\r\t \u2028\u2029";for($=Object.create(null),e=0;e<64;++e)$["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e)]=e;for($["-"]=62,$._=63,e=0;e<9;++e)$[r.charAt(e)]=-1}var i=[],n=0,s=0;for(e=0;e<t.length;++e){var o=t.charAt(e);if("="==o)break;if(-1!=(o=$[o])){if(void 0===o)throw new Error("Illegal character at offset "+e);n|=o,++s>=4?(i[i.length]=n>>16,i[i.length]=n>>8&255,i[i.length]=255&n,n=0,s=0):n<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:i[i.length]=n>>10;break;case 3:i[i.length]=n>>16,i[i.length]=n>>8&255}return i},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(t){var e=W.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error("RegExp out of sync");t=e[2]}return W.decode(t)}},Y=1e13,J=function(){function t(t){this.buf=[+t||0]}return t.prototype.mulAdd=function(t,e){var r,i,n=this.buf,s=n.length;for(r=0;r<s;++r)(i=n[r]*t+e)<Y?e=0:i-=(e=0|i/Y)*Y,n[r]=i;e>0&&(n[r]=e)},t.prototype.sub=function(t){var e,r,i=this.buf,n=i.length;for(e=0;e<n;++e)(r=i[e]-t)<0?(r+=Y,t=1):t=0,i[e]=r;for(;0===i[i.length-1];)i.pop()},t.prototype.toString=function(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,r=e[e.length-1].toString(),i=e.length-2;i>=0;--i)r+=(Y+e[i]).toString().substring(1);return r},t.prototype.valueOf=function(){for(var t=this.buf,e=0,r=t.length-1;r>=0;--r)e=e*Y+t[r];return e},t.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},t}(),X=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,tt=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function et(t,e){return t.length>e&&(t=t.substring(0,e)+"…"),t}var rt,it=function(){function t(e,r){this.hexDigits="0123456789ABCDEF",e instanceof t?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=r)}return t.prototype.get=function(t){if(void 0===t&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},t.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},t.prototype.hexDump=function(t,e,r){for(var i="",n=t;n<e;++n)if(i+=this.hexByte(this.get(n)),!0!==r)switch(15&n){case 7:i+="  ";break;case 15:i+="\n";break;default:i+=" "}return i},t.prototype.isASCII=function(t,e){for(var r=t;r<e;++r){var i=this.get(r);if(i<32||i>176)return!1}return!0},t.prototype.parseStringISO=function(t,e){for(var r="",i=t;i<e;++i)r+=String.fromCharCode(this.get(i));return r},t.prototype.parseStringUTF=function(t,e){for(var r="",i=t;i<e;){var n=this.get(i++);r+=n<128?String.fromCharCode(n):n>191&&n<224?String.fromCharCode((31&n)<<6|63&this.get(i++)):String.fromCharCode((15&n)<<12|(63&this.get(i++))<<6|63&this.get(i++))}return r},t.prototype.parseStringBMP=function(t,e){for(var r,i,n="",s=t;s<e;)r=this.get(s++),i=this.get(s++),n+=String.fromCharCode(r<<8|i);return n},t.prototype.parseTime=function(t,e,r){var i=this.parseStringISO(t,e),n=(r?X:tt).exec(i);return n?(r&&(n[1]=+n[1],n[1]+=+n[1]<70?2e3:1900),i=n[1]+"-"+n[2]+"-"+n[3]+" "+n[4],n[5]&&(i+=":"+n[5],n[6]&&(i+=":"+n[6],n[7]&&(i+="."+n[7]))),n[8]&&(i+=" UTC","Z"!=n[8]&&(i+=n[8],n[9]&&(i+=":"+n[9]))),i):"Unrecognized time: "+i},t.prototype.parseInteger=function(t,e){for(var r,i=this.get(t),n=i>127,s=n?255:0,o="";i==s&&++t<e;)i=this.get(t);if(0===(r=e-t))return n?-1:0;if(r>4){for(o=i,r<<=3;!(128&(+o^s));)o=+o<<1,--r;o="("+r+" bit)\n"}n&&(i-=256);for(var a=new J(i),h=t+1;h<e;++h)a.mulAdd(256,this.get(h));return o+a.toString()},t.prototype.parseBitString=function(t,e,r){for(var i=this.get(t),n="("+((e-t-1<<3)-i)+" bit)\n",s="",o=t+1;o<e;++o){for(var a=this.get(o),h=o==e-1?i:0,u=7;u>=h;--u)s+=a>>u&1?"1":"0";if(s.length>r)return n+et(s,r)}return n+s},t.prototype.parseOctetString=function(t,e,r){if(this.isASCII(t,e))return et(this.parseStringISO(t,e),r);var i=e-t,n="("+i+" byte)\n";i>(r/=2)&&(e=t+r);for(var s=t;s<e;++s)n+=this.hexByte(this.get(s));return i>r&&(n+="…"),n},t.prototype.parseOID=function(t,e,r){for(var i="",n=new J,s=0,o=t;o<e;++o){var a=this.get(o);if(n.mulAdd(128,127&a),s+=7,!(128&a)){if(""===i)if((n=n.simplify())instanceof J)n.sub(80),i="2."+n.toString();else{var h=n<80?n<40?0:1:2;i=h+"."+(n-40*h)}else i+="."+n.toString();if(i.length>r)return et(i,r);n=new J,s=0}}return s>0&&(i+=".incomplete"),i},t}(),nt=function(){function t(t,e,r,i,n){if(!(i instanceof st))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=r,this.tag=i,this.sub=n}return t.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},t.prototype.content=function(t){if(void 0===this.tag)return null;void 0===t&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return et(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return et(this.stream.parseStringISO(e,e+r),t);case 30:return et(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,23==this.tag.tagNumber)}return null},t.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},t.prototype.toPrettyString=function(t){void 0===t&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var r=0,i=this.sub.length;r<i;++r)e+=this.sub[r].toPrettyString(t)}return e},t.prototype.posStart=function(){return this.stream.pos},t.prototype.posContent=function(){return this.stream.pos+this.header},t.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},t.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},t.decodeLength=function(t){var e=t.get(),r=127&e;if(r==e)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0===r)return null;e=0;for(var i=0;i<r;++i)e=256*e+t.get();return e},t.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)},t.decode=function(e){var r;r=e instanceof it?e:new it(e,0);var i=new it(r),n=new st(r),s=t.decodeLength(r),o=r.pos,a=o-i.pos,h=null,u=function(){var e=[];if(null!==s){for(var i=o+s;r.pos<i;)e[e.length]=t.decode(r);if(r.pos!=i)throw new Error("Content size is not correct for container starting at offset "+o)}else try{for(;;){var n=t.decode(r);if(n.tag.isEOC())break;e[e.length]=n}s=o-r.pos}catch(a){throw new Error("Exception while decoding undefined length content: "+a)}return e};if(n.tagConstructed)h=u();else if(n.isUniversal()&&(3==n.tagNumber||4==n.tagNumber))try{if(3==n.tagNumber&&0!=r.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");h=u();for(var c=0;c<h.length;++c)if(h[c].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(l){h=null}if(null===h){if(null===s)throw new Error("We can't skip over an invalid tag with undefined length at offset "+o);r.pos=o+Math.abs(s)}return new t(i,a,s,n,h)},t}(),st=function(){function t(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=!!(32&e),this.tagNumber=31&e,31==this.tagNumber){var r=new J;do{e=t.get(),r.mulAdd(128,127&e)}while(128&e);this.tagNumber=r.simplify()}}return t.prototype.isUniversal=function(){return 0===this.tagClass},t.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},t}(),ot=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],at=(1<<26)/ot[ot.length-1],ht=function(){function t(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}return t.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,i=(1<<e)-1,n=!1,s="",o=this.t,a=this.DB-o*this.DB%e;if(o-- >0)for(a<this.DB&&(r=this[o]>>a)>0&&(n=!0,s=M(r));o>=0;)a<e?(r=(this[o]&(1<<a)-1)<<e-a,r|=this[--o]>>(a+=this.DB-e)):(r=this[o]>>(a-=e)&i,a<=0&&(a+=this.DB,--o)),r>0&&(n=!0),n&&(s+=M(r));return n?s:"0"},t.prototype.negate=function(){var e=pt();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+wt(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var r=pt();return this.abs().divRemTo(e,null,r),this.s<0&&r.compareTo(t.ZERO)>0&&e.subTo(r,r),r},t.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new ct(e):new lt(e),this.exp(t,r)},t.prototype.clone=function(){var t=pt();return this.copyTo(t),t},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var r,i=this.DB-t*this.DB%8,n=0;if(t-- >0)for(i<this.DB&&(r=this[t]>>i)!=(this.s&this.DM)>>i&&(e[n++]=r|this.s<<this.DB-i);t>=0;)i<8?(r=(this[t]&(1<<i)-1)<<8-i,r|=this[--t]>>(i+=this.DB-8)):(r=this[t]>>(i-=8)&255,i<=0&&(i+=this.DB,--t)),128&r&&(r|=-256),0==n&&(128&this.s)!=(128&r)&&++n,(n>0||r!=this.s)&&(e[n++]=r);return e},t.prototype.equals=function(t){return 0==this.compareTo(t)},t.prototype.min=function(t){return this.compareTo(t)<0?this:t},t.prototype.max=function(t){return this.compareTo(t)>0?this:t},t.prototype.and=function(t){var e=pt();return this.bitwiseTo(t,j,e),e},t.prototype.or=function(t){var e=pt();return this.bitwiseTo(t,L,e),e},t.prototype.xor=function(t){var e=pt();return this.bitwiseTo(t,H,e),e},t.prototype.andNot=function(t){var e=pt();return this.bitwiseTo(t,U,e),e},t.prototype.not=function(){for(var t=pt(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},t.prototype.shiftLeft=function(t){var e=pt();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},t.prototype.shiftRight=function(t){var e=pt();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},t.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+k(this[t]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=K(this[r]^e);return t},t.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:!!(this[e]&1<<t%this.DB)},t.prototype.setBit=function(t){return this.changeBit(t,L)},t.prototype.clearBit=function(t){return this.changeBit(t,U)},t.prototype.flipBit=function(t){return this.changeBit(t,H)},t.prototype.add=function(t){var e=pt();return this.addTo(t,e),e},t.prototype.subtract=function(t){var e=pt();return this.subTo(t,e),e},t.prototype.multiply=function(t){var e=pt();return this.multiplyTo(t,e),e},t.prototype.divide=function(t){var e=pt();return this.divRemTo(t,e,null),e},t.prototype.remainder=function(t){var e=pt();return this.divRemTo(t,null,e),e},t.prototype.divideAndRemainder=function(t){var e=pt(),r=pt();return this.divRemTo(t,e,r),[e,r]},t.prototype.modPow=function(t,e){var r,i,n=t.bitLength(),s=St(1);if(n<=0)return s;r=n<18?1:n<48?3:n<144?4:n<768?5:6,i=n<8?new ct(e):e.isEven()?new ft(e):new lt(e);var o=[],a=3,h=r-1,u=(1<<r)-1;if(o[1]=i.convert(this),r>1){var c=pt();for(i.sqrTo(o[1],c);a<=u;)o[a]=pt(),i.mulTo(c,o[a-2],o[a]),a+=2}var l,f,p=t.t-1,d=!0,g=pt();for(n=wt(t[p])-1;p>=0;){for(n>=h?l=t[p]>>n-h&u:(l=(t[p]&(1<<n+1)-1)<<h-n,p>0&&(l|=t[p-1]>>this.DB+n-h)),a=r;!(1&l);)l>>=1,--a;if((n-=a)<0&&(n+=this.DB,--p),d)o[l].copyTo(s),d=!1;else{for(;a>1;)i.sqrTo(s,g),i.sqrTo(g,s),a-=2;a>0?i.sqrTo(s,g):(f=s,s=g,g=f),i.mulTo(g,o[l],s)}for(;p>=0&&!(t[p]&1<<n);)i.sqrTo(s,g),f=s,s=g,g=f,--n<0&&(n=this.DB-1,--p)}return i.revert(s)},t.prototype.modInverse=function(e){var r=e.isEven();if(this.isEven()&&r||0==e.signum())return t.ZERO;for(var i=e.clone(),n=this.clone(),s=St(1),o=St(0),a=St(0),h=St(1);0!=i.signum();){for(;i.isEven();)i.rShiftTo(1,i),r?(s.isEven()&&o.isEven()||(s.addTo(this,s),o.subTo(e,o)),s.rShiftTo(1,s)):o.isEven()||o.subTo(e,o),o.rShiftTo(1,o);for(;n.isEven();)n.rShiftTo(1,n),r?(a.isEven()&&h.isEven()||(a.addTo(this,a),h.subTo(e,h)),a.rShiftTo(1,a)):h.isEven()||h.subTo(e,h),h.rShiftTo(1,h);i.compareTo(n)>=0?(i.subTo(n,i),r&&s.subTo(a,s),o.subTo(h,o)):(n.subTo(i,n),r&&a.subTo(s,a),h.subTo(o,h))}return 0!=n.compareTo(t.ONE)?t.ZERO:h.compareTo(e)>=0?h.subtract(e):h.signum()<0?(h.addTo(e,h),h.signum()<0?h.add(e):h):h},t.prototype.pow=function(t){return this.exp(t,new ut)},t.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var i=e;e=r,r=i}var n=e.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)return e;for(n<s&&(s=n),s>0&&(e.rShiftTo(s,e),r.rShiftTo(s,r));e.signum()>0;)(n=e.getLowestSetBit())>0&&e.rShiftTo(n,e),(n=r.getLowestSetBit())>0&&r.rShiftTo(n,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return s>0&&r.lShiftTo(s,r),r},t.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=ot[ot.length-1]){for(e=0;e<ot.length;++e)if(r[0]==ot[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<ot.length;){for(var i=ot[e],n=e+1;n<ot.length&&i<at;)i*=ot[n++];for(i=r.modInt(i);e<n;)if(i%ot[e++]==0)return!1}return r.millerRabin(t)},t.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},t.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},t.prototype.fromString=function(e,r){var i;if(16==r)i=4;else if(8==r)i=3;else if(256==r)i=8;else if(2==r)i=1;else if(32==r)i=5;else{if(4!=r)return void this.fromRadix(e,r);i=2}this.t=0,this.s=0;for(var n=e.length,s=!1,o=0;--n>=0;){var a=8==i?255&+e[n]:bt(e,n);a<0?"-"==e.charAt(n)&&(s=!0):(s=!1,0==o?this[this.t++]=a:o+i>this.DB?(this[this.t-1]|=(a&(1<<this.DB-o)-1)<<o,this[this.t++]=a>>this.DB-o):this[this.t-1]|=a<<o,(o+=i)>=this.DB&&(o-=this.DB))}8==i&&128&+e[0]&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),s&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},t.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},t.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},t.prototype.lShiftTo=function(t,e){for(var r=t%this.DB,i=this.DB-r,n=(1<<i)-1,s=Math.floor(t/this.DB),o=this.s<<r&this.DM,a=this.t-1;a>=0;--a)e[a+s+1]=this[a]>>i|o,o=(this[a]&n)<<r;for(a=s-1;a>=0;--a)e[a]=0;e[s]=o,e.t=this.t+s+1,e.s=this.s,e.clamp()},t.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var i=t%this.DB,n=this.DB-i,s=(1<<i)-1;e[0]=this[r]>>i;for(var o=r+1;o<this.t;++o)e[o-r-1]|=(this[o]&s)<<n,e[o-r]=this[o]>>i;i>0&&(e[this.t-r-1]|=(this.s&s)<<n),e.t=this.t-r,e.clamp()}},t.prototype.subTo=function(t,e){for(var r=0,i=0,n=Math.min(t.t,this.t);r<n;)i+=this[r]-t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i-=t.s;r<this.t;)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i-=t[r],e[r++]=i&this.DM,i>>=this.DB;i-=t.s}e.s=i<0?-1:0,i<-1?e[r++]=this.DV+i:i>0&&(e[r++]=i),e.t=r,e.clamp()},t.prototype.multiplyTo=function(e,r){var i=this.abs(),n=e.abs(),s=i.t;for(r.t=s+n.t;--s>=0;)r[s]=0;for(s=0;s<n.t;++s)r[s+i.t]=i.am(0,n[s],r,s,0,i.t);r.s=0,r.clamp(),this.s!=e.s&&t.ZERO.subTo(r,r)},t.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var i=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,i,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},t.prototype.divRemTo=function(e,r,i){var n=e.abs();if(!(n.t<=0)){var s=this.abs();if(s.t<n.t)return null!=r&&r.fromInt(0),void(null!=i&&this.copyTo(i));null==i&&(i=pt());var o=pt(),a=this.s,h=e.s,u=this.DB-wt(n[n.t-1]);u>0?(n.lShiftTo(u,o),s.lShiftTo(u,i)):(n.copyTo(o),s.copyTo(i));var c=o.t,l=o[c-1];if(0!=l){var f=l*(1<<this.F1)+(c>1?o[c-2]>>this.F2:0),p=this.FV/f,d=(1<<this.F1)/f,g=1<<this.F2,m=i.t,v=m-c,y=null==r?pt():r;for(o.dlShiftTo(v,y),i.compareTo(y)>=0&&(i[i.t++]=1,i.subTo(y,i)),t.ONE.dlShiftTo(c,y),y.subTo(o,o);o.t<c;)o[o.t++]=0;for(;--v>=0;){var b=i[--m]==l?this.DM:Math.floor(i[m]*p+(i[m-1]+g)*d);if((i[m]+=o.am(0,b,i,v,0,c))<b)for(o.dlShiftTo(v,y),i.subTo(y,i);i[m]<--b;)i.subTo(y,i)}null!=r&&(i.drShiftTo(c,r),a!=h&&t.ZERO.subTo(r,r)),i.t=c,i.clamp(),u>0&&i.rShiftTo(u,i),a<0&&t.ZERO.subTo(i,i)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},t.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},t.prototype.exp=function(e,r){if(e>4294967295||e<1)return t.ONE;var i=pt(),n=pt(),s=r.convert(this),o=wt(e)-1;for(s.copyTo(i);--o>=0;)if(r.sqrTo(i,n),(e&1<<o)>0)r.mulTo(n,s,i);else{var a=i;i=n,n=a}return r.revert(i)},t.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},t.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),i=St(r),n=pt(),s=pt(),o="";for(this.divRemTo(i,n,s);n.signum()>0;)o=(r+s.intValue()).toString(t).substr(1)+o,n.divRemTo(i,n,s);return s.intValue().toString(t)+o},t.prototype.fromRadix=function(e,r){this.fromInt(0),null==r&&(r=10);for(var i=this.chunkSize(r),n=Math.pow(r,i),s=!1,o=0,a=0,h=0;h<e.length;++h){var u=bt(e,h);u<0?"-"==e.charAt(h)&&0==this.signum()&&(s=!0):(a=r*a+u,++o>=i&&(this.dMultiply(n),this.dAddOffset(a,0),o=0,a=0))}o>0&&(this.dMultiply(Math.pow(r,o)),this.dAddOffset(a,0)),s&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,r,i){if("number"==typeof r)if(e<2)this.fromInt(1);else for(this.fromNumber(e,i),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),L,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(r);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var n=[],s=7&e;n.length=1+(e>>3),r.nextBytes(n),s>0?n[0]&=(1<<s)-1:n[0]=0,this.fromString(n,256)}},t.prototype.bitwiseTo=function(t,e,r){var i,n,s=Math.min(t.t,this.t);for(i=0;i<s;++i)r[i]=e(this[i],t[i]);if(t.t<this.t){for(n=t.s&this.DM,i=s;i<this.t;++i)r[i]=e(this[i],n);r.t=this.t}else{for(n=this.s&this.DM,i=s;i<t.t;++i)r[i]=e(n,t[i]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},t.prototype.changeBit=function(e,r){var i=t.ONE.shiftLeft(e);return this.bitwiseTo(i,r,i),i},t.prototype.addTo=function(t,e){for(var r=0,i=0,n=Math.min(t.t,this.t);r<n;)i+=this[r]+t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i+=t.s;r<this.t;)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i+=t[r],e[r++]=i&this.DM,i>>=this.DB;i+=t.s}e.s=i<0?-1:0,i>0?e[r++]=i:i<-1&&(e[r++]=this.DV+i),e.t=r,e.clamp()},t.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},t.prototype.multiplyLowerTo=function(t,e,r){var i=Math.min(this.t+t.t,e);for(r.s=0,r.t=i;i>0;)r[--i]=0;for(var n=r.t-this.t;i<n;++i)r[i+this.t]=this.am(0,t[i],r,i,0,this.t);for(n=Math.min(t.t,e);i<n;++i)this.am(0,t[i],r,i,0,e-i);r.clamp()},t.prototype.multiplyUpperTo=function(t,e,r){--e;var i=r.t=this.t+t.t-e;for(r.s=0;--i>=0;)r[i]=0;for(i=Math.max(e-this.t,0);i<t.t;++i)r[this.t+i-e]=this.am(e-i,t[i],r,0,0,this.t+i-e);r.clamp(),r.drShiftTo(1,r)},t.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var i=this.t-1;i>=0;--i)r=(e*r+this[i])%t;return r},t.prototype.millerRabin=function(e){var r=this.subtract(t.ONE),i=r.getLowestSetBit();if(i<=0)return!1;var n=r.shiftRight(i);(e=e+1>>1)>ot.length&&(e=ot.length);for(var s=pt(),o=0;o<e;++o){s.fromInt(ot[Math.floor(Math.random()*ot.length)]);var a=s.modPow(n,this);if(0!=a.compareTo(t.ONE)&&0!=a.compareTo(r)){for(var h=1;h++<i&&0!=a.compareTo(r);)if(0==(a=a.modPowInt(2,this)).compareTo(t.ONE))return!1;if(0!=a.compareTo(r))return!1}}return!0},t.prototype.square=function(){var t=pt();return this.squareTo(t),t},t.prototype.gcda=function(t,e){var r=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(r.compareTo(i)<0){var n=r;r=i,i=n}var s=r.getLowestSetBit(),o=i.getLowestSetBit();if(o<0)e(r);else{s<o&&(o=s),o>0&&(r.rShiftTo(o,r),i.rShiftTo(o,i));var a=function(){(s=r.getLowestSetBit())>0&&r.rShiftTo(s,r),(s=i.getLowestSetBit())>0&&i.rShiftTo(s,i),r.compareTo(i)>=0?(r.subTo(i,r),r.rShiftTo(1,r)):(i.subTo(r,i),i.rShiftTo(1,i)),r.signum()>0?setTimeout(a,0):(o>0&&i.lShiftTo(o,i),setTimeout(function(){e(i)},0))};setTimeout(a,10)}},t.prototype.fromNumberAsync=function(e,r,i,n){if("number"==typeof r)if(e<2)this.fromInt(1);else{this.fromNumber(e,i),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),L,this),this.isEven()&&this.dAddOffset(1,0);var s=this,o=function(){s.dAddOffset(2,0),s.bitLength()>e&&s.subTo(t.ONE.shiftLeft(e-1),s),s.isProbablePrime(r)?setTimeout(function(){n()},0):setTimeout(o,0)};setTimeout(o,0)}else{var a=[],h=7&e;a.length=1+(e>>3),r.nextBytes(a),h>0?a[0]&=(1<<h)-1:a[0]=0,this.fromString(a,256)}},t}(),ut=function(){function t(){}return t.prototype.convert=function(t){return t},t.prototype.revert=function(t){return t},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},t.prototype.sqrTo=function(t,e){t.squareTo(e)},t}(),ct=function(){function t(t){this.m=t}return t.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),lt=function(){function t(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return t.prototype.convert=function(t){var e=pt();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(ht.ZERO)>0&&this.m.subTo(e,e),e},t.prototype.revert=function(t){var e=pt();return t.copyTo(e),this.reduce(e),e},t.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],i=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,i,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),ft=function(){function t(t){this.m=t,this.r2=pt(),this.q3=pt(),ht.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return t.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=pt();return t.copyTo(e),this.reduce(e),e},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}();function pt(){return new ht(null)}function dt(t,e){return new ht(t,e)}var gt="undefined"!=typeof navigator;gt&&"Microsoft Internet Explorer"==navigator.appName?(ht.prototype.am=function(t,e,r,i,n,s){for(var o=32767&e,a=e>>15;--s>=0;){var h=32767&this[t],u=this[t++]>>15,c=a*h+u*o;n=((h=o*h+((32767&c)<<15)+r[i]+(1073741823&n))>>>30)+(c>>>15)+a*u+(n>>>30),r[i++]=1073741823&h}return n},rt=30):gt&&"Netscape"!=navigator.appName?(ht.prototype.am=function(t,e,r,i,n,s){for(;--s>=0;){var o=e*this[t++]+r[i]+n;n=Math.floor(o/67108864),r[i++]=67108863&o}return n},rt=26):(ht.prototype.am=function(t,e,r,i,n,s){for(var o=16383&e,a=e>>14;--s>=0;){var h=16383&this[t],u=this[t++]>>14,c=a*h+u*o;n=((h=o*h+((16383&c)<<14)+r[i]+n)>>28)+(c>>14)+a*u,r[i++]=268435455&h}return n},rt=28),ht.prototype.DB=rt,ht.prototype.DM=(1<<rt)-1,ht.prototype.DV=1<<rt;ht.prototype.FV=Math.pow(2,52),ht.prototype.F1=52-rt,ht.prototype.F2=2*rt-52;var mt,vt,yt=[];for(mt="0".charCodeAt(0),vt=0;vt<=9;++vt)yt[mt++]=vt;for(mt="a".charCodeAt(0),vt=10;vt<36;++vt)yt[mt++]=vt;for(mt="A".charCodeAt(0),vt=10;vt<36;++vt)yt[mt++]=vt;function bt(t,e){var r=yt[t.charCodeAt(e)];return null==r?-1:r}function St(t){var e=pt();return e.fromInt(t),e}function wt(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}ht.ZERO=St(0),ht.ONE=St(1);var Tt=function(){function t(){this.i=0,this.j=0,this.S=[]}return t.prototype.init=function(t){var e,r,i;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,i=this.S[e],this.S[e]=this.S[r],this.S[r]=i;this.i=0,this.j=0},t.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},t}();var Et,Dt,xt=null;if(null==xt){xt=[],Dt=0;var At=void 0;if("undefined"!=typeof window&&window.crypto&&window.crypto.getRandomValues){var Rt=new Uint32Array(256);for(window.crypto.getRandomValues(Rt),At=0;At<Rt.length;++At)xt[Dt++]=255&Rt[At]}var Bt=0,Vt=function(t){if((Bt=Bt||0)>=256||Dt>=256)window.removeEventListener?window.removeEventListener("mousemove",Vt,!1):window.detachEvent&&window.detachEvent("onmousemove",Vt);else try{var e=t.x+t.y;xt[Dt++]=255&e,Bt+=1}catch(r){}};"undefined"!=typeof window&&(window.addEventListener?window.addEventListener("mousemove",Vt,!1):window.attachEvent&&window.attachEvent("onmousemove",Vt))}function Ot(){if(null==Et){for(Et=new Tt;Dt<256;){var t=Math.floor(65536*Math.random());xt[Dt++]=255&t}for(Et.init(xt),Dt=0;Dt<xt.length;++Dt)xt[Dt]=0;Dt=0}return Et.next()}var It=function(){function t(){}return t.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=Ot()},t}();var Nt=function(){function t(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return t.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},t.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},t.prototype.setPublic=function(t,e){null!=t&&null!=e&&t.length>0&&e.length>0&&(this.n=dt(t,16),this.e=parseInt(e,16))},t.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,r=function(t,e){if(e<t.length+11)return null;for(var r=[],i=t.length-1;i>=0&&e>0;){var n=t.charCodeAt(i--);n<128?r[--e]=n:n>127&&n<2048?(r[--e]=63&n|128,r[--e]=n>>6|192):(r[--e]=63&n|128,r[--e]=n>>6&63|128,r[--e]=n>>12|224)}r[--e]=0;for(var s=new It,o=[];e>2;){for(o[0]=0;0==o[0];)s.nextBytes(o);r[--e]=o[0]}return r[--e]=2,r[--e]=0,new ht(r)}(t,e);if(null==r)return null;var i=this.doPublic(r);if(null==i)return null;for(var n=i.toString(16),s=n.length,o=0;o<2*e-s;o++)n="0"+n;return n},t.prototype.setPrivate=function(t,e,r){null!=t&&null!=e&&t.length>0&&e.length>0&&(this.n=dt(t,16),this.e=parseInt(e,16),this.d=dt(r,16))},t.prototype.setPrivateEx=function(t,e,r,i,n,s,o,a){null!=t&&null!=e&&t.length>0&&e.length>0&&(this.n=dt(t,16),this.e=parseInt(e,16),this.d=dt(r,16),this.p=dt(i,16),this.q=dt(n,16),this.dmp1=dt(s,16),this.dmq1=dt(o,16),this.coeff=dt(a,16))},t.prototype.generate=function(t,e){var r=new It,i=t>>1;this.e=parseInt(e,16);for(var n=new ht(e,16);;){for(;this.p=new ht(t-i,1,r),0!=this.p.subtract(ht.ONE).gcd(n).compareTo(ht.ONE)||!this.p.isProbablePrime(10););for(;this.q=new ht(i,1,r),0!=this.q.subtract(ht.ONE).gcd(n).compareTo(ht.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var s=this.p;this.p=this.q,this.q=s}var o=this.p.subtract(ht.ONE),a=this.q.subtract(ht.ONE),h=o.multiply(a);if(0==h.gcd(n).compareTo(ht.ONE)){this.n=this.p.multiply(this.q),this.d=n.modInverse(h),this.dmp1=this.d.mod(o),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},t.prototype.decrypt=function(t){var e=dt(t,16),r=this.doPrivate(e);return null==r?null:function(t,e){var r=t.toByteArray(),i=0;for(;i<r.length&&0==r[i];)++i;if(r.length-i!=e-1||2!=r[i])return null;++i;for(;0!=r[i];)if(++i>=r.length)return null;var n="";for(;++i<r.length;){var s=255&r[i];s<128?n+=String.fromCharCode(s):s>191&&s<224?(n+=String.fromCharCode((31&s)<<6|63&r[i+1]),++i):(n+=String.fromCharCode((15&s)<<12|(63&r[i+1])<<6|63&r[i+2]),i+=2)}return n}(r,this.n.bitLength()+7>>3)},t.prototype.generateAsync=function(t,e,r){var i=new It,n=t>>1;this.e=parseInt(e,16);var s=new ht(e,16),o=this,a=function(){var e=function(){if(o.p.compareTo(o.q)<=0){var t=o.p;o.p=o.q,o.q=t}var e=o.p.subtract(ht.ONE),i=o.q.subtract(ht.ONE),n=e.multiply(i);0==n.gcd(s).compareTo(ht.ONE)?(o.n=o.p.multiply(o.q),o.d=s.modInverse(n),o.dmp1=o.d.mod(e),o.dmq1=o.d.mod(i),o.coeff=o.q.modInverse(o.p),setTimeout(function(){r()},0)):setTimeout(a,0)},h=function(){o.q=pt(),o.q.fromNumberAsync(n,1,i,function(){o.q.subtract(ht.ONE).gcda(s,function(t){0==t.compareTo(ht.ONE)&&o.q.isProbablePrime(10)?setTimeout(e,0):setTimeout(h,0)})})},u=function(){o.p=pt(),o.p.fromNumberAsync(t-n,1,i,function(){o.p.subtract(ht.ONE).gcda(s,function(t){0==t.compareTo(ht.ONE)&&o.p.isProbablePrime(10)?setTimeout(h,0):setTimeout(u,0)})})};setTimeout(u,0)};setTimeout(a,0)},t.prototype.sign=function(t,e,r){var i=function(t,e){if(e<t.length+22)return null;for(var r=e-t.length-6,i="",n=0;n<r;n+=2)i+="ff";return dt("0001"+i+"00"+t,16)}((Pt[r]||"")+e(t).toString(),this.n.bitLength()/4);if(null==i)return null;var n=this.doPrivate(i);if(null==n)return null;var s=n.toString(16);return 1&s.length?"0"+s:s},t.prototype.verify=function(t,e,r){var i=dt(e,16),n=this.doPublic(i);return null==n?null:function(t){for(var e in Pt)if(Pt.hasOwnProperty(e)){var r=Pt[e],i=r.length;if(t.substr(0,i)==r)return t.substr(i)}return t}
/*!
Copyright (c) 2011, Yahoo! Inc. All rights reserved.
Code licensed under the BSD License:
http://developer.yahoo.com/yui/license.html
version: 2.9.0
*/(n.toString(16).replace(/^1f+00/,""))==r(t).toString()},t}();var Pt={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"};var _t={};_t.lang={extend:function(t,e,r){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var i=function(){};if(i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),r){var n;for(n in r)t.prototype[n]=r[n];var s=function(){},o=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(s=function(t,e){for(n=0;n<o.length;n+=1){var r=o[n],i=e[r];"function"==typeof i&&i!=Object.prototype[r]&&(t[r]=i)}})}catch(a){}s(t.prototype,r)}}};
/**
 * @fileOverview
 * @name asn1-1.0.js
 * <AUTHOR>
 * @version asn1 1.0.13 (2017-Jun-02)
 * @since jsrsasign 2.1
 * @license <a href="https://kjur.github.io/jsrsasign/license/">MIT License</a>
 */
var qt={};void 0!==qt.asn1&&qt.asn1||(qt.asn1={}),qt.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var r=e.substr(1).length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);for(var i="",n=0;n<r;n++)i+="f";e=new ht(i,16).xor(t).add(ht.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return hextopem(t,e)},this.newObject=function(t){var e=qt.asn1,r=e.DERBoolean,i=e.DERInteger,n=e.DERBitString,s=e.DEROctetString,o=e.DERNull,a=e.DERObjectIdentifier,h=e.DEREnumerated,u=e.DERUTF8String,c=e.DERNumericString,l=e.DERPrintableString,f=e.DERTeletexString,p=e.DERIA5String,d=e.DERUTCTime,g=e.DERGeneralizedTime,m=e.DERSequence,v=e.DERSet,y=e.DERTaggedObject,b=e.ASN1Util.newObject,S=Object.keys(t);if(1!=S.length)throw"key of param shall be only one.";var w=S[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+w+":"))throw"undefined key: "+w;if("bool"==w)return new r(t[w]);if("int"==w)return new i(t[w]);if("bitstr"==w)return new n(t[w]);if("octstr"==w)return new s(t[w]);if("null"==w)return new o(t[w]);if("oid"==w)return new a(t[w]);if("enum"==w)return new h(t[w]);if("utf8str"==w)return new u(t[w]);if("numstr"==w)return new c(t[w]);if("prnstr"==w)return new l(t[w]);if("telstr"==w)return new f(t[w]);if("ia5str"==w)return new p(t[w]);if("utctime"==w)return new d(t[w]);if("gentime"==w)return new g(t[w]);if("seq"==w){for(var T=t[w],E=[],D=0;D<T.length;D++){var x=b(T[D]);E.push(x)}return new m({array:E})}if("set"==w){for(T=t[w],E=[],D=0;D<T.length;D++){x=b(T[D]);E.push(x)}return new v({array:E})}if("tag"==w){var A=t[w];if("[object Array]"===Object.prototype.toString.call(A)&&3==A.length){var R=b(A[2]);return new y({tag:A[0],explicit:A[1],obj:R})}var B={};if(void 0!==A.explicit&&(B.explicit=A.explicit),void 0!==A.tag&&(B.tag=A.tag),void 0===A.obj)throw"obj shall be specified for 'tag'.";return B.obj=b(A.obj),new y(B)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},qt.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",r=parseInt(t.substr(0,2),16),i=(e=Math.floor(r/40)+"."+r%40,""),n=2;n<t.length;n+=2){var s=("00000000"+parseInt(t.substr(n,2),16).toString(2)).slice(-8);if(i+=s.substr(1,7),"0"==s.substr(0,1))e=e+"."+new ht(i,2).toString(10),i=""}return e},qt.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",i=new ht(t,10).toString(2),n=7-i.length%7;7==n&&(n=0);for(var s="",o=0;o<n;o++)s+="0";i=s+i;for(o=0;o<i.length-1;o+=7){var a=i.substr(o,7);o!=i.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",n=t.split("."),s=40*parseInt(n[0])+parseInt(n[1]);i+=e(s),n.splice(0,2);for(var o=0;o<n.length;o++)i+=r(n[o]);return i},qt.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n=0,v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var r=e.length/2;if(r>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+r).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},qt.asn1.DERAbstractString=function(t){qt.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},_t.lang.extend(qt.asn1.DERAbstractString,qt.asn1.ASN1Object),qt.asn1.DERAbstractTime=function(t){qt.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var i=this.zeroPadding,n=this.localDateToUTC(t),s=String(n.getFullYear());"utc"==e&&(s=s.substr(2,2));var o=s+i(String(n.getMonth()+1),2)+i(String(n.getDate()),2)+i(String(n.getHours()),2)+i(String(n.getMinutes()),2)+i(String(n.getSeconds()),2);if(!0===r){var a=n.getMilliseconds();if(0!=a){var h=i(String(a),3);o=o+"."+(h=h.replace(/[0]+$/,""))}}return o+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,r,i,n,s){var o=new Date(Date.UTC(t,e-1,r,i,n,s,0));this.setByDate(o)},this.getFreshValueHex=function(){return this.hV}},_t.lang.extend(qt.asn1.DERAbstractTime,qt.asn1.ASN1Object),qt.asn1.DERAbstractStructured=function(t){qt.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},_t.lang.extend(qt.asn1.DERAbstractStructured,qt.asn1.ASN1Object),qt.asn1.DERBoolean=function(){qt.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},_t.lang.extend(qt.asn1.DERBoolean,qt.asn1.ASN1Object),qt.asn1.DERInteger=function(t){qt.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=qt.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new ht(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},_t.lang.extend(qt.asn1.DERInteger,qt.asn1.ASN1Object),qt.asn1.DERBitString=function(t){if(void 0!==t&&void 0!==t.obj){var e=qt.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.getEncodedHex()}qt.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var r="0"+t;this.hTLV=null,this.isModified=!0,this.hV=r+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,"")).length%8;8==e&&(e=0);for(var r=0;r<=e;r++)t+="0";var i="";for(r=0;r<t.length-1;r+=8){var n=t.substr(r,8),s=parseInt(n,2).toString(16);1==s.length&&(s="0"+s),i+=s}this.hTLV=null,this.isModified=!0,this.hV="0"+e+i},this.setByBooleanArray=function(t){for(var e="",r=0;r<t.length;r++)1==t[r]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},_t.lang.extend(qt.asn1.DERBitString,qt.asn1.ASN1Object),qt.asn1.DEROctetString=function(t){if(void 0!==t&&void 0!==t.obj){var e=qt.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}qt.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},_t.lang.extend(qt.asn1.DEROctetString,qt.asn1.DERAbstractString),qt.asn1.DERNull=function(){qt.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},_t.lang.extend(qt.asn1.DERNull,qt.asn1.ASN1Object),qt.asn1.DERObjectIdentifier=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",i=new ht(t,10).toString(2),n=7-i.length%7;7==n&&(n=0);for(var s="",o=0;o<n;o++)s+="0";i=s+i;for(o=0;o<i.length-1;o+=7){var a=i.substr(o,7);o!=i.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};qt.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",n=t.split("."),s=40*parseInt(n[0])+parseInt(n[1]);i+=e(s),n.splice(0,2);for(var o=0;o<n.length;o++)i+=r(n[o]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=i},this.setValueName=function(t){var e=qt.asn1.x509.OID.name2oid(t);if(""===e)throw"DERObjectIdentifier oidName undefined: "+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):void 0!==t.oid?this.setValueOidString(t.oid):void 0!==t.hex?this.setValueHex(t.hex):void 0!==t.name&&this.setValueName(t.name))},_t.lang.extend(qt.asn1.DERObjectIdentifier,qt.asn1.ASN1Object),qt.asn1.DEREnumerated=function(t){qt.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=qt.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new ht(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},_t.lang.extend(qt.asn1.DEREnumerated,qt.asn1.ASN1Object),qt.asn1.DERUTF8String=function(t){qt.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},_t.lang.extend(qt.asn1.DERUTF8String,qt.asn1.DERAbstractString),qt.asn1.DERNumericString=function(t){qt.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},_t.lang.extend(qt.asn1.DERNumericString,qt.asn1.DERAbstractString),qt.asn1.DERPrintableString=function(t){qt.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},_t.lang.extend(qt.asn1.DERPrintableString,qt.asn1.DERAbstractString),qt.asn1.DERTeletexString=function(t){qt.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},_t.lang.extend(qt.asn1.DERTeletexString,qt.asn1.DERAbstractString),qt.asn1.DERIA5String=function(t){qt.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},_t.lang.extend(qt.asn1.DERIA5String,qt.asn1.DERAbstractString),qt.asn1.DERUTCTime=function(t){qt.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},_t.lang.extend(qt.asn1.DERUTCTime,qt.asn1.DERAbstractTime),qt.asn1.DERGeneralizedTime=function(t){qt.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},_t.lang.extend(qt.asn1.DERGeneralizedTime,qt.asn1.DERAbstractTime),qt.asn1.DERSequence=function(t){qt.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++){t+=this.asn1Array[e].getEncodedHex()}return this.hV=t,this.hV}},_t.lang.extend(qt.asn1.DERSequence,qt.asn1.DERAbstractStructured),qt.asn1.DERSet=function(t){qt.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},_t.lang.extend(qt.asn1.DERSet,qt.asn1.DERAbstractStructured),qt.asn1.DERTaggedObject=function(t){qt.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.tag&&(this.hT=t.tag),void 0!==t.explicit&&(this.isExplicit=t.explicit),void 0!==t.obj&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},_t.lang.extend(qt.asn1.DERTaggedObject,qt.asn1.ASN1Object);var Ct,Mt,jt=globalThis&&globalThis.__extends||(Ct=function(t,e){return(Ct=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}Ct(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),Lt=function(t){function e(r){var i=t.call(this)||this;return r&&("string"==typeof r?i.parseKey(r):(e.hasPrivateKeyProperty(r)||e.hasPublicKeyProperty(r))&&i.parsePropertiesFrom(r)),i}return jt(e,t),e.prototype.parseKey=function(t){try{var e=0,r=0,i=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(t)?Q(t):W.unarmor(t),n=nt.decode(i);if(3===n.sub.length&&(n=n.sub[2].sub[0]),9===n.sub.length){e=n.sub[1].getHexStringValue(),this.n=dt(e,16),r=n.sub[2].getHexStringValue(),this.e=parseInt(r,16);var s=n.sub[3].getHexStringValue();this.d=dt(s,16);var o=n.sub[4].getHexStringValue();this.p=dt(o,16);var a=n.sub[5].getHexStringValue();this.q=dt(a,16);var h=n.sub[6].getHexStringValue();this.dmp1=dt(h,16);var u=n.sub[7].getHexStringValue();this.dmq1=dt(u,16);var c=n.sub[8].getHexStringValue();this.coeff=dt(c,16)}else{if(2!==n.sub.length)return!1;if(n.sub[0].sub){var l=n.sub[1].sub[0];e=l.sub[0].getHexStringValue(),this.n=dt(e,16),r=l.sub[1].getHexStringValue(),this.e=parseInt(r,16)}else e=n.sub[0].getHexStringValue(),this.n=dt(e,16),r=n.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(f){return!1}},e.prototype.getPrivateBaseKey=function(){var t={array:[new qt.asn1.DERInteger({int:0}),new qt.asn1.DERInteger({bigint:this.n}),new qt.asn1.DERInteger({int:this.e}),new qt.asn1.DERInteger({bigint:this.d}),new qt.asn1.DERInteger({bigint:this.p}),new qt.asn1.DERInteger({bigint:this.q}),new qt.asn1.DERInteger({bigint:this.dmp1}),new qt.asn1.DERInteger({bigint:this.dmq1}),new qt.asn1.DERInteger({bigint:this.coeff})]};return new qt.asn1.DERSequence(t).getEncodedHex()},e.prototype.getPrivateBaseKeyB64=function(){return G(this.getPrivateBaseKey())},e.prototype.getPublicBaseKey=function(){var t=new qt.asn1.DERSequence({array:[new qt.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new qt.asn1.DERNull]}),e=new qt.asn1.DERSequence({array:[new qt.asn1.DERInteger({bigint:this.n}),new qt.asn1.DERInteger({int:this.e})]}),r=new qt.asn1.DERBitString({hex:"00"+e.getEncodedHex()});return new qt.asn1.DERSequence({array:[t,r]}).getEncodedHex()},e.prototype.getPublicBaseKeyB64=function(){return G(this.getPublicBaseKey())},e.wordwrap=function(t,e){if(!t)return t;var r="(.{1,"+(e=e||64)+"})( +|$\n?)|(.{1,"+e+"})";return t.match(RegExp(r,"g")).join("\n")},e.prototype.getPrivateKey=function(){var t="-----BEGIN RSA PRIVATE KEY-----\n";return t+=e.wordwrap(this.getPrivateBaseKeyB64())+"\n",t+="-----END RSA PRIVATE KEY-----"},e.prototype.getPublicKey=function(){var t="-----BEGIN PUBLIC KEY-----\n";return t+=e.wordwrap(this.getPublicBaseKeyB64())+"\n",t+="-----END PUBLIC KEY-----"},e.hasPublicKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")},e.hasPrivateKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")&&t.hasOwnProperty("d")&&t.hasOwnProperty("p")&&t.hasOwnProperty("q")&&t.hasOwnProperty("dmp1")&&t.hasOwnProperty("dmq1")&&t.hasOwnProperty("coeff")},e.prototype.parsePropertiesFrom=function(t){this.n=t.n,this.e=t.e,t.hasOwnProperty("d")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)},e}(Nt),Ht="undefined"!=typeof process?null===(Mt=process.env)||void 0===Mt?void 0:Mt.npm_package_version:void 0;!function(){function t(t){void 0===t&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}t.prototype.setKey=function(t){this.log&&this.key,this.key=new Lt(t)},t.prototype.setPrivateKey=function(t){this.setKey(t)},t.prototype.setPublicKey=function(t){this.setKey(t)},t.prototype.decrypt=function(t){try{return this.getKey().decrypt(Z(t))}catch(e){return!1}},t.prototype.encrypt=function(t){try{return G(this.getKey().encrypt(t))}catch(e){return!1}},t.prototype.sign=function(t,e,r){try{return G(this.getKey().sign(t,e,r))}catch(i){return!1}},t.prototype.verify=function(t,e,r){try{return this.getKey().verify(t,Z(e),r)}catch(i){return!1}},t.prototype.getKey=function(t){if(!this.key){if(this.key=new Lt,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},t.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},t.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},t.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},t.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},t.version=Ht}();function Ut(){try{if(!window.crypto||!window.crypto.subtle)return!1;if(!s||!s.lib||!s.lib.WordArray)return!1;if(!window.crypto.getRandomValues)return!1;const t=new Uint8Array(32);return window.crypto.getRandomValues(t),!t.every(t=>0===t)}catch(t){return!1}}function kt(t){const e=t.replace(/-----BEGIN PUBLIC KEY-----/g,"").replace(/-----END PUBLIC KEY-----/g,"").replace(/\s/g,""),r=atob(e),i=new Uint8Array(r.length);for(let n=0;n<r.length;n++)i[n]=r.charCodeAt(n);return i.buffer}const Kt=new Set;function Ft(){try{const t=document.createElement("canvas"),e=t.getContext("webgl")||t.getContext("experimental-webgl");if(!e)return"not_supported";const r=e.getExtension("WEBGL_debug_renderer_info"),i=r?e.getParameter(r.UNMASKED_VENDOR_WEBGL):"unknown";return`${i}_${r?e.getParameter(r.UNMASKED_RENDERER_WEBGL):"unknown"}`.substring(0,50)}catch(t){return"webgl_error"}}function zt(){try{const t=["Arial","Times New Roman","Courier New","Helvetica","Georgia"],e=document.createElement("canvas").getContext("2d");return t.map(t=>(e.font=`12px ${t}`,e.measureText("QQ Auth Test").width)).join(",")}catch(t){return"font_error"}}function Gt(){try{if(!navigator.plugins)return"no_plugins";return Array.from(navigator.plugins).map(t=>t.name).sort().slice(0,5).join(",").substring(0,100)}catch(t){return"plugin_error"}}async function Zt(t,e,r){performance.now();try{if(!t||!e||!r)throw new Error("认证参数不完整");if(r.length<6||r.length>128)throw new Error("密码长度不符合安全要求");const i=await async function(){try{const t=await fetch("/api/auth/encryption-info"),e=await t.json();if(!e.success)throw new Error("获取公钥失败");return e.data.publicKey}catch(t){throw t}}(),n=function(){try{const t=new Uint8Array(32);if(window.crypto.getRandomValues(t),t.every(t=>0===t)||t.every(e=>e===t[0]))throw new Error("密钥质量不足，重新生成");return s.lib.WordArray.create(t)}catch(t){throw new Error("密钥生成失败: "+t.message)}}(),o=await async function(t,e){let r=null;for(let n=1;n<=3;n++)try{if(!e||!e.includes("BEGIN PUBLIC KEY"))throw new Error("无效的RSA公钥格式");const r=kt(e);if(r.byteLength<290||r.byteLength>310)throw new Error("RSA密钥长度不符合银行级标准（需要2048位）");const i=await window.crypto.subtle.importKey("spki",r,{name:"RSA-OAEP",hash:"SHA-256"},!1,["encrypt"]),n=s.enc.Base64.stringify(t),o=(new TextEncoder).encode(n);if(44!==o.byteLength)throw new Error("AES密钥长度不正确");const a=await window.crypto.subtle.encrypt({name:"RSA-OAEP"},i,o);if(256!==a.byteLength)throw new Error("RSA加密结果长度异常");const h=new Uint8Array(a);return btoa(String.fromCharCode.apply(null,h))}catch(i){r=i,n<3&&await new Promise(t=>setTimeout(t,100*n))}throw new Error("RSA-OAEP加密失败: "+r.message)}(n,i),{encryptedPassword:a,iv:h,tag:u}=await async function(t,e){let r=null;for(let n=1;n<=3;n++)try{const r=new Uint8Array(12);window.crypto.getRandomValues(r);const i=Array.from(r).map(t=>t.toString(16).padStart(2,"0")).join("");if(Kt.has(i))throw new Error("IV重复，重新生成");if(r.every(t=>0===t)||r.every(t=>t===r[0]))throw new Error("IV质量不足，重新生成");const n=s.enc.Base64.stringify(e),o=Uint8Array.from(atob(n),t=>t.charCodeAt(0));if(32!==o.length)throw new Error("AES密钥长度不正确");const a=await window.crypto.subtle.importKey("raw",o,{name:"AES-GCM"},!1,["encrypt"]),h=(new TextEncoder).encode(t);if(h.length>1024)throw new Error("密码长度超出安全限制");const u=await window.crypto.subtle.encrypt({name:"AES-GCM",iv:r,tagLength:128},a,h),c=new Uint8Array(u),l=c.slice(0,-16),f=c.slice(-16);if(16!==f.length)throw new Error("认证标签长度不正确");return Kt.add(i),Kt.size>1e4&&Array.from(Kt).slice(0,5e3).forEach(t=>Kt.delete(t)),{encryptedPassword:btoa(String.fromCharCode.apply(null,l)),iv:i,tag:Array.from(f).map(t=>t.toString(16).padStart(2,"0")).join("")}}catch(i){r=i,n<3&&await new Promise(t=>setTimeout(t,50*n))}throw new Error("AES-GCM加密失败: "+r.message)}(r,n),c=function(){try{const t=document.createElement("canvas"),e=t.getContext("2d");e.textBaseline="top",e.font="14px Arial",e.fillText("QQ Auth System Enhanced Fingerprint",2,2);const r={screen:`${screen.width}x${screen.height}x${screen.colorDepth}`,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,language:navigator.language,languages:navigator.languages?navigator.languages.join(","):"",platform:navigator.platform,userAgent:navigator.userAgent.substring(0,100),canvas:t.toDataURL().substring(0,100),webgl:Ft(),fonts:zt(),plugins:Gt(),timestamp:Date.now()};return btoa(JSON.stringify(r))}catch(t){return btoa(JSON.stringify({error:"fingerprint_generation_failed",timestamp:Date.now()}))}}(),l=Date.now(),f=Array.from(window.crypto.getRandomValues(new Uint8Array(16))).map(t=>t.toString(16).padStart(2,"0")).join(""),p={qq:t,username:e,encryptedAESKey:o,encryptedPassword:a,iv:h,tag:u,algorithm:"RSA-2048-OAEP+AES-256-GCM",timestamp:l,nonce:f,fingerprint:c,version:"2.0",integrity:null},d=JSON.stringify({qq:t,username:e,encryptedAESKey:o,encryptedPassword:a,iv:h,tag:u,timestamp:l,nonce:f,fingerprint:c}),g=(new TextEncoder).encode(d),m=await window.crypto.subtle.digest("SHA-256",g),v=new Uint8Array(m);p.integrity=Array.from(v).map(t=>t.toString(16).padStart(2,"0")).join("");performance.now();return p}catch(i){throw new Error("银行级加密失败: "+i.message)}}const $t={name:"AuthProgress",props:{visible:{type:Boolean,default:!1},title:{type:String,default:"SSO认证进行中"},currentStep:{type:Number,default:0},steps:{type:Array,default:()=>[{title:"连接认证服务",description:"正在连接到WebVPN服务..."},{title:"CAS登录验证",description:"正在验证用户凭据..."},{title:"获取学生信息",description:"正在从教务系统获取信息..."},{title:"创建用户记录",description:"正在保存认证信息..."},{title:"认证完成",description:"认证流程已完成"}]},error:{type:String,default:null},success:{type:Boolean,default:!1},successData:{type:Object,default:null},canRetry:{type:Boolean,default:!0}},emits:["retry","cancel","close"],setup(t,{emit:e}){const r=a(t.visible),i=h(()=>t.success?100:t.error?t.currentStep/t.steps.length*100:(t.currentStep+1)/t.steps.length*100),n=h(()=>t.success?"100% - 认证成功":t.error?`${Math.round(i.value)}% - 认证失败`:`${Math.round(i.value)}% - 正在进行...`);u(()=>t.visible,t=>{r.value=t});return{isVisible:r,progress:i,progressText:n,retry:()=>{e("retry")},cancel:()=>{e("cancel"),r.value=!1}}}},Qt={class:"auth-progress-container"},Wt={key:0,class:"progress-indicator"},Yt={class:"progress-modal"},Jt={class:"progress-header"},Xt={class:"progress-bar"},te={class:"progress-text"},ee={class:"progress-steps"},re={class:"step-icon"},ie={key:0,class:"el-icon-check"},ne={key:1,class:"el-icon-loading"},se={key:2},oe={class:"step-content"},ae={class:"step-title"},he={class:"step-description"},ue={key:0,class:"step-details"},ce={key:0,class:"error-section"},le={class:"error-message"},fe={class:"error-actions"},pe={key:1,class:"success-section"},de={key:0,class:"success-details"};const ge=t($t,[["render",function(t,e,r,i,n,s){return c(),l("div",Qt,[i.isVisible?(c(),l("div",Wt,[f("div",Yt,[f("div",Jt,[f("h3",null,p(r.title),1),f("div",Xt,[f("div",{class:"progress-fill",style:d({width:`${i.progress}%`})},null,4)]),f("div",te,p(i.progressText),1)]),f("div",ee,[(c(!0),l(g,null,m(r.steps,(t,e)=>(c(),l("div",{key:e,class:v(["step-item",{active:r.currentStep===e,completed:r.currentStep>e,pending:r.currentStep<e}])},[f("div",re,[r.currentStep>e?(c(),l("i",ie)):r.currentStep===e?(c(),l("i",ne)):(c(),l("span",se,p(e+1),1))]),f("div",oe,[f("div",ae,p(t.title),1),f("div",he,p(t.description),1),r.currentStep===e&&t.details?(c(),l("div",ue,p(t.details),1)):y("",!0)])],2))),128))]),r.error?(c(),l("div",ce,[f("div",le,[e[2]||(e[2]=f("i",{class:"el-icon-warning"},null,-1)),b(" "+p(r.error),1)]),f("div",fe,[r.canRetry?(c(),l("button",{key:0,onClick:e[0]||(e[0]=(...t)=>i.retry&&i.retry(...t)),class:"retry-btn"},"重试")):y("",!0),f("button",{onClick:e[1]||(e[1]=(...t)=>i.cancel&&i.cancel(...t)),class:"cancel-btn"},"取消")])])):y("",!0),r.success?(c(),l("div",pe,[e[6]||(e[6]=f("div",{class:"success-message"},[f("i",{class:"el-icon-success"}),b(" 认证成功！ ")],-1)),r.successData?(c(),l("div",de,[f("p",null,[e[3]||(e[3]=f("strong",null,"姓名:",-1)),b(" "+p(r.successData.name),1)]),f("p",null,[e[4]||(e[4]=f("strong",null,"学号:",-1)),b(" "+p(r.successData.studentId),1)]),f("p",null,[e[5]||(e[5]=f("strong",null,"学院:",-1)),b(" "+p(r.successData.college),1)])])):y("",!0)])):y("",!0)])])):y("",!0)])}],["__scopeId","data-v-518e5a07"]]),me={class:"step-select-method"},ve={class:"method-cards"},ye={class:"method-icon"},be={class:"method-icon"},Se={class:"form-actions"},we=t({__name:"StepSelectMethod",props:{configs:Object,selectedMethod:String},emits:["select-method","next-step"],setup(t,{emit:e}){const r=e,i=t=>{r("select-method",t)},n=()=>{r("next-step")};return(e,r)=>{const s=x,o=A;return c(),l("div",me,[r[9]||(r[9]=f("div",{class:"method-header"},[f("h3",null,"请选择认证方式"),f("p",null,"选择最适合您的身份验证方式")],-1)),f("div",ve,[t.configs.buaa_sso_enabled?(c(),l("div",{key:0,class:v(["method-card",{active:"sso"===t.selectedMethod}]),onClick:r[0]||(r[0]=t=>i("sso"))},[f("div",ye,[S(s,{size:"32",color:"#409EFF"},{default:w(()=>[S(T(E))]),_:1})]),r[2]||(r[2]=f("h4",null,"SSO统一认证",-1)),r[3]||(r[3]=f("p",null,"使用北航统一身份认证系统登录",-1)),r[4]||(r[4]=f("div",{class:"method-features"},[f("span",{class:"feature-tag"},"推荐"),f("span",{class:"feature-tag"},"安全"),f("span",{class:"feature-tag"},"快速")],-1))],2)):y("",!0),t.configs.buaa_email_enabled?(c(),l("div",{key:1,class:v(["method-card",{active:"email"===t.selectedMethod}]),onClick:r[1]||(r[1]=t=>i("email"))},[f("div",be,[S(s,{size:"32",color:"#67C23A"},{default:w(()=>[S(T(D))]),_:1})]),r[5]||(r[5]=f("h4",null,"邮箱验证",-1)),r[6]||(r[6]=f("p",null,"使用@buaa.edu.cn邮箱进行验证",-1)),r[7]||(r[7]=f("div",{class:"method-features"},[f("span",{class:"feature-tag"},"便捷"),f("span",{class:"feature-tag"},"可靠")],-1))],2)):y("",!0)]),f("div",Se,[S(o,{type:"primary",size:"large",disabled:!t.selectedMethod,onClick:n},{default:w(()=>r[8]||(r[8]=[b(" 下一步 ",-1)])),_:1,__:[8]},8,["disabled"])])])}}},[["__scopeId","data-v-1ef155cd"]]),Te={class:"step-sso"},Ee={class:"step-header"},De={class:"step-icon"},xe={class:"security-notice"},Ae={class:"form-actions"},Re=t({__name:"StepSSO",props:{loading:Boolean},emits:["prev-step","next-step","sso-login"],setup(t,{emit:e}){const r=e,i=a(),n=R({qq:"",username:"",password:""}),s={qq:[{required:!0,message:"请输入QQ号",trigger:"blur"},{pattern:/^[1-9][0-9]{4,10}$/,message:"QQ号格式不正确",trigger:"blur"}],username:[{required:!0,message:"请输入学号",trigger:"blur"},{min:6,max:20,message:"学号长度应为6-20位",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]},o=()=>{r("prev-step")},h=async()=>{if(!i.value)return;await i.value.validate().catch(()=>!1)&&r("sso-login",n)};return(e,r)=>{const a=x,u=I,d=N,g=P,m=A;return c(),l("div",Te,[f("div",Ee,[f("div",De,[S(a,{size:"48",color:"#409EFF"},{default:w(()=>[S(T(E))]),_:1})]),r[3]||(r[3]=f("h3",null,"SSO统一认证",-1)),r[4]||(r[4]=f("p",null,"请输入您的统一身份认证信息",-1))]),S(g,{ref_key:"ssoFormRef",ref:i,model:n,rules:s,"label-position":"top",class:"sso-form"},{default:w(()=>[S(d,{label:"QQ号",prop:"qq"},{default:w(()=>[S(u,{modelValue:n.qq,"onUpdate:modelValue":r[0]||(r[0]=t=>n.qq=t),placeholder:"请输入您的QQ号",size:"large","prefix-icon":T(B),clearable:""},null,8,["modelValue","prefix-icon"])]),_:1}),S(d,{label:"学号",prop:"username"},{default:w(()=>[S(u,{modelValue:n.username,"onUpdate:modelValue":r[1]||(r[1]=t=>n.username=t),placeholder:"请输入您的学号",size:"large","prefix-icon":T(V),clearable:""},null,8,["modelValue","prefix-icon"])]),_:1}),S(d,{label:"密码",prop:"password"},{default:w(()=>[S(u,{modelValue:n.password,"onUpdate:modelValue":r[2]||(r[2]=t=>n.password=t),type:"password",placeholder:"请输入您的统一身份认证密码",size:"large","prefix-icon":T(E),"show-password":"",clearable:""},null,8,["modelValue","prefix-icon"])]),_:1})]),_:1},8,["model"]),f("div",xe,[S(a,{size:"16",color:"#E6A23C"},{default:w(()=>[S(T(O))]),_:1}),r[5]||(r[5]=f("span",null,"您的密码将通过加密传输，我们不会存储您的密码信息",-1))]),f("div",Ae,[S(m,{size:"large",onClick:o},{default:w(()=>r[6]||(r[6]=[b(" 上一步 ",-1)])),_:1,__:[6]}),S(m,{type:"primary",size:"large",loading:t.loading,onClick:h},{default:w(()=>[b(p(t.loading?"验证中...":"开始验证"),1)]),_:1},8,["loading"])])])}}},[["__scopeId","data-v-b0dbbe64"]]),Be={key:2,class:"placeholder-content"},Ve={class:"form-actions"},Oe=t({__name:"BuaaAuth",setup(t){e();const s=r(),u=a(0),d=a(""),m=a(!1),v=a(!1),T=a(0),E=a(null),D=a(!1),x=a(null),B=[{title:"连接认证服务",description:"正在连接到WebVPN服务..."},{title:"CAS登录验证",description:"正在验证用户凭据..."},{title:"获取学生信息",description:"正在从教务系统获取信息..."},{title:"创建用户记录",description:"正在保存认证信息..."},{title:"认证完成",description:"认证流程已完成"}],V=["选择认证方式","身份验证","手机验证","认证完成"],O=R({qq:"",username:"",password:""});a(null);const I=h(()=>s.configs),N=t=>{d.value=t},P=()=>{u.value<3&&u.value++},M=()=>{u.value>0&&u.value--},j=async t=>{var e,r,s,o,a,h,u;T.value=0,E.value=null,D.value=!1,x.value=null,v.value=!0,m.value=!0;let c=null;try{let e;c=setInterval(()=>{T.value<B.length-1&&T.value++},800);Ut(),window.CryptoJS,window.JSEncrypt,!window.crypto||window.crypto.getRandomValues,Ut();if(!Ut())throw new Error("浏览器不支持银行级混合加密，请使用现代浏览器");e=await Zt(t.qq,t.username,t.password);const r=await i.post("/api/auth/sso",e);if(c&&clearInterval(c),r.success){T.value=B.length-1,D.value=!0,x.value=r.data;try{const{useUserSessionStore:t}=await n(()=>import("./userSession-03354358.js"),["assets/userSession-03354358.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css"]);t().setEditToken(r.editToken,r.editTokenExpiresIn)}catch(l){}setTimeout(()=>{C.success("SSO认证成功"),O.qq=t.qq,O.username=t.username,setTimeout(()=>{v.value=!1,P()},1200)},600)}else E.value=r.data.message||"SSO认证失败",D.value=!1}catch(f){c&&clearInterval(c);const t=(null==(r=null==(e=f.response)?void 0:e.data)?void 0:r.message)||f.message||"SSO认证失败",i=(null==(o=null==(s=f.response)?void 0:s.data)?void 0:o.error)||"UNKNOWN_ERROR";E.value=t,D.value=!1;let n=t;"SSO_AUTH_FAILED"===i?n="用户名或密码错误，请检查后重试":401===(null==(a=f.response)?void 0:a.status)?n="认证失败：用户名或密码错误":423===(null==(h=f.response)?void 0:h.status)?n="账户被锁定，请稍后重试":429===(null==(u=f.response)?void 0:u.status)?n="请求过于频繁，请稍后再试":t.includes("timeout")&&(n="网络超时，请检查网络连接后重试"),C.error(n)}finally{m.value=!1}},L=()=>{O.qq&&O.username&&O.password&&j(O)},H=()=>{v.value=!1,E.value=null,D.value=!1};return _(async()=>{s.lastUpdated||await s.init()}),(t,e)=>{const r=A;return c(),l(g,null,[S(o,{title:"本校学生身份认证",subtitle:"Current Student Authentication","page-title":"本校学生认证","page-description":"请选择认证方式完成身份验证","icon-component":"School",features:["安全可靠的身份验证","统一身份认证系统","自动获取学籍信息"],steps:V,"current-step":u.value,"show-steps":!0},{default:w(()=>[0===u.value?(c(),q(we,{key:0,configs:I.value,"selected-method":d.value,onSelectMethod:N,onNextStep:P},null,8,["configs","selected-method"])):y("",!0),1===u.value&&"sso"===d.value?(c(),q(Re,{key:1,loading:m.value,onPrevStep:M,onSsoLogin:j},null,8,["loading"])):y("",!0),u.value>1?(c(),l("div",Be,[f("h3",null,"步骤 "+p(u.value+1),1),f("p",null,p(V[u.value]),1),f("div",Ve,[S(r,{onClick:M},{default:w(()=>e[0]||(e[0]=[b("上一步",-1)])),_:1,__:[0]}),S(r,{type:"primary",onClick:P},{default:w(()=>e[1]||(e[1]=[b("下一步",-1)])),_:1,__:[1]})])])):y("",!0)]),_:1},8,["current-step"]),S(ge,{visible:v.value,title:"SSO认证进行中","current-step":T.value,steps:B,error:E.value,success:D.value,"success-data":x.value,"can-retry":!!E.value,onRetry:L,onCancel:H},null,8,["visible","current-step","error","success","success-data","can-retry"])],64)}}},[["__scopeId","data-v-db080023"]]);export{Oe as default};
