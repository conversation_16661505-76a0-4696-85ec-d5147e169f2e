{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-attrs/index.ts"], "sourcesContent": ["import { computed, getCurrentInstance } from 'vue'\nimport { fromPairs } from 'lodash-unified'\nimport { debugWarn } from '@element-plus/utils'\n\nimport type { ComputedRef } from 'vue'\n\ninterface Params {\n  excludeListeners?: boolean\n  excludeKeys?: ComputedRef<string[]>\n}\n\nconst DEFAULT_EXCLUDE_KEYS = ['class', 'style']\nconst LISTENER_PREFIX = /^on[A-Z]/\n\nexport const useAttrs = (\n  params: Params = {}\n): ComputedRef<Record<string, unknown>> => {\n  const { excludeListeners = false, excludeKeys } = params\n  const allExcludeKeys = computed<string[]>(() => {\n    return (excludeKeys?.value || []).concat(DEFAULT_EXCLUDE_KEYS)\n  })\n\n  const instance = getCurrentInstance()\n  if (!instance) {\n    debugWarn(\n      'use-attrs',\n      'getCurrentInstance() returned null. useAttrs() must be called at the top of a setup function'\n    )\n    return computed(() => ({}))\n  }\n\n  return computed(() =>\n    fromPairs(\n      Object.entries(instance.proxy?.$attrs!).filter(\n        ([key]) =>\n          !allExcludeKeys.value.includes(key) &&\n          !(excludeListeners && LISTENER_PREFIX.test(key))\n      )\n    )\n  )\n}\n"], "names": ["computed", "getCurrentInstance", "fromPairs"], "mappings": ";;;;;;;AAGA,MAAM,oBAAoB,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAChD,MAAM,eAAe,GAAG,UAAU,CAAC;AACvB,MAAC,QAAQ,GAAG,CAAC,MAAM,GAAG,EAAE,KAAK;AACzC,EAAE,MAAM,EAAE,gBAAgB,GAAG,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC;AAC3D,EAAE,MAAM,cAAc,GAAGA,YAAQ,CAAC,MAAM;AACxC,IAAI,OAAO,CAAC,CAAC,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,KAAK,EAAE,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACnG,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,QAAQ,GAAGC,sBAAkB,EAAE,CAAC;AACxC,EAAE,IAAI,CAAC,QAAQ,EAAE;AAEjB,IAAI,OAAOD,YAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,OAAOA,YAAQ,CAAC,MAAM;AACxB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAOE,uBAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpM,GAAG,CAAC,CAAC;AACL;;;;"}