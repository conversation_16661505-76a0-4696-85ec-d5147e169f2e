import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { DescribeAlarmNotifyHistoriesRequest, DescribeAlarmNotifyHistoriesResponse } from "./monitor_models";
/**
 * monitor client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 按需查询告警的通知历史
     */
    DescribeAlarmNotifyHistories(req: DescribeAlarmNotifyHistoriesRequest, cb?: (error: string, rep: DescribeAlarmNotifyHistoriesResponse) => void): Promise<DescribeAlarmNotifyHistoriesResponse>;
}
