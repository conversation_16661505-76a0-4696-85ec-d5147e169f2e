{"version": 3, "file": "use-checked-change.js", "sources": ["../../../../../../../packages/components/transfer/src/composables/use-checked-change.ts"], "sourcesContent": ["import { LEFT_CHECK_CHANGE_EVENT, RIGHT_CHECK_CHANGE_EVENT } from '../transfer'\n\nimport type { SetupContext } from 'vue'\nimport type {\n  TransferCheckedState,\n  TransferEmits,\n  TransferKey,\n} from '../transfer'\n\nexport const useCheckedChange = (\n  checkedState: TransferCheckedState,\n  emit: SetupContext<TransferEmits>['emit']\n) => {\n  const onSourceCheckedChange = (\n    val: TransferKey[],\n    movedKeys?: TransferKey[]\n  ) => {\n    checkedState.leftChecked = val\n    if (!movedKeys) return\n    emit(LEFT_CHECK_CHANGE_EVENT, val, movedKeys)\n  }\n\n  const onTargetCheckedChange = (\n    val: TransferKey[],\n    movedKeys?: TransferKey[]\n  ) => {\n    checkedState.rightChecked = val\n    if (!movedKeys) return\n    emit(RIGHT_CHECK_CHANGE_EVENT, val, movedKeys)\n  }\n\n  return {\n    onSourceCheckedChange,\n    onTargetCheckedChange,\n  }\n}\n"], "names": ["LEFT_CHECK_CHANGE_EVENT", "RIGHT_CHECK_CHANGE_EVENT"], "mappings": ";;;;;;AACY,MAAC,gBAAgB,GAAG,CAAC,YAAY,EAAE,IAAI,KAAK;AACxD,EAAE,MAAM,qBAAqB,GAAG,CAAC,GAAG,EAAE,SAAS,KAAK;AACpD,IAAI,YAAY,CAAC,WAAW,GAAG,GAAG,CAAC;AACnC,IAAI,IAAI,CAAC,SAAS;AAClB,MAAM,OAAO;AACb,IAAI,IAAI,CAACA,gCAAuB,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;AAClD,GAAG,CAAC;AACJ,EAAE,MAAM,qBAAqB,GAAG,CAAC,GAAG,EAAE,SAAS,KAAK;AACpD,IAAI,YAAY,CAAC,YAAY,GAAG,GAAG,CAAC;AACpC,IAAI,IAAI,CAAC,SAAS;AAClB,MAAM,OAAO;AACb,IAAI,IAAI,CAACC,iCAAwB,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;AACnD,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,GAAG,CAAC;AACJ;;;;"}