import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("trro.tencentcloudapi.com", "2022-03-25", clientConfig);
    }
    async ModifyCallbackUrl(req, cb) {
        return this.request("ModifyCallbackUrl", req, cb);
    }
    async DescribeProjectInfo(req, cb) {
        return this.request("DescribeProjectInfo", req, cb);
    }
    async GetDevices(req, cb) {
        return this.request("GetDevices", req, cb);
    }
    async StopPublishLiveStream(req, cb) {
        return this.request("StopPublishLiveStream", req, cb);
    }
    async ModifyDevice(req, cb) {
        return this.request("ModifyDevice", req, cb);
    }
    async DescribePolicy(req, cb) {
        return this.request("DescribePolicy", req, cb);
    }
    async GetLicenseStat(req, cb) {
        return this.request("GetLicenseStat", req, cb);
    }
    async DescribeSessionStatisticsByInterval(req, cb) {
        return this.request("DescribeSessionStatisticsByInterval", req, cb);
    }
    async BoundLicenses(req, cb) {
        return this.request("BoundLicenses", req, cb);
    }
    async BatchDeletePolicy(req, cb) {
        return this.request("BatchDeletePolicy", req, cb);
    }
    async StartPublishLiveStream(req, cb) {
        return this.request("StartPublishLiveStream", req, cb);
    }
    async GetLicenses(req, cb) {
        return this.request("GetLicenses", req, cb);
    }
    async CreateCloudRecording(req, cb) {
        return this.request("CreateCloudRecording", req, cb);
    }
    async DescribeDeviceList(req, cb) {
        return this.request("DescribeDeviceList", req, cb);
    }
    async ModifyProject(req, cb) {
        return this.request("ModifyProject", req, cb);
    }
    async DescribeDeviceSessionDetails(req, cb) {
        return this.request("DescribeDeviceSessionDetails", req, cb);
    }
    async ModifyPolicy(req, cb) {
        return this.request("ModifyPolicy", req, cb);
    }
    async CreateDevice(req, cb) {
        return this.request("CreateDevice", req, cb);
    }
    async BatchDeleteDevices(req, cb) {
        return this.request("BatchDeleteDevices", req, cb);
    }
    async DescribeSessionStatistics(req, cb) {
        return this.request("DescribeSessionStatistics", req, cb);
    }
    async DescribeDeviceInfo(req, cb) {
        return this.request("DescribeDeviceInfo", req, cb);
    }
    async ModifyProjectSecMode(req, cb) {
        return this.request("ModifyProjectSecMode", req, cb);
    }
    async DescribeDeviceSessionList(req, cb) {
        return this.request("DescribeDeviceSessionList", req, cb);
    }
    async DeleteCloudRecording(req, cb) {
        return this.request("DeleteCloudRecording", req, cb);
    }
    async CreateProject(req, cb) {
        return this.request("CreateProject", req, cb);
    }
    async DescribeRecentSessionList(req, cb) {
        return this.request("DescribeRecentSessionList", req, cb);
    }
    async GetDeviceLicense(req, cb) {
        return this.request("GetDeviceLicense", req, cb);
    }
    async DeleteProject(req, cb) {
        return this.request("DeleteProject", req, cb);
    }
    async DescribeProjectList(req, cb) {
        return this.request("DescribeProjectList", req, cb);
    }
}
