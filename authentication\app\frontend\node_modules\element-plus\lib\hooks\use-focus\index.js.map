{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-focus/index.ts"], "sourcesContent": ["import type { Ref } from 'vue'\n\nexport const useFocus = (\n  el: Ref<{\n    focus: () => void\n  } | null>\n) => {\n  return {\n    focus: () => {\n      el.value?.focus?.()\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;AAAY,MAAC,QAAQ,GAAG,CAAC,EAAE,KAAK;AAChC,EAAE,OAAO;AACT,IAAI,KAAK,EAAE,MAAM;AACjB,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACxF,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}