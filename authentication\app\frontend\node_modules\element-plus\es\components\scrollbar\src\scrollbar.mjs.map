{"version": 3, "file": "scrollbar.mjs", "sources": ["../../../../../../packages/components/scrollbar/src/scrollbar.ts"], "sourcesContent": ["import { buildProps, definePropType, isNumber } from '@element-plus/utils'\nimport { useAriaProps } from '@element-plus/hooks'\n\nimport type {\n  ExtractPropTypes,\n  StyleValue,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type Scrollbar from './scrollbar.vue'\n\nexport const scrollbarProps = buildProps({\n  /**\n   * @description trigger distance(px)\n   */\n  distance: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description height of scrollbar\n   */\n  height: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description max height of scrollbar\n   */\n  maxHeight: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description whether to use the native scrollbar\n   */\n  native: Boolean,\n  /**\n   * @description style of wrap\n   */\n  wrapStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n    default: '',\n  },\n  /**\n   * @description class of wrap\n   */\n  wrapClass: {\n    type: [String, Array],\n    default: '',\n  },\n  /**\n   * @description class of view\n   */\n  viewClass: {\n    type: [String, Array],\n    default: '',\n  },\n  /**\n   * @description style of view\n   */\n  viewStyle: {\n    type: [String, Array, Object],\n    default: '',\n  },\n  /**\n   * @description do not respond to container size changes, if the container size does not change, it is better to set it to optimize performance\n   */\n  noresize: Boolean, // 如果 container 尺寸不会发生变化，最好设置它可以优化性能\n  /**\n   * @description element tag of the view\n   */\n  tag: {\n    type: String,\n    default: 'div',\n  },\n  /**\n   * @description always show\n   */\n  always: Boolean,\n  /**\n   * @description minimum size of scrollbar\n   */\n  minSize: {\n    type: Number,\n    default: 20,\n  },\n  /**\n   * @description Wrap tabindex\n   */\n  tabindex: {\n    type: [String, Number],\n    default: undefined,\n  },\n  /**\n   * @description id of view\n   */\n  id: String,\n  /**\n   * @description role of view\n   */\n  role: String,\n  ...useAriaProps(['ariaLabel', 'ariaOrientation']),\n} as const)\nexport type ScrollbarProps = ExtractPropTypes<typeof scrollbarProps>\nexport type ScrollbarPropsPublic = __ExtractPublicPropTypes<\n  typeof scrollbarProps\n>\n\nexport const scrollbarEmits = {\n  'end-reached': (direction: ScrollbarDirection) =>\n    ['left', 'right', 'top', 'bottom'].includes(direction),\n  scroll: ({\n    scrollTop,\n    scrollLeft,\n  }: {\n    scrollTop: number\n    scrollLeft: number\n  }) => [scrollTop, scrollLeft].every(isNumber),\n}\nexport type ScrollbarEmits = typeof scrollbarEmits\nexport type ScrollbarDirection = 'top' | 'bottom' | 'left' | 'right'\n\nexport type ScrollbarInstance = InstanceType<typeof Scrollbar> & unknown\n"], "names": [], "mappings": ";;;;AAEY,MAAC,cAAc,GAAG,UAAU,CAAC;AACzC,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;AACzB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;AACzB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;AACjC,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,EAAE,EAAE,MAAM;AACZ,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,GAAG,YAAY,CAAC,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AACnD,CAAC,EAAE;AACS,MAAC,cAAc,GAAG;AAC9B,EAAE,aAAa,EAAE,CAAC,SAAS,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC;AACtF,EAAE,MAAM,EAAE,CAAC;AACX,IAAI,SAAS;AACb,IAAI,UAAU;AACd,GAAG,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC/C;;;;"}