"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.debouncedCallback = exports.debouncedAnimationFrame = void 0;
/**
 * Wrap a function in debouncing trigger function. A requestAnimationFrame() is scheduled
 * after the first schedule() call, and subsequent schedule() calls will be ignored until the
 * animation callback executes.
 */
function debouncedAnimationFrame(cb) {
    return buildScheduler(function (cb, _delayMs) { return requestAnimationFrame(cb); }, cb);
}
exports.debouncedAnimationFrame = debouncedAnimationFrame;
function debouncedCallback(cb) {
    return buildScheduler(function (cb, delayMs) {
        if (delayMs === void 0) { delayMs = 0; }
        return setTimeout(cb, delayMs);
    }, cb);
}
exports.debouncedCallback = debouncedCallback;
function buildScheduler(scheduleFn, cb) {
    var scheduleCount = 0;
    var promiseRunning = false;
    var awaitingPromise;
    var awaitingDone;
    var busy = function () {
        return promiseRunning;
    };
    var done = function () {
        promiseRunning = false;
        awaitingDone === null || awaitingDone === void 0 ? void 0 : awaitingDone();
        awaitingDone = undefined;
        awaitingPromise = undefined;
        if (scheduleCount > 0) {
            scheduleFn(scheduleCb);
        }
    };
    var scheduleCb = function () {
        var count = scheduleCount;
        scheduleCount = 0;
        promiseRunning = true;
        var maybePromise = cb({ count: count });
        if (!maybePromise) {
            done();
            return;
        }
        maybePromise.then(done).catch(done);
    };
    return {
        schedule: function (delayMs) {
            if (scheduleCount === 0 && !busy()) {
                scheduleFn(scheduleCb, delayMs);
            }
            scheduleCount++;
        },
        await: function () {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            if (!busy()) {
                                return [2 /*return*/];
                            }
                            if (awaitingPromise == null) {
                                awaitingPromise = new Promise(function (resolve) {
                                    awaitingDone = resolve;
                                });
                            }
                            _a.label = 1;
                        case 1:
                            if (!busy()) return [3 /*break*/, 3];
                            return [4 /*yield*/, awaitingPromise];
                        case 2:
                            _a.sent();
                            return [3 /*break*/, 1];
                        case 3: return [2 /*return*/];
                    }
                });
            });
        },
    };
}
//# sourceMappingURL=render.js.map