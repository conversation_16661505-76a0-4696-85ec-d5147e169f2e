import type Node from './model/node';
import type { ComponentInternalInstance, PropType } from 'vue';
import type { Nullable } from 'element-plus/es/utils';
import type { AllowDragFunction, AllowDropFunction, FilterValue, RenderContentFunction, TreeComponentProps, TreeData, TreeKey, TreeNodeData } from './tree.type';
declare const _default: import("vue").DefineComponent<{
    data: {
        type: PropType<TreeData>;
        default: () => never[];
    };
    emptyText: {
        type: StringConstructor;
    };
    renderAfterExpand: {
        type: BooleanConstructor;
        default: boolean;
    };
    nodeKey: StringConstructor;
    checkStrictly: BooleanConstructor;
    defaultExpandAll: BooleanConstructor;
    expandOnClickNode: {
        type: BooleanConstructor;
        default: boolean;
    };
    checkOnClickNode: BooleanConstructor;
    checkOnClickLeaf: {
        type: BooleanConstructor;
        default: boolean;
    };
    checkDescendants: BooleanConstructor;
    autoExpandParent: {
        type: BooleanConstructor;
        default: boolean;
    };
    defaultCheckedKeys: PropType<TreeComponentProps["defaultCheckedKeys"]>;
    defaultExpandedKeys: PropType<TreeComponentProps["defaultExpandedKeys"]>;
    currentNodeKey: PropType<string | number>;
    renderContent: {
        type: PropType<RenderContentFunction>;
    };
    showCheckbox: BooleanConstructor;
    draggable: BooleanConstructor;
    allowDrag: {
        type: PropType<AllowDragFunction>;
    };
    allowDrop: {
        type: PropType<AllowDropFunction>;
    };
    props: {
        type: PropType<TreeComponentProps["props"]>;
        default: () => {
            children: string;
            label: string;
            disabled: string;
        };
    };
    lazy: BooleanConstructor;
    highlightCurrent: BooleanConstructor;
    load: PropType<TreeComponentProps["load"]>;
    filterNodeMethod: PropType<TreeComponentProps["filterNodeMethod"]>;
    accordion: BooleanConstructor;
    indent: {
        type: NumberConstructor;
        default: number;
    };
    icon: {
        type: PropType<string | import("vue").Component>;
    };
}, {
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string) => string;
        m: (modifier?: string) => string;
        be: (blockSuffix?: string, element?: string) => string;
        em: (element?: string, modifier?: string) => string;
        bm: (blockSuffix?: string, modifier?: string) => string;
        bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    store: import("vue").Ref<{
        currentNode: {
            id: number;
            text: string | null;
            checked: boolean;
            indeterminate: boolean;
            data: TreeNodeData;
            expanded: boolean;
            parent: any | null;
            visible: boolean;
            isCurrent: boolean;
            store: any;
            isLeafByUser: boolean | undefined;
            isLeaf: boolean | undefined;
            canFocus: boolean;
            level: number;
            loaded: boolean;
            childNodes: any[];
            loading: boolean;
            initialize: () => void;
            setData: (data: TreeNodeData) => void;
            readonly label: string;
            readonly key: TreeKey | null | undefined;
            readonly disabled: boolean;
            readonly nextSibling: any | null;
            readonly previousSibling: any | null;
            contains: (target: Node, deep?: boolean) => boolean;
            remove: () => void;
            insertChild: (child?: import("./tree.type").FakeNode | Node, index?: number, batch?: boolean) => void;
            insertBefore: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
            insertAfter: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
            removeChild: (child: Node) => void;
            removeChildByData: (data: TreeNodeData | null) => void;
            expand: (callback?: (() => void) | null, expandParent?: boolean) => void;
            doCreateChildren: (array: TreeNodeData[], defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
            collapse: () => void;
            shouldLoadData: () => boolean;
            updateLeafState: () => void;
            setChecked: (value?: boolean | string, deep?: boolean, recursion?: boolean, passValue?: boolean) => void;
            getChildren: (forceInit?: boolean) => TreeNodeData | TreeNodeData[] | null;
            updateChildren: () => void;
            loadData: (callback: (data?: TreeNodeData[]) => void, defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
            eachNode: (callback: (node: Node) => void) => void;
            reInitChecked: () => void;
        } | null;
        currentNodeKey: TreeKey | null;
        nodesMap: import("./tree.type").TreeStoreNodesMap;
        root: {
            id: number;
            text: string | null;
            checked: boolean;
            indeterminate: boolean;
            data: TreeNodeData;
            expanded: boolean;
            parent: any | null;
            visible: boolean;
            isCurrent: boolean;
            store: any;
            isLeafByUser: boolean | undefined;
            isLeaf: boolean | undefined;
            canFocus: boolean;
            level: number;
            loaded: boolean;
            childNodes: any[];
            loading: boolean;
            initialize: () => void;
            setData: (data: TreeNodeData) => void;
            readonly label: string;
            readonly key: TreeKey | null | undefined;
            readonly disabled: boolean;
            readonly nextSibling: any | null;
            readonly previousSibling: any | null;
            contains: (target: Node, deep?: boolean) => boolean;
            remove: () => void;
            insertChild: (child?: import("./tree.type").FakeNode | Node, index?: number, batch?: boolean) => void;
            insertBefore: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
            insertAfter: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
            removeChild: (child: Node) => void;
            removeChildByData: (data: TreeNodeData | null) => void;
            expand: (callback?: (() => void) | null, expandParent?: boolean) => void;
            doCreateChildren: (array: TreeNodeData[], defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
            collapse: () => void;
            shouldLoadData: () => boolean;
            updateLeafState: () => void;
            setChecked: (value?: boolean | string, deep?: boolean, recursion?: boolean, passValue?: boolean) => void;
            getChildren: (forceInit?: boolean) => TreeNodeData | TreeNodeData[] | null;
            updateChildren: () => void;
            loadData: (callback: (data?: TreeNodeData[]) => void, defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
            eachNode: (callback: (node: Node) => void) => void;
            reInitChecked: () => void;
        };
        data: TreeNodeData[];
        lazy: boolean;
        load?: import("./tree.type").LoadFunction | undefined;
        filterNodeMethod?: import("./tree.type").FilterNodeMethodFunction | undefined;
        key: TreeKey;
        defaultCheckedKeys?: TreeKey[] | undefined;
        checkStrictly: boolean;
        defaultExpandedKeys?: TreeKey[] | undefined;
        autoExpandParent: boolean;
        defaultExpandAll: boolean;
        checkDescendants: boolean;
        props: {
            children?: string | undefined;
            label?: (string | ((data: TreeNodeData, node: Node) => string)) | undefined;
            disabled?: (string | ((data: TreeNodeData, node: Node) => boolean)) | undefined;
            isLeaf?: (string | ((data: TreeNodeData, node: Node) => boolean)) | undefined;
            class?: ((data: TreeNodeData, node: Node) => string | {
                [key: string]: boolean;
            }) | undefined;
        };
        initialize: () => void;
        filter: (value: FilterValue) => void;
        setData: (newVal: TreeData) => void;
        getNode: (data: TreeKey | TreeNodeData | Node) => Node;
        insertBefore: (data: TreeNodeData, refData: TreeKey | TreeNodeData | Node) => void;
        insertAfter: (data: TreeNodeData, refData: TreeKey | TreeNodeData | Node) => void;
        remove: (data: TreeNodeData | Node) => void;
        append: (data: TreeNodeData, parentData: TreeNodeData | TreeKey | Node) => void;
        _initDefaultCheckedNodes: () => void;
        _initDefaultCheckedNode: (node: Node) => void;
        setDefaultCheckedKey: (newVal: TreeKey[]) => void;
        registerNode: (node: Node) => void;
        deregisterNode: (node: Node) => void;
        getCheckedNodes: (leafOnly?: boolean, includeHalfChecked?: boolean) => TreeNodeData[];
        getCheckedKeys: (leafOnly?: boolean) => TreeKey[];
        getHalfCheckedNodes: () => TreeNodeData[];
        getHalfCheckedKeys: () => TreeKey[];
        _getAllNodes: () => Node[];
        updateChildren: (key: TreeKey, data: TreeData) => void;
        _setCheckedKeys: (key: TreeKey, leafOnly: boolean | undefined, checkedKeys: {
            [key: string]: boolean;
        }) => void;
        setCheckedNodes: (array: Node[], leafOnly?: boolean) => void;
        setCheckedKeys: (keys: TreeKey[], leafOnly?: boolean) => void;
        setDefaultExpandedKeys: (keys: TreeKey[]) => void;
        setChecked: (data: TreeKey | TreeNodeData, checked: boolean, deep: boolean) => void;
        getCurrentNode: () => Node | null;
        setCurrentNode: (currentNode: Node) => void;
        setUserCurrentNode: (node: Node, shouldAutoExpandParent?: boolean) => void;
        setCurrentNodeKey: (key: TreeKey | null, shouldAutoExpandParent?: boolean) => void;
    }>;
    root: import("vue").Ref<{
        id: number;
        text: string | null;
        checked: boolean;
        indeterminate: boolean;
        data: TreeNodeData;
        expanded: boolean;
        parent: any | null;
        visible: boolean;
        isCurrent: boolean;
        store: any;
        isLeafByUser: boolean | undefined;
        isLeaf: boolean | undefined;
        canFocus: boolean;
        level: number;
        loaded: boolean;
        childNodes: any[];
        loading: boolean;
        initialize: () => void;
        setData: (data: TreeNodeData) => void;
        readonly label: string;
        readonly key: TreeKey | null | undefined;
        readonly disabled: boolean;
        readonly nextSibling: any | null;
        readonly previousSibling: any | null;
        contains: (target: Node, deep?: boolean) => boolean;
        remove: () => void;
        insertChild: (child?: import("./tree.type").FakeNode | Node, index?: number, batch?: boolean) => void;
        insertBefore: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
        insertAfter: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
        removeChild: (child: Node) => void;
        removeChildByData: (data: TreeNodeData | null) => void;
        expand: (callback?: (() => void) | null, expandParent?: boolean) => void;
        doCreateChildren: (array: TreeNodeData[], defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
        collapse: () => void;
        shouldLoadData: () => boolean;
        updateLeafState: () => void;
        setChecked: (value?: boolean | string, deep?: boolean, recursion?: boolean, passValue?: boolean) => void;
        getChildren: (forceInit?: boolean) => TreeNodeData | TreeNodeData[] | null;
        updateChildren: () => void;
        loadData: (callback: (data?: TreeNodeData[]) => void, defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
        eachNode: (callback: (node: Node) => void) => void;
        reInitChecked: () => void;
    }>;
    currentNode: import("vue").Ref<{
        id: number;
        text: string | null;
        checked: boolean;
        indeterminate: boolean;
        data: TreeNodeData;
        expanded: boolean;
        parent: any | null;
        visible: boolean;
        isCurrent: boolean;
        store: any;
        isLeafByUser: boolean | undefined;
        isLeaf: boolean | undefined;
        canFocus: boolean;
        level: number;
        loaded: boolean;
        childNodes: any[];
        loading: boolean;
        initialize: () => void;
        setData: (data: TreeNodeData) => void;
        readonly label: string;
        readonly key: TreeKey | null | undefined;
        readonly disabled: boolean;
        readonly nextSibling: any | null;
        readonly previousSibling: any | null;
        contains: (target: Node, deep?: boolean) => boolean;
        remove: () => void;
        insertChild: (child?: import("./tree.type").FakeNode | Node, index?: number, batch?: boolean) => void;
        insertBefore: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
        insertAfter: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
        removeChild: (child: Node) => void;
        removeChildByData: (data: TreeNodeData | null) => void;
        expand: (callback?: (() => void) | null, expandParent?: boolean) => void;
        doCreateChildren: (array: TreeNodeData[], defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
        collapse: () => void;
        shouldLoadData: () => boolean;
        updateLeafState: () => void;
        setChecked: (value?: boolean | string, deep?: boolean, recursion?: boolean, passValue?: boolean) => void;
        getChildren: (forceInit?: boolean) => TreeNodeData | TreeNodeData[] | null;
        updateChildren: () => void;
        loadData: (callback: (data?: TreeNodeData[]) => void, defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
        eachNode: (callback: (node: Node) => void) => void;
        reInitChecked: () => void;
    } | null>;
    dragState: import("vue").Ref<{
        allowDrop: boolean;
        dropType: import("./tree.type").NodeDropType | null;
        draggingNode: {
            node: {
                id: number;
                text: string | null;
                checked: boolean;
                indeterminate: boolean;
                data: TreeNodeData;
                expanded: boolean;
                parent: any | null;
                visible: boolean;
                isCurrent: boolean;
                store: any;
                isLeafByUser: boolean | undefined;
                isLeaf: boolean | undefined;
                canFocus: boolean;
                level: number;
                loaded: boolean;
                childNodes: any[];
                loading: boolean;
                initialize: () => void;
                setData: (data: TreeNodeData) => void;
                readonly label: string;
                readonly key: TreeKey | null | undefined;
                readonly disabled: boolean;
                readonly nextSibling: any | null;
                readonly previousSibling: any | null;
                contains: (target: Node, deep?: boolean) => boolean;
                remove: () => void;
                insertChild: (child?: import("./tree.type").FakeNode | Node, index?: number, batch?: boolean) => void;
                insertBefore: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
                insertAfter: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
                removeChild: (child: Node) => void;
                removeChildByData: (data: TreeNodeData | null) => void;
                expand: (callback?: (() => void) | null, expandParent?: boolean) => void;
                doCreateChildren: (array: TreeNodeData[], defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
                collapse: () => void;
                shouldLoadData: () => boolean;
                updateLeafState: () => void;
                setChecked: (value?: boolean | string, deep?: boolean, recursion?: boolean, passValue?: boolean) => void;
                getChildren: (forceInit?: boolean) => TreeNodeData | TreeNodeData[] | null;
                updateChildren: () => void;
                loadData: (callback: (data?: TreeNodeData[]) => void, defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
                eachNode: (callback: (node: Node) => void) => void;
                reInitChecked: () => void;
            };
            $el?: HTMLElement | undefined;
        } | null;
        showDropIndicator: boolean;
        dropNode: {
            node: {
                id: number;
                text: string | null;
                checked: boolean;
                indeterminate: boolean;
                data: TreeNodeData;
                expanded: boolean;
                parent: any | null;
                visible: boolean;
                isCurrent: boolean;
                store: any;
                isLeafByUser: boolean | undefined;
                isLeaf: boolean | undefined;
                canFocus: boolean;
                level: number;
                loaded: boolean;
                childNodes: any[];
                loading: boolean;
                initialize: () => void;
                setData: (data: TreeNodeData) => void;
                readonly label: string;
                readonly key: TreeKey | null | undefined;
                readonly disabled: boolean;
                readonly nextSibling: any | null;
                readonly previousSibling: any | null;
                contains: (target: Node, deep?: boolean) => boolean;
                remove: () => void;
                insertChild: (child?: import("./tree.type").FakeNode | Node, index?: number, batch?: boolean) => void;
                insertBefore: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
                insertAfter: (child: import("./tree.type").FakeNode | Node, ref: Node) => void;
                removeChild: (child: Node) => void;
                removeChildByData: (data: TreeNodeData | null) => void;
                expand: (callback?: (() => void) | null, expandParent?: boolean) => void;
                doCreateChildren: (array: TreeNodeData[], defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
                collapse: () => void;
                shouldLoadData: () => boolean;
                updateLeafState: () => void;
                setChecked: (value?: boolean | string, deep?: boolean, recursion?: boolean, passValue?: boolean) => void;
                getChildren: (forceInit?: boolean) => TreeNodeData | TreeNodeData[] | null;
                updateChildren: () => void;
                loadData: (callback: (data?: TreeNodeData[]) => void, defaultProps?: import("./tree.type").TreeNodeLoadedDefaultProps) => void;
                eachNode: (callback: (node: Node) => void) => void;
                reInitChecked: () => void;
            };
            $el?: HTMLElement | undefined;
        } | null;
    }>;
    el$: import("vue").Ref<Nullable<HTMLElement>>;
    dropIndicator$: import("vue").Ref<Nullable<HTMLElement>>;
    isEmpty: import("vue").ComputedRef<boolean>;
    filter: (value: FilterValue) => void;
    getNodeKey: (node: Node) => any;
    getNodePath: (data: TreeKey | TreeNodeData) => TreeNodeData[];
    getCheckedNodes: (leafOnly?: boolean, includeHalfChecked?: boolean) => TreeNodeData[];
    getCheckedKeys: (leafOnly?: boolean) => TreeKey[];
    getCurrentNode: () => TreeNodeData | null;
    getCurrentKey: () => TreeKey | null;
    setCheckedNodes: (nodes: Node[], leafOnly?: boolean) => void;
    setCheckedKeys: (keys: TreeKey[], leafOnly?: boolean) => void;
    setChecked: (data: TreeKey | TreeNodeData, checked: boolean, deep: boolean) => void;
    getHalfCheckedNodes: () => TreeNodeData[];
    getHalfCheckedKeys: () => TreeKey[];
    setCurrentNode: (node: Node, shouldAutoExpandParent?: boolean) => void;
    setCurrentKey: (key?: TreeKey, shouldAutoExpandParent?: boolean) => void;
    t: import("element-plus/es/hooks").Translator;
    getNode: (data: TreeKey | TreeNodeData) => Node;
    remove: (data: TreeNodeData | Node) => void;
    append: (data: TreeNodeData, parentNode: TreeNodeData | TreeKey | Node) => void;
    insertBefore: (data: TreeNodeData, refNode: TreeKey | TreeNodeData | Node) => void;
    insertAfter: (data: TreeNodeData, refNode: TreeKey | TreeNodeData | Node) => void;
    handleNodeExpand: (nodeData: TreeNodeData, node: Node, instance: ComponentInternalInstance) => void;
    updateKeyChildren: (key: TreeKey, data: TreeData) => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, string[], string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    data: {
        type: PropType<TreeData>;
        default: () => never[];
    };
    emptyText: {
        type: StringConstructor;
    };
    renderAfterExpand: {
        type: BooleanConstructor;
        default: boolean;
    };
    nodeKey: StringConstructor;
    checkStrictly: BooleanConstructor;
    defaultExpandAll: BooleanConstructor;
    expandOnClickNode: {
        type: BooleanConstructor;
        default: boolean;
    };
    checkOnClickNode: BooleanConstructor;
    checkOnClickLeaf: {
        type: BooleanConstructor;
        default: boolean;
    };
    checkDescendants: BooleanConstructor;
    autoExpandParent: {
        type: BooleanConstructor;
        default: boolean;
    };
    defaultCheckedKeys: PropType<TreeComponentProps["defaultCheckedKeys"]>;
    defaultExpandedKeys: PropType<TreeComponentProps["defaultExpandedKeys"]>;
    currentNodeKey: PropType<string | number>;
    renderContent: {
        type: PropType<RenderContentFunction>;
    };
    showCheckbox: BooleanConstructor;
    draggable: BooleanConstructor;
    allowDrag: {
        type: PropType<AllowDragFunction>;
    };
    allowDrop: {
        type: PropType<AllowDropFunction>;
    };
    props: {
        type: PropType<TreeComponentProps["props"]>;
        default: () => {
            children: string;
            label: string;
            disabled: string;
        };
    };
    lazy: BooleanConstructor;
    highlightCurrent: BooleanConstructor;
    load: PropType<TreeComponentProps["load"]>;
    filterNodeMethod: PropType<TreeComponentProps["filterNodeMethod"]>;
    accordion: BooleanConstructor;
    indent: {
        type: NumberConstructor;
        default: number;
    };
    icon: {
        type: PropType<string | import("vue").Component>;
    };
}>> & {
    [x: `on${Capitalize<string>}`]: ((...args: any[]) => any) | undefined;
}, {
    data: TreeData;
    props: import("./tree.type").TreeOptionProps;
    draggable: boolean;
    checkStrictly: boolean;
    lazy: boolean;
    checkOnClickNode: boolean;
    checkOnClickLeaf: boolean;
    accordion: boolean;
    defaultExpandAll: boolean;
    indent: number;
    autoExpandParent: boolean;
    checkDescendants: boolean;
    renderAfterExpand: boolean;
    showCheckbox: boolean;
    expandOnClickNode: boolean;
    highlightCurrent: boolean;
}>;
export default _default;
