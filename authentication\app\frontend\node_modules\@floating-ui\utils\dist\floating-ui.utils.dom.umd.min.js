!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIUtilsDOM={})}(this,(function(e){"use strict";function n(){return"undefined"!=typeof window}function t(e){return i(e)?(e.nodeName||"").toLowerCase():"#document"}function o(e){var n;return(null==e||null==(n=e.ownerDocument)?void 0:n.defaultView)||window}function r(e){var n;return null==(n=(i(e)?e.ownerDocument:e.document)||window.document)?void 0:n.documentElement}function i(e){return!!n()&&(e instanceof Node||e instanceof o(e).Node)}function c(e){return!!n()&&(e instanceof Element||e instanceof o(e).Element)}function l(e){return!!n()&&(e instanceof HTMLElement||e instanceof o(e).HTMLElement)}function s(e){return!(!n()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof o(e).ShadowRoot)}const u=new Set(["inline","contents"]);function a(e){const{overflow:n,overflowX:t,overflowY:o,display:r}=b(e);return/auto|scroll|overlay|hidden|clip/.test(n+o+t)&&!u.has(r)}const f=new Set(["table","td","th"]);const d=[":popover-open",":modal"];function m(e){return d.some((n=>{try{return e.matches(n)}catch(e){return!1}}))}const p=["transform","translate","scale","rotate","perspective"],w=["transform","translate","scale","rotate","perspective","filter"],y=["paint","layout","strict","content"];function v(e){const n=g(),t=c(e)?b(e):e;return p.some((e=>!!t[e]&&"none"!==t[e]))||!!t.containerType&&"normal"!==t.containerType||!n&&!!t.backdropFilter&&"none"!==t.backdropFilter||!n&&!!t.filter&&"none"!==t.filter||w.some((e=>(t.willChange||"").includes(e)))||y.some((e=>(t.contain||"").includes(e)))}function g(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}const h=new Set(["html","body","#document"]);function S(e){return h.has(t(e))}function b(e){return o(e).getComputedStyle(e)}function T(e){if("html"===t(e))return e;const n=e.assignedSlot||e.parentNode||s(e)&&e.host||r(e);return s(n)?n.host:n}function E(e){const n=T(e);return S(n)?e.ownerDocument?e.ownerDocument.body:e.body:l(n)&&a(n)?n:E(n)}function N(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}e.getComputedStyle=b,e.getContainingBlock=function(e){let n=T(e);for(;l(n)&&!S(n);){if(v(n))return n;if(m(n))return null;n=T(n)}return null},e.getDocumentElement=r,e.getFrameElement=N,e.getNearestOverflowAncestor=E,e.getNodeName=t,e.getNodeScroll=function(e){return c(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}},e.getOverflowAncestors=function e(n,t,r){var i;void 0===t&&(t=[]),void 0===r&&(r=!0);const c=E(n),l=c===(null==(i=n.ownerDocument)?void 0:i.body),s=o(c);if(l){const n=N(s);return t.concat(s,s.visualViewport||[],a(c)?c:[],n&&r?e(n):[])}return t.concat(c,e(c,[],r))},e.getParentNode=T,e.getWindow=o,e.isContainingBlock=v,e.isElement=c,e.isHTMLElement=l,e.isLastTraversableNode=S,e.isNode=i,e.isOverflowElement=a,e.isShadowRoot=s,e.isTableElement=function(e){return f.has(t(e))},e.isTopLayer=m,e.isWebKit=g}));
