import { BaseComponentWrapper, WrappableInterface } from 'ag-grid-community';
interface VueWrappableInterface extends WrappableInterface {
    overrideProcessing(methodName: string): boolean;
    processMethod(methodName: string, args: IArguments): any;
}
export declare class Vue<PERSON>rameworkComponentWrapper extends BaseComponentWrapper<WrappableInterface> {
    private parent;
    private static provides;
    constructor(parent: any, provides?: any);
    createWrapper(component: any): WrappableInterface;
    createComponent<T>(component: any, params: any): any;
    protected createMethodProxy(wrapper: VueWrappableInterface, methodName: string, mandatory: boolean): () => any;
    protected destroy(): void;
}
export {};
