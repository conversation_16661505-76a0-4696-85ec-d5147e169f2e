{"version": 3, "file": "defaults.mjs", "sources": ["../../../../../../../packages/components/table/src/table-body/defaults.ts"], "sourcesContent": ["import type { PropType } from 'vue'\nimport type { Store } from '../store'\nimport type {\n  ColumnCls,\n  ColumnStyle,\n  DefaultRow,\n  Table,\n} from '../table/defaults'\nimport type { TableOverflowTooltipOptions } from '../util'\n\ninterface TableBodyProps<T extends DefaultRow> {\n  store: Store<T>\n  stripe?: boolean\n  context: Table<T>\n  rowClassName: ColumnCls<T>\n  rowStyle: ColumnStyle<T>\n  fixed: string\n  highlight: boolean\n  tooltipEffect?: string\n  tooltipOptions?: TableOverflowTooltipOptions\n}\n\nconst defaultProps = {\n  store: {\n    required: true,\n    type: Object as PropType<TableBodyProps<any>['store']>,\n  },\n  stripe: Boolean,\n  tooltipEffect: String,\n  tooltipOptions: {\n    type: Object as PropType<TableBodyProps<any>['tooltipOptions']>,\n  },\n  context: {\n    default: () => ({}),\n    type: Object as PropType<TableBodyProps<any>['context']>,\n  },\n  rowClassName: [String, Function] as PropType<\n    TableBodyProps<any>['rowClassName']\n  >,\n  rowStyle: [Object, Function] as PropType<TableBodyProps<any>['rowStyle']>,\n  fixed: {\n    type: String,\n    default: '',\n  },\n  highlight: Boolean,\n}\n\nexport { TableBodyProps }\nexport default defaultProps\n"], "names": [], "mappings": "AAAK,MAAC,YAAY,GAAG;AACrB,EAAE,KAAK,EAAE;AACT,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,aAAa,EAAE,MAAM;AACvB,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,YAAY,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AAClC,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;AAC9B,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB;;;;"}