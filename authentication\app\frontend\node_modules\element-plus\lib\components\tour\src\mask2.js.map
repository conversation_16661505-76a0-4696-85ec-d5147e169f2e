{"version": 3, "file": "mask2.js", "sources": ["../../../../../../packages/components/tour/src/mask.vue"], "sourcesContent": ["<template>\n  <div v-if=\"visible\" :class=\"ns.e('mask')\" :style=\"maskStyle\" v-bind=\"$attrs\">\n    <svg\n      :style=\"{\n        width: '100%',\n        height: '100%',\n      }\"\n    >\n      <path :class=\"ns.e('hollow')\" :style=\"pathStyle\" :d=\"path\" />\n    </svg>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject, toRef } from 'vue'\nimport { useLockscreen } from '@element-plus/hooks'\nimport { maskProps } from './mask'\nimport { tourKey } from './helper'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElTourMask',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(maskProps)\n\nconst { ns } = inject(tourKey)!\nconst radius = computed(() => props.pos?.radius ?? 2)\nconst roundInfo = computed(() => {\n  const v = radius.value\n  const baseInfo = `a${v},${v} 0 0 1`\n  return {\n    topRight: `${baseInfo} ${v},${v}`,\n    bottomRight: `${baseInfo} ${-v},${v}`,\n    bottomLeft: `${baseInfo} ${-v},${-v}`,\n    topLeft: `${baseInfo} ${v},${-v}`,\n  }\n})\n\nconst path = computed(() => {\n  const width = window.innerWidth\n  const height = window.innerHeight\n  const info = roundInfo.value\n  const _path = `M${width},0 L0,0 L0,${height} L${width},${height} L${width},0 Z`\n  const _radius = radius.value\n  return props.pos\n    ? `${_path} M${props.pos.left + _radius},${props.pos.top} h${\n        props.pos.width - _radius * 2\n      } ${info.topRight} v${props.pos.height - _radius * 2} ${\n        info.bottomRight\n      } h${-props.pos.width + _radius * 2} ${info.bottomLeft} v${\n        -props.pos.height + _radius * 2\n      } ${info.topLeft} z`\n    : _path\n})\n\nconst maskStyle = computed<CSSProperties>(() => ({\n  position: 'fixed',\n  left: 0,\n  right: 0,\n  top: 0,\n  bottom: 0,\n  zIndex: props.zIndex,\n  pointerEvents: props.pos && props.targetAreaClickable ? 'none' : 'auto',\n}))\n\nconst pathStyle = computed<CSSProperties>(() => ({\n  fill: props.fill,\n  pointerEvents: 'auto',\n  cursor: 'auto',\n}))\n\nuseLockscreen(toRef(props, 'visible'), {\n  ns,\n})\n</script>\n"], "names": ["inject", "tourKey", "computed"], "mappings": ";;;;;;;;;;uCAqBc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAIA,IAAA,MAAM,EAAE,EAAA,EAAO,GAAAA,UAAA,CAAOC,cAAO,CAAA,CAAA;AAC7B,IAAA,MAAM,SAASC,YAAS,CAAA,MAAM;AAC9B,MAAM,IAAA,EAAA,EAAA,EAAA,CAAA;AACJ,MAAA,UAAU,GAAO,CAAA,EAAA,GAAA,KAAA,CAAA,GAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA;AACjB,KAAA,CAAA,CAAA;AACA,IAAO,MAAA,SAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MAAA,gBACQ,CAAA,KAAA,CAAQ;AAAU,MAAA,iBACf,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAQ,UAAU;AAAC,MACnC,OAAA;AAAmC,QACnC,UAAY,CAAA,EAAA,YAAa,CAAA,CAAA,CAAA,EAAK,CAAC,CAAA,CAAA;AAAA,QACjC,WAAA,EAAA,CAAA,EAAA,QAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AAAA,QACD,UAAA,EAAA,CAAA,EAAA,QAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAED,QAAM,OAAA,EAAO,WAAe,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAC1B,OAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,mBAAuB,CAAA,MAAA;AACvB,MAAM,MAAA,KAAA,GAAQ,MAAI,CAAA,UAAmB,CAAA;AACrC,MAAA,MAAM,eAAiB,CAAA,WAAA,CAAA;AACvB,MAAO,MAAA,IAAA,GAAM,SACN,CAAA;AAOH,MACL,MAAA,KAAA,GAAA,CAAA,CAAA,EAAA,KAAA,CAAA,WAAA,EAAA,MAAA,CAAA,EAAA,EAAA,KAAA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAED,MAAM,MAAA,OAAA,GAAY,YAA+B,CAAA;AAAA,MAC/C,OAAU,KAAA,CAAA,GAAA,GAAA,CAAA,EAAA,KAAA,CAAA,EAAA,EAAA,KAAA,CAAA,GAAA,CAAA,IAAA,GAAA,OAAA,CAAA,CAAA,EAAA,KAAA,CAAA,GAAA,CAAA,GAAA,CAAA,EAAA,EAAA,KAAA,CAAA,GAAA,CAAA,KAAA,GAAA,OAAA,GAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,EAAA,KAAA,CAAA,GAAA,CAAA,MAAA,GAAA,OAAA,GAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA,EAAA,EAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,GAAA,OAAA,GAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,UAAA,CAAA,EAAA,EAAA,CAAA,KAAA,CAAA,GAAA,CAAA,MAAA,GAAA,OAAA,GAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,OAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA;AAAA,KAAA,CACV,CAAM;AAAA,IAAA,MACC,SAAA,GAAAA,YAAA,CAAA,OAAA;AAAA,MACP,QAAK,EAAA,OAAA;AAAA,MACL,IAAQ,EAAA,CAAA;AAAA,MACR,QAAQ;AAAM,MACd,GAAe,EAAA,CAAA;AAAkD,MACjE,MAAA,EAAA,CAAA;AAEF,MAAM,MAAA,EAAA,KAAA,CAAA;AAA2C,MAC/C,aAAY,EAAA,KAAA,CAAA,GAAA,IAAA,KAAA,CAAA,mBAAA,GAAA,MAAA,GAAA,MAAA;AAAA,KAAA,CACZ,CAAe,CAAA;AAAA,IAAA,MACP,SAAA,GAAAA,YAAA,CAAA,OAAA;AAAA,MACR,IAAA,EAAA,KAAA,CAAA,IAAA;AAEF,MAAc,aAAA,EAAA,MAAa;AAAY,MACrC,MAAA,EAAA,MAAA;AAAA,KACD,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}