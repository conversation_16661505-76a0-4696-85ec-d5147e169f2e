{"version": 3, "file": "size.mjs", "sources": ["../../../../../packages/utils/vue/size.ts"], "sourcesContent": ["import { componentSizeMap } from '@element-plus/constants'\n\nimport type { ComponentSize } from '@element-plus/constants'\n\nexport const getComponentSize = (size?: ComponentSize) => {\n  return componentSizeMap[size || 'default']\n}\n"], "names": [], "mappings": ";;AACY,MAAC,gBAAgB,GAAG,CAAC,IAAI,KAAK;AAC1C,EAAE,OAAO,gBAAgB,CAAC,IAAI,IAAI,SAAS,CAAC,CAAC;AAC7C;;;;"}