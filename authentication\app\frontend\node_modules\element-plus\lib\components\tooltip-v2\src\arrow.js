'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var common = require('./common.js');
var runtime = require('../../../utils/vue/props/runtime.js');

const tooltipV2ArrowProps = runtime.buildProps({
  width: {
    type: Number,
    default: 10
  },
  height: {
    type: Number,
    default: 10
  },
  style: {
    type: runtime.definePropType(Object),
    default: null
  }
});
const tooltipV2ArrowSpecialProps = runtime.buildProps({
  side: {
    type: runtime.definePropType(String),
    values: common.tooltipV2Sides,
    required: true
  }
});

exports.tooltipV2ArrowProps = tooltipV2ArrowProps;
exports.tooltipV2ArrowSpecialProps = tooltipV2ArrowSpecialProps;
//# sourceMappingURL=arrow.js.map
