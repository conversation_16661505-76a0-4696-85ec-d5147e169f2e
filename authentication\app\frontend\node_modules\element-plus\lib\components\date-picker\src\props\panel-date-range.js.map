{"version": 3, "file": "panel-date-range.js", "sources": ["../../../../../../../packages/components/date-picker/src/props/panel-date-range.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { panelRangeSharedProps, panelSharedProps } from './shared'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const panelDateRangeProps = buildProps({\n  ...panelSharedProps,\n  ...panelRangeSharedProps,\n} as const)\n\nexport type PanelDateRangeProps = ExtractPropTypes<typeof panelDateRangeProps>\nexport type PanelDateRangePropsPublic = __ExtractPublicPropTypes<\n  typeof panelDateRangeProps\n>\n"], "names": ["buildProps", "panelSharedProps", "panelRangeSharedProps"], "mappings": ";;;;;;;AAEY,MAAC,mBAAmB,GAAGA,kBAAU,CAAC;AAC9C,EAAE,GAAGC,uBAAgB;AACrB,EAAE,GAAGC,4BAAqB;AAC1B,CAAC;;;;"}