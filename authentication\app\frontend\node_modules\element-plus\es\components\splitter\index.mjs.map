{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/splitter/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\nimport Splitter from './src/splitter.vue'\nimport SplitPanel from './src/split-panel.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSplitter: SFCWithInstall<typeof Splitter> & {\n  SplitPanel: typeof SplitPanel\n} = withInstall(Splitter, {\n  SplitPanel,\n})\nexport default ElSplitter\n\nexport const ElSplitterPanel: SFCWithInstall<typeof SplitPanel> =\n  withNoopInstall(SplitPanel)\n\nexport * from './src/splitter'\nexport * from './src/split-panel'\n"], "names": [], "mappings": ";;;;;;AAGY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ,EAAE;AAChD,EAAE,UAAU;AACZ,CAAC,EAAE;AAES,MAAC,eAAe,GAAG,eAAe,CAAC,UAAU;;;;"}