import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("ckafka.tencentcloudapi.com", "2019-08-19", clientConfig);
    }
    async DescribeGroupInfo(req, cb) {
        return this.request("DescribeGroupInfo", req, cb);
    }
    async DescribeCvmInfo(req, cb) {
        return this.request("DescribeCvmInfo", req, cb);
    }
    async ModifyInstancePre(req, cb) {
        return this.request("ModifyInstancePre", req, cb);
    }
    async SendMessage(req, cb) {
        return this.request("SendMessage", req, cb);
    }
    async DeleteConnectResource(req, cb) {
        return this.request("DeleteConnectResource", req, cb);
    }
    async DescribeUser(req, cb) {
        return this.request("DescribeUser", req, cb);
    }
    async DescribeDatahubGroupOffsets(req, cb) {
        return this.request("DescribeDatahubGroupOffsets", req, cb);
    }
    async DescribeConsumerGroup(req, cb) {
        return this.request("DescribeConsumerGroup", req, cb);
    }
    async CreateTopic(req, cb) {
        return this.request("CreateTopic", req, cb);
    }
    async DescribeDatahubTask(req, cb) {
        return this.request("DescribeDatahubTask", req, cb);
    }
    async CheckCdcCluster(req, cb) {
        return this.request("CheckCdcCluster", req, cb);
    }
    async DescribeTopicFlowRanking(req, cb) {
        return this.request("DescribeTopicFlowRanking", req, cb);
    }
    async CreatePartition(req, cb) {
        return this.request("CreatePartition", req, cb);
    }
    async FetchLatestDatahubMessageList(req, cb) {
        return this.request("FetchLatestDatahubMessageList", req, cb);
    }
    async CreateToken(req, cb) {
        return this.request("CreateToken", req, cb);
    }
    async DeleteInstancePre(req, cb) {
        return this.request("DeleteInstancePre", req, cb);
    }
    async DescribeInstancesDetail(req, cb) {
        return this.request("DescribeInstancesDetail", req, cb);
    }
    async InstanceScalingDown(req, cb) {
        return this.request("InstanceScalingDown", req, cb);
    }
    async DescribeTopicAttributes(req, cb) {
        return this.request("DescribeTopicAttributes", req, cb);
    }
    async FetchMessageListByTimestamp(req, cb) {
        return this.request("FetchMessageListByTimestamp", req, cb);
    }
    async CreateAclRule(req, cb) {
        return this.request("CreateAclRule", req, cb);
    }
    async CreateUser(req, cb) {
        return this.request("CreateUser", req, cb);
    }
    async ModifyPassword(req, cb) {
        return this.request("ModifyPassword", req, cb);
    }
    async RenewCkafkaInstance(req, cb) {
        return this.request("RenewCkafkaInstance", req, cb);
    }
    async DescribeAclRule(req, cb) {
        return this.request("DescribeAclRule", req, cb);
    }
    async DescribePrometheus(req, cb) {
        return this.request("DescribePrometheus", req, cb);
    }
    async DescribeDatahubTasks(req, cb) {
        return this.request("DescribeDatahubTasks", req, cb);
    }
    async ModifyDatahubTopic(req, cb) {
        return this.request("ModifyDatahubTopic", req, cb);
    }
    async FetchMessageListByOffset(req, cb) {
        return this.request("FetchMessageListByOffset", req, cb);
    }
    async CreateDatahubTopic(req, cb) {
        return this.request("CreateDatahubTopic", req, cb);
    }
    async CancelAuthorizationToken(req, cb) {
        return this.request("CancelAuthorizationToken", req, cb);
    }
    async DeleteDatahubTopic(req, cb) {
        return this.request("DeleteDatahubTopic", req, cb);
    }
    async CreateConnectResource(req, cb) {
        return this.request("CreateConnectResource", req, cb);
    }
    async FetchDatahubMessageByOffset(req, cb) {
        return this.request("FetchDatahubMessageByOffset", req, cb);
    }
    async DeleteAcl(req, cb) {
        return this.request("DeleteAcl", req, cb);
    }
    async DescribeInstanceAttributes(req, cb) {
        return this.request("DescribeInstanceAttributes", req, cb);
    }
    async DescribeTopicSyncReplica(req, cb) {
        return this.request("DescribeTopicSyncReplica", req, cb);
    }
    async AuthorizeToken(req, cb) {
        return this.request("AuthorizeToken", req, cb);
    }
    async DescribeDatahubTopic(req, cb) {
        return this.request("DescribeDatahubTopic", req, cb);
    }
    async DescribeCkafkaZone(req, cb) {
        return this.request("DescribeCkafkaZone", req, cb);
    }
    async DescribeRoute(req, cb) {
        return this.request("DescribeRoute", req, cb);
    }
    async ModifyDatahubTask(req, cb) {
        return this.request("ModifyDatahubTask", req, cb);
    }
    async DescribeConnectResource(req, cb) {
        return this.request("DescribeConnectResource", req, cb);
    }
    async BatchModifyTopicAttributes(req, cb) {
        return this.request("BatchModifyTopicAttributes", req, cb);
    }
    async DeleteRoute(req, cb) {
        return this.request("DeleteRoute", req, cb);
    }
    async ModifyTopicAttributes(req, cb) {
        return this.request("ModifyTopicAttributes", req, cb);
    }
    async CreateTopicIpWhiteList(req, cb) {
        return this.request("CreateTopicIpWhiteList", req, cb);
    }
    async ModifyGroupOffsets(req, cb) {
        return this.request("ModifyGroupOffsets", req, cb);
    }
    async ModifyInstanceAttributes(req, cb) {
        return this.request("ModifyInstanceAttributes", req, cb);
    }
    async DeleteGroup(req, cb) {
        return this.request("DeleteGroup", req, cb);
    }
    async DeleteInstancePost(req, cb) {
        return this.request("DeleteInstancePost", req, cb);
    }
    async DescribeACL(req, cb) {
        return this.request("DescribeACL", req, cb);
    }
    async CreateInstancePre(req, cb) {
        return this.request("CreateInstancePre", req, cb);
    }
    async DeleteTopicIpWhiteList(req, cb) {
        return this.request("DeleteTopicIpWhiteList", req, cb);
    }
    async DescribeTaskStatus(req, cb) {
        return this.request("DescribeTaskStatus", req, cb);
    }
    async DescribeAppInfo(req, cb) {
        return this.request("DescribeAppInfo", req, cb);
    }
    async BatchCreateAcl(req, cb) {
        return this.request("BatchCreateAcl", req, cb);
    }
    async CreateDatahubTask(req, cb) {
        return this.request("CreateDatahubTask", req, cb);
    }
    async DeleteAclRule(req, cb) {
        return this.request("DeleteAclRule", req, cb);
    }
    async ModifyAclRule(req, cb) {
        return this.request("ModifyAclRule", req, cb);
    }
    async DeleteUser(req, cb) {
        return this.request("DeleteUser", req, cb);
    }
    async DescribeTopicProduceConnection(req, cb) {
        return this.request("DescribeTopicProduceConnection", req, cb);
    }
    async DescribeTopic(req, cb) {
        return this.request("DescribeTopic", req, cb);
    }
    async DescribeConnectResources(req, cb) {
        return this.request("DescribeConnectResources", req, cb);
    }
    async CreateConsumer(req, cb) {
        return this.request("CreateConsumer", req, cb);
    }
    async DescribeTypeInstances(req, cb) {
        return this.request("DescribeTypeInstances", req, cb);
    }
    async DescribeDatahubTopics(req, cb) {
        return this.request("DescribeDatahubTopics", req, cb);
    }
    async DescribeSecurityGroupRoutes(req, cb) {
        return this.request("DescribeSecurityGroupRoutes", req, cb);
    }
    async DeleteDatahubTask(req, cb) {
        return this.request("DeleteDatahubTask", req, cb);
    }
    async DescribeGroup(req, cb) {
        return this.request("DescribeGroup", req, cb);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async CreateRoute(req, cb) {
        return this.request("CreateRoute", req, cb);
    }
    async DescribeTopicDetail(req, cb) {
        return this.request("DescribeTopicDetail", req, cb);
    }
    async FetchMessageByOffset(req, cb) {
        return this.request("FetchMessageByOffset", req, cb);
    }
    async ModifyConnectResource(req, cb) {
        return this.request("ModifyConnectResource", req, cb);
    }
    async CreateAcl(req, cb) {
        return this.request("CreateAcl", req, cb);
    }
    async DescribeTopicSubscribeGroup(req, cb) {
        return this.request("DescribeTopicSubscribeGroup", req, cb);
    }
    async ModifyRoutineMaintenanceTask(req, cb) {
        return this.request("ModifyRoutineMaintenanceTask", req, cb);
    }
    async CreatePostPaidInstance(req, cb) {
        return this.request("CreatePostPaidInstance", req, cb);
    }
    async CreatePrometheus(req, cb) {
        return this.request("CreatePrometheus", req, cb);
    }
    async DeleteRouteTriggerTime(req, cb) {
        return this.request("DeleteRouteTriggerTime", req, cb);
    }
    async BatchModifyGroupOffsets(req, cb) {
        return this.request("BatchModifyGroupOffsets", req, cb);
    }
    async DescribeGroupOffsets(req, cb) {
        return this.request("DescribeGroupOffsets", req, cb);
    }
    async DescribeRegion(req, cb) {
        return this.request("DescribeRegion", req, cb);
    }
    async CreateCdcCluster(req, cb) {
        return this.request("CreateCdcCluster", req, cb);
    }
    async DeleteTopic(req, cb) {
        return this.request("DeleteTopic", req, cb);
    }
    async InquireCkafkaPrice(req, cb) {
        return this.request("InquireCkafkaPrice", req, cb);
    }
}
