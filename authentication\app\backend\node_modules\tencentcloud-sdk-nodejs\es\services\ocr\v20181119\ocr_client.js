import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("ocr.tencentcloudapi.com", "2018-11-19", clientConfig);
    }
    async QuestionOCR(req, cb) {
        return this.request("QuestionOCR", req, cb);
    }
    async TextDetect(req, cb) {
        return this.request("TextDetect", req, cb);
    }
    async QrcodeOCR(req, cb) {
        return this.request("QrcodeOCR", req, cb);
    }
    async GeneralAccurateOCR(req, cb) {
        return this.request("GeneralAccurateOCR", req, cb);
    }
    async RecognizeContainerOCR(req, cb) {
        return this.request("RecognizeContainerOCR", req, cb);
    }
    async RecognizeEncryptedIDCardOCR(req, cb) {
        return this.request("RecognizeEncryptedIDCardOCR", req, cb);
    }
    async RecognizeStoreName(req, cb) {
        return this.request("RecognizeStoreName", req, cb);
    }
    async MLIDPassportOCR(req, cb) {
        return this.request("MLIDPassportOCR", req, cb);
    }
    async TrainTicketOCR(req, cb) {
        return this.request("TrainTicketOCR", req, cb);
    }
    async PropOwnerCertOCR(req, cb) {
        return this.request("PropOwnerCertOCR", req, cb);
    }
    async InvoiceGeneralOCR(req, cb) {
        return this.request("InvoiceGeneralOCR", req, cb);
    }
    async HKIDCardOCR(req, cb) {
        return this.request("HKIDCardOCR", req, cb);
    }
    async PermitOCR(req, cb) {
        return this.request("PermitOCR", req, cb);
    }
    async BusInvoiceOCR(req, cb) {
        return this.request("BusInvoiceOCR", req, cb);
    }
    async EnglishOCR(req, cb) {
        return this.request("EnglishOCR", req, cb);
    }
    async SmartStructuralOCR(req, cb) {
        return this.request("SmartStructuralOCR", req, cb);
    }
    async ClassifyStoreName(req, cb) {
        return this.request("ClassifyStoreName", req, cb);
    }
    async TaxiInvoiceOCR(req, cb) {
        return this.request("TaxiInvoiceOCR", req, cb);
    }
    async RideHailingDriverLicenseOCR(req, cb) {
        return this.request("RideHailingDriverLicenseOCR", req, cb);
    }
    async RecognizeForeignPermanentResidentIdCard(req, cb) {
        return this.request("RecognizeForeignPermanentResidentIdCard", req, cb);
    }
    async MainlandPermitOCR(req, cb) {
        return this.request("MainlandPermitOCR", req, cb);
    }
    async FlightInvoiceOCR(req, cb) {
        return this.request("FlightInvoiceOCR", req, cb);
    }
    async RecognizeGeneralInvoice(req, cb) {
        return this.request("RecognizeGeneralInvoice", req, cb);
    }
    async InsuranceBillOCR(req, cb) {
        return this.request("InsuranceBillOCR", req, cb);
    }
    async VatInvoiceVerifyNew(req, cb) {
        return this.request("VatInvoiceVerifyNew", req, cb);
    }
    async VinOCR(req, cb) {
        return this.request("VinOCR", req, cb);
    }
    async VehicleLicenseOCR(req, cb) {
        return this.request("VehicleLicenseOCR", req, cb);
    }
    async BizLicenseOCR(req, cb) {
        return this.request("BizLicenseOCR", req, cb);
    }
    async RecognizeGeneralCardWarn(req, cb) {
        return this.request("RecognizeGeneralCardWarn", req, cb);
    }
    async GeneralBasicOCR(req, cb) {
        return this.request("GeneralBasicOCR", req, cb);
    }
    async RecognizeThaiIDCardOCR(req, cb) {
        return this.request("RecognizeThaiIDCardOCR", req, cb);
    }
    async ExtractDocMultiPro(req, cb) {
        return this.request("ExtractDocMultiPro", req, cb);
    }
    async TableOCR(req, cb) {
        return this.request("TableOCR", req, cb);
    }
    async ArithmeticOCR(req, cb) {
        return this.request("ArithmeticOCR", req, cb);
    }
    async EstateCertOCR(req, cb) {
        return this.request("EstateCertOCR", req, cb);
    }
    async RecognizeValidIDCardOCR(req, cb) {
        return this.request("RecognizeValidIDCardOCR", req, cb);
    }
    async BankCardOCR(req, cb) {
        return this.request("BankCardOCR", req, cb);
    }
    async GetOCRToken(req, cb) {
        return this.request("GetOCRToken", req, cb);
    }
    async EduPaperOCR(req, cb) {
        return this.request("EduPaperOCR", req, cb);
    }
    async EnterpriseLicenseOCR(req, cb) {
        return this.request("EnterpriseLicenseOCR", req, cb);
    }
    async BusinessCardOCR(req, cb) {
        return this.request("BusinessCardOCR", req, cb);
    }
    async IDCardOCR(req, cb) {
        return this.request("IDCardOCR", req, cb);
    }
    async RecognizeGeneralTextImageWarn(req, cb) {
        return this.request("RecognizeGeneralTextImageWarn", req, cb);
    }
    async InstitutionOCR(req, cb) {
        return this.request("InstitutionOCR", req, cb);
    }
    async RecognizeTableAccurateOCR(req, cb) {
        return this.request("RecognizeTableAccurateOCR", req, cb);
    }
    async MixedInvoiceDetect(req, cb) {
        return this.request("MixedInvoiceDetect", req, cb);
    }
    async VatRollInvoiceOCR(req, cb) {
        return this.request("VatRollInvoiceOCR", req, cb);
    }
    async QuotaInvoiceOCR(req, cb) {
        return this.request("QuotaInvoiceOCR", req, cb);
    }
    async GeneralHandwritingOCR(req, cb) {
        return this.request("GeneralHandwritingOCR", req, cb);
    }
    async ExtractDocBasic(req, cb) {
        return this.request("ExtractDocBasic", req, cb);
    }
    async RecognizeOnlineTaxiItineraryOCR(req, cb) {
        return this.request("RecognizeOnlineTaxiItineraryOCR", req, cb);
    }
    async RecognizeTableOCR(req, cb) {
        return this.request("RecognizeTableOCR", req, cb);
    }
    async WaybillOCR(req, cb) {
        return this.request("WaybillOCR", req, cb);
    }
    async HandwritingEssayOCR(req, cb) {
        return this.request("HandwritingEssayOCR", req, cb);
    }
    async HmtResidentPermitOCR(req, cb) {
        return this.request("HmtResidentPermitOCR", req, cb);
    }
    async TollInvoiceOCR(req, cb) {
        return this.request("TollInvoiceOCR", req, cb);
    }
    async ClassifyDetectOCR(req, cb) {
        return this.request("ClassifyDetectOCR", req, cb);
    }
    async RecognizeFormulaOCR(req, cb) {
        return this.request("RecognizeFormulaOCR", req, cb);
    }
    async SealOCR(req, cb) {
        return this.request("SealOCR", req, cb);
    }
    async BankSlipOCR(req, cb) {
        return this.request("BankSlipOCR", req, cb);
    }
    async ResidenceBookletOCR(req, cb) {
        return this.request("ResidenceBookletOCR", req, cb);
    }
    async RecognizeTravelCardOCR(req, cb) {
        return this.request("RecognizeTravelCardOCR", req, cb);
    }
    async VerifyOfdVatInvoiceOCR(req, cb) {
        return this.request("VerifyOfdVatInvoiceOCR", req, cb);
    }
    async DriverLicenseOCR(req, cb) {
        return this.request("DriverLicenseOCR", req, cb);
    }
    async FinanBillOCR(req, cb) {
        return this.request("FinanBillOCR", req, cb);
    }
    async MixedInvoiceOCR(req, cb) {
        return this.request("MixedInvoiceOCR", req, cb);
    }
    async RecognizeHealthCodeOCR(req, cb) {
        return this.request("RecognizeHealthCodeOCR", req, cb);
    }
    async ImageEnhancement(req, cb) {
        return this.request("ImageEnhancement", req, cb);
    }
    async QuestionSplitOCR(req, cb) {
        return this.request("QuestionSplitOCR", req, cb);
    }
    async MLIDCardOCR(req, cb) {
        return this.request("MLIDCardOCR", req, cb);
    }
    async RecognizeMedicalInvoiceOCR(req, cb) {
        return this.request("RecognizeMedicalInvoiceOCR", req, cb);
    }
    async RecognizeTableMultiOCR(req, cb) {
        return this.request("RecognizeTableMultiOCR", req, cb);
    }
    async GeneralFastOCR(req, cb) {
        return this.request("GeneralFastOCR", req, cb);
    }
    async VatInvoiceOCR(req, cb) {
        return this.request("VatInvoiceOCR", req, cb);
    }
    async DutyPaidProofOCR(req, cb) {
        return this.request("DutyPaidProofOCR", req, cb);
    }
    async OrgCodeCertOCR(req, cb) {
        return this.request("OrgCodeCertOCR", req, cb);
    }
    async FinanBillSliceOCR(req, cb) {
        return this.request("FinanBillSliceOCR", req, cb);
    }
    async LicensePlateOCR(req, cb) {
        return this.request("LicensePlateOCR", req, cb);
    }
    async ExtractDocMulti(req, cb) {
        return this.request("ExtractDocMulti", req, cb);
    }
    async GeneralEfficientOCR(req, cb) {
        return this.request("GeneralEfficientOCR", req, cb);
    }
    async AdvertiseOCR(req, cb) {
        return this.request("AdvertiseOCR", req, cb);
    }
    async VehicleRegCertOCR(req, cb) {
        return this.request("VehicleRegCertOCR", req, cb);
    }
    async CarInvoiceOCR(req, cb) {
        return this.request("CarInvoiceOCR", req, cb);
    }
    async GetOCRResult(req, cb) {
        return this.request("GetOCRResult", req, cb);
    }
    async FormulaOCR(req, cb) {
        return this.request("FormulaOCR", req, cb);
    }
    async PassportOCR(req, cb) {
        return this.request("PassportOCR", req, cb);
    }
    async ShipInvoiceOCR(req, cb) {
        return this.request("ShipInvoiceOCR", req, cb);
    }
    async RideHailingTransportLicenseOCR(req, cb) {
        return this.request("RideHailingTransportLicenseOCR", req, cb);
    }
}
