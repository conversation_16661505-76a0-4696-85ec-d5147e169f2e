import { Column, ColumnGroup, ColumnWidthCallbackParams, RowHeightCallbackParams, ExcelCell, ExcelColumn, ExcelHeaderFooterConfig, ExcelImage, ExcelRow, ExcelSheetMargin, ExcelSheetPageSetup, ExcelStyle, ExcelWorksheet, RowNode } from "ag-grid-community";
import { BaseGridSerializingSession, GridSerializingParams, RowAccumulator, RowSpanningAccumulator, RowType } from "ag-grid-community";
export interface StyleLinkerInterface {
    rowType: RowType;
    rowIndex: number;
    value: string;
    column?: Column;
    columnGroup?: ColumnGroup;
    node?: RowNode;
}
export interface ExcelGridSerializingParams extends GridSerializingParams {
    autoConvertFormulas?: boolean;
    baseExcelStyles: ExcelStyle[];
    columnWidth?: number | ((params: ColumnWidthCallbackParams) => number);
    headerFooterConfig?: ExcelHeaderFooterConfig;
    headerRowHeight?: number | ((params: RowHeightCallbackParams) => number);
    rowHeight?: number | ((params: RowHeightCallbackParams) => number);
    margins?: ExcelSheetMargin;
    pageSetup?: ExcelSheetPageSetup;
    sheetName: string;
    suppressColumnOutline?: boolean;
    suppressRowOutline?: boolean;
    rowGroupExpandState?: 'expanded' | 'collapsed' | 'match';
    styleLinker: (params: StyleLinkerInterface) => string[];
    addImageToCell?: (rowIndex: number, column: Column, value: string) => {
        image: ExcelImage;
        value?: string;
    } | undefined;
    suppressTextAsCDATA?: boolean;
}
interface ExcelMixedStyle {
    key: string;
    excelID: string;
    result: ExcelStyle;
}
export declare abstract class BaseExcelSerializingSession<T> extends BaseGridSerializingSession<ExcelRow[]> {
    protected readonly config: ExcelGridSerializingParams;
    protected readonly stylesByIds: {
        [key: string]: ExcelStyle;
    };
    protected mixedStyles: {
        [key: string]: ExcelMixedStyle;
    };
    protected mixedStyleCounter: number;
    protected readonly excelStyles: ExcelStyle[];
    protected rows: ExcelRow[];
    protected cols: ExcelColumn[];
    protected columnsToExport: Column[];
    constructor(config: ExcelGridSerializingParams);
    protected abstract createExcel(data: ExcelWorksheet): string;
    protected abstract getDataTypeForValue(valueForCell?: string): T;
    protected abstract getType(type: T, style: ExcelStyle | null, value: string | null): T | null;
    protected abstract createCell(styleId: string | null, type: T, value: string, valueFormatted?: string | null): ExcelCell;
    protected abstract addImage(rowIndex: number, column: Column, value: string): {
        image: ExcelImage;
        value?: string;
    } | undefined;
    protected abstract createMergedCell(styleId: string | null, type: T, value: string, numOfCells: number): ExcelCell;
    addCustomContent(customContent: ExcelRow[]): void;
    onNewHeaderGroupingRow(): RowSpanningAccumulator;
    onNewHeaderRow(): RowAccumulator;
    onNewBodyRow(node?: RowNode): RowAccumulator;
    private addRowOutlineIfNecessary;
    private isAnyParentCollapsed;
    prepare(columnsToExport: Column[]): void;
    parse(): string;
    protected isFormula(value: string | null): boolean | undefined;
    protected isNumerical(value: any): boolean;
    protected getStyleById(styleId?: string | null): ExcelStyle | null;
    private convertColumnToExcel;
    private onNewHeaderColumn;
    private onNewRow;
    private onNewBodyColumn;
    private getStyleId;
    private addNewMixedStyle;
}
export {};
