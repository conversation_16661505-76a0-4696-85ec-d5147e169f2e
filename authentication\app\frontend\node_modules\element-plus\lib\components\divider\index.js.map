{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/divider/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Divider from './src/divider.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDivider: SFCWithInstall<typeof Divider> = withInstall(Divider)\nexport default ElDivider\n\nexport * from './src/divider'\n"], "names": ["withInstall", "Divider"], "mappings": ";;;;;;;;AAEY,MAAC,SAAS,GAAGA,mBAAW,CAACC,oBAAO;;;;;;"}