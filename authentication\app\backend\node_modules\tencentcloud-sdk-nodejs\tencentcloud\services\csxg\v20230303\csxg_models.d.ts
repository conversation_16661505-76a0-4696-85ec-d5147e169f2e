/**
 * Describe5GAPNs返回参数结构体
 */
export interface Describe5GAPNsResponse {
    /**
     * 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
     */
    RequestId?: string;
}
/**
 * Modify5GInstanceAttribute请求参数结构体
 */
export declare type Modify5GInstanceAttributeRequest = null;
/**
 * Delete5GInstance返回参数结构体
 */
export interface Delete5GInstanceResponse {
    /**
     * 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
     */
    RequestId?: string;
}
/**
 * Modify5GInstanceAttribute返回参数结构体
 */
export interface Modify5GInstanceAttributeResponse {
    /**
     * 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
     */
    RequestId?: string;
}
/**
 * Describe5GInstances返回参数结构体
 */
export interface Describe5GInstancesResponse {
    /**
     * 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
     */
    RequestId?: string;
}
/**
 * Describe5GAPNs请求参数结构体
 */
export declare type Describe5GAPNsRequest = null;
/**
 * Create5GInstance返回参数结构体
 */
export interface Create5GInstanceResponse {
    /**
     * 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
     */
    RequestId?: string;
}
/**
 * Delete5GInstance请求参数结构体
 */
export declare type Delete5GInstanceRequest = null;
/**
 * Describe5GInstances请求参数结构体
 */
export declare type Describe5GInstancesRequest = null;
/**
 * Create5GInstance请求参数结构体
 */
export declare type Create5GInstanceRequest = null;
