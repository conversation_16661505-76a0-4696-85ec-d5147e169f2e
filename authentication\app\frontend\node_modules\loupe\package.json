{"name": "loupe", "version": "2.3.7", "description": "Inspect utility for Node.js and browsers", "homepage": "https://github.com/chaijs/loupe", "license": "MIT", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> (https://github.com/keithamus)"], "main": "./loupe.js", "module": "./index.js", "browser": {"./index.js": "./loupe.js", "util": false}, "repository": {"type": "git", "url": "https://github.com/chaijs/loupe"}, "files": ["loupe.js", "index.js", "lib/*"], "scripts": {"bench": "node -r esm bench", "commit-msg": "commitlint -x angular", "lint": "eslint --ignore-path .gitignore .", "prepare": "rollup -c rollup.conf.js", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "test": "npm run test:node && npm run test:browser", "pretest:browser": "npm run prepare", "test:browser": "karma start --singleRun=true", "posttest:browser": "npm run upload-coverage", "test:node": "nyc mocha -r esm", "posttest:node": "nyc report --report-dir \"coverage/node-$(node --version)\" --reporter=lcovonly && npm run upload-coverage", "upload-coverage": "codecov"}, "eslintConfig": {"root": true, "parserOptions": {"ecmaVersion": 2020}, "env": {"es6": true}, "plugins": ["filenames", "prettier"], "extends": ["strict/es6"], "rules": {"comma-dangle": "off", "func-style": "off", "no-magic-numbers": "off", "class-methods-use-this": "off", "array-bracket-spacing": "off", "array-element-newline": "off", "space-before-function-paren": "off", "arrow-parens": "off", "template-curly-spacing": "off", "quotes": "off", "generator-star-spacing": "off", "prefer-destructuring": "off", "no-mixed-operators": "off", "id-blacklist": "off", "curly": "off", "semi": ["error", "never"], "prettier/prettier": ["error", {"printWidth": 120, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "trailingComma": "es5", "arrowParens": "avoid", "bracketSpacing": true}]}}, "prettier": {"printWidth": 120, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "trailingComma": "es5", "arrowParens": "avoid", "bracketSpacing": true}, "dependencies": {"get-func-name": "^2.0.1"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/preset-env": "^7.12.11", "@commitlint/cli": "^11.0.0", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "benchmark": "^2.1.4", "chai": "^4.2.0", "codecov": "^3.8.1", "commitlint-config-angular": "^11.0.0", "core-js": "^3.8.3", "cross-env": "^7.0.3", "eslint": "^7.18.0", "eslint-config-strict": "^14.0.1", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-prettier": "^3.3.1", "esm": "^3.2.25", "husky": "^4.3.8", "karma": "^5.2.3", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.0.3", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^2.1.0", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^2.0.1", "karma-opera-launcher": "^1.0.0", "karma-safari-launcher": "^1.0.0", "karma-safaritechpreview-launcher": "^2.0.2", "karma-sauce-launcher": "^4.3.4", "mocha": "^8.2.1", "nyc": "^15.1.0", "prettier": "^2.2.1", "rollup": "^2.37.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-istanbul": "^3.0.0", "semantic-release": "^17.3.6", "simple-assert": "^1.0.0"}}