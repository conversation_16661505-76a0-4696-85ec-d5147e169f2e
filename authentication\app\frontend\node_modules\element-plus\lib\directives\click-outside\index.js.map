{"version": 3, "file": "index.js", "sources": ["../../../../../packages/directives/click-outside/index.ts"], "sourcesContent": ["import { isArray, isClient, isElement } from '@element-plus/utils'\n\nimport type {\n  ComponentPublicInstance,\n  DirectiveBinding,\n  ObjectDirective,\n} from 'vue'\n\ntype DocumentHandler = <T extends MouseEvent>(mouseup: T, mousedown: T) => void\ntype FlushList = Map<\n  HTMLElement,\n  {\n    documentHandler: DocumentHandler\n    bindingFn: (...args: unknown[]) => unknown\n  }[]\n>\n\nconst nodeList: FlushList = new Map()\n\nif (isClient) {\n  let startClick: MouseEvent | undefined\n  document.addEventListener('mousedown', (e: MouseEvent) => (startClick = e))\n  document.addEventListener('mouseup', (e: MouseEvent) => {\n    if (startClick) {\n      for (const handlers of nodeList.values()) {\n        for (const { documentHandler } of handlers) {\n          documentHandler(e as MouseEvent, startClick)\n        }\n      }\n      startClick = undefined\n    }\n  })\n}\n\nfunction createDocumentHandler(\n  el: HTMLElement,\n  binding: DirectiveBinding\n): DocumentHandler {\n  let excludes: HTMLElement[] = []\n  if (isArray(binding.arg)) {\n    excludes = binding.arg\n  } else if (isElement(binding.arg)) {\n    // due to current implementation on binding type is wrong the type casting is necessary here\n    excludes.push(binding.arg as unknown as HTMLElement)\n  }\n  return function (mouseup, mousedown) {\n    const popperRef = (\n      binding.instance as ComponentPublicInstance<{\n        popperRef: HTMLElement\n      }>\n    ).popperRef\n    const mouseUpTarget = mouseup.target as Node\n    const mouseDownTarget = mousedown?.target as Node\n    const isBound = !binding || !binding.instance\n    const isTargetExists = !mouseUpTarget || !mouseDownTarget\n    const isContainedByEl =\n      el.contains(mouseUpTarget) || el.contains(mouseDownTarget)\n    const isSelf = el === mouseUpTarget\n\n    const isTargetExcluded =\n      (excludes.length &&\n        excludes.some((item) => item?.contains(mouseUpTarget))) ||\n      (excludes.length && excludes.includes(mouseDownTarget as HTMLElement))\n    const isContainedByPopper =\n      popperRef &&\n      (popperRef.contains(mouseUpTarget) || popperRef.contains(mouseDownTarget))\n    if (\n      isBound ||\n      isTargetExists ||\n      isContainedByEl ||\n      isSelf ||\n      isTargetExcluded ||\n      isContainedByPopper\n    ) {\n      return\n    }\n    binding.value(mouseup, mousedown)\n  }\n}\n\nconst ClickOutside: ObjectDirective = {\n  beforeMount(el: HTMLElement, binding: DirectiveBinding) {\n    // there could be multiple handlers on the element\n    if (!nodeList.has(el)) {\n      nodeList.set(el, [])\n    }\n\n    nodeList.get(el)!.push({\n      documentHandler: createDocumentHandler(el, binding),\n      bindingFn: binding.value,\n    })\n  },\n  updated(el: HTMLElement, binding: DirectiveBinding) {\n    if (!nodeList.has(el)) {\n      nodeList.set(el, [])\n    }\n\n    const handlers = nodeList.get(el)!\n    const oldHandlerIndex = handlers.findIndex(\n      (item) => item.bindingFn === binding.oldValue\n    )\n    const newHandler = {\n      documentHandler: createDocumentHandler(el, binding),\n      bindingFn: binding.value,\n    }\n\n    if (oldHandlerIndex >= 0) {\n      // replace the old handler to the new handler\n      handlers.splice(oldHandlerIndex, 1, newHandler)\n    } else {\n      handlers.push(newHandler)\n    }\n  },\n  unmounted(el: HTMLElement) {\n    // remove all listeners when a component unmounted\n    nodeList.delete(el)\n  },\n}\n\nexport default ClickOutside\n"], "names": ["isClient", "isArray", "isElement"], "mappings": ";;;;;;;;AACA,MAAM,QAAQ,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC3C,IAAIA,aAAQ,EAAE;AACd,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,UAAU,GAAG,CAAC,CAAC,CAAC;AAChE,EAAE,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK;AAC9C,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,KAAK,MAAM,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE,EAAE;AAChD,QAAQ,KAAK,MAAM,EAAE,eAAe,EAAE,IAAI,QAAQ,EAAE;AACpD,UAAU,eAAe,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AACzC,SAAS;AACT,OAAO;AACP,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC;AAC1B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,CAAC;AACD,SAAS,qBAAqB,CAAC,EAAE,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,QAAQ,GAAG,EAAE,CAAC;AACpB,EAAE,IAAIC,cAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AAC5B,IAAI,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC;AAC3B,GAAG,MAAM,IAAIC,eAAS,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACrC,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,SAAS,OAAO,EAAE,SAAS,EAAE;AACtC,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;AACjD,IAAI,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;AACzC,IAAI,MAAM,eAAe,GAAG,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AAC1E,IAAI,MAAM,OAAO,GAAG,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;AAClD,IAAI,MAAM,cAAc,GAAG,CAAC,aAAa,IAAI,CAAC,eAAe,CAAC;AAC9D,IAAI,MAAM,eAAe,GAAG,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;AACvF,IAAI,MAAM,MAAM,GAAG,EAAE,KAAK,aAAa,CAAC;AACxC,IAAI,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;AACvL,IAAI,MAAM,mBAAmB,GAAG,SAAS,KAAK,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;AACxH,IAAI,IAAI,OAAO,IAAI,cAAc,IAAI,eAAe,IAAI,MAAM,IAAI,gBAAgB,IAAI,mBAAmB,EAAE;AAC3G,MAAM,OAAO;AACb,KAAK;AACL,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACtC,GAAG,CAAC;AACJ,CAAC;AACI,MAAC,YAAY,GAAG;AACrB,EAAE,WAAW,CAAC,EAAE,EAAE,OAAO,EAAE;AAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AAC3B,MAAM,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;AAC1B,MAAM,eAAe,EAAE,qBAAqB,CAAC,EAAE,EAAE,OAAO,CAAC;AACzD,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK;AAC9B,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AAC3B,MAAM,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC3B,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACtC,IAAI,MAAM,eAAe,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC9F,IAAI,MAAM,UAAU,GAAG;AACvB,MAAM,eAAe,EAAE,qBAAqB,CAAC,EAAE,EAAE,OAAO,CAAC;AACzD,MAAM,SAAS,EAAE,OAAO,CAAC,KAAK;AAC9B,KAAK,CAAC;AACN,IAAI,IAAI,eAAe,IAAI,CAAC,EAAE;AAC9B,MAAM,QAAQ,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;AACtD,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChC,KAAK;AACL,GAAG;AACH,EAAE,SAAS,CAAC,EAAE,EAAE;AAChB,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACxB,GAAG;AACH;;;;"}