export var SECONDS_A_MINUTE = 60;
export var SECONDS_A_HOUR = SECONDS_A_MINUTE * 60;
export var SECONDS_A_DAY = SECONDS_A_HOUR * 24;
export var SECONDS_A_WEEK = SECONDS_A_DAY * 7;
export var MILLISECONDS_A_SECOND = 1e3;
export var MILLISECONDS_A_MINUTE = SECONDS_A_MINUTE * MILLISECONDS_A_SECOND;
export var MILLISECONDS_A_HOUR = SECONDS_A_HOUR * MILLISECONDS_A_SECOND;
export var MILLISECONDS_A_DAY = SECONDS_A_DAY * MILLISECONDS_A_SECOND;
export var MILLISECONDS_A_WEEK = SECONDS_A_WEEK * MILLISECONDS_A_SECOND; // English locales

export var MS = 'millisecond';
export var S = 'second';
export var MIN = 'minute';
export var H = 'hour';
export var D = 'day';
export var W = 'week';
export var M = 'month';
export var Q = 'quarter';
export var Y = 'year';
export var DATE = 'date';
export var FORMAT_DEFAULT = 'YYYY-MM-DDTHH:mm:ssZ';
export var INVALID_DATE_STRING = 'Invalid Date'; // regex

export var REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
export var REGEX_FORMAT = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g;