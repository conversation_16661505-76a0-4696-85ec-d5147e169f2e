import{s as e}from"./index-bcbc0702.js";import{useUserSessionStore as t}from"./userSession-03354358.js";import"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";function i(e={}){const i=t();return i.editToken?{...e,Authorization:`Edit ${i.editToken}`}:e}function a(e={}){const i=t();return i.resubmitToken?{...e,Authorization:`Resubmit ${i.resubmitToken}`}:e}const s={cancel:t=>e.post("/api/application/cancel",{reason:t},{headers:i()}),revoke:t=>e.post("/api/application/revoke",{reason:t},{headers:i()}),edit:t=>e.patch("/api/application/edit",t,{headers:i()}),resubmit:t=>e.post("/api/application/resubmit",t,{headers:a()}),getResubmitChallenge:()=>e.get("/api/application/resubmit/capture/challenge",{headers:a()}),getResubmitTarget:()=>e.get("/api/application/resubmit/target",{headers:a()}),getStatus:()=>e.get("/api/application/status",{headers:i()}),getDetail:()=>e.get("/api/application/detail",{headers:i()})};export{s as applicationApi};
