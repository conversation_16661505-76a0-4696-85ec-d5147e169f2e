import{_ as a,s}from"./index-bcbc0702.js";/* empty css                      *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                       */import{r as t,d as e,A as l,B as i,C as c,D as d,H as r,F as u,G as n,ae as o,af as v,J as p,b2 as g,al as y,E as m,br as f,L as h,b8 as b,b9 as _,bc as w,I as A,bC as j,b5 as I,b7 as k,bD as C}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const P={class:"security-audit"},z={class:"stats-grid"},S={class:"stat-card"},D={class:"stat-content"},U={class:"stat-value"},x={class:"stat-card"},J={class:"stat-content"},O={class:"stat-value"},L={class:"stat-card"},N={class:"stat-content"},V={class:"stat-value"},q={class:"stat-card"},E={class:"stat-content"},F={class:"stat-value"},H={class:"time-range-selector"},R={key:0,class:"suspicious-activities"},B={class:"activity-section"},G={class:"activity-list"},Q={class:"activity-info"},$={class:"activity-user"},K={class:"activity-details"},M={class:"activity-meta"},T={key:0,class:"activity-section"},W={class:"activity-list"},X={class:"activity-info"},Y={class:"activity-user"},Z={class:"activity-meta"},aa={class:"charts-section"},sa={class:"chart-container"},ta={class:"logs-section"},ea={class:"details-preview"},la={class:"pagination-container"},ia=a({__name:"SecurityAudit",setup(a){const ia=t(!1),ca=t("7"),da=t(1),ra=t(20),ua=t(0),na=e({totalAttempts:0,successfulAttempts:0,failedAttempts:0,successRate:0,uniqueUsers:0}),oa=e({failedAttempts:[],blockedIPs:[],multipleFailures:[]}),va=t([]),pa=t(null),ga=async()=>{ia.value=!0;try{await Promise.all([ya(),ma(),fa()])}catch(a){m.error("加载数据失败")}finally{ia.value=!1}},ya=async()=>{try{const a=await s.get(`/api/admin/security/auth-stats?days=${ca.value}`);a.data.success&&Object.assign(na,a.data.data)}catch(a){}},ma=async()=>{try{const a=await s.get("/api/admin/security/suspicious-activity?hours="+24*ca.value);a.data.success&&Object.assign(oa,a.data.data)}catch(a){}},fa=async()=>{try{const a=await s.get("/api/admin/security/audit-logs",{params:{page:da.value,pageSize:ra.value,days:ca.value}});a.data.success&&(va.value=a.data.data.logs,ua.value=a.data.data.total)}catch(a){}},ha=a=>new Date(a).toLocaleString("zh-CN");return l(()=>{ga()}),(a,s)=>{const t=j,e=f,l=h,m=I,ya=k,ma=C,ba=b,_a=_,wa=w;return i(),c("div",P,[s[21]||(s[21]=d("div",{class:"page-header"},[d("h1",null,"安全审计"),d("p",null,"监控系统安全事件和认证活动")],-1)),d("div",z,[d("div",S,[s[4]||(s[4]=d("div",{class:"stat-icon success"},[d("i",{class:"el-icon-success"})],-1)),d("div",D,[d("div",U,r(na.successfulAttempts),1),s[3]||(s[3]=d("div",{class:"stat-label"},"成功认证",-1))])]),d("div",x,[s[6]||(s[6]=d("div",{class:"stat-icon error"},[d("i",{class:"el-icon-error"})],-1)),d("div",J,[d("div",O,r(na.failedAttempts),1),s[5]||(s[5]=d("div",{class:"stat-label"},"失败认证",-1))])]),d("div",L,[s[8]||(s[8]=d("div",{class:"stat-icon info"},[d("i",{class:"el-icon-user"})],-1)),d("div",N,[d("div",V,r(na.uniqueUsers),1),s[7]||(s[7]=d("div",{class:"stat-label"},"活跃用户",-1))])]),d("div",q,[s[10]||(s[10]=d("div",{class:"stat-icon warning"},[d("i",{class:"el-icon-warning"})],-1)),d("div",E,[d("div",F,r(na.successRate)+"%",1),s[9]||(s[9]=d("div",{class:"stat-label"},"成功率",-1))])])]),d("div",H,[u(e,{modelValue:ca.value,"onUpdate:modelValue":s[0]||(s[0]=a=>ca.value=a),onChange:ga},{default:n(()=>[u(t,{label:"1"},{default:n(()=>s[11]||(s[11]=[A("最近1天",-1)])),_:1,__:[11]}),u(t,{label:"7"},{default:n(()=>s[12]||(s[12]=[A("最近7天",-1)])),_:1,__:[12]}),u(t,{label:"30"},{default:n(()=>s[13]||(s[13]=[A("最近30天",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"]),u(l,{onClick:ga,loading:ia.value},{default:n(()=>s[14]||(s[14]=[d("i",{class:"el-icon-refresh"},null,-1),A(" 刷新数据 ",-1)])),_:1,__:[14]},8,["loading"])]),oa.failedAttempts.length>0?(i(),c("div",R,[s[18]||(s[18]=d("h2",null,"可疑活动警报",-1)),d("div",B,[s[15]||(s[15]=d("h3",null,"失败认证尝试",-1)),d("div",G,[(i(!0),c(o,null,v(oa.failedAttempts.slice(0,10),a=>(i(),c("div",{key:a.id,class:"activity-item failed"},[d("div",Q,[d("div",$,"用户: "+r(a.userId),1),d("div",K,r(a.details.error||"认证失败"),1),d("div",M," IP: "+r(a.ipAddress)+" | 时间: "+r(ha(a.createdAt)),1)])]))),128))])]),oa.blockedIPs.length>0?(i(),c("div",T,[s[17]||(s[17]=d("h3",null,"被阻止的IP地址",-1)),d("div",W,[(i(!0),c(o,null,v(oa.blockedIPs.slice(0,5),a=>(i(),c("div",{key:a.id,class:"activity-item blocked"},[d("div",X,[d("div",Y,"IP: "+r(a.ipAddress),1),s[16]||(s[16]=d("div",{class:"activity-details"},"频率限制触发",-1)),d("div",Z," 用户: "+r(a.userId)+" | 时间: "+r(ha(a.createdAt)),1)])]))),128))])])):p("",!0)])):p("",!0),d("div",aa,[s[19]||(s[19]=d("h2",null,"认证趋势",-1)),d("div",sa,[d("canvas",{ref_key:"chartCanvas",ref:pa,width:"800",height:"400"},null,512)])]),d("div",ta,[s[20]||(s[20]=d("h2",null,"详细审计日志",-1)),g((i(),y(ba,{data:va.value,style:{width:"100%"}},{default:n(()=>[u(m,{prop:"userId",label:"用户ID",width:"120"}),u(m,{prop:"action",label:"操作",width:"150"}),u(m,{prop:"result",label:"结果",width:"100"},{default:n(a=>[u(ya,{type:"success"===a.row.result?"success":"failure"===a.row.result?"danger":"blocked"===a.row.result?"warning":"info"},{default:n(()=>[A(r(a.row.result),1)]),_:2},1032,["type"])]),_:1}),u(m,{prop:"ipAddress",label:"IP地址",width:"140"}),u(m,{prop:"createdAt",label:"时间",width:"180"},{default:n(a=>[A(r(ha(a.row.createdAt)),1)]),_:1}),u(m,{prop:"details",label:"详情"},{default:n(a=>[u(ma,{placement:"top",width:"300",trigger:"hover"},{reference:n(()=>{return[d("span",ea,r((s=a.row.details,"string"==typeof s?s:"object"==typeof s?s.error||s.message||JSON.stringify(s).substring(0,50)+"...":"无详情")),1)];var s}),default:n(()=>[d("pre",null,r(JSON.stringify(a.row.details,null,2)),1)]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[wa,ia.value]]),d("div",la,[u(_a,{"current-page":da.value,"onUpdate:currentPage":s[1]||(s[1]=a=>da.value=a),"page-size":ra.value,"onUpdate:pageSize":s[2]||(s[2]=a=>ra.value=a),"page-sizes":[10,20,50,100],total:ua.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:fa,onCurrentChange:fa},null,8,["current-page","page-size","total"])])])])}}},[["__scopeId","data-v-8b6002d6"]]);export{ia as default};
