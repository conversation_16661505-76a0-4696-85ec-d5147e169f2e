TN:
SF:lib\bitmapper.js
FN:7,(anonymous_0)
FN:11,(anonymous_1)
FN:25,(anonymous_2)
FN:39,(anonymous_3)
FN:52,(anonymous_4)
FN:66,(anonymous_5)
FN:70,(anonymous_6)
FN:80,(anonymous_7)
FN:90,(anonymous_8)
FN:99,(anonymous_9)
FN:107,bitRetriever
FN:111,split
FN:153,(anonymous_12)
FN:161,(anonymous_13)
FN:164,(anonymous_14)
FN:172,mapImage8Bit
FN:187,mapImageCustomBit
FN:202,(anonymous_17)
FN:229,(anonymous_18)
FNF:19
FNH:17
FNDA:0,(anonymous_0)
FNDA:313632,(anonymous_1)
FNDA:8192,(anonymous_2)
FNDA:1228160,(anonymous_3)
FNDA:272640,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:1193380,(anonymous_6)
FNDA:8192,(anonymous_7)
FNDA:24576,(anonymous_8)
FNDA:8192,(anonymous_9)
FNDA:209,bitRetriever
FNDA:334250,split
FNDA:1234340,(anonymous_12)
FNDA:8106,(anonymous_13)
FNDA:209,(anonymous_14)
FNDA:210,mapImage8Bit
FNDA:529,mapImageCustomBit
FNDA:335,(anonymous_17)
FNDA:2732114,(anonymous_18)
DA:3,1
DA:5,1
DA:12,313632
DA:13,0
DA:16,313632
DA:17,313632
DA:18,313632
DA:19,313632
DA:20,313632
DA:26,8192
DA:27,0
DA:30,8192
DA:31,8192
DA:32,8192
DA:33,8192
DA:34,8192
DA:40,1228160
DA:41,0
DA:44,1228160
DA:45,1228160
DA:46,1228160
DA:47,1228160
DA:53,272640
DA:54,0
DA:57,272640
DA:58,272640
DA:59,272640
DA:60,272640
DA:64,1
DA:71,1193380
DA:72,1193380
DA:73,1193380
DA:74,1193380
DA:75,1193380
DA:81,8192
DA:82,8192
DA:83,8192
DA:84,8192
DA:85,8192
DA:91,24576
DA:92,24576
DA:93,24576
DA:94,24576
DA:100,8192
DA:101,8192
DA:102,8192
DA:103,8192
DA:108,209
DA:109,209
DA:112,334250
DA:113,0
DA:115,334250
DA:116,334250
DA:118,334250
DA:120,0
DA:122,149504
DA:123,149504
DA:124,149504
DA:125,149504
DA:127,49612
DA:128,49612
DA:129,49612
DA:130,49612
DA:132,2968
DA:133,2968
DA:134,2968
DA:135,2968
DA:136,2968
DA:137,2968
DA:139,132166
DA:140,132166
DA:141,132166
DA:142,132166
DA:143,132166
DA:144,132166
DA:145,132166
DA:146,132166
DA:147,132166
DA:148,132166
DA:152,209
DA:154,1234340
DA:155,334250
DA:157,1234340
DA:158,1234340
DA:159,1234340
DA:162,8106
DA:165,209
DA:166,0
DA:174,210
DA:175,210
DA:176,210
DA:177,210
DA:178,7288
DA:179,1822624
DA:180,1822624
DA:181,1822624
DA:184,210
DA:189,529
DA:190,529
DA:191,529
DA:192,529
DA:193,8106
DA:194,1234340
DA:195,1234340
DA:196,1234340
DA:198,8106
DA:202,1
DA:203,335
DA:204,335
DA:205,335
DA:206,335
DA:207,335
DA:210,335
DA:211,209
DA:214,335
DA:215,269
DA:217,66
DA:219,335
DA:220,335
DA:224,335
DA:225,72
DA:226,72
DA:228,263
DA:229,263
DA:230,2732114
DA:231,2732114
DA:232,2732114
DA:234,263
DA:237,335
DA:238,739
DA:239,210
DA:248,529
DA:258,335
DA:259,126
DA:260,0
DA:263,209
DA:266,335
LF:137
LH:129
BRDA:12,0,0,0
BRDA:12,0,1,313632
BRDA:26,1,0,0
BRDA:26,1,1,8192
BRDA:40,2,0,0
BRDA:40,2,1,1228160
BRDA:53,3,0,0
BRDA:53,3,1,272640
BRDA:112,4,0,0
BRDA:112,4,1,334250
BRDA:118,5,0,0
BRDA:118,5,1,149504
BRDA:118,5,2,49612
BRDA:118,5,3,2968
BRDA:118,5,4,132166
BRDA:165,6,0,0
BRDA:165,6,1,209
BRDA:210,7,0,209
BRDA:210,7,1,126
BRDA:214,8,0,269
BRDA:214,8,1,66
BRDA:224,9,0,72
BRDA:224,9,1,263
BRDA:238,10,0,210
BRDA:238,10,1,529
BRDA:258,11,0,126
BRDA:258,11,1,209
BRDA:259,12,0,0
BRDA:259,12,1,126
BRF:29
BRH:21
end_of_record
TN:
SF:lib\bitpacker.js
FN:5,(anonymous_0)
FN:11,(anonymous_1)
FN:52,getRGBA
FNF:3
FNH:2
FNDA:325,(anonymous_0)
FNDA:325,(anonymous_1)
FNDA:0,getRGBA
DA:3,1
DA:5,1
DA:7,325
DA:10,325
DA:11,325
DA:12,325
DA:13,325
DA:15,325
DA:18,325
DA:19,325
DA:24,0
DA:26,0
DA:27,0
DA:28,0
DA:29,0
DA:31,0
DA:32,0
DA:33,0
DA:34,0
DA:36,0
DA:38,0
DA:39,0
DA:41,0
DA:42,0
DA:43,0
DA:45,0
DA:46,0
DA:48,0
DA:49,0
DA:56,0
DA:57,0
DA:59,0
DA:60,0
DA:61,0
DA:62,0
DA:63,0
DA:65,0
DA:66,0
DA:67,0
DA:68,0
DA:70,0
DA:71,0
DA:72,0
DA:73,0
DA:74,0
DA:76,0
DA:77,0
DA:78,0
DA:79,0
DA:81,0
DA:88,0
DA:89,0
DA:90,0
DA:91,0
DA:95,0
DA:99,0
DA:105,0
DA:108,0
DA:109,0
DA:110,0
DA:112,0
DA:115,0
DA:116,0
DA:117,0
DA:118,0
DA:119,0
DA:120,0
DA:123,0
DA:124,0
DA:125,0
DA:126,0
DA:127,0
DA:130,0
DA:134,0
DA:135,0
DA:136,0
DA:137,0
DA:138,0
DA:141,0
DA:142,0
DA:143,0
DA:146,0
DA:149,0
DA:152,0
DA:153,0
DA:157,0
LF:86
LH:10
BRDA:10,0,0,325
BRDA:10,0,1,0
BRDA:18,1,0,325
BRDA:18,1,1,0
BRDA:18,2,0,325
BRDA:18,2,1,0
BRDA:18,2,2,0
BRDA:24,3,0,0
BRDA:24,3,1,0
BRDA:28,4,0,0
BRDA:28,4,1,0
BRDA:28,5,0,0
BRDA:28,5,1,0
BRDA:32,6,0,0
BRDA:32,6,1,0
BRDA:41,7,0,0
BRDA:41,7,1,0
BRDA:42,8,0,0
BRDA:42,8,1,0
BRDA:45,9,0,0
BRDA:45,9,1,0
BRDA:48,10,0,0
BRDA:48,10,1,0
BRDA:57,11,0,0
BRDA:57,11,1,0
BRDA:57,11,2,0
BRDA:57,11,3,0
BRDA:57,11,4,0
BRDA:88,12,0,0
BRDA:88,12,1,0
BRDA:89,13,0,0
BRDA:89,13,1,0
BRDA:112,14,0,0
BRDA:112,14,1,0
BRDA:112,14,2,0
BRDA:112,14,3,0
BRDA:112,14,4,0
BRDA:115,15,0,0
BRDA:115,15,1,0
BRDA:119,16,0,0
BRDA:119,16,1,0
BRDA:126,17,0,0
BRDA:126,17,1,0
BRDA:135,18,0,0
BRDA:135,18,1,0
BRDA:137,19,0,0
BRDA:137,19,1,0
BRDA:142,20,0,0
BRDA:142,20,1,0
BRF:49
BRH:3
end_of_record
TN:
SF:lib\chunkstream.js
FN:6,(anonymous_0)
FN:20,(anonymous_1)
FN:28,(anonymous_2)
FN:41,(anonymous_3)
FN:67,(anonymous_4)
FN:90,(anonymous_5)
FN:98,(anonymous_6)
FN:110,(anonymous_7)
FN:132,(anonymous_8)
FN:163,(anonymous_9)
FNF:10
FNH:10
FNDA:529,(anonymous_0)
FNDA:13318,(anonymous_1)
FNDA:13318,(anonymous_2)
FNDA:943,(anonymous_3)
FNDA:311,(anonymous_4)
FNDA:311,(anonymous_5)
FNDA:327,(anonymous_6)
FNDA:741,(anonymous_7)
FNDA:12408,(anonymous_8)
FNDA:14263,(anonymous_9)
DA:3,1
DA:4,1
DA:6,1
DA:7,529
DA:9,529
DA:10,529
DA:12,529
DA:13,529
DA:15,529
DA:16,529
DA:18,1
DA:20,1
DA:21,13318
DA:27,13318
DA:29,13318
DA:32,13318
DA:33,0
DA:35,0
DA:41,1
DA:42,943
DA:43,0
DA:44,0
DA:48,943
DA:49,943
DA:51,0
DA:54,943
DA:55,943
DA:57,943
DA:60,943
DA:61,346
DA:64,943
DA:67,1
DA:68,311
DA:69,0
DA:72,311
DA:75,311
DA:76,0
DA:80,311
DA:81,309
DA:83,2
DA:84,2
DA:88,1
DA:90,1
DA:91,311
DA:92,3
DA:95,311
DA:98,1
DA:99,327
DA:100,0
DA:103,327
DA:104,327
DA:105,327
DA:107,327
DA:110,1
DA:112,741
DA:115,741
DA:118,741
DA:119,568
DA:120,568
DA:122,568
DA:125,173
DA:126,173
DA:128,173
DA:132,1
DA:133,12408
DA:135,12408
DA:136,12408
DA:137,12408
DA:140,12408
DA:141,12617
DA:142,12617
DA:144,12617
DA:145,12617
DA:148,12617
DA:149,11863
DA:154,12408
DA:155,532
DA:158,12408
DA:160,12408
DA:163,1
DA:164,14263
DA:166,14263
DA:167,15656
DA:170,15656
DA:171,741
DA:172,14915
DA:175,12408
DA:179,2507
DA:183,14263
DA:184,2
DA:187,0
LF:91
LH:82
BRDA:32,0,0,0
BRDA:32,0,1,13318
BRDA:32,1,0,13318
BRDA:32,1,1,10215
BRDA:42,2,0,0
BRDA:42,2,1,943
BRDA:48,3,0,943
BRDA:48,3,1,0
BRDA:51,4,0,0
BRDA:51,4,1,0
BRDA:60,5,0,346
BRDA:60,5,1,597
BRDA:60,6,0,943
BRDA:60,6,1,929
BRDA:64,7,0,943
BRDA:64,7,1,929
BRDA:68,8,0,0
BRDA:68,8,1,311
BRDA:75,9,0,0
BRDA:75,9,1,311
BRDA:80,10,0,309
BRDA:80,10,1,2
BRDA:91,11,0,3
BRDA:91,11,1,308
BRDA:99,12,0,0
BRDA:99,12,1,327
BRDA:118,13,0,568
BRDA:118,13,1,173
BRDA:148,14,0,11863
BRDA:148,14,1,754
BRDA:154,15,0,532
BRDA:154,15,1,11876
BRDA:166,16,0,27412
BRDA:166,16,1,15708
BRDA:166,16,2,15671
BRDA:170,17,0,741
BRDA:170,17,1,14915
BRDA:172,18,0,12408
BRDA:172,18,1,2507
BRDA:183,19,0,2
BRDA:183,19,1,14261
BRDA:183,20,0,14263
BRDA:183,20,1,14213
BRF:43
BRH:35
end_of_record
TN:
SF:lib\constants.js
FNF:0
FNH:0
DA:3,1
LF:1
LH:1
BRF:0
BRH:0
end_of_record
TN:
SF:lib\crc.js
FN:5,(anonymous_0)
FN:19,(anonymous_1)
FN:23,(anonymous_2)
FN:30,(anonymous_3)
FN:34,(anonymous_4)
FNF:5
FNH:5
FNDA:1,(anonymous_0)
FNDA:2461,(anonymous_1)
FNDA:4839,(anonymous_2)
FNDA:2195,(anonymous_3)
FNDA:1592,(anonymous_4)
DA:3,1
DA:5,1
DA:6,1
DA:7,256
DA:8,256
DA:9,2048
DA:10,1024
DA:12,1024
DA:15,256
DA:19,1
DA:20,2461
DA:23,1
DA:24,4839
DA:25,823519
DA:27,4839
DA:30,1
DA:31,2195
DA:34,1
DA:35,1592
DA:36,1592
DA:37,305287
DA:39,1592
LF:22
LH:22
BRDA:9,0,0,1024
BRDA:9,0,1,1024
BRF:2
BRH:2
end_of_record
TN:
SF:lib\filter-pack.js
FN:5,filterNone
FN:11,filterSumNone
FN:21,filterSub
FN:30,filterSumSub
FN:42,filterUp
FN:51,filterSumUp
FN:64,filterAvg
FN:74,filterSumAvg
FN:87,filterPaeth
FN:99,filterSumPaeth
FN:130,(anonymous_10)
FNF:11
FNH:11
FNDA:44,filterNone
FNDA:10490,filterSumNone
FNDA:637,filterSub
FNDA:10490,filterSumSub
FNDA:2670,filterUp
FNDA:10490,filterSumUp
FNDA:208,filterAvg
FNDA:10490,filterSumAvg
FNDA:6931,filterPaeth
FNDA:10490,filterSumPaeth
FNDA:325,(anonymous_10)
DA:3,1
DA:6,44
DA:7,5136
DA:12,10490
DA:13,10490
DA:15,10490
DA:16,2776736
DA:18,10490
DA:22,637
DA:23,107032
DA:24,107032
DA:26,107032
DA:31,10490
DA:32,10490
DA:33,2776736
DA:34,2776736
DA:36,2776736
DA:39,10490
DA:43,2670
DA:44,581344
DA:45,581344
DA:47,581344
DA:52,10490
DA:53,10490
DA:54,10490
DA:55,2776736
DA:56,2776736
DA:58,2776736
DA:61,10490
DA:65,208
DA:66,204096
DA:67,204096
DA:68,204096
DA:70,204096
DA:75,10490
DA:76,10490
DA:77,2776736
DA:78,2776736
DA:79,2776736
DA:81,2776736
DA:84,10490
DA:88,6931
DA:89,1879128
DA:90,1879128
DA:92,1879128
DA:93,1879128
DA:95,1879128
DA:100,10490
DA:101,10490
DA:102,2776736
DA:103,2776736
DA:105,2776736
DA:106,2776736
DA:108,2776736
DA:111,10490
DA:114,1
DA:122,1
DA:130,1
DA:132,325
DA:133,325
DA:134,0
DA:135,0
DA:137,0
DA:140,325
DA:141,0
DA:143,325
DA:144,325
DA:145,325
DA:146,325
DA:148,325
DA:150,325
DA:151,10490
DA:153,10490
DA:155,10490
DA:156,52450
DA:157,52450
DA:158,35370
DA:159,35370
DA:164,10490
DA:165,10490
DA:166,10490
DA:167,10490
DA:168,10490
DA:170,325
LF:84
LH:80
BRDA:23,0,0,104484
BRDA:23,0,1,2548
BRDA:33,1,0,2734776
BRDA:33,1,1,41960
BRDA:44,2,0,581344
BRDA:44,2,1,0
BRDA:55,3,0,2734120
BRDA:55,3,1,42616
BRDA:66,4,0,203264
BRDA:66,4,1,832
BRDA:67,5,0,204096
BRDA:67,5,1,0
BRDA:77,6,0,2734776
BRDA:77,6,1,41960
BRDA:78,7,0,2734120
BRDA:78,7,1,42616
BRDA:89,8,0,1851404
BRDA:89,8,1,27724
BRDA:90,9,0,1879128
BRDA:90,9,1,0
BRDA:92,10,0,1851404
BRDA:92,10,1,27724
BRDA:92,11,0,1879128
BRDA:92,11,1,1879128
BRDA:102,12,0,2734776
BRDA:102,12,1,41960
BRDA:103,13,0,2734120
BRDA:103,13,1,42616
BRDA:105,14,0,2693460
BRDA:105,14,1,83276
BRDA:105,15,0,2776736
BRDA:105,15,1,2734120
BRDA:132,16,0,325
BRDA:132,16,1,0
BRDA:132,17,0,325
BRDA:132,17,1,1
BRDA:134,18,0,0
BRDA:134,18,1,0
BRDA:140,19,0,0
BRDA:140,19,1,325
BRDA:151,20,0,10490
BRDA:151,20,1,0
BRDA:157,21,0,35370
BRDA:157,21,1,17080
BRF:44
BRH:36
end_of_record
TN:
SF:lib\filter-parse-async.js
FN:7,(anonymous_0)
FN:14,(anonymous_1)
FN:17,(anonymous_2)
FNF:3
FNH:3
FNDA:176,(anonymous_0)
FNDA:9057,(anonymous_1)
FNDA:173,(anonymous_2)
DA:3,1
DA:4,1
DA:5,1
DA:7,1
DA:8,176
DA:10,176
DA:11,176
DA:12,176
DA:15,9057
DA:18,173
DA:22,176
DA:24,1
LF:12
LH:12
BRF:0
BRH:0
end_of_record
TN:
SF:lib\filter-parse-sync.js
FN:6,(anonymous_0)
FN:11,(anonymous_1)
FN:14,(anonymous_2)
FNF:3
FNH:3
FNDA:162,(anonymous_0)
FNDA:6337,(anonymous_1)
FNDA:162,(anonymous_2)
DA:3,1
DA:4,1
DA:6,1
DA:7,162
DA:8,162
DA:9,162
DA:12,6337
DA:17,162
DA:18,162
DA:20,162
LF:10
LH:10
BRF:0
BRH:0
end_of_record
TN:
SF:lib\filter-parse.js
FN:6,getByteWidth
FN:14,(anonymous_1)
FN:57,(anonymous_2)
FN:64,(anonymous_3)
FN:79,(anonymous_4)
FN:93,(anonymous_5)
FN:111,(anonymous_6)
FN:130,(anonymous_7)
FNF:8
FNH:8
FNDA:742,getByteWidth
FNDA:338,(anonymous_1)
FNDA:338,(anonymous_2)
FNDA:1939,(anonymous_3)
FNDA:800,(anonymous_4)
FNDA:172,(anonymous_5)
FNDA:4165,(anonymous_6)
FNDA:15394,(anonymous_7)
DA:3,1
DA:4,1
DA:7,742
DA:8,742
DA:9,531
DA:11,742
DA:14,1
DA:15,338
DA:16,338
DA:17,338
DA:18,338
DA:19,338
DA:21,338
DA:22,338
DA:23,338
DA:25,338
DA:26,338
DA:27,338
DA:28,72
DA:29,72
DA:30,476
DA:37,266
DA:48,338
DA:49,127
DA:50,211
DA:51,66
DA:53,145
DA:57,1
DA:58,338
DA:64,1
DA:69,1939
DA:70,1939
DA:72,1939
DA:73,3563504
DA:74,3563504
DA:75,3563504
DA:79,1
DA:84,800
DA:86,800
DA:87,330256
DA:88,330256
DA:89,330256
DA:93,1
DA:98,172
DA:99,172
DA:100,172
DA:102,172
DA:103,9952
DA:104,9952
DA:105,9952
DA:106,9952
DA:107,9952
DA:111,1
DA:116,4165
DA:117,4165
DA:118,4165
DA:120,4165
DA:121,1152544
DA:122,1152544
DA:123,1152544
DA:124,1152544
DA:125,1152544
DA:126,1152544
DA:130,1
DA:131,15394
DA:133,15394
DA:134,15394
DA:136,15394
DA:137,8318
DA:139,7076
DA:141,7076
DA:143,1939
DA:144,1939
DA:146,800
DA:147,800
DA:149,172
DA:150,172
DA:152,4165
DA:153,4165
DA:155,0
DA:159,15394
DA:161,15394
DA:162,15394
DA:163,739
DA:164,739
DA:165,739
DA:167,14655
DA:170,15394
DA:172,15059
DA:174,335
DA:175,335
LF:91
LH:90
BRDA:8,0,0,531
BRDA:8,0,1,211
BRDA:27,1,0,72
BRDA:27,1,1,266
BRDA:48,2,0,127
BRDA:48,2,1,211
BRDA:50,3,0,66
BRDA:50,3,1,145
BRDA:74,4,0,3557696
BRDA:74,4,1,5808
BRDA:88,5,0,330000
BRDA:88,5,1,256
BRDA:104,6,0,9536
BRDA:104,6,1,416
BRDA:105,7,0,9554
BRDA:105,7,1,398
BRDA:122,8,0,1152288
BRDA:122,8,1,256
BRDA:123,9,0,1137296
BRDA:123,9,1,15248
BRDA:124,10,0,1137048
BRDA:124,10,1,15496
BRDA:124,11,0,1152544
BRDA:124,11,1,1137296
BRDA:136,12,0,8318
BRDA:136,12,1,7076
BRDA:141,13,0,1939
BRDA:141,13,1,800
BRDA:141,13,2,172
BRDA:141,13,3,4165
BRDA:141,13,4,0
BRDA:162,14,0,739
BRDA:162,14,1,14655
BRDA:170,15,0,15059
BRDA:170,15,1,335
BRF:35
BRH:34
end_of_record
TN:
SF:lib\format-normaliser.js
FN:3,dePalette
FN:22,replaceTransparentColor
FN:49,scaleDepth
FN:66,(anonymous_3)
FNF:4
FNH:4
FNDA:131,dePalette
FNDA:12,replaceTransparentColor
FNDA:106,scaleDepth
FNDA:335,(anonymous_3)
DA:4,131
DA:6,131
DA:7,4912
DA:8,1418436
DA:10,1418436
DA:11,0
DA:14,1418436
DA:15,5673744
DA:17,1418436
DA:23,12
DA:24,12
DA:25,384
DA:26,12288
DA:28,12288
DA:29,6144
DA:30,2762
DA:32,6144
DA:37,2718
DA:39,12288
DA:40,5480
DA:41,21920
DA:44,12288
DA:50,106
DA:51,106
DA:52,106
DA:54,106
DA:55,3392
DA:56,108544
DA:57,434176
DA:61,108544
DA:66,1
DA:67,335
DA:68,335
DA:69,335
DA:70,335
DA:71,335
DA:72,335
DA:74,335
DA:76,335
DA:78,131
DA:80,204
DA:81,12
DA:84,204
DA:86,106
DA:87,66
DA:89,106
DA:92,335
LF:47
LH:46
BRDA:10,0,0,0
BRDA:10,0,1,1418436
BRDA:28,1,0,6144
BRDA:28,1,1,6144
BRDA:29,2,0,2762
BRDA:29,2,1,3382
BRDA:32,3,0,2718
BRDA:32,3,1,3426
BRDA:33,4,0,6144
BRDA:33,4,1,2718
BRDA:33,4,2,2718
BRDA:39,5,0,5480
BRDA:39,5,1,6808
BRDA:76,6,0,131
BRDA:76,6,1,204
BRDA:80,7,0,12
BRDA:80,7,1,192
BRDA:84,8,0,106
BRDA:84,8,1,98
BRDA:86,9,0,66
BRDA:86,9,1,40
BRF:21
BRH:20
end_of_record
TN:
SF:lib\interlace.js
FN:52,(anonymous_0)
FN:83,(anonymous_1)
FN:84,(anonymous_2)
FNF:3
FNH:3
FNDA:144,(anonymous_0)
FNDA:72,(anonymous_1)
FNDA:324850,(anonymous_2)
DA:14,1
DA:52,1
DA:53,144
DA:54,144
DA:55,144
DA:56,144
DA:57,144
DA:58,144
DA:59,1008
DA:60,1008
DA:61,1008
DA:62,1008
DA:63,1556
DA:64,672
DA:66,884
DA:69,1008
DA:70,1360
DA:71,552
DA:73,808
DA:76,1008
DA:77,952
DA:80,144
DA:83,1
DA:84,72
DA:85,324850
DA:87,324850
DA:89,324850
DA:91,324850
DA:93,324850
LF:29
LH:29
BRDA:63,0,0,672
BRDA:63,0,1,884
BRDA:70,1,0,552
BRDA:70,1,1,808
BRDA:76,2,0,952
BRDA:76,2,1,56
BRDA:76,3,0,1008
BRDA:76,3,1,980
BRF:8
BRH:8
end_of_record
TN:
SF:lib\packer-async.js
FN:8,(anonymous_0)
FN:20,(anonymous_1)
FN:36,(anonymous_2)
FN:43,(anonymous_3)
FNF:4
FNH:4
FNDA:353,(anonymous_0)
FNDA:325,(anonymous_1)
FNDA:652,(anonymous_2)
FNDA:325,(anonymous_3)
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:8,1
DA:9,353
DA:11,353
DA:13,353
DA:14,353
DA:16,353
DA:18,1
DA:20,1
DA:22,325
DA:23,325
DA:25,325
DA:26,290
DA:29,325
DA:32,325
DA:34,325
DA:37,652
DA:41,325
DA:44,325
DA:45,325
DA:49,325
LF:24
LH:24
BRDA:11,0,0,353
BRDA:11,0,1,0
BRDA:25,1,0,290
BRDA:25,1,1,35
BRF:4
BRH:3
end_of_record
TN:
SF:lib\packer-sync.js
FN:11,(anonymous_0)
FNF:1
FNH:0
FNDA:0,(anonymous_0)
DA:3,1
DA:4,1
DA:5,1
DA:6,0
DA:8,1
DA:9,1
DA:11,1
DA:12,0
DA:13,0
DA:18,0
DA:20,0
DA:22,0
DA:25,0
DA:28,0
DA:30,0
DA:31,0
DA:34,0
DA:41,0
DA:45,0
DA:47,0
DA:48,0
DA:50,0
DA:53,0
DA:55,0
LF:24
LH:6
BRDA:5,0,0,0
BRDA:5,0,1,1
BRDA:12,1,0,0
BRDA:12,1,1,0
BRDA:18,2,0,0
BRDA:18,2,1,0
BRDA:30,3,0,0
BRDA:30,3,1,0
BRDA:47,4,0,0
BRDA:47,4,1,0
BRDA:47,5,0,0
BRDA:47,5,1,0
BRF:12
BRH:1
end_of_record
TN:
SF:lib\packer.js
FN:9,(anonymous_0)
FN:64,(anonymous_1)
FN:72,(anonymous_2)
FN:76,(anonymous_3)
FN:86,(anonymous_4)
FN:104,(anonymous_5)
FN:110,(anonymous_6)
FN:123,(anonymous_7)
FN:127,(anonymous_8)
FNF:9
FNH:9
FNDA:353,(anonymous_0)
FNDA:353,(anonymous_1)
FNDA:353,(anonymous_2)
FNDA:325,(anonymous_3)
FNDA:1592,(anonymous_4)
FNDA:290,(anonymous_5)
FNDA:325,(anonymous_6)
FNDA:652,(anonymous_7)
FNDA:325,(anonymous_8)
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:9,1
DA:10,353
DA:12,353
DA:13,353
DA:15,353
DA:17,353
DA:19,353
DA:20,353
DA:22,353
DA:26,353
DA:31,353
DA:39,0
DA:43,353
DA:51,0
DA:57,353
DA:58,0
DA:64,1
DA:65,353
DA:72,1
DA:73,353
DA:76,1
DA:78,325
DA:81,325
DA:82,325
DA:83,325
DA:86,1
DA:87,1592
DA:88,1592
DA:90,1592
DA:91,1592
DA:93,1592
DA:94,1267
DA:97,1592
DA:101,1592
DA:104,1
DA:105,290
DA:106,290
DA:107,290
DA:110,1
DA:111,325
DA:112,325
DA:113,325
DA:114,325
DA:115,325
DA:116,325
DA:117,325
DA:118,325
DA:120,325
DA:123,1
DA:124,652
DA:127,1
DA:128,325
LF:57
LH:54
BRDA:12,0,0,353
BRDA:12,0,1,353
BRDA:14,1,0,0
BRDA:14,1,1,353
BRDA:16,2,0,0
BRDA:16,2,1,353
BRDA:18,3,0,0
BRDA:18,3,1,353
BRDA:19,4,0,353
BRDA:19,4,1,353
BRDA:20,5,0,353
BRDA:20,5,1,353
BRDA:23,6,0,0
BRDA:23,6,1,353
BRDA:27,7,0,0
BRDA:27,7,1,353
BRDA:31,8,0,0
BRDA:31,8,1,353
BRDA:43,9,0,0
BRDA:43,9,1,353
BRDA:57,10,0,0
BRDA:57,10,1,353
BRDA:57,11,0,353
BRDA:57,11,1,0
BRDA:87,12,0,1267
BRDA:87,12,1,325
BRDA:93,13,0,1267
BRDA:93,13,1,325
BRF:28
BRH:19
end_of_record
TN:
SF:lib\paeth-predictor.js
FN:3,paethPredictor
FNF:1
FNH:1
FNDA:5808408,paethPredictor
DA:3,1
DA:4,5808408
DA:5,5808408
DA:6,5808408
DA:7,5808408
DA:9,5808408
DA:10,5156742
DA:12,651666
DA:13,539893
DA:15,111773
LF:10
LH:10
BRDA:9,0,0,5156742
BRDA:9,0,1,651666
BRDA:9,1,0,5808408
BRDA:9,1,1,5231305
BRDA:12,2,0,539893
BRDA:12,2,1,111773
BRF:6
BRH:6
end_of_record
TN:
SF:lib\parser-async.js
FN:11,(anonymous_0)
FN:33,(anonymous_1)
FN:49,(anonymous_2)
FN:55,(anonymous_3)
FN:79,(anonymous_4)
FN:89,(anonymous_5)
FN:109,(anonymous_6)
FN:116,(anonymous_7)
FN:120,(anonymous_8)
FN:124,(anonymous_9)
FN:128,(anonymous_10)
FN:134,(anonymous_11)
FN:147,(anonymous_12)
FNF:13
FNH:11
FNDA:353,(anonymous_0)
FNDA:14,(anonymous_1)
FNDA:0,(anonymous_2)
FNDA:741,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:487,(anonymous_5)
FNDA:176,(anonymous_6)
FNDA:7,(anonymous_7)
FNDA:77,(anonymous_8)
FNDA:15,(anonymous_9)
FNDA:174,(anonymous_10)
FNDA:173,(anonymous_11)
FNDA:173,(anonymous_12)
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:8,1
DA:9,1
DA:11,1
DA:12,353
DA:14,353
DA:26,353
DA:27,353
DA:29,353
DA:31,1
DA:33,1
DA:34,14
DA:36,14
DA:38,14
DA:40,14
DA:41,1
DA:44,14
DA:45,2
DA:49,2
DA:52,14
DA:55,1
DA:56,741
DA:57,174
DA:58,36
DA:60,36
DA:61,36
DA:63,36
DA:66,138
DA:72,138
DA:73,138
DA:75,138
DA:76,138
DA:78,138
DA:79,138
DA:80,0
DA:81,0
DA:84,0
DA:86,138
DA:88,138
DA:89,138
DA:90,487
DA:91,0
DA:94,487
DA:95,0
DA:98,487
DA:100,487
DA:103,138
DA:106,741
DA:109,1
DA:110,176
DA:111,176
DA:113,176
DA:116,1
DA:117,7
DA:120,1
DA:121,77
DA:124,1
DA:125,15
DA:128,1
DA:131,174
DA:134,1
DA:135,173
DA:136,0
DA:139,173
DA:140,1
DA:143,172
DA:147,1
DA:148,173
DA:149,0
DA:154,173
DA:155,173
DA:157,173
DA:158,173
DA:160,0
DA:161,0
DA:164,173
LF:80
LH:71
BRDA:40,0,0,1
BRDA:40,0,1,13
BRDA:40,1,0,14
BRDA:40,1,1,1
BRDA:44,2,0,2
BRDA:44,2,1,12
BRDA:56,3,0,174
BRDA:56,3,1,567
BRDA:57,4,0,36
BRDA:57,4,1,138
BRDA:80,5,0,0
BRDA:80,5,1,0
BRDA:90,6,0,0
BRDA:90,6,1,487
BRDA:94,7,0,0
BRDA:94,7,1,487
BRDA:135,8,0,0
BRDA:135,8,1,173
BRDA:139,9,0,1
BRDA:139,9,1,172
BRDA:148,10,0,0
BRDA:148,10,1,173
BRF:22
BRH:16
end_of_record
TN:
SF:lib\parser-sync.js
FN:15,(anonymous_0)
FN:23,handleError
FN:28,handleMetaData
FN:32,handleTransColor
FN:36,handlePalette
FN:40,handleSimpleTransparency
FN:45,handleGamma
FN:50,handleInflateData
FNF:8
FNH:8
FNDA:176,(anonymous_0)
FNDA:13,handleError
FNDA:165,handleMetaData
FNDA:5,handleTransColor
FNDA:73,handlePalette
FNDA:12,handleSimpleTransparency
FNDA:147,handleGamma
FNDA:492,handleInflateData
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,0
DA:9,1
DA:10,1
DA:11,1
DA:12,1
DA:13,1
DA:15,1
DA:16,176
DA:17,0
DA:24,13
DA:29,165
DA:33,5
DA:37,73
DA:41,12
DA:46,147
DA:49,176
DA:51,492
DA:54,176
DA:56,176
DA:67,176
DA:68,176
DA:70,176
DA:71,13
DA:75,163
DA:76,163
DA:79,163
DA:80,36
DA:83,127
DA:84,127
DA:85,127
DA:90,162
DA:92,162
DA:93,0
DA:96,162
DA:97,162
DA:99,162
DA:100,162
DA:102,162
DA:104,162
DA:105,162
DA:107,162
LF:45
LH:42
BRDA:6,0,0,0
BRDA:6,0,1,1
BRDA:16,1,0,0
BRDA:16,1,1,176
BRDA:70,2,0,13
BRDA:70,2,1,163
BRDA:79,3,0,36
BRDA:79,3,1,127
BRDA:92,4,0,0
BRDA:92,4,1,162
BRDA:92,5,0,162
BRDA:92,5,1,162
BRDA:105,6,0,162
BRDA:105,6,1,17
BRF:14
BRH:11
end_of_record
TN:
SF:lib\parser.js
FN:6,(anonymous_0)
FN:36,(anonymous_1)
FN:39,(anonymous_2)
FN:43,(anonymous_3)
FN:55,(anonymous_4)
FN:93,(anonymous_5)
FN:97,(anonymous_6)
FN:101,(anonymous_7)
FN:116,(anonymous_8)
FN:119,(anonymous_9)
FN:183,(anonymous_10)
FN:186,(anonymous_11)
FN:201,(anonymous_12)
FN:205,(anonymous_13)
FN:241,(anonymous_14)
FN:244,(anonymous_15)
FN:251,(anonymous_16)
FN:258,(anonymous_17)
FN:278,(anonymous_18)
FN:281,(anonymous_19)
FNF:20
FNH:20
FNDA:529,(anonymous_0)
FNDA:163,(anonymous_1)
FNDA:529,(anonymous_2)
FNDA:365,(anonymous_3)
FNDA:2461,(anonymous_4)
FNDA:256,(anonymous_5)
FNDA:2195,(anonymous_6)
FNDA:2195,(anonymous_7)
FNDA:351,(anonymous_8)
FNDA:351,(anonymous_9)
FNDA:135,(anonymous_10)
FNDA:135,(anonymous_11)
FNDA:27,(anonymous_12)
FNDA:27,(anonymous_13)
FNDA:296,(anonymous_14)
FNDA:296,(anonymous_15)
FNDA:1233,(anonymous_16)
FNDA:1233,(anonymous_17)
FNDA:336,(anonymous_18)
FNDA:336,(anonymous_19)
DA:3,1
DA:4,1
DA:6,1
DA:7,529
DA:8,529
DA:10,529
DA:11,529
DA:12,529
DA:15,529
DA:16,529
DA:18,529
DA:19,529
DA:20,529
DA:21,529
DA:22,529
DA:23,529
DA:24,529
DA:26,529
DA:27,529
DA:28,529
DA:29,529
DA:30,529
DA:31,529
DA:32,529
DA:33,529
DA:34,529
DA:35,529
DA:36,529
DA:39,1
DA:40,529
DA:43,1
DA:44,365
DA:46,365
DA:47,2867
DA:48,13
DA:49,13
DA:52,352
DA:55,1
DA:57,2461
DA:60,2461
DA:61,2461
DA:62,2461
DA:63,9844
DA:69,2461
DA:73,2461
DA:74,0
DA:75,0
DA:78,2461
DA:79,2461
DA:81,2461
DA:82,2205
DA:85,256
DA:86,0
DA:87,0
DA:90,256
DA:93,1
DA:94,256
DA:97,1
DA:98,2195
DA:101,1
DA:102,2195
DA:103,2195
DA:106,2195
DA:107,4
DA:108,4
DA:111,2191
DA:112,1855
DA:116,1
DA:117,351
DA:119,1
DA:120,351
DA:122,351
DA:123,351
DA:124,351
DA:125,351
DA:126,351
DA:127,351
DA:128,351
DA:135,351
DA:142,6
DA:143,6
DA:145,345
DA:146,4
DA:147,4
DA:149,341
DA:150,0
DA:151,0
DA:153,341
DA:154,0
DA:155,0
DA:157,341
DA:158,0
DA:159,0
DA:162,341
DA:164,341
DA:166,341
DA:168,341
DA:180,341
DA:183,1
DA:184,135
DA:186,1
DA:187,135
DA:189,135
DA:192,135
DA:193,7350
DA:196,135
DA:198,135
DA:201,1
DA:202,27
DA:203,27
DA:205,1
DA:206,27
DA:209,27
DA:210,15
DA:211,0
DA:212,0
DA:214,15
DA:215,0
DA:216,0
DA:218,15
DA:219,529
DA:221,15
DA:226,27
DA:228,6
DA:230,27
DA:231,6
DA:238,27
DA:241,1
DA:242,296
DA:244,1
DA:245,296
DA:246,296
DA:248,296
DA:251,1
DA:252,1233
DA:253,337
DA:254,337
DA:256,1233
DA:258,1
DA:259,1233
DA:261,1233
DA:265,0
DA:268,1233
DA:269,1233
DA:271,1233
DA:272,173
DA:274,1060
DA:278,1
DA:279,336
DA:281,1
DA:282,336
DA:284,336
DA:285,336
DA:287,336
DA:288,173
LF:155
LH:140
BRDA:36,0,0,529
BRDA:36,0,1,176
BRDA:47,1,0,13
BRDA:47,1,1,2854
BRDA:73,2,0,0
BRDA:73,2,1,2461
BRDA:73,3,0,2461
BRDA:73,3,1,351
BRDA:81,4,0,2205
BRDA:81,4,1,256
BRDA:85,5,0,0
BRDA:85,5,1,256
BRDA:106,6,0,4
BRDA:106,6,1,2191
BRDA:106,7,0,2195
BRDA:106,7,1,2195
BRDA:111,8,0,1855
BRDA:111,8,1,336
BRDA:135,9,0,6
BRDA:135,9,1,345
BRDA:136,10,0,351
BRDA:136,10,1,219
BRDA:136,10,2,132
BRDA:136,10,3,101
BRDA:136,10,4,72
BRDA:145,11,0,4
BRDA:145,11,1,341
BRDA:149,12,0,0
BRDA:149,12,1,341
BRDA:153,13,0,0
BRDA:153,13,1,341
BRDA:157,14,0,0
BRDA:157,14,1,341
BRDA:157,15,0,341
BRDA:157,15,1,72
BRDA:209,16,0,15
BRDA:209,16,1,12
BRDA:210,17,0,0
BRDA:210,17,1,15
BRDA:214,18,0,0
BRDA:214,18,1,15
BRDA:226,19,0,6
BRDA:226,19,1,21
BRDA:230,20,0,6
BRDA:230,20,1,21
BRDA:252,21,0,337
BRDA:252,21,1,896
BRDA:261,22,0,0
BRDA:261,22,1,1233
BRDA:262,23,0,1233
BRDA:262,23,1,131
BRDA:271,24,0,173
BRDA:271,24,1,1060
BRDA:287,25,0,173
BRDA:287,25,1,163
BRF:55
BRH:47
end_of_record
TN:
SF:lib\png-sync.js
FN:6,(anonymous_0)
FN:10,(anonymous_1)
FNF:2
FNH:1
FNDA:176,(anonymous_0)
FNDA:0,(anonymous_1)
DA:3,1
DA:4,1
DA:6,1
DA:7,176
DA:10,1
DA:11,0
LF:6
LH:5
BRDA:7,0,0,176
BRDA:7,0,1,176
BRF:2
BRH:2
end_of_record
TN:
SF:lib\png.js
FN:9,(anonymous_0)
FN:38,(anonymous_1)
FN:54,(anonymous_2)
FN:61,(anonymous_3)
FN:69,(anonymous_4)
FN:73,(anonymous_5)
FN:80,(anonymous_6)
FN:94,(anonymous_7)
FN:99,(anonymous_8)
FN:103,(anonymous_9)
FN:110,(anonymous_10)
FN:114,(anonymous_11)
FN:120,(anonymous_12)
FN:160,(anonymous_13)
FN:175,(anonymous_14)
FN:192,(anonymous_15)
FNF:16
FNH:9
FNDA:353,(anonymous_0)
FNDA:173,(anonymous_1)
FNDA:325,(anonymous_2)
FNDA:325,(anonymous_3)
FNDA:0,(anonymous_4)
FNDA:0,(anonymous_5)
FNDA:0,(anonymous_6)
FNDA:412,(anonymous_7)
FNDA:175,(anonymous_8)
FNDA:174,(anonymous_9)
FNDA:149,(anonymous_10)
FNDA:378,(anonymous_11)
FNDA:0,(anonymous_12)
FNDA:0,(anonymous_13)
FNDA:0,(anonymous_14)
FNDA:0,(anonymous_15)
DA:3,1
DA:4,1
DA:5,1
DA:6,1
DA:7,1
DA:9,1
DA:10,353
DA:12,353
DA:15,353
DA:16,353
DA:18,353
DA:23,353
DA:24,0
DA:27,353
DA:28,353
DA:30,353
DA:32,353
DA:33,353
DA:34,353
DA:35,353
DA:36,353
DA:39,173
DA:40,173
DA:44,353
DA:45,353
DA:46,353
DA:47,353
DA:48,353
DA:50,1
DA:52,1
DA:54,1
DA:55,325
DA:56,0
DA:57,0
DA:60,325
DA:62,325
DA:66,325
DA:69,1
DA:70,0
DA:73,0
DA:74,0
DA:76,0
DA:77,0
DA:80,0
DA:81,0
DA:83,0
DA:86,0
DA:87,0
DA:90,0
DA:91,0
DA:94,1
DA:95,412
DA:96,412
DA:99,1
DA:100,175
DA:103,1
DA:104,174
DA:105,174
DA:107,174
DA:110,1
DA:111,149
DA:114,1
DA:115,378
DA:116,0
DA:120,1
DA:124,0
DA:125,0
DA:126,0
DA:127,0
DA:128,0
DA:129,0
DA:132,0
DA:138,0
DA:141,0
DA:147,0
DA:150,0
DA:151,0
DA:160,1
DA:171,0
DA:172,0
DA:175,1
DA:176,0
DA:177,0
DA:178,0
DA:179,0
DA:181,0
DA:182,0
DA:183,0
DA:184,0
DA:188,0
DA:192,1
DA:193,0
LF:92
LH:52
BRDA:12,0,0,353
BRDA:12,0,1,348
BRDA:19,1,0,1
BRDA:19,1,1,352
BRDA:19,2,0,353
BRDA:19,2,1,1
BRDA:23,3,0,0
BRDA:23,3,1,353
BRDA:23,4,0,353
BRDA:23,4,1,0
BRDA:55,5,0,0
BRDA:55,5,1,325
BRDA:55,6,0,325
BRDA:55,6,1,325
BRDA:70,7,0,0
BRDA:70,7,1,0
BRDA:115,8,0,0
BRDA:115,8,1,378
BRDA:115,9,0,378
BRDA:115,9,1,378
BRDA:132,10,0,0
BRDA:132,10,1,0
BRDA:133,11,0,0
BRDA:133,11,1,0
BRDA:133,11,2,0
BRDA:133,11,3,0
BRDA:141,12,0,0
BRDA:141,12,1,0
BRDA:142,13,0,0
BRDA:142,13,1,0
BRDA:142,13,2,0
BRDA:142,13,3,0
BRDA:176,14,0,0
BRDA:176,14,1,0
BRF:34
BRH:14
end_of_record
TN:
SF:lib\sync-inflate.js
FN:9,Inflate
FN:29,createInflate
FN:33,_close
FN:47,(anonymous_3)
FN:63,(anonymous_4)
FN:67,handleChunk
FN:145,zlibBufferSync
FN:161,inflateSync
FNF:8
FNH:7
FNDA:127,Inflate
FNDA:0,createInflate
FNDA:126,_close
FNDA:127,(anonymous_3)
FNDA:1,(anonymous_4)
FNDA:126,handleChunk
FNDA:127,zlibBufferSync
FNDA:127,inflateSync
DA:3,1
DA:4,1
DA:5,1
DA:7,1
DA:10,127
DA:11,0
DA:14,127
DA:15,9
DA:18,127
DA:21,127
DA:22,127
DA:24,127
DA:25,127
DA:30,0
DA:34,126
DA:35,0
DA:39,126
DA:40,0
DA:43,126
DA:44,126
DA:47,1
DA:48,127
DA:49,0
DA:52,127
DA:54,127
DA:55,127
DA:56,127
DA:57,127
DA:59,127
DA:60,127
DA:63,127
DA:64,1
DA:68,126
DA:69,0
DA:72,126
DA:73,126
DA:75,126
DA:76,126
DA:77,126
DA:79,126
DA:80,9
DA:83,126
DA:84,126
DA:85,126
DA:87,126
DA:88,126
DA:92,0
DA:93,0
DA:94,0
DA:95,0
DA:98,0
DA:99,0
DA:100,0
DA:102,0
DA:105,0
DA:108,127
DA:110,127
DA:111,127
DA:121,127
DA:124,127
DA:125,1
DA:128,126
DA:129,0
DA:130,0
DA:137,126
DA:138,126
DA:140,126
DA:143,1
DA:146,127
DA:147,0
DA:149,127
DA:150,0
DA:153,127
DA:154,127
DA:155,0
DA:158,127
DA:162,127
DA:165,1
DA:166,1
DA:167,1
DA:168,1
LF:81
LH:61
BRDA:10,0,0,0
BRDA:10,0,1,127
BRDA:14,1,0,9
BRDA:14,1,1,118
BRDA:14,2,0,127
BRDA:14,2,1,127
BRDA:21,3,0,127
BRDA:21,3,1,0
BRDA:22,4,0,127
BRDA:22,4,1,127
BRDA:24,5,0,127
BRDA:24,5,1,0
BRDA:24,6,0,127
BRDA:24,6,1,127
BRDA:34,7,0,0
BRDA:34,7,1,126
BRDA:39,8,0,0
BRDA:39,8,1,126
BRDA:48,9,0,0
BRDA:48,9,1,127
BRDA:54,10,0,127
BRDA:54,10,1,127
BRDA:68,11,0,0
BRDA:68,11,1,126
BRDA:75,12,0,126
BRDA:75,12,1,0
BRDA:79,13,0,9
BRDA:79,13,1,117
BRDA:87,14,0,126
BRDA:87,14,1,0
BRDA:92,15,0,0
BRDA:92,15,1,0
BRDA:92,16,0,0
BRDA:92,16,1,0
BRDA:98,17,0,0
BRDA:98,17,1,0
BRDA:122,18,0,127
BRDA:122,18,1,126
BRDA:121,19,0,127
BRDA:121,19,1,127
BRDA:124,20,0,1
BRDA:124,20,1,126
BRDA:128,21,0,0
BRDA:128,21,1,126
BRDA:146,22,0,0
BRDA:146,22,1,127
BRDA:149,23,0,0
BRDA:149,23,1,127
BRDA:154,24,0,0
BRDA:154,24,1,127
BRF:50
BRH:31
end_of_record
TN:
SF:lib\sync-reader.js
FN:3,(anonymous_0)
FN:8,(anonymous_1)
FN:16,(anonymous_2)
FNF:3
FNH:3
FNDA:338,(anonymous_0)
FNDA:9900,(anonymous_1)
FNDA:338,(anonymous_2)
DA:3,1
DA:4,338
DA:5,338
DA:8,1
DA:9,9900
DA:16,1
DA:18,338
DA:19,9900
DA:21,9900
DA:26,9900
DA:28,9900
DA:30,9900
DA:32,9900
DA:34,0
DA:38,338
DA:39,0
DA:42,338
DA:43,13
LF:18
LH:16
BRDA:18,0,0,10238
BRDA:18,0,1,9900
BRDA:21,1,0,9900
BRDA:21,1,1,0
BRDA:22,2,0,9900
BRDA:22,2,1,9900
BRDA:22,2,2,0
BRDA:38,3,0,0
BRDA:38,3,1,338
BRDA:42,4,0,13
BRDA:42,4,1,325
BRF:11
BRH:8
end_of_record
