import{_ as e,r as a}from"./index-bcbc0702.js";/* empty css                      *//* empty css                             *//* empty css                   *//* empty css                     *//* empty css                *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                    */import"./el-tooltip-4ed993c7.js";/* empty css                        *//* empty css                         *//* empty css                     *//* empty css               *//* empty css                       */import{r as l,d as t,A as d,B as u,C as o,F as s,G as r,D as n,u as i,I as p,am as c,al as m,b2 as _,H as f,J as g,ae as h,af as b,E as v,z as y,K as w,L as V,ai as k,Y as q,au as x,av as C,b3 as U,Z as j,b4 as D,b5 as I,b6 as R,aK as z,aL as N,aM as Q,b7 as Y,a$ as $,b8 as M,b9 as F,X as A,aj as L,ak as O,_ as S,ba as H,bb as K,bc as T,b0 as E,bd as B,O as G,be as J,bf as P,bg as X,bh as Z,bi as W,bj as ee,bk as ae}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const le={class:"user-management"},te={class:"card-header"},de={class:"header-actions"},ue={class:"search-bar"},oe={class:"batch-actions"},se={class:"stats-bar"},re={class:"table-container"},ne={class:"filter-header"},ie={key:0},pe={key:1,class:"text-muted"},ce={key:0},me={key:1,class:"text-muted"},_e={class:"filter-header"},fe={class:"filter-header"},ge={class:"pagination"},he={key:0,class:"user-detail"},be={key:0,class:"remark-section"},ve={key:1,class:"extra-info-section"},ye=e({__name:"UserManagement",setup(e){const ye=l(!1),we=l(!1),Ve=l(!1),ke=l([]),qe=l([]),xe=l(!1),Ce=l(!1),Ue=l(!1),je=l({}),De=l(null),Ie=l(null),Re=t({keyword:"",category:"",status:"",dateRange:[]}),ze=t({page:1,size:20,total:0}),Ne=t({prop:"",order:""}),Qe=t({total:0,buaa:0,external:0,today:0}),Ye=t({qq:"",realName:"",category:"",school:"",studentId:"",email:"",phone:"",department:"",status:"active",remark:""}),$e=t({qq:"",realName:"",category:"",school:"",studentId:"",email:"",phone:"",department:"",status:"",remark:""}),Me={qq:[{required:!0,message:"请输入QQ号",trigger:"blur"},{pattern:/^\d{5,15}$/,message:"QQ号格式不正确",trigger:"blur"}],realName:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{min:2,max:50,message:"姓名长度在2-50个字符",trigger:"blur"}],category:[{required:!0,message:"请选择用户类型",trigger:"change"}],school:[{required:!0,message:"请输入学校名称",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]},Fe={...Me},Ae=l(null),Le=()=>{Ae.value&&clearTimeout(Ae.value),Ae.value=setTimeout(()=>{ze.page=1,Oe()},300)},Oe=async()=>{var e;ye.value=!0;try{const l={page:ze.page,limit:ze.size,search:Re.keyword,category:Re.category,status:Re.status,...Ne};Re.dateRange&&2===Re.dateRange.length&&(l.start_date=Re.dateRange[0],l.end_date=Re.dateRange[1]);const t=await a.get("/api/users/",{params:l});t.success&&(ke.value=t.data.users||[],ze.total=(null==(e=t.data.pagination)?void 0:e.total)||0,Object.assign(Qe,t.data.stats||{}))}catch(l){v.error("加载用户列表失败")}finally{ye.value=!1}},Se=()=>{ze.page=1,Oe()},He=()=>{Le()},Ke=()=>{Le()},Te=()=>{Le()},Ee=()=>{Object.assign(Re,{keyword:"",category:"",status:"",dateRange:[]}),ze.page=1,Oe()},Be=e=>{qe.value=e},Ge=e=>{["asc","desc"].includes(e)?(Ne.prop="category",Ne.order="asc"===e?"ascending":"descending"):(Re.category=e,Ne.prop="",Ne.order=""),Oe()},Je=e=>{["asc","desc"].includes(e)?(Ne.prop="status",Ne.order="asc"===e?"ascending":"descending"):(Re.status=e,Ne.prop="",Ne.order=""),Oe()},Pe=e=>{const a=new Date;let l=null,t=null;if(["asc","desc"].includes(e))Ne.prop="authenticatedAt",Ne.order="asc"===e?"ascending":"descending",Re.dateRange=[];else{switch(e){case"today":l=new Date(a.getFullYear(),a.getMonth(),a.getDate()),t=new Date(a.getFullYear(),a.getMonth(),a.getDate(),23,59,59);break;case"week":const e=new Date(a);e.setDate(a.getDate()-a.getDay()),e.setHours(0,0,0,0),l=e,t=new Date(e),t.setDate(e.getDate()+6),t.setHours(23,59,59);break;case"month":l=new Date(a.getFullYear(),a.getMonth(),1),t=new Date(a.getFullYear(),a.getMonth()+1,0,23,59,59);break;case"year":l=new Date(a.getFullYear(),0,1),t=new Date(a.getFullYear(),11,31,23,59,59)}l&&t&&(Re.dateRange=[l,t]),Ne.prop="",Ne.order=""}Oe()},Xe=e=>({buaa:"primary",freshman:"success",external:"warning",invite:"info","本校":"success","新生":"warning","外校":"info","其他":"info"}[e]||""),Ze=e=>({buaa:"本校学生",freshman:"新生",external:"外校学生",invite:"邀请码","本校":"本校学生","新生":"新生","外校":"外校学生","其他":"其他"}[e]||e),We=e=>e?new Date(e).toLocaleString("zh-CN"):"",ea=e=>e||"",aa=e=>{je.value={...e},Ue.value=!0},la=e=>{Object.assign($e,e),Ce.value=!0,Ue.value=!1},ta=async()=>{if(De.value)try{await De.value.validate(),we.value=!0;const e={...Ye};e.phone&&""!==e.phone.trim()||delete e.phone,e.email&&""!==e.email.trim()||delete e.email,e.studentId&&""!==e.studentId.trim()||delete e.studentId,e.department&&""!==e.department.trim()||delete e.department,e.grade&&""!==e.grade.trim()||delete e.grade,e.remark&&""!==e.remark.trim()||delete e.remark;(await a.post("/api/users/",e)).success&&(v.success("添加用户成功"),xe.value=!1,ua(),Oe())}catch(e){!1!==e&&v.error("添加用户失败")}finally{we.value=!1}},da=async()=>{if(Ie.value)try{await Ie.value.validate(),Ve.value=!0;const e={...$e};e.phone&&""!==e.phone.trim()||delete e.phone,e.email&&""!==e.email.trim()||delete e.email,e.studentId&&""!==e.studentId.trim()||delete e.studentId,e.department&&""!==e.department.trim()||delete e.department,e.grade&&""!==e.grade.trim()||delete e.grade,e.remark&&""!==e.remark.trim()||delete e.remark;(await a.put(`/api/users/${$e.qq}`,e)).success&&(v.success("更新用户成功"),Ce.value=!1,Oe())}catch(e){!1!==e&&v.error("更新用户失败")}finally{Ve.value=!1}},ua=()=>{Object.assign(Ye,{qq:"",realName:"",category:"",school:"",studentId:"",email:"",phone:"",department:"",status:"active",remark:""})},oa=async()=>{if(0!==qe.value.length)try{await y.confirm(`确定要批量切换 ${qe.value.length} 个用户的状态吗？`,"确认操作");const e=qe.value.map(e=>e.qq);(await a.post("/api/users/batch-toggle-status",{user_ids:e})).success&&(v.success("批量操作成功"),Oe())}catch(e){"cancel"!==e&&v.error("批量操作失败")}},sa=async()=>{if(0!==qe.value.length)try{await y.confirm(`确定要批量删除 ${qe.value.length} 个用户吗？此操作不可恢复！`,"确认删除",{type:"warning"});const e=qe.value.map(e=>e.qq);(await a.post("/api/users/batch-delete",{user_ids:e})).success&&(v.success("批量删除成功"),Oe())}catch(e){"cancel"!==e&&v.error("批量删除失败")}},ra=async()=>{try{const e={search:Re.keyword,category:Re.category,status:Re.status};Re.dateRange&&2===Re.dateRange.length&&(e.start_date=Re.dateRange[0],e.end_date=Re.dateRange[1]);const l=await a.get("/api/users/export",{params:e,responseType:"blob"}),t=new Blob([l],{type:"text/csv;charset=utf-8"}),d=window.URL.createObjectURL(t),u=document.createElement("a");u.href=d,u.download=`用户数据_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(d),v.success("导出成功")}catch(e){v.error("导出失败: "+e.message)}};return d(()=>{Oe()}),(e,l)=>{const t=w,d=V,Ne=k,Ae=q,Le=x,ua=C,na=U,ia=j,pa=D,ca=I,ma=R,_a=z,fa=N,ga=Q,ha=Y,ba=$,va=M,ya=F,wa=A,Va=L,ka=O,qa=S,xa=H,Ca=K,Ua=T;return u(),o("div",le,[s(wa,null,{header:r(()=>[n("div",te,[l[37]||(l[37]=n("span",null,"用户管理",-1)),n("div",de,[s(d,{type:"primary",onClick:l[0]||(l[0]=e=>xe.value=!0)},{default:r(()=>[s(t,null,{default:r(()=>[s(i(E))]),_:1}),l[34]||(l[34]=p(" 添加用户 ",-1))]),_:1,__:[34]}),s(d,{onClick:ra},{default:r(()=>[s(t,null,{default:r(()=>[s(i(B))]),_:1}),l[35]||(l[35]=p(" 导出数据 ",-1))]),_:1,__:[35]}),s(d,{onClick:Oe},{default:r(()=>[s(t,null,{default:r(()=>[s(i(G))]),_:1}),l[36]||(l[36]=p(" 刷新 ",-1))]),_:1,__:[36]})])])]),default:r(()=>[n("div",ue,[s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:5},{default:r(()=>[s(Ne,{modelValue:Re.keyword,"onUpdate:modelValue":l[1]||(l[1]=e=>Re.keyword=e),placeholder:"搜索QQ号、姓名、邮箱、手机号",clearable:"",onKeyup:c(Se,["enter"]),onInput:He,onClear:He},{prefix:r(()=>[ye.value?(u(),m(t,{key:1,class:"is-loading"},{default:r(()=>[s(i(P))]),_:1})):(u(),m(t,{key:0},{default:r(()=>[s(i(J))]),_:1}))]),_:1},8,["modelValue"])]),_:1}),s(Ae,{span:3},{default:r(()=>[s(ua,{modelValue:Re.category,"onUpdate:modelValue":l[2]||(l[2]=e=>Re.category=e),placeholder:"用户类型",clearable:"",onChange:Ke},{default:r(()=>[s(Le,{label:"本校学生",value:"本校"}),s(Le,{label:"新生",value:"新生"}),s(Le,{label:"外校学生",value:"外校"}),s(Le,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),s(Ae,{span:3},{default:r(()=>[s(ua,{modelValue:Re.status,"onUpdate:modelValue":l[3]||(l[3]=e=>Re.status=e),placeholder:"状态",clearable:"",onChange:Ke},{default:r(()=>[s(Le,{label:"正常",value:"active"}),s(Le,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1}),s(Ae,{span:4},{default:r(()=>[s(na,{modelValue:Re.dateRange,"onUpdate:modelValue":l[4]||(l[4]=e=>Re.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",size:"default",onChange:Te},null,8,["modelValue"])]),_:1}),s(Ae,{span:4},{default:r(()=>[s(d,{type:"primary",onClick:Se},{default:r(()=>l[38]||(l[38]=[p("搜索",-1)])),_:1,__:[38]}),s(d,{onClick:Ee},{default:r(()=>l[39]||(l[39]=[p("重置",-1)])),_:1,__:[39]})]),_:1}),s(Ae,{span:5},{default:r(()=>[n("div",oe,[s(d,{type:"warning",disabled:0===qe.value.length,onClick:oa},{default:r(()=>l[40]||(l[40]=[p(" 批量禁用/启用 ",-1)])),_:1,__:[40]},8,["disabled"]),s(d,{type:"danger",disabled:0===qe.value.length,onClick:sa},{default:r(()=>l[41]||(l[41]=[p(" 批量删除 ",-1)])),_:1,__:[41]},8,["disabled"])])]),_:1})]),_:1})]),n("div",se,[s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:6},{default:r(()=>[s(pa,{title:"总用户数",value:Qe.total},null,8,["value"])]),_:1}),s(Ae,{span:6},{default:r(()=>[s(pa,{title:"本校学生",value:Qe.buaa},null,8,["value"])]),_:1}),s(Ae,{span:6},{default:r(()=>[s(pa,{title:"外校学生",value:Qe.external},null,8,["value"])]),_:1}),s(Ae,{span:6},{default:r(()=>[s(pa,{title:"今日新增",value:Qe.today},null,8,["value"])]),_:1})]),_:1})]),n("div",re,[_((u(),m(va,{data:ke.value,stripe:"",border:"",height:"600",style:{width:"100%"},onSelectionChange:Be},{default:r(()=>[s(ca,{type:"selection",width:"50",fixed:"left"}),s(ca,{prop:"qq",label:"QQ号",width:"120",sortable:"custom"},{default:r(({row:e})=>[s(ma,{type:"primary",onClick:a=>aa(e)},{default:r(()=>[p(f(e.qq),1)]),_:2},1032,["onClick"])]),_:1}),s(ca,{prop:"realName",label:"姓名",width:"100",sortable:"custom"}),s(ca,{prop:"category",label:"用户类型",width:"120"},{header:r(()=>[n("div",ne,[l[49]||(l[49]=n("span",null,"用户类型",-1)),s(ga,{onCommand:Ge,trigger:"click"},{dropdown:r(()=>[s(fa,null,{default:r(()=>[s(_a,{command:""},{default:r(()=>l[42]||(l[42]=[p("全部",-1)])),_:1,__:[42]}),s(_a,{command:"本校"},{default:r(()=>l[43]||(l[43]=[p("本校学生",-1)])),_:1,__:[43]}),s(_a,{command:"新生"},{default:r(()=>l[44]||(l[44]=[p("新生",-1)])),_:1,__:[44]}),s(_a,{command:"外校"},{default:r(()=>l[45]||(l[45]=[p("外校学生",-1)])),_:1,__:[45]}),s(_a,{command:"其他"},{default:r(()=>l[46]||(l[46]=[p("其他",-1)])),_:1,__:[46]}),s(_a,{divided:"",command:"asc"},{default:r(()=>l[47]||(l[47]=[p("按类型升序",-1)])),_:1,__:[47]}),s(_a,{command:"desc"},{default:r(()=>l[48]||(l[48]=[p("按类型降序",-1)])),_:1,__:[48]})]),_:1})]),default:r(()=>[s(t,{class:"filter-icon"},{default:r(()=>[s(i(X))]),_:1})]),_:1})])]),default:r(({row:e})=>[s(ha,{type:Xe(e.category),size:"small"},{default:r(()=>[p(f(Ze(e.category)),1)]),_:2},1032,["type"])]),_:1}),s(ca,{prop:"school",label:"学校",width:"180","show-overflow-tooltip":""}),s(ca,{prop:"studentId",label:"学号/考生号",width:"120","show-overflow-tooltip":""}),s(ca,{prop:"email",label:"邮箱",width:"200","show-overflow-tooltip":""},{default:r(({row:e})=>[s(ma,{href:`mailto:${e.email}`,type:"primary"},{default:r(()=>[p(f(e.email),1)]),_:2},1032,["href"])]),_:1}),s(ca,{prop:"phone",label:"手机号",width:"120"},{default:r(({row:e})=>[e.phone?(u(),o("span",ie,f(ea(e.phone)),1)):(u(),o("span",pe,"未填写"))]),_:1}),s(ca,{prop:"department",label:"院系",width:"150","show-overflow-tooltip":""},{default:r(({row:e})=>[e.department?(u(),o("span",ce,f(e.department),1)):(u(),o("span",me,"未填写"))]),_:1}),s(ca,{prop:"authenticatedAt",label:"认证时间",width:"180"},{header:r(()=>[n("div",_e,[l[56]||(l[56]=n("span",null,"认证时间",-1)),s(ga,{onCommand:Pe,trigger:"click"},{dropdown:r(()=>[s(fa,null,{default:r(()=>[s(_a,{command:"today"},{default:r(()=>l[50]||(l[50]=[p("今天",-1)])),_:1,__:[50]}),s(_a,{command:"week"},{default:r(()=>l[51]||(l[51]=[p("本周",-1)])),_:1,__:[51]}),s(_a,{command:"month"},{default:r(()=>l[52]||(l[52]=[p("本月",-1)])),_:1,__:[52]}),s(_a,{command:"year"},{default:r(()=>l[53]||(l[53]=[p("今年",-1)])),_:1,__:[53]}),s(_a,{divided:"",command:"asc"},{default:r(()=>l[54]||(l[54]=[p("按时间升序",-1)])),_:1,__:[54]}),s(_a,{command:"desc"},{default:r(()=>l[55]||(l[55]=[p("按时间降序",-1)])),_:1,__:[55]})]),_:1})]),default:r(()=>[s(t,{class:"filter-icon"},{default:r(()=>[s(i(X))]),_:1})]),_:1})])]),default:r(({row:e})=>[p(f(We(e.authenticatedAt)),1)]),_:1}),s(ca,{prop:"lastUpdatedAt",label:"最后更新",width:"160",sortable:"custom"},{default:r(({row:e})=>[p(f(We(e.lastUpdatedAt)),1)]),_:1}),s(ca,{prop:"status",label:"状态",width:"100"},{header:r(()=>[n("div",fe,[l[62]||(l[62]=n("span",null,"状态",-1)),s(ga,{onCommand:Je,trigger:"click"},{dropdown:r(()=>[s(fa,null,{default:r(()=>[s(_a,{command:""},{default:r(()=>l[57]||(l[57]=[p("全部",-1)])),_:1,__:[57]}),s(_a,{command:"active"},{default:r(()=>l[58]||(l[58]=[p("正常",-1)])),_:1,__:[58]}),s(_a,{command:"disabled"},{default:r(()=>l[59]||(l[59]=[p("禁用",-1)])),_:1,__:[59]}),s(_a,{divided:"",command:"asc"},{default:r(()=>l[60]||(l[60]=[p("按状态升序",-1)])),_:1,__:[60]}),s(_a,{command:"desc"},{default:r(()=>l[61]||(l[61]=[p("按状态降序",-1)])),_:1,__:[61]})]),_:1})]),default:r(()=>[s(t,{class:"filter-icon"},{default:r(()=>[s(i(X))]),_:1})]),_:1})])]),default:r(({row:e})=>[s(ha,{type:"active"===e.status?"success":"danger",size:"small"},{default:r(()=>[p(f("active"===e.status?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),s(ca,{label:"操作",width:"280",fixed:"right"},{default:r(({row:e})=>[s(ba,null,{default:r(()=>[s(d,{size:"small",onClick:a=>aa(e)},{default:r(()=>[s(t,null,{default:r(()=>[s(i(Z))]),_:1}),l[63]||(l[63]=p(" 查看 ",-1))]),_:2,__:[63]},1032,["onClick"]),s(d,{size:"small",type:"primary",onClick:a=>la(e)},{default:r(()=>[s(t,null,{default:r(()=>[s(i(W))]),_:1}),l[64]||(l[64]=p(" 编辑 ",-1))]),_:2,__:[64]},1032,["onClick"]),s(d,{size:"small",type:"active"===e.status?"warning":"success",onClick:l=>(async e=>{const l="active"===e.status?"禁用":"启用";try{await y.confirm(`确定要${l}用户 ${e.realName} 吗？`,"确认操作"),(await a.put(`/api/users/${e.qq}`,{status:"active"===e.status?"disabled":"active"})).success&&(v.success(`${l}用户成功`),Oe())}catch(t){"cancel"!==t&&v.error(`${l}用户失败`)}})(e)},{default:r(()=>[s(t,null,{default:r(()=>[s(i(ee))]),_:1}),p(" "+f("active"===e.status?"禁用":"启用"),1)]),_:2},1032,["type","onClick"]),s(d,{size:"small",type:"danger",onClick:l=>(async e=>{try{await y.confirm(`确定要删除用户 ${e.realName} 吗？此操作不可恢复！`,"确认删除",{type:"warning"}),(await a.delete(`/api/users/${e.qq}`)).success&&(v.success("删除用户成功"),Oe())}catch(l){"cancel"!==l&&v.error("删除用户失败")}})(e)},{default:r(()=>[s(t,null,{default:r(()=>[s(i(ae))]),_:1}),l[65]||(l[65]=p(" 删除 ",-1))]),_:2,__:[65]},1032,["onClick"])]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Ua,ye.value]])]),n("div",ge,[s(ya,{"current-page":ze.page,"onUpdate:currentPage":l[5]||(l[5]=e=>ze.page=e),"page-size":ze.size,"onUpdate:pageSize":l[6]||(l[6]=e=>ze.size=e),"page-sizes":[10,20,50,100],total:ze.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Oe,onCurrentChange:Oe},null,8,["current-page","page-size","total"])])]),_:1}),s(qa,{modelValue:xe.value,"onUpdate:modelValue":l[18]||(l[18]=e=>xe.value=e),title:"添加用户",width:"700px","close-on-click-modal":!1},{footer:r(()=>[s(d,{onClick:l[17]||(l[17]=e=>xe.value=!1)},{default:r(()=>l[66]||(l[66]=[p("取消",-1)])),_:1,__:[66]}),s(d,{type:"primary",onClick:ta,loading:we.value},{default:r(()=>l[67]||(l[67]=[p("确定",-1)])),_:1,__:[67]},8,["loading"])]),default:r(()=>[s(ka,{model:Ye,rules:Me,ref_key:"addFormRef",ref:De,"label-width":"120px"},{default:r(()=>[s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:12},{default:r(()=>[s(Va,{label:"QQ号",prop:"qq"},{default:r(()=>[s(Ne,{modelValue:Ye.qq,"onUpdate:modelValue":l[7]||(l[7]=e=>Ye.qq=e),placeholder:"请输入QQ号",maxlength:"15"},null,8,["modelValue"])]),_:1})]),_:1}),s(Ae,{span:12},{default:r(()=>[s(Va,{label:"真实姓名",prop:"realName"},{default:r(()=>[s(Ne,{modelValue:Ye.realName,"onUpdate:modelValue":l[8]||(l[8]=e=>Ye.realName=e),placeholder:"请输入真实姓名",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:12},{default:r(()=>[s(Va,{label:"用户类型",prop:"category"},{default:r(()=>[s(ua,{modelValue:Ye.category,"onUpdate:modelValue":l[9]||(l[9]=e=>Ye.category=e),placeholder:"请选择用户类型",style:{width:"100%"}},{default:r(()=>[s(Le,{label:"本校学生",value:"本校"}),s(Le,{label:"新生",value:"新生"}),s(Le,{label:"外校学生",value:"外校"}),s(Le,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),s(Ae,{span:12},{default:r(()=>[s(Va,{label:"学校",prop:"school"},{default:r(()=>[s(Ne,{modelValue:Ye.school,"onUpdate:modelValue":l[10]||(l[10]=e=>Ye.school=e),placeholder:"请输入学校名称",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:12},{default:r(()=>[s(Va,{label:"学号/考生号",prop:"studentId"},{default:r(()=>[s(Ne,{modelValue:Ye.studentId,"onUpdate:modelValue":l[11]||(l[11]=e=>Ye.studentId=e),placeholder:"请输入学号或考生号",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1}),s(Ae,{span:12},{default:r(()=>[s(Va,{label:"邮箱地址",prop:"email"},{default:r(()=>[s(Ne,{modelValue:Ye.email,"onUpdate:modelValue":l[12]||(l[12]=e=>Ye.email=e),placeholder:"请输入邮箱地址",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:12},{default:r(()=>[s(Va,{label:"手机号",prop:"phone"},{default:r(()=>[s(Ne,{modelValue:Ye.phone,"onUpdate:modelValue":l[13]||(l[13]=e=>Ye.phone=e),placeholder:"请输入手机号",maxlength:"11"},null,8,["modelValue"])]),_:1})]),_:1}),s(Ae,{span:12},{default:r(()=>[s(Va,{label:"院系",prop:"department"},{default:r(()=>[s(Ne,{modelValue:Ye.department,"onUpdate:modelValue":l[14]||(l[14]=e=>Ye.department=e),placeholder:"请输入院系名称",maxlength:"200"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:12},{default:r(()=>[s(Va,{label:"初始状态",prop:"status"},{default:r(()=>[s(ua,{modelValue:Ye.status,"onUpdate:modelValue":l[15]||(l[15]=e=>Ye.status=e),placeholder:"请选择初始状态",style:{width:"100%"}},{default:r(()=>[s(Le,{label:"正常",value:"active"}),s(Le,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(Va,{label:"备注信息"},{default:r(()=>[s(Ne,{modelValue:Ye.remark,"onUpdate:modelValue":l[16]||(l[16]=e=>Ye.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息（可选）",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(qa,{modelValue:Ce.value,"onUpdate:modelValue":l[30]||(l[30]=e=>Ce.value=e),title:"编辑用户",width:"700px","close-on-click-modal":!1},{footer:r(()=>[s(d,{onClick:l[29]||(l[29]=e=>Ce.value=!1)},{default:r(()=>l[68]||(l[68]=[p("取消",-1)])),_:1,__:[68]}),s(d,{type:"primary",onClick:da,loading:Ve.value},{default:r(()=>l[69]||(l[69]=[p("确定",-1)])),_:1,__:[69]},8,["loading"])]),default:r(()=>[s(ka,{model:$e,rules:Fe,ref_key:"editFormRef",ref:Ie,"label-width":"120px"},{default:r(()=>[s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:12},{default:r(()=>[s(Va,{label:"QQ号",prop:"qq"},{default:r(()=>[s(Ne,{modelValue:$e.qq,"onUpdate:modelValue":l[19]||(l[19]=e=>$e.qq=e),disabled:""},null,8,["modelValue"])]),_:1})]),_:1}),s(Ae,{span:12},{default:r(()=>[s(Va,{label:"真实姓名",prop:"realName"},{default:r(()=>[s(Ne,{modelValue:$e.realName,"onUpdate:modelValue":l[20]||(l[20]=e=>$e.realName=e),placeholder:"请输入真实姓名",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:12},{default:r(()=>[s(Va,{label:"用户类型",prop:"category"},{default:r(()=>[s(ua,{modelValue:$e.category,"onUpdate:modelValue":l[21]||(l[21]=e=>$e.category=e),placeholder:"请选择用户类型",style:{width:"100%"}},{default:r(()=>[s(Le,{label:"本校学生",value:"本校"}),s(Le,{label:"新生",value:"新生"}),s(Le,{label:"外校学生",value:"外校"}),s(Le,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1})]),_:1}),s(Ae,{span:12},{default:r(()=>[s(Va,{label:"学校",prop:"school"},{default:r(()=>[s(Ne,{modelValue:$e.school,"onUpdate:modelValue":l[22]||(l[22]=e=>$e.school=e),placeholder:"请输入学校名称",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:12},{default:r(()=>[s(Va,{label:"学号/考生号",prop:"studentId"},{default:r(()=>[s(Ne,{modelValue:$e.studentId,"onUpdate:modelValue":l[23]||(l[23]=e=>$e.studentId=e),placeholder:"请输入学号或考生号",maxlength:"50"},null,8,["modelValue"])]),_:1})]),_:1}),s(Ae,{span:12},{default:r(()=>[s(Va,{label:"邮箱地址",prop:"email"},{default:r(()=>[s(Ne,{modelValue:$e.email,"onUpdate:modelValue":l[24]||(l[24]=e=>$e.email=e),placeholder:"请输入邮箱地址",maxlength:"100"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:12},{default:r(()=>[s(Va,{label:"手机号",prop:"phone"},{default:r(()=>[s(Ne,{modelValue:$e.phone,"onUpdate:modelValue":l[25]||(l[25]=e=>$e.phone=e),placeholder:"请输入手机号",maxlength:"11"},null,8,["modelValue"])]),_:1})]),_:1}),s(Ae,{span:12},{default:r(()=>[s(Va,{label:"院系",prop:"department"},{default:r(()=>[s(Ne,{modelValue:$e.department,"onUpdate:modelValue":l[26]||(l[26]=e=>$e.department=e),placeholder:"请输入院系名称",maxlength:"200"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(ia,{gutter:20},{default:r(()=>[s(Ae,{span:12},{default:r(()=>[s(Va,{label:"状态",prop:"status"},{default:r(()=>[s(ua,{modelValue:$e.status,"onUpdate:modelValue":l[27]||(l[27]=e=>$e.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:r(()=>[s(Le,{label:"正常",value:"active"}),s(Le,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),s(Va,{label:"备注信息"},{default:r(()=>[s(Ne,{modelValue:$e.remark,"onUpdate:modelValue":l[28]||(l[28]=e=>$e.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息（可选）",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),s(qa,{modelValue:Ue.value,"onUpdate:modelValue":l[33]||(l[33]=e=>Ue.value=e),title:"用户详情",width:"800px"},{footer:r(()=>[s(d,{onClick:l[31]||(l[31]=e=>Ue.value=!1)},{default:r(()=>l[72]||(l[72]=[p("关闭",-1)])),_:1,__:[72]}),s(d,{type:"primary",onClick:l[32]||(l[32]=e=>la(je.value))},{default:r(()=>l[73]||(l[73]=[p("编辑",-1)])),_:1,__:[73]})]),default:r(()=>[aa?(u(),o("div",he,[s(Ca,{column:2,border:""},{default:r(()=>[s(xa,{label:"QQ号"},{default:r(()=>[p(f(je.value.qq),1)]),_:1}),s(xa,{label:"真实姓名"},{default:r(()=>[p(f(je.value.realName),1)]),_:1}),s(xa,{label:"用户类型"},{default:r(()=>[s(ha,{type:Xe(je.value.category)},{default:r(()=>[p(f(Ze(je.value.category)),1)]),_:1},8,["type"])]),_:1}),s(xa,{label:"学校"},{default:r(()=>[p(f(je.value.school),1)]),_:1}),s(xa,{label:"学号/考生号"},{default:r(()=>[p(f(je.value.studentId||"未填写"),1)]),_:1}),s(xa,{label:"邮箱地址"},{default:r(()=>[p(f(je.value.email),1)]),_:1}),s(xa,{label:"手机号"},{default:r(()=>[p(f(ea(je.value.phone)||"未填写"),1)]),_:1}),s(xa,{label:"院系"},{default:r(()=>[p(f(je.value.department||"未填写"),1)]),_:1}),s(xa,{label:"状态"},{default:r(()=>[s(ha,{type:"active"===je.value.status?"success":"danger"},{default:r(()=>[p(f("active"===je.value.status?"正常":"禁用"),1)]),_:1},8,["type"])]),_:1}),s(xa,{label:"认证时间"},{default:r(()=>[p(f(We(je.value.authenticatedAt)),1)]),_:1}),s(xa,{label:"最后更新"},{default:r(()=>[p(f(We(je.value.lastUpdatedAt)),1)]),_:1})]),_:1}),je.value.remark?(u(),o("div",be,[l[70]||(l[70]=n("h4",null,"备注信息",-1)),n("p",null,f(je.value.remark),1)])):g("",!0),je.value.extra_info?(u(),o("div",ve,[l[71]||(l[71]=n("h4",null,"扩展信息",-1)),s(Ca,{column:1,border:""},{default:r(()=>[(u(!0),o(h,null,b(je.value.extra_info,(e,a)=>(u(),m(xa,{key:a,label:a},{default:r(()=>[p(f(e),1)]),_:2},1032,["label"]))),128))]),_:1})])):g("",!0)])):g("",!0)]),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-152b4391"]]);export{ye as default};
