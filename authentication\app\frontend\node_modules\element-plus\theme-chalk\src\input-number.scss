@use 'sass:math';
@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(input-number) {
  position: relative;
  display: inline-flex;
  width: map.get($input-number-width, 'default');
  line-height: #{map.get($input-height, 'default') - 2};
  vertical-align: middle;

  .#{$namespace}-input {
    &__wrapper {
      padding-left: #{map.get($input-height, 'default') + 10};
      padding-right: #{map.get($input-height, 'default') + 10};
    }

    &__inner {
      -webkit-appearance: none;
      -moz-appearance: textfield;
      text-align: center;
      line-height: 1;
      &::-webkit-inner-spin-button,
      &::-webkit-outer-spin-button {
        margin: 0;
        -webkit-appearance: none;
      }
    }
  }
  @each $align in (left, right, center) {
    @include when($align) {
      .#{$namespace}-input__inner {
        text-align: $align;
      }
    }
  }
  @include e((increase, decrease)) {
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;

    position: absolute;
    z-index: 1;
    top: 1px;
    bottom: 1px;

    width: map.get($input-height, 'default');
    background: getCssVar('fill-color', 'light');
    color: getCssVar('text-color', 'regular');
    cursor: pointer;
    font-size: 13px;
    user-select: none;

    &:hover {
      color: getCssVar('color-primary');

      & ~ .#{$namespace}-input:not(.is-disabled) .#{$namespace}-input__wrapper {
        box-shadow: 0 0 0 1px
          var(
            #{getCssVarName('input', 'focus-border-color')},
            map.get($input, 'focus-border-color')
          )
          inset;
      }
    }

    &.is-disabled {
      color: getCssVar('disabled-text-color');
      cursor: not-allowed;
    }
  }

  @include e(increase) {
    right: 1px;
    border-radius: 0 getCssVar('border-radius-base')
      getCssVar('border-radius-base') 0;
    border-left: getCssVar('border');
  }

  @include e(decrease) {
    left: 1px;
    border-radius: getCssVar('border-radius-base') 0 0
      getCssVar('border-radius-base');
    border-right: getCssVar('border');
  }

  @include when(disabled) {
    @include e((increase, decrease)) {
      border-color: getCssVar('disabled-border-color');
      color: getCssVar('disabled-border-color');

      &:hover {
        color: getCssVar('disabled-border-color');
        cursor: not-allowed;
      }
    }
  }

  @each $size in (large, small) {
    @include m($size) {
      width: map.get($input-number-width, $size);
      line-height: #{map.get($input-height, $size) - 2};

      @include e((increase, decrease)) {
        width: map.get($input-height, $size);
        font-size: map.get($input-font-size, $size);
      }

      @include when(controls-right) {
        .#{$namespace}-input--#{$size} {
          .#{$namespace}-input__wrapper {
            padding-right: #{map.get($input-height, $size) + 7};
          }
        }
      }

      .#{$namespace}-input--#{$size} {
        .#{$namespace}-input__wrapper {
          padding-left: #{map.get($input-height, $size) + 7};
          padding-right: #{map.get($input-height, $size) + 7};
        }
      }
    }
  }

  @include m(small) {
    @include e((increase, decrease)) {
      [class*='#{$namespace}-icon'] {
        transform: scale(0.9);
      }
    }
  }

  @include when(without-controls) {
    .#{$namespace}-input__wrapper {
      padding-left: 15px;
      padding-right: 15px;
    }
  }

  @include when(controls-right) {
    .#{$namespace}-input__wrapper {
      padding-left: 15px;
      padding-right: #{map.get($input-height, 'default') + 10};
    }

    @include e((increase, decrease)) {
      @include set-css-var-value(
        ('input', 'number-controls-height'),
        math.div(map.get($input-height, 'default') - 2, 2)
      );

      height: getCssVar('input-number-controls-height');
      line-height: getCssVar('input-number-controls-height');

      [class*='#{$namespace}-icon'] {
        transform: scale(0.8);
      }
    }

    @include e(increase) {
      bottom: auto;
      left: auto;
      border-radius: 0 getCssVar('border-radius-base') 0 0;
      border-bottom: getCssVar('border');
    }

    @include e(decrease) {
      right: 1px;
      top: auto;
      left: auto;
      border-right: none;
      border-left: getCssVar('border');
      border-radius: 0 0 getCssVar('border-radius-base') 0;
    }

    @each $size in (large, small) {
      &[class*='#{$size}'] {
        [class*='increase'],
        [class*='decrease'] {
          @include set-css-var-value(
            ('input', 'number-controls-height'),
            math.div(map.get($input-height, $size) - 2, 2)
          );
        }
      }
    }
  }
}
