{"version": 3, "file": "id.min.mjs", "sources": ["../../../../packages/locale/lang/id.ts"], "sourcesContent": ["export default {\n  name: 'id',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON><PERSON><PERSON>',\n      clear: 'Kosongkan',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON><PERSON>',\n      today: '<PERSON> ini',\n      cancel: '<PERSON><PERSON>',\n      clear: 'Kosong<PERSON>',\n      confirm: 'Ya',\n      selectDate: '<PERSON><PERSON>h tanggal',\n      selectTime: 'Pilih waktu',\n      startDate: 'Tanggal Mulai',\n      startTime: '<PERSON><PERSON>tu <PERSON>',\n      endDate: 'Tanggal Selesai',\n      endTime: '<PERSON><PERSON><PERSON>',\n      prevYear: 'Tahun Sebelumnya',\n      nextYear: 'Tahun Selanjutnya',\n      prevMonth: 'Bulan Sebelumnya',\n      nextMonth: '<PERSON><PERSON><PERSON>',\n      year: '<PERSON>hun',\n      month1: 'Januari',\n      month2: 'Februari',\n      month3: 'Maret',\n      month4: 'April',\n      month5: 'Mei',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: '<PERSON><PERSON><PERSON>',\n      month9: 'September',\n      month10: 'Okto<PERSON>',\n      month11: 'November',\n      month12: 'Desember',\n      week: '<PERSON><PERSON>',\n      weeks: {\n        sun: 'Min',\n        mon: 'Sen',\n        tue: 'Sel',\n        wed: 'Rab',\n        thu: 'Kam',\n        fri: 'Jum',\n        sat: 'Sab',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mei',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Agu',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Des',\n      },\n    },\n    select: {\n      loading: 'Memuat',\n      noMatch: 'Tidak ada data yg cocok',\n      noData: 'Tidak ada data',\n      placeholder: 'Pilih',\n    },\n    mention: {\n      loading: 'Memuat',\n    },\n    cascader: {\n      noMatch: 'Tidak ada data yg cocok',\n      loading: 'Memuat',\n      placeholder: 'Pilih',\n      noData: 'Tidak ada data',\n    },\n    pagination: {\n      goto: 'Pergi ke',\n      pagesize: '/halaman',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n      deprecationWarning:\n        'Penggunaan yang tidak akan digunakan lagi terdeteksi, silakan lihat dokumentasi el-pagination untuk lebih jelasnya',\n    },\n    messagebox: {\n      title: 'Pesan',\n      confirm: 'Ya',\n      cancel: 'Batal',\n      error: 'Masukan ilegal',\n    },\n    upload: {\n      deleteTip: 'Tekan hapus untuk melanjutkan',\n      delete: 'Hapus',\n      preview: 'Pratinjau',\n      continue: 'Lanjutkan',\n    },\n    table: {\n      emptyText: 'Tidak ada data',\n      confirmFilter: 'Konfirmasi',\n      resetFilter: 'Atur ulang',\n      clearFilter: 'Semua',\n      sumText: 'Jumlah',\n    },\n    tree: {\n      emptyText: 'Tidak ada data',\n    },\n    transfer: {\n      noMatch: 'Tidak ada data yg cocok',\n      noData: 'Tidak ada data',\n      titles: ['Daftar 1', 'Daftar 2'],\n      filterPlaceholder: 'Masukan kata kunci',\n      noCheckedFormat: '{total} item',\n      hasCheckedFormat: '{checked}/{total} terpilih',\n    },\n    image: {\n      error: 'GAGAL',\n    },\n    pageHeader: {\n      title: 'Kembali',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ya',\n      cancelButtonText: 'Tidak',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,oHAAoH,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,eAAe,CAAC,cAAc,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}