import{_ as e,c as l,u as a}from"./index-bcbc0702.js";/* empty css                *//* empty css                     *//* empty css                 *//* empty css               *//* empty css                   *//* empty css                 */import{useUserSessionStore as u}from"./userSession-03354358.js";import{applicationApi as r}from"./application-7212cdc3.js";import{C as t}from"./CameraCapture-b9781556.js";import{r as o,A as s,bB as n,B as d,C as i,F as m,G as p,E as c,X as v,D as g,H as f,J as _,u as b,al as h,I as y,an as V,ap as k,b7 as w,L as j,ai as I,aj as x,ak as C}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const q={class:"resubmit-page"},U={key:0},N={key:1},R={key:3},T={key:0,class:"challenge-row"},A={class:"ml-10"},B={key:1,class:"preview"},M=["src"],D={class:"form-actions"},H=e({__name:"Resubmit",setup(e){const H=l(),z=a(),E=u(),F=o(null),G={realName:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{min:2,max:20,message:"姓名长度在 2 到 20 个字符",trigger:"blur"}],school:[{required:!0,message:"请输入学校名称",trigger:"blur"}],studentId:[{required:!0,message:"请输入学号",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]},J=o(!0),L=o(!1),P=o(""),S=o({}),X=o(null),$=o(!1),K=o(0);let O=null;const Q=o({image:"",capture:null});async function W(){try{$.value=!0;const e=await r.getResubmitChallenge();if(e.success){X.value=e.data;const l=Math.max(0,Math.floor((e.data.expiresAt-Date.now())/1e3));K.value=l,O&&clearInterval(O),O=setInterval(()=>{K.value=Math.max(0,K.value-1),K.value<=0&&(clearInterval(O),O=null)},1e3)}}catch(e){c.error("获取挑战码失败")}finally{$.value=!1}}function Y(e){Q.value=e}function Z(){Q.value={image:"",capture:null}}async function ee(){var e,l;if(F.value){try{await F.value.validate()}catch(a){return void c.warning("请完善表单信息")}if(Q.value.image&&X.value)try{L.value=!0;const e={...S.value,uploadedImages:[Q.value.image],extraInfo:{...S.value.extraInfo||{},capture:{...Q.value.capture,code:X.value.code,challengeToken:X.value.token}}};await r.resubmit(e),c.success("已重新提交，等待审核"),z.replace({name:"Home"})}catch(u){c.error((null==(l=null==(e=null==u?void 0:u.response)?void 0:e.data)?void 0:l.message)||"提交失败")}finally{L.value=!1}else c.warning("请先重新拍摄身份照片并确保挑战码未过期")}}return s(async()=>{const e=H.query.token;if(!e)return P.value="链接无效",void(J.value=!1);E.setResubmitToken(e),await Promise.all([preloadTarget(),W()]),J.value=!1}),n(()=>{O&&clearInterval(O)}),(e,l)=>{const a=V,u=k,r=w,o=j,s=I,n=x,c=C,H=v;return d(),i("div",q,[m(H,{class:"resubmit-card"},{default:p(()=>{var e;return[l[15]||(l[15]=g("h2",null,"重新提交审核信息",-1)),J.value?(d(),i("p",U,"正在加载...")):P.value?(d(),i("p",N,f(P.value),1)):_("",!0),b(E).resubmitToken?(d(),i("div",R,[m(a,{type:"info",closable:!1,"show-icon":""},{default:p(()=>l[8]||(l[8]=[g("div",null,"您的申请曾被驳回，请按提示修改后重新提交。",-1)])),_:1}),m(u,null,{default:p(()=>l[9]||(l[9]=[y("实时拍照验证",-1)])),_:1,__:[9]}),X.value?(d(),i("div",T,[l[11]||(l[11]=g("span",null,"挑战码：",-1)),m(r,{type:"warning"},{default:p(()=>[y(f(X.value.code),1)]),_:1}),g("span",A,"剩余 "+f(K.value)+"s",1),m(o,{link:"",onClick:W,disabled:$.value},{default:p(()=>l[10]||(l[10]=[y("刷新",-1)])),_:1,__:[10]},8,["disabled"])])):_("",!0),m(t,{"overlay-text":null==(e=X.value)?void 0:e.code,onCaptured:Y},null,8,["overlay-text"]),Q.value.image?(d(),i("div",B,[g("img",{src:Q.value.image,alt:"预览"},null,8,M),m(o,{size:"small",onClick:Z},{default:p(()=>l[12]||(l[12]=[y("重拍",-1)])),_:1,__:[12]})])):_("",!0),m(c,{ref_key:"formRef",ref:F,model:S.value,rules:G,"label-width":"120px",class:"mt-20"},{default:p(()=>[m(n,{label:"真实姓名",prop:"realName"},{default:p(()=>[m(s,{modelValue:S.value.realName,"onUpdate:modelValue":l[0]||(l[0]=e=>S.value.realName=e),placeholder:"请输入真实姓名"},null,8,["modelValue"])]),_:1}),m(n,{label:"学校",prop:"school"},{default:p(()=>[m(s,{modelValue:S.value.school,"onUpdate:modelValue":l[1]||(l[1]=e=>S.value.school=e),placeholder:"请输入学校名称"},null,8,["modelValue"])]),_:1}),m(n,{label:"学号",prop:"studentId"},{default:p(()=>[m(s,{modelValue:S.value.studentId,"onUpdate:modelValue":l[2]||(l[2]=e=>S.value.studentId=e),placeholder:"请输入学号"},null,8,["modelValue"])]),_:1}),m(n,{label:"邮箱",prop:"email"},{default:p(()=>[m(s,{modelValue:S.value.email,"onUpdate:modelValue":l[3]||(l[3]=e=>S.value.email=e),type:"email",placeholder:"请输入邮箱地址"},null,8,["modelValue"])]),_:1}),m(n,{label:"手机号",prop:"phone"},{default:p(()=>[m(s,{modelValue:S.value.phone,"onUpdate:modelValue":l[4]||(l[4]=e=>S.value.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),m(n,{label:"院系",prop:"department"},{default:p(()=>[m(s,{modelValue:S.value.department,"onUpdate:modelValue":l[5]||(l[5]=e=>S.value.department=e),placeholder:"请输入院系"},null,8,["modelValue"])]),_:1}),m(n,{label:"年级",prop:"grade"},{default:p(()=>[m(s,{modelValue:S.value.grade,"onUpdate:modelValue":l[6]||(l[6]=e=>S.value.grade=e),placeholder:"请输入年级"},null,8,["modelValue"])]),_:1}),m(n,{label:"身份照片",required:""},{default:p(()=>[m(a,{title:"必须重新实时拍摄身份照片",type:"warning",closable:!1,"show-icon":"",class:"mb-10"},{default:p(()=>l[13]||(l[13]=[g("p",null,"为确保身份信息真实性，重新提交时必须重新拍摄身份照片，不允许使用之前的照片。",-1)])),_:1})]),_:1}),g("div",D,[m(o,{type:"primary",loading:L.value,onClick:ee},{default:p(()=>l[14]||(l[14]=[y("提交重审",-1)])),_:1,__:[14]},8,["loading"])])]),_:1},8,["model"])])):(d(),h(a,{key:2,type:"warning",closable:!1,"show-icon":"",style:{"margin-bottom":"8px"}},{default:p(()=>l[7]||(l[7]=[y(" 需通过邮件中的重提链接进入本页。 ",-1)])),_:1,__:[7]}))]}),_:1,__:[15]})])}}},[["__scopeId","data-v-3a656a13"]]);export{H as default};
