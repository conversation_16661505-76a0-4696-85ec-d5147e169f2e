export { B as BaseSequencer, V as VitestPlugin, c as createVitest, r as registerConsoleShortcuts, s as startVitest } from './vendor-node.a7c48fe1.js';
import 'pathe';
import 'vite';
import 'node:path';
import 'node:url';
import 'node:process';
import 'node:fs';
import './vendor-constants.538d9b49.js';
import './vendor-_commonjsHelpers.7d1333e8.js';
import 'os';
import 'path';
import 'util';
import 'stream';
import 'events';
import 'fs';
import 'picocolors';
import 'vite-node/utils';
import 'vite-node/client';
import '@vitest/snapshot/manager';
import 'vite-node/server';
import './vendor-index.29282562.js';
import 'std-env';
import '@vitest/runner/utils';
import '@vitest/utils';
import './vendor-global.97e4527c.js';
import './vendor-coverage.78040316.js';
import './vendor-paths.84fc7a99.js';
import 'node:v8';
import 'node:child_process';
import './vendor-index.b271ebe4.js';
import './vendor-base.9c08bbd0.js';
import 'node:worker_threads';
import 'node:os';
import 'tinypool';
import 'local-pkg';
import './vendor-reporters.f6975b8d.js';
import 'node:perf_hooks';
import './vendor-tasks.f9d75aed.js';
import '@vitest/utils/source-map';
import 'node:module';
import 'node:crypto';
import './vendor-index.85fc950a.js';
import 'node:buffer';
import 'child_process';
import 'assert';
import 'buffer';
import 'node:util';
import 'node:fs/promises';
import 'module';
import 'acorn';
import 'acorn-walk';
import 'magic-string';
import 'strip-literal';
import './vendor-environments.7aba93d9.js';
import './vendor-index.0b5b3600.js';
import 'node:assert';
import 'node:console';
import 'node:readline';
import 'readline';
