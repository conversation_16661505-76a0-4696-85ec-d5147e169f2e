/*
 BYXT 教务系统调试脚本 - 完整模拟浏览器行为
 用法：
   SSO_USERNAME=23374368 SSO_PASSWORD='20050626Zxy.' BYXT_PREFIX=77726476706e69737468656265737421f2ee598869327d517f468ca88d1b203b npm run debug:byxt
*/

import 'dotenv/config';
import axios from 'axios';
import { wrapper } from 'axios-cookiejar-support';
import { CookieJar } from 'tough-cookie';
import { logger } from '@/utils/logger';

const WEBVPN_BASE = 'https://d.buaa.edu.cn';
const BYXT_PREFIX = process.env.BYXT_PREFIX || '77726476706e69737468656265737421f2ee598869327d517f468ca88d1b203b';

// 创建带 CookieJar 的客户端
const jar = new CookieJar();
const client = wrapper(axios.create({
  withCredentials: true,
  jar,
  timeout: 30000,
  validateStatus: () => true
}));

async function step1_vpnLogin() {
  console.log('\n=== 步骤1: VPN 登录 ===');
  
  // 1. 获取 VPN 基础页面
  const baseResp = await client.get(`${WEBVPN_BASE}/`);
  console.log(`VPN 基础页面: ${baseResp.status}`);
  
  // 2. 获取登录页面
  const loginPageResp = await client.get(`${WEBVPN_BASE}/https/77726476706e69737468656265737421f1e751d225256951300d8db9d6562d/uc/wap/login?redirect=https%3A%2F%2Fapp.buaa.edu.cn%2Fuc%2Fwap%2Fuser%2Findex`);
  console.log(`登录页面: ${loginPageResp.status}`);
  
  // 3. 提取 execution
  const html = loginPageResp.data;
  const execMatch = html.match(/name="execution"\s+value="([^"]+)"/);
  const execution = execMatch ? execMatch[1] : '';
  console.log(`Execution: ${execution}`);
  
  // 4. 提交登录
  const loginData = new URLSearchParams({
    username: process.env.SSO_USERNAME || '',
    password: process.env.SSO_PASSWORD || '',
    execution,
    _eventId: 'submit',
    geolocation: ''
  });
  
  const loginResp = await client.post(
    `${WEBVPN_BASE}/https/77726476706e69737468656265737421f1e751d225256951300d8db9d6562d/uc/wap/login?redirect=https%3A%2F%2Fapp.buaa.edu.cn%2Fuc%2Fwap%2Fuser%2Findex`,
    loginData,
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    }
  );
  
  console.log(`登录响应: ${loginResp.status}`);
  if (loginResp.headers.location) {
    console.log(`重定向到: ${loginResp.headers.location}`);
  }
  
  // 5. 跟随重定向链
  let currentUrl = loginResp.headers.location;
  let redirectCount = 0;
  
  while (currentUrl && redirectCount < 10) {
    console.log(`跟随重定向 ${redirectCount + 1}: ${currentUrl}`);
    const redirectResp = await client.get(currentUrl);
    console.log(`重定向响应: ${redirectResp.status}`);
    
    if (redirectResp.status === 302 && redirectResp.headers.location) {
      currentUrl = redirectResp.headers.location;
      redirectCount++;
    } else {
      break;
    }
  }
  
  console.log('VPN 登录完成');
}

async function step2_byxtAccess() {
  console.log('\n=== 步骤2: BYXT 模块访问 ===');
  
  // 1. 访问 BYXT 模块首页
  const indexUrl = `${WEBVPN_BASE}/https/${BYXT_PREFIX}/jwapp/sys/xsjbxxgl/*default/index.do?THEME=indigo&EMAP_LANG=zh&forceApp=xsjbxxgl&_yhz=00000526c0d716f8a4faaa930f6fee15f68ba&min=1`;
  console.log(`访问 BYXT 首页: ${indexUrl}`);
  
  const indexResp = await client.get(indexUrl);
  console.log(`BYXT 首页响应: ${indexResp.status} ${indexResp.headers['content-type']}`);

  // 检查首页内容，寻找可能的 CAS 票据或会话信息
  const indexContent = String(indexResp.data);
  console.log(`BYXT 首页内容长度: ${indexContent.length}`);

  // 寻找可能的 CAS 票据验证 URL
  const casTicketMatch = indexContent.match(/ticket=([^&"']+)/);
  if (casTicketMatch) {
    console.log(`发现 CAS 票据: ${casTicketMatch[1]}`);
  }

  // 寻找可能的会话初始化脚本
  const scriptMatches = indexContent.match(/<script[^>]*>([\s\S]*?)<\/script>/gi);
  if (scriptMatches) {
    console.log(`发现 ${scriptMatches.length} 个脚本标签`);
    for (let i = 0; i < Math.min(3, scriptMatches.length); i++) {
      const script = scriptMatches[i];
      if (script.includes('cas') || script.includes('login') || script.includes('session') || script.includes('init')) {
        console.log(`相关脚本 ${i + 1}: ${script.slice(0, 300)}`);
      }
    }
  }

  // 2. 尝试获取列表数据
  const listUrl = `${WEBVPN_BASE}/https/${BYXT_PREFIX}/jwapp/sys/xsjbxxgl/modules/xsjbxx/cxxsjbxxlb.do?vpn-12-o2-byxt.buaa.edu.cn`;
  const listForm = 'querySetting=%5B%5D&ZDYL=field_checkbox%3Bpinned%2CXUH%3Bpinned%2CXH%3Bpinned%2CXM%3Bpinned%2CXZNJ%3Bpinned%2CSFZJH%3Bpinned%2CXBDM%3Bpinned%2CZZMMDM%3Bpinned%2CMZDM%3Bpinned%2CYXDM%3Bpinned%2CSFZJ%3Bpinned%2CZYDM%3Bpinned%2CBYZYDM%3Bpinned%2CBJMC%3Bpinned%2CXSLBDM%3Bpinned%2CXZ%3Bpinned%2CJG%3Bpinned%2CGATQDM%3Bpinned%2CSFZX%3Bpinned%2CXJZTDM%3Bpinned%2CXXXQDM%3Bpinned%2CTSXSLXDM&*order=%2BBJDM%2C%2BXH&pageSize=50&pageNumber=1';
  
  console.log(`POST 列表接口: ${listUrl}`);
  const listResp = await client.post(listUrl, listForm, {
    headers: {
      'Accept': 'application/json, text/javascript, */*; q=0.01',
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'X-Requested-With': 'XMLHttpRequest',
      'Origin': WEBVPN_BASE,
      'Referer': indexUrl,
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin'
    }
  });
  
  console.log(`列表响应: ${listResp.status} ${listResp.headers['content-type']}`);
  
  let listData;
  try {
    listData = typeof listResp.data === 'string' ? JSON.parse(listResp.data) : listResp.data;
    console.log('列表数据结构:');
    console.log(JSON.stringify(listData, null, 2).slice(0, 2000));
  } catch (e) {
    console.log('列表响应不是 JSON:');
    console.log(String(listResp.data).slice(0, 1000));
    return;
  }
  
  // 3. 检查是否需要登录
  if (listData.success === false && listData.url === '/login') {
    console.log('\n需要额外登录，开始完整的 BYXT 登录流程');

    // 步骤1: 访问 BYXT 登录页面
    const loginUrl = `${WEBVPN_BASE}/https/${BYXT_PREFIX}/login`;
    console.log(`访问 BYXT 登录页: ${loginUrl}`);
    const loginPageResp = await client.get(loginUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': indexUrl
      }
    });
    console.log(`BYXT 登录页响应: ${loginPageResp.status} ${loginPageResp.headers['content-type']}`);

    // 步骤2: 检查是否有重定向到 CAS
    if (loginPageResp.status === 302 && loginPageResp.headers.location) {
      console.log(`BYXT 登录重定向到: ${loginPageResp.headers.location}`);

      // 跟随重定向链
      let currentUrl = loginPageResp.headers.location;
      let redirectCount = 0;

      while (currentUrl && redirectCount < 10) {
        console.log(`跟随 BYXT 重定向 ${redirectCount + 1}: ${currentUrl}`);
        const redirectResp = await client.get(currentUrl);
        console.log(`BYXT 重定向响应: ${redirectResp.status}`);

        if (redirectResp.status === 302 && redirectResp.headers.location) {
          currentUrl = redirectResp.headers.location;
          redirectCount++;
        } else {
          break;
        }
      }
    }

    // 步骤3: 尝试访问一些可能的初始化接口
    const initUrls = [
      `${WEBVPN_BASE}/https/${BYXT_PREFIX}/jwapp/sys/xsjbxxgl/modules/xsjbxx/init.do`,
      `${WEBVPN_BASE}/https/${BYXT_PREFIX}/jwapp/sys/xsjbxxgl/init.do`,
      `${WEBVPN_BASE}/https/${BYXT_PREFIX}/jwapp/sys/xsjbxxgl/modules/init.do`,
      `${WEBVPN_BASE}/https/${BYXT_PREFIX}/jwapp/api/cas/validate`
    ];

    for (const initUrl of initUrls) {
      console.log(`尝试初始化接口: ${initUrl}`);
      try {
        const initResp = await client.get(initUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': indexUrl
          },
          timeout: 10000
        });
        console.log(`初始化接口响应: ${initResp.status} ${initResp.headers['content-type']}`);
        if (initResp.status === 200) {
          // 检查响应内容
          const content = String(initResp.data).slice(0, 1000);
          console.log(`初始化接口内容预览: ${content}`);

          // 检查是否有重定向信息
          if (content.includes('window.location') || content.includes('redirect')) {
            console.log('发现重定向信息');
          }

          console.log('初始化成功，跳出循环');
          break;
        }
      } catch (e) {
        console.log(`初始化接口失败: ${e.message}`);
      }
    }

    // 步骤4: 重新访问 BYXT 首页
    console.log('重新访问 BYXT 首页');
    await client.get(indexUrl);

    // 步骤5: 重试列表
    console.log('重试列表接口');
    const retryResp = await client.post(listUrl, listForm, {
      headers: {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': WEBVPN_BASE,
        'Referer': indexUrl,
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin'
      }
    });

    console.log(`重试响应: ${retryResp.status} ${retryResp.headers['content-type']}`);
    try {
      listData = typeof retryResp.data === 'string' ? JSON.parse(retryResp.data) : retryResp.data;
      console.log('重试列表数据:');
      console.log(JSON.stringify(listData, null, 2).slice(0, 2000));
    } catch (e) {
      console.log('重试响应不是 JSON:');
      console.log(String(retryResp.data).slice(0, 1000));
      return;
    }
  }
  
  // 4. 提取 WID
  const rows = listData?.datas?.cxxsjbxxlb?.rows || [];
  console.log(`找到 ${rows.length} 条记录`);
  
  if (rows.length === 0) {
    console.log('没有找到学生记录');
    return;
  }
  
  const wid = rows[0].WID;
  console.log(`提取到 WID: ${wid}`);
  
  // 5. 获取详情
  const detailUrl = `${WEBVPN_BASE}/https/${BYXT_PREFIX}/jwapp/sys/xsjbxxgl/modules/xsjbxx/cxxsjbxx.do?vpn-12-o2-byxt.buaa.edu.cn`;
  const detailForm = `WID=${encodeURIComponent(wid)}`;
  
  console.log(`POST 详情接口: ${detailUrl}`);
  const detailResp = await client.post(detailUrl, detailForm, {
    headers: {
      'Accept': 'application/json, text/javascript, */*; q=0.01',
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'X-Requested-With': 'XMLHttpRequest',
      'Origin': WEBVPN_BASE,
      'Referer': indexUrl,
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin'
    }
  });
  
  console.log(`详情响应: ${detailResp.status} ${detailResp.headers['content-type']}`);
  
  try {
    const detailData = typeof detailResp.data === 'string' ? JSON.parse(detailResp.data) : detailResp.data;
    console.log('详情数据:');
    console.log(JSON.stringify(detailData, null, 2));
  } catch (e) {
    console.log('详情响应不是 JSON:');
    console.log(String(detailResp.data).slice(0, 1000));
  }
  
  // 6. 输出学生信息
  const student = rows[0];
  console.log('\n=== 学生信息 ===');
  console.log(`学号: ${student.XH}`);
  console.log(`姓名: ${student.XM}`);
  console.log(`学院: ${student.YXDM_DISPLAY}`);
  console.log(`专业: ${student.ZYDM_DISPLAY}`);
  console.log(`年级: ${student.XZNJ || student.XZNJ_DISPLAY}`);
  console.log(`邮箱: ${student.DZXX || '无'}`);
  console.log(`手机: ${student.SJH || '无'}`);
}

async function main() {
  try {
    await step1_vpnLogin();
    await step2_byxtAccess();
  } catch (error) {
    console.error('调试过程出错:', error);
  }
}

main();
