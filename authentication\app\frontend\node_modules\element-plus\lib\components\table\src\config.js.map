{"version": 3, "file": "config.js", "sources": ["../../../../../../packages/components/table/src/config.ts"], "sourcesContent": ["import { h } from 'vue'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ArrowRight, Loading } from '@element-plus/icons-vue'\nimport { getProp, isBoolean, isFunction, isNumber } from '@element-plus/utils'\n\nimport type { VNode } from 'vue'\nimport type { TableColumnCtx } from './table-column/defaults'\nimport type { Store } from './store'\nimport type { DefaultRow, TreeNode } from './table/defaults'\n\nconst defaultClassNames = {\n  selection: 'table-column--selection',\n  expand: 'table__expand-column',\n} as const\n\nexport const cellStarts = {\n  default: {\n    order: '',\n  },\n  selection: {\n    width: 48,\n    minWidth: 48,\n    realWidth: 48,\n    order: '',\n  },\n  expand: {\n    width: 48,\n    minWidth: 48,\n    realWidth: 48,\n    order: '',\n  },\n  index: {\n    width: 48,\n    minWidth: 48,\n    realWidth: 48,\n    order: '',\n  },\n}\n\nexport const getDefaultClassName = (type: string) => {\n  return defaultClassNames[type as keyof typeof defaultClassNames] || ''\n}\n\n// 这些选项不应该被覆盖\nexport const cellForced = {\n  selection: {\n    renderHeader<T extends DefaultRow>({\n      store,\n      column,\n    }: {\n      store: Store<T>\n      column: TableColumnCtx<T>\n    }) {\n      function isDisabled() {\n        return store.states.data.value && store.states.data.value.length === 0\n      }\n      return h(ElCheckbox, {\n        disabled: isDisabled(),\n        size: store.states.tableSize.value,\n        indeterminate:\n          store.states.selection.value.length > 0 &&\n          !store.states.isAllSelected.value,\n        'onUpdate:modelValue': store.toggleAllSelection ?? undefined,\n        modelValue: store.states.isAllSelected.value,\n        ariaLabel: column.label,\n      })\n    },\n    renderCell<T extends DefaultRow>({\n      row,\n      column,\n      store,\n      $index,\n    }: {\n      row: T\n      column: TableColumnCtx<T>\n      store: Store<T>\n      $index: number\n    }) {\n      return h(ElCheckbox, {\n        disabled: column.selectable\n          ? !column.selectable.call(null, row, $index)\n          : false,\n        size: store.states.tableSize.value,\n        onChange: () => {\n          store.commit('rowSelectedChanged', row)\n        },\n        onClick: (event: Event) => event.stopPropagation(),\n        modelValue: store.isSelected(row),\n        ariaLabel: column.label,\n      })\n    },\n    sortable: false,\n    resizable: false,\n  },\n  index: {\n    renderHeader<T extends DefaultRow>({\n      column,\n    }: {\n      column: TableColumnCtx<T>\n    }) {\n      return column.label || '#'\n    },\n    renderCell<T extends DefaultRow>({\n      column,\n      $index,\n    }: {\n      column: TableColumnCtx<T>\n      $index: number\n    }) {\n      let i = $index + 1\n      const index = column.index\n\n      if (isNumber(index)) {\n        i = $index + index\n      } else if (isFunction(index)) {\n        i = index($index)\n      }\n      return h('div', {}, [i])\n    },\n    sortable: false,\n  },\n  expand: {\n    renderHeader<T extends DefaultRow>({\n      column,\n    }: {\n      column: TableColumnCtx<T>\n    }) {\n      return column.label || ''\n    },\n    renderCell<T extends DefaultRow>({\n      column,\n      row,\n      store,\n      expanded,\n    }: {\n      column: TableColumnCtx<T>\n      row: T\n      store: Store<T>\n      expanded: boolean\n    }) {\n      const { ns } = store\n      const classes = [ns.e('expand-icon')]\n\n      if (!column.renderExpand && expanded) {\n        classes.push(ns.em('expand-icon', 'expanded'))\n      }\n      const callback = function (e: Event) {\n        e.stopPropagation()\n        store.toggleRowExpansion(row)\n      }\n      return h(\n        'div',\n        {\n          class: classes,\n          onClick: callback,\n        },\n        {\n          default: () => {\n            if (column.renderExpand) {\n              return [\n                column.renderExpand({\n                  expanded,\n                }),\n              ]\n            }\n\n            return [\n              h(ElIcon, null, {\n                default: () => {\n                  return [h(ArrowRight)]\n                },\n              }),\n            ]\n          },\n        }\n      )\n    },\n    sortable: false,\n    resizable: false,\n  },\n}\n\nexport function defaultRenderCell<T extends DefaultRow>({\n  row,\n  column,\n  $index,\n}: {\n  row: T\n  column: TableColumnCtx<T>\n  $index: number\n}) {\n  const property = column.property\n  const value = property && getProp(row, property).value\n  if (column && column.formatter) {\n    return column.formatter(row, column, value, $index)\n  }\n  return value?.toString?.() || ''\n}\n\nexport function treeCellPrefix<T extends DefaultRow>(\n  {\n    row,\n    treeNode,\n    store,\n  }: {\n    row: T\n    treeNode: TreeNode\n    store: Store<T>\n  },\n  createPlaceholder = false\n) {\n  const { ns } = store\n  if (!treeNode) {\n    if (createPlaceholder) {\n      return [\n        h('span', {\n          class: ns.e('placeholder'),\n        }),\n      ]\n    }\n    return null\n  }\n  const ele: VNode[] = []\n  const callback = function (e: Event) {\n    e.stopPropagation()\n    if (treeNode.loading) {\n      return\n    }\n    store.loadOrToggle(row)\n  }\n  if (treeNode.indent) {\n    ele.push(\n      h('span', {\n        class: ns.e('indent'),\n        style: { 'padding-left': `${treeNode.indent}px` },\n      })\n    )\n  }\n  if (isBoolean(treeNode.expanded) && !treeNode.noLazyChildren) {\n    const expandClasses = [\n      ns.e('expand-icon'),\n      treeNode.expanded ? ns.em('expand-icon', 'expanded') : '',\n    ]\n    let icon = ArrowRight\n    if (treeNode.loading) {\n      icon = Loading\n    }\n\n    ele.push(\n      h(\n        'div',\n        {\n          class: expandClasses,\n          onClick: callback,\n        },\n        {\n          default: () => {\n            return [\n              h(\n                ElIcon,\n                { class: { [ns.is('loading')]: treeNode.loading } },\n                {\n                  default: () => [h(icon)],\n                }\n              ),\n            ]\n          },\n        }\n      )\n    )\n  } else {\n    ele.push(\n      h('span', {\n        class: ns.e('placeholder'),\n      })\n    )\n  }\n  return ele\n}\n"], "names": ["h", "ElCheckbox", "isNumber", "isFunction", "ElIcon", "ArrowRight", "getProp", "isBoolean", "Loading"], "mappings": ";;;;;;;;;;;;AAKA,MAAM,iBAAiB,GAAG;AAC1B,EAAE,SAAS,EAAE,yBAAyB;AACtC,EAAE,MAAM,EAAE,sBAAsB;AAChC,CAAC,CAAC;AACU,MAAC,UAAU,GAAG;AAC1B,EAAE,OAAO,EAAE;AACX,IAAI,KAAK,EAAE,EAAE;AACb,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,KAAK,EAAE,EAAE;AACb,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,SAAS,EAAE,EAAE;AACjB,IAAI,KAAK,EAAE,EAAE;AACb,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,KAAK,EAAE,EAAE;AACb,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,SAAS,EAAE,EAAE;AACjB,IAAI,KAAK,EAAE,EAAE;AACb,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,KAAK,EAAE,EAAE;AACb,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,SAAS,EAAE,EAAE;AACjB,IAAI,KAAK,EAAE,EAAE;AACb,GAAG;AACH,EAAE;AACU,MAAC,mBAAmB,GAAG,CAAC,IAAI,KAAK;AAC7C,EAAE,OAAO,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AACvC,EAAE;AACU,MAAC,UAAU,GAAG;AAC1B,EAAE,SAAS,EAAE;AACb,IAAI,YAAY,CAAC;AACjB,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,KAAK,EAAE;AACP,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,SAAS,UAAU,GAAG;AAC5B,QAAQ,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;AAC/E,OAAO;AACP,MAAM,OAAOA,KAAC,CAACC,gBAAU,EAAE;AAC3B,QAAQ,QAAQ,EAAE,UAAU,EAAE;AAC9B,QAAQ,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK;AAC1C,QAAQ,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK;AACnG,QAAQ,qBAAqB,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,kBAAkB,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AACpF,QAAQ,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK;AACpD,QAAQ,SAAS,EAAE,MAAM,CAAC,KAAK;AAC/B,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,UAAU,CAAC;AACf,MAAM,GAAG;AACT,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,KAAK,EAAE;AACP,MAAM,OAAOD,KAAC,CAACC,gBAAU,EAAE;AAC3B,QAAQ,QAAQ,EAAE,MAAM,CAAC,UAAU,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,KAAK;AACxF,QAAQ,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK;AAC1C,QAAQ,QAAQ,EAAE,MAAM;AACxB,UAAU,KAAK,CAAC,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;AAClD,SAAS;AACT,QAAQ,OAAO,EAAE,CAAC,KAAK,KAAK,KAAK,CAAC,eAAe,EAAE;AACnD,QAAQ,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC;AACzC,QAAQ,SAAS,EAAE,MAAM,CAAC,KAAK;AAC/B,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,QAAQ,EAAE,KAAK;AACnB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,YAAY,CAAC;AACjB,MAAM,MAAM;AACZ,KAAK,EAAE;AACP,MAAM,OAAO,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC;AACjC,KAAK;AACL,IAAI,UAAU,CAAC;AACf,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,KAAK,EAAE;AACP,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;AACzB,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACjC,MAAM,IAAIC,cAAQ,CAAC,KAAK,CAAC,EAAE;AAC3B,QAAQ,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;AAC3B,OAAO,MAAM,IAAIC,iBAAU,CAAC,KAAK,CAAC,EAAE;AACpC,QAAQ,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAC1B,OAAO;AACP,MAAM,OAAOH,KAAC,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,QAAQ,EAAE,KAAK;AACnB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,YAAY,CAAC;AACjB,MAAM,MAAM;AACZ,KAAK,EAAE;AACP,MAAM,OAAO,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;AAChC,KAAK;AACL,IAAI,UAAU,CAAC;AACf,MAAM,MAAM;AACZ,MAAM,GAAG;AACT,MAAM,KAAK;AACX,MAAM,QAAQ;AACd,KAAK,EAAE;AACP,MAAM,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;AAC3B,MAAM,MAAM,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;AAC5C,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,QAAQ,EAAE;AAC5C,QAAQ,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC;AACvD,OAAO;AACP,MAAM,MAAM,QAAQ,GAAG,SAAS,CAAC,EAAE;AACnC,QAAQ,CAAC,CAAC,eAAe,EAAE,CAAC;AAC5B,QAAQ,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AACtC,OAAO,CAAC;AACR,MAAM,OAAOA,KAAC,CAAC,KAAK,EAAE;AACtB,QAAQ,KAAK,EAAE,OAAO;AACtB,QAAQ,OAAO,EAAE,QAAQ;AACzB,OAAO,EAAE;AACT,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,IAAI,MAAM,CAAC,YAAY,EAAE;AACnC,YAAY,OAAO;AACnB,cAAc,MAAM,CAAC,YAAY,CAAC;AAClC,gBAAgB,QAAQ;AACxB,eAAe,CAAC;AAChB,aAAa,CAAC;AACd,WAAW;AACX,UAAU,OAAO;AACjB,YAAYA,KAAC,CAACI,cAAM,EAAE,IAAI,EAAE;AAC5B,cAAc,OAAO,EAAE,MAAM;AAC7B,gBAAgB,OAAO,CAACJ,KAAC,CAACK,mBAAU,CAAC,CAAC,CAAC;AACvC,eAAe;AACf,aAAa,CAAC;AACd,WAAW,CAAC;AACZ,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,QAAQ,EAAE,KAAK;AACnB,IAAI,SAAS,EAAE,KAAK;AACpB,GAAG;AACH,EAAE;AACK,SAAS,iBAAiB,CAAC;AAClC,EAAE,GAAG;AACL,EAAE,MAAM;AACR,EAAE,MAAM;AACR,CAAC,EAAE;AACH,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACnC,EAAE,MAAM,KAAK,GAAG,QAAQ,IAAIC,eAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,CAAC;AACzD,EAAE,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE;AAClC,IAAI,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACxD,GAAG;AACH,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AAClG,CAAC;AACM,SAAS,cAAc,CAAC;AAC/B,EAAE,GAAG;AACL,EAAE,QAAQ;AACV,EAAE,KAAK;AACP,CAAC,EAAE,iBAAiB,GAAG,KAAK,EAAE;AAC9B,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC;AACvB,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,IAAI,iBAAiB,EAAE;AAC3B,MAAM,OAAO;AACb,QAAQN,KAAC,CAAC,MAAM,EAAE;AAClB,UAAU,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC;AACpC,SAAS,CAAC;AACV,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC;AACjB,EAAE,MAAM,QAAQ,GAAG,SAAS,CAAC,EAAE;AAC/B,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC;AACxB,IAAI,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC1B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC5B,GAAG,CAAC;AACJ,EAAE,IAAI,QAAQ,CAAC,MAAM,EAAE;AACvB,IAAI,GAAG,CAAC,IAAI,CAACA,KAAC,CAAC,MAAM,EAAE;AACvB,MAAM,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC;AAC3B,MAAM,KAAK,EAAE,EAAE,cAAc,EAAE,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;AACvD,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH,EAAE,IAAIO,eAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;AAChE,IAAI,MAAM,aAAa,GAAG;AAC1B,MAAM,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC;AACzB,MAAM,QAAQ,CAAC,QAAQ,GAAG,EAAE,CAAC,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC,GAAG,EAAE;AAC/D,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,GAAGF,mBAAU,CAAC;AAC1B,IAAI,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC1B,MAAM,IAAI,GAAGG,gBAAO,CAAC;AACrB,KAAK;AACL,IAAI,GAAG,CAAC,IAAI,CAACR,KAAC,CAAC,KAAK,EAAE;AACtB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK,EAAE;AACP,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO;AACf,UAAUA,KAAC,CAACI,cAAM,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,OAAO,EAAE,EAAE,EAAE;AACzE,YAAY,OAAO,EAAE,MAAM,CAACJ,KAAC,CAAC,IAAI,CAAC,CAAC;AACpC,WAAW,CAAC;AACZ,SAAS,CAAC;AACV,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,MAAM;AACT,IAAI,GAAG,CAAC,IAAI,CAACA,KAAC,CAAC,MAAM,EAAE;AACvB,MAAM,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC;AAChC,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb;;;;;;;;"}