{"version": 3, "file": "option.js", "sources": ["../../../../../../packages/components/select/src/option.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\n\nexport const COMPONENT_NAME = 'ElOption'\nexport const optionProps = buildProps({\n  /**\n   * @description value of option\n   */\n  value: {\n    type: [String, Number, Boolean, Object],\n    required: true as const,\n  },\n  /**\n   * @description label of option, same as `value` if omitted\n   */\n  label: {\n    type: [String, Number],\n  },\n  created: Boolean,\n  /**\n   * @description whether option is disabled\n   */\n  disabled: Boolean,\n})\n"], "names": ["buildProps"], "mappings": ";;;;;;AACY,MAAC,cAAc,GAAG,WAAW;AAC7B,MAAC,WAAW,GAAGA,kBAAU,CAAC;AACtC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;AAC3C,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC;;;;;"}