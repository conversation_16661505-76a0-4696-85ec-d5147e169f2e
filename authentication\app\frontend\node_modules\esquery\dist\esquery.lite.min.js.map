{"version": 3, "file": "esquery.lite.min.js", "sources": ["../parser.js", "../esquery.js"], "sourcesContent": ["/*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n(function(root, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define([], factory);\n  } else if (typeof module === \"object\" && module.exports) {\n    module.exports = factory();\n  }\n})(this, function() {\n  \"use strict\";\n\n  function peg$subclass(child, parent) {\n    function ctor() { this.constructor = child; }\n    ctor.prototype = parent.prototype;\n    child.prototype = new ctor();\n  }\n\n  function peg$SyntaxError(message, expected, found, location) {\n    this.message  = message;\n    this.expected = expected;\n    this.found    = found;\n    this.location = location;\n    this.name     = \"SyntaxError\";\n\n    if (typeof Error.captureStackTrace === \"function\") {\n      Error.captureStackTrace(this, peg$SyntaxError);\n    }\n  }\n\n  peg$subclass(peg$SyntaxError, Error);\n\n  peg$SyntaxError.buildMessage = function(expected, found) {\n    var DESCRIBE_EXPECTATION_FNS = {\n          literal: function(expectation) {\n            return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n          },\n\n          \"class\": function(expectation) {\n            var escapedParts = \"\",\n                i;\n\n            for (i = 0; i < expectation.parts.length; i++) {\n              escapedParts += expectation.parts[i] instanceof Array\n                ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1])\n                : classEscape(expectation.parts[i]);\n            }\n\n            return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n          },\n\n          any: function(expectation) {\n            return \"any character\";\n          },\n\n          end: function(expectation) {\n            return \"end of input\";\n          },\n\n          other: function(expectation) {\n            return expectation.description;\n          }\n        };\n\n    function hex(ch) {\n      return ch.charCodeAt(0).toString(16).toUpperCase();\n    }\n\n    function literalEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\"/g,  '\\\\\"')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function classEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\\]/g, '\\\\]')\n        .replace(/\\^/g, '\\\\^')\n        .replace(/-/g,  '\\\\-')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function describeExpectation(expectation) {\n      return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n    }\n\n    function describeExpected(expected) {\n      var descriptions = new Array(expected.length),\n          i, j;\n\n      for (i = 0; i < expected.length; i++) {\n        descriptions[i] = describeExpectation(expected[i]);\n      }\n\n      descriptions.sort();\n\n      if (descriptions.length > 0) {\n        for (i = 1, j = 1; i < descriptions.length; i++) {\n          if (descriptions[i - 1] !== descriptions[i]) {\n            descriptions[j] = descriptions[i];\n            j++;\n          }\n        }\n        descriptions.length = j;\n      }\n\n      switch (descriptions.length) {\n        case 1:\n          return descriptions[0];\n\n        case 2:\n          return descriptions[0] + \" or \" + descriptions[1];\n\n        default:\n          return descriptions.slice(0, -1).join(\", \")\n            + \", or \"\n            + descriptions[descriptions.length - 1];\n      }\n    }\n\n    function describeFound(found) {\n      return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n    }\n\n    return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n  };\n\n  function peg$parse(input, options) {\n    options = options !== void 0 ? options : {};\n\n    var peg$FAILED = {},\n\n        peg$startRuleFunctions = { start: peg$parsestart },\n        peg$startRuleFunction  = peg$parsestart,\n\n        peg$c0 = function(ss) {\n            return ss.length === 1 ? ss[0] : { type: 'matches', selectors: ss };\n          },\n        peg$c1 = function() { return void 0; },\n        peg$c2 = \" \",\n        peg$c3 = peg$literalExpectation(\" \", false),\n        peg$c4 = /^[^ [\\],():#!=><~+.]/,\n        peg$c5 = peg$classExpectation([\" \", \"[\", \"]\", \",\", \"(\", \")\", \":\", \"#\", \"!\", \"=\", \">\", \"<\", \"~\", \"+\", \".\"], true, false),\n        peg$c6 = function(i) { return i.join(''); },\n        peg$c7 = \">\",\n        peg$c8 = peg$literalExpectation(\">\", false),\n        peg$c9 = function() { return 'child'; },\n        peg$c10 = \"~\",\n        peg$c11 = peg$literalExpectation(\"~\", false),\n        peg$c12 = function() { return 'sibling'; },\n        peg$c13 = \"+\",\n        peg$c14 = peg$literalExpectation(\"+\", false),\n        peg$c15 = function() { return 'adjacent'; },\n        peg$c16 = function() { return 'descendant'; },\n        peg$c17 = \",\",\n        peg$c18 = peg$literalExpectation(\",\", false),\n        peg$c19 = function(s, ss) {\n          return [s].concat(ss.map(function (s) { return s[3]; }));\n        },\n        peg$c20 = function(op, s) {\n            if (!op) return s;\n            return { type: op, left: { type: 'exactNode' }, right: s };\n          },\n        peg$c21 = function(a, ops) {\n            return ops.reduce(function (memo, rhs) {\n              return { type: rhs[0], left: memo, right: rhs[1] };\n            }, a);\n          },\n        peg$c22 = \"!\",\n        peg$c23 = peg$literalExpectation(\"!\", false),\n        peg$c24 = function(subject, as) {\n            const b = as.length === 1 ? as[0] : { type: 'compound', selectors: as };\n            if(subject) b.subject = true;\n            return b;\n          },\n        peg$c25 = \"*\",\n        peg$c26 = peg$literalExpectation(\"*\", false),\n        peg$c27 = function(a) { return { type: 'wildcard', value: a }; },\n        peg$c28 = \"#\",\n        peg$c29 = peg$literalExpectation(\"#\", false),\n        peg$c30 = function(i) { return { type: 'identifier', value: i }; },\n        peg$c31 = \"[\",\n        peg$c32 = peg$literalExpectation(\"[\", false),\n        peg$c33 = \"]\",\n        peg$c34 = peg$literalExpectation(\"]\", false),\n        peg$c35 = function(v) { return v; },\n        peg$c36 = /^[><!]/,\n        peg$c37 = peg$classExpectation([\">\", \"<\", \"!\"], false, false),\n        peg$c38 = \"=\",\n        peg$c39 = peg$literalExpectation(\"=\", false),\n        peg$c40 = function(a) { return (a || '') + '='; },\n        peg$c41 = /^[><]/,\n        peg$c42 = peg$classExpectation([\">\", \"<\"], false, false),\n        peg$c43 = \".\",\n        peg$c44 = peg$literalExpectation(\".\", false),\n        peg$c45 = function(a, as) {\n            return [].concat.apply([a], as).join('');\n          },\n        peg$c46 = function(name, op, value) {\n              return { type: 'attribute', name: name, operator: op, value: value };\n            },\n        peg$c47 = function(name) { return { type: 'attribute', name: name }; },\n        peg$c48 = \"\\\"\",\n        peg$c49 = peg$literalExpectation(\"\\\"\", false),\n        peg$c50 = /^[^\\\\\"]/,\n        peg$c51 = peg$classExpectation([\"\\\\\", \"\\\"\"], true, false),\n        peg$c52 = \"\\\\\",\n        peg$c53 = peg$literalExpectation(\"\\\\\", false),\n        peg$c54 = peg$anyExpectation(),\n        peg$c55 = function(a, b) { return a + b; },\n        peg$c56 = function(d) {\n                return { type: 'literal', value: strUnescape(d.join('')) };\n              },\n        peg$c57 = \"'\",\n        peg$c58 = peg$literalExpectation(\"'\", false),\n        peg$c59 = /^[^\\\\']/,\n        peg$c60 = peg$classExpectation([\"\\\\\", \"'\"], true, false),\n        peg$c61 = /^[0-9]/,\n        peg$c62 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n        peg$c63 = function(a, b) {\n                // Can use `a.flat().join('')` once supported\n                const leadingDecimals = a ? [].concat.apply([], a).join('') : '';\n                return { type: 'literal', value: parseFloat(leadingDecimals + b.join('')) };\n              },\n        peg$c64 = function(i) { return { type: 'literal', value: i }; },\n        peg$c65 = \"type(\",\n        peg$c66 = peg$literalExpectation(\"type(\", false),\n        peg$c67 = /^[^ )]/,\n        peg$c68 = peg$classExpectation([\" \", \")\"], true, false),\n        peg$c69 = \")\",\n        peg$c70 = peg$literalExpectation(\")\", false),\n        peg$c71 = function(t) { return { type: 'type', value: t.join('') }; },\n        peg$c72 = /^[imsu]/,\n        peg$c73 = peg$classExpectation([\"i\", \"m\", \"s\", \"u\"], false, false),\n        peg$c74 = \"/\",\n        peg$c75 = peg$literalExpectation(\"/\", false),\n        peg$c76 = /^[^\\/]/,\n        peg$c77 = peg$classExpectation([\"/\"], true, false),\n        peg$c78 = function(d, flgs) { return {\n              type: 'regexp', value: new RegExp(d.join(''), flgs ? flgs.join('') : '') };\n            },\n        peg$c79 = function(i, is) {\n          return { type: 'field', name: is.reduce(function(memo, p){ return memo + p[0] + p[1]; }, i)};\n        },\n        peg$c80 = \":not(\",\n        peg$c81 = peg$literalExpectation(\":not(\", false),\n        peg$c82 = function(ss) { return { type: 'not', selectors: ss }; },\n        peg$c83 = \":matches(\",\n        peg$c84 = peg$literalExpectation(\":matches(\", false),\n        peg$c85 = function(ss) { return { type: 'matches', selectors: ss }; },\n        peg$c86 = \":has(\",\n        peg$c87 = peg$literalExpectation(\":has(\", false),\n        peg$c88 = function(ss) { return { type: 'has', selectors: ss }; },\n        peg$c89 = \":first-child\",\n        peg$c90 = peg$literalExpectation(\":first-child\", false),\n        peg$c91 = function() { return nth(1); },\n        peg$c92 = \":last-child\",\n        peg$c93 = peg$literalExpectation(\":last-child\", false),\n        peg$c94 = function() { return nthLast(1); },\n        peg$c95 = \":nth-child(\",\n        peg$c96 = peg$literalExpectation(\":nth-child(\", false),\n        peg$c97 = function(n) { return nth(parseInt(n.join(''), 10)); },\n        peg$c98 = \":nth-last-child(\",\n        peg$c99 = peg$literalExpectation(\":nth-last-child(\", false),\n        peg$c100 = function(n) { return nthLast(parseInt(n.join(''), 10)); },\n        peg$c101 = \":\",\n        peg$c102 = peg$literalExpectation(\":\", false),\n        peg$c103 = function(c) {\n          return { type: 'class', name: c };\n        },\n\n        peg$currPos          = 0,\n        peg$savedPos         = 0,\n        peg$posDetailsCache  = [{ line: 1, column: 1 }],\n        peg$maxFailPos       = 0,\n        peg$maxFailExpected  = [],\n        peg$silentFails      = 0,\n\n        peg$resultsCache = {},\n\n        peg$result;\n\n    if (\"startRule\" in options) {\n      if (!(options.startRule in peg$startRuleFunctions)) {\n        throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n      }\n\n      peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n    }\n\n    function text() {\n      return input.substring(peg$savedPos, peg$currPos);\n    }\n\n    function location() {\n      return peg$computeLocation(peg$savedPos, peg$currPos);\n    }\n\n    function expected(description, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildStructuredError(\n        [peg$otherExpectation(description)],\n        input.substring(peg$savedPos, peg$currPos),\n        location\n      );\n    }\n\n    function error(message, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildSimpleError(message, location);\n    }\n\n    function peg$literalExpectation(text, ignoreCase) {\n      return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n    }\n\n    function peg$classExpectation(parts, inverted, ignoreCase) {\n      return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n    }\n\n    function peg$anyExpectation() {\n      return { type: \"any\" };\n    }\n\n    function peg$endExpectation() {\n      return { type: \"end\" };\n    }\n\n    function peg$otherExpectation(description) {\n      return { type: \"other\", description: description };\n    }\n\n    function peg$computePosDetails(pos) {\n      var details = peg$posDetailsCache[pos], p;\n\n      if (details) {\n        return details;\n      } else {\n        p = pos - 1;\n        while (!peg$posDetailsCache[p]) {\n          p--;\n        }\n\n        details = peg$posDetailsCache[p];\n        details = {\n          line:   details.line,\n          column: details.column\n        };\n\n        while (p < pos) {\n          if (input.charCodeAt(p) === 10) {\n            details.line++;\n            details.column = 1;\n          } else {\n            details.column++;\n          }\n\n          p++;\n        }\n\n        peg$posDetailsCache[pos] = details;\n        return details;\n      }\n    }\n\n    function peg$computeLocation(startPos, endPos) {\n      var startPosDetails = peg$computePosDetails(startPos),\n          endPosDetails   = peg$computePosDetails(endPos);\n\n      return {\n        start: {\n          offset: startPos,\n          line:   startPosDetails.line,\n          column: startPosDetails.column\n        },\n        end: {\n          offset: endPos,\n          line:   endPosDetails.line,\n          column: endPosDetails.column\n        }\n      };\n    }\n\n    function peg$fail(expected) {\n      if (peg$currPos < peg$maxFailPos) { return; }\n\n      if (peg$currPos > peg$maxFailPos) {\n        peg$maxFailPos = peg$currPos;\n        peg$maxFailExpected = [];\n      }\n\n      peg$maxFailExpected.push(expected);\n    }\n\n    function peg$buildSimpleError(message, location) {\n      return new peg$SyntaxError(message, null, null, location);\n    }\n\n    function peg$buildStructuredError(expected, found, location) {\n      return new peg$SyntaxError(\n        peg$SyntaxError.buildMessage(expected, found),\n        expected,\n        found,\n        location\n      );\n    }\n\n    function peg$parsestart() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 32 + 0,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseselectors();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c0(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parse_();\n        if (s1 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c1();\n        }\n        s0 = s1;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parse_() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 1,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = [];\n      if (input.charCodeAt(peg$currPos) === 32) {\n        s1 = peg$c2;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c3); }\n      }\n      while (s1 !== peg$FAILED) {\n        s0.push(s1);\n        if (input.charCodeAt(peg$currPos) === 32) {\n          s1 = peg$c2;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c3); }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseidentifierName() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 2,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      if (peg$c4.test(input.charAt(peg$currPos))) {\n        s2 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c5); }\n      }\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          if (peg$c4.test(input.charAt(peg$currPos))) {\n            s2 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c5); }\n          }\n        }\n      } else {\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c6(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsebinaryOp() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 32 + 3,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 62) {\n          s2 = peg$c7;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c8); }\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c9();\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parse_();\n        if (s1 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 126) {\n            s2 = peg$c10;\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c11); }\n          }\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parse_();\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c12();\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parse_();\n          if (s1 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 43) {\n              s2 = peg$c13;\n              peg$currPos++;\n            } else {\n              s2 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c14); }\n            }\n            if (s2 !== peg$FAILED) {\n              s3 = peg$parse_();\n              if (s3 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c15();\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n          if (s0 === peg$FAILED) {\n            s0 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 32) {\n              s1 = peg$c2;\n              peg$currPos++;\n            } else {\n              s1 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c3); }\n            }\n            if (s1 !== peg$FAILED) {\n              s2 = peg$parse_();\n              if (s2 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c16();\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsehasSelectors() {\n      var s0, s1, s2, s3, s4, s5, s6, s7;\n\n      var key    = peg$currPos * 32 + 4,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsehasSelector();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parse_();\n        if (s4 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s5 = peg$c17;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c18); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parse_();\n            if (s6 !== peg$FAILED) {\n              s7 = peg$parsehasSelector();\n              if (s7 !== peg$FAILED) {\n                s4 = [s4, s5, s6, s7];\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parse_();\n          if (s4 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 44) {\n              s5 = peg$c17;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c18); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parse_();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parsehasSelector();\n                if (s7 !== peg$FAILED) {\n                  s4 = [s4, s5, s6, s7];\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c19(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseselectors() {\n      var s0, s1, s2, s3, s4, s5, s6, s7;\n\n      var key    = peg$currPos * 32 + 5,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseselector();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parse_();\n        if (s4 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s5 = peg$c17;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c18); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parse_();\n            if (s6 !== peg$FAILED) {\n              s7 = peg$parseselector();\n              if (s7 !== peg$FAILED) {\n                s4 = [s4, s5, s6, s7];\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parse_();\n          if (s4 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 44) {\n              s5 = peg$c17;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c18); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parse_();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parseselector();\n                if (s7 !== peg$FAILED) {\n                  s4 = [s4, s5, s6, s7];\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c19(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsehasSelector() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 6,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsebinaryOp();\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseselector();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c20(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseselector() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 7,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsesequence();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parsebinaryOp();\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsesequence();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parsebinaryOp();\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsesequence();\n            if (s5 !== peg$FAILED) {\n              s4 = [s4, s5];\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c21(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsesequence() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 32 + 8,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 33) {\n        s1 = peg$c22;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c23); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parseatom();\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            s3 = peg$parseatom();\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c24(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseatom() {\n      var s0;\n\n      var key    = peg$currPos * 32 + 9,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$parsewildcard();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parseidentifier();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parseattr();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parsefield();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parsenegation();\n              if (s0 === peg$FAILED) {\n                s0 = peg$parsematches();\n                if (s0 === peg$FAILED) {\n                  s0 = peg$parsehas();\n                  if (s0 === peg$FAILED) {\n                    s0 = peg$parsefirstChild();\n                    if (s0 === peg$FAILED) {\n                      s0 = peg$parselastChild();\n                      if (s0 === peg$FAILED) {\n                        s0 = peg$parsenthChild();\n                        if (s0 === peg$FAILED) {\n                          s0 = peg$parsenthLastChild();\n                          if (s0 === peg$FAILED) {\n                            s0 = peg$parseclass();\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsewildcard() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 10,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 42) {\n        s1 = peg$c25;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c26); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c27(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseidentifier() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 11,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 35) {\n        s1 = peg$c28;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c29); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c30(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattr() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 12,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 91) {\n        s1 = peg$c31;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c32); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseattrValue();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 93) {\n                s5 = peg$c33;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c34); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c35(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrOps() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 13,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (peg$c36.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c37); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s2 = peg$c38;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c39); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c40(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        if (peg$c41.test(input.charAt(peg$currPos))) {\n          s0 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s0 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c42); }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrEqOps() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 14,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 33) {\n        s1 = peg$c22;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c23); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s2 = peg$c38;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c39); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c40(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrName() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 15,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseidentifierName();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s4 = peg$c43;\n          peg$currPos++;\n        } else {\n          s4 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c44); }\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parseidentifierName();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s4 = peg$c43;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c44); }\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parseidentifierName();\n            if (s5 !== peg$FAILED) {\n              s4 = [s4, s5];\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c45(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrValue() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 16,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseattrName();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseattrEqOps();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parsetype();\n              if (s5 === peg$FAILED) {\n                s5 = peg$parseregex();\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c46(s1, s3, s5);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parseattrName();\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parse_();\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parseattrOps();\n            if (s3 !== peg$FAILED) {\n              s4 = peg$parse_();\n              if (s4 !== peg$FAILED) {\n                s5 = peg$parsestring();\n                if (s5 === peg$FAILED) {\n                  s5 = peg$parsenumber();\n                  if (s5 === peg$FAILED) {\n                    s5 = peg$parsepath();\n                  }\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c46(s1, s3, s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parseattrName();\n          if (s1 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c47(s1);\n          }\n          s0 = s1;\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsestring() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 17,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 34) {\n        s1 = peg$c48;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c49); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c50.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c51); }\n        }\n        if (s3 === peg$FAILED) {\n          s3 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 92) {\n            s4 = peg$c52;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c53); }\n          }\n          if (s4 !== peg$FAILED) {\n            if (input.length > peg$currPos) {\n              s5 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c54); }\n            }\n            if (s5 !== peg$FAILED) {\n              peg$savedPos = s3;\n              s4 = peg$c55(s4, s5);\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          if (peg$c50.test(input.charAt(peg$currPos))) {\n            s3 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c51); }\n          }\n          if (s3 === peg$FAILED) {\n            s3 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 92) {\n              s4 = peg$c52;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c53); }\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.length > peg$currPos) {\n                s5 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c54); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s3;\n                s4 = peg$c55(s4, s5);\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 34) {\n            s3 = peg$c48;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c49); }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c56(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 39) {\n          s1 = peg$c57;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c58); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = [];\n          if (peg$c59.test(input.charAt(peg$currPos))) {\n            s3 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c60); }\n          }\n          if (s3 === peg$FAILED) {\n            s3 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 92) {\n              s4 = peg$c52;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c53); }\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.length > peg$currPos) {\n                s5 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c54); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s3;\n                s4 = peg$c55(s4, s5);\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          }\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c59.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c60); }\n            }\n            if (s3 === peg$FAILED) {\n              s3 = peg$currPos;\n              if (input.charCodeAt(peg$currPos) === 92) {\n                s4 = peg$c52;\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c53); }\n              }\n              if (s4 !== peg$FAILED) {\n                if (input.length > peg$currPos) {\n                  s5 = input.charAt(peg$currPos);\n                  peg$currPos++;\n                } else {\n                  s5 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c54); }\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$savedPos = s3;\n                  s4 = peg$c55(s4, s5);\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            }\n          }\n          if (s2 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 39) {\n              s3 = peg$c57;\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c58); }\n            }\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c56(s2);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenumber() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 32 + 18,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = [];\n      if (peg$c61.test(input.charAt(peg$currPos))) {\n        s3 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c62); }\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        if (peg$c61.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c62); }\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s3 = peg$c43;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c44); }\n        }\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c61.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c62); }\n        }\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c61.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c62); }\n            }\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c63(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsepath() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 19,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseidentifierName();\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c64(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsetype() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 20,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c65) {\n        s1 = peg$c65;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c66); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c67.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c68); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c67.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c68); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c71(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseflags() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 21,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = [];\n      if (peg$c72.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c73); }\n      }\n      if (s1 !== peg$FAILED) {\n        while (s1 !== peg$FAILED) {\n          s0.push(s1);\n          if (peg$c72.test(input.charAt(peg$currPos))) {\n            s1 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c73); }\n          }\n        }\n      } else {\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseregex() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 32 + 22,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 47) {\n        s1 = peg$c74;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c75); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c76.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c77); }\n        }\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c76.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c77); }\n            }\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 47) {\n            s3 = peg$c74;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c75); }\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parseflags();\n            if (s4 === peg$FAILED) {\n              s4 = null;\n            }\n            if (s4 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c78(s2, s4);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefield() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      var key    = peg$currPos * 32 + 23,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s1 = peg$c43;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c44); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s5 = peg$c43;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c44); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parseidentifierName();\n            if (s6 !== peg$FAILED) {\n              s5 = [s5, s6];\n              s4 = s5;\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s4;\n            s4 = peg$FAILED;\n          }\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 46) {\n              s5 = peg$c43;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c44); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parseidentifierName();\n              if (s6 !== peg$FAILED) {\n                s5 = [s5, s6];\n                s4 = s5;\n              } else {\n                peg$currPos = s4;\n                s4 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c79(s2, s3);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenegation() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 24,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c80) {\n        s1 = peg$c80;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c81); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c82(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsematches() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 25,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 9) === peg$c83) {\n        s1 = peg$c83;\n        peg$currPos += 9;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c84); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c85(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsehas() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 26,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c86) {\n        s1 = peg$c86;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c87); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parsehasSelectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c88(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefirstChild() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 27,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 12) === peg$c89) {\n        s1 = peg$c89;\n        peg$currPos += 12;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c90); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c91();\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parselastChild() {\n      var s0, s1;\n\n      var key    = peg$currPos * 32 + 28,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 11) === peg$c92) {\n        s1 = peg$c92;\n        peg$currPos += 11;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c93); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c94();\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenthChild() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 29,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 11) === peg$c95) {\n        s1 = peg$c95;\n        peg$currPos += 11;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c96); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c61.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c62); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c61.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c62); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c97(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenthLastChild() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 32 + 30,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 16) === peg$c98) {\n        s1 = peg$c98;\n        peg$currPos += 16;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c99); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c61.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c62); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c61.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c62); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c69;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c70); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c100(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseclass() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 32 + 31,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 58) {\n        s1 = peg$c101;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c102); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c103(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n\n      function nth(n) { return { type: 'nth-child', index: { type: 'literal', value: n } }; }\n      function nthLast(n) { return { type: 'nth-last-child', index: { type: 'literal', value: n } }; }\n      function strUnescape(s) {\n        return s.replace(/\\\\(.)/g, function(match, ch) {\n          switch(ch) {\n            case 'b': return '\\b';\n            case 'f': return '\\f';\n            case 'n': return '\\n';\n            case 'r': return '\\r';\n            case 't': return '\\t';\n            case 'v': return '\\v';\n            default: return ch;\n          }\n        });\n      }\n\n\n    peg$result = peg$startRuleFunction();\n\n    if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n      return peg$result;\n    } else {\n      if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n        peg$fail(peg$endExpectation());\n      }\n\n      throw peg$buildStructuredError(\n        peg$maxFailExpected,\n        peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n        peg$maxFailPos < input.length\n          ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n          : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n      );\n    }\n  }\n\n  return {\n    SyntaxError: peg$SyntaxError,\n    parse:       peg$parse\n  };\n});\n", "/* vim: set sw=4 sts=4 : */\nimport estraverse from 'estraverse';\nimport parser from './parser.js';\n\n/**\n* @typedef {\"LEFT_SIDE\"|\"RIGHT_SIDE\"} Side\n*/\n\nconst LEFT_SIDE = 'LEFT_SIDE';\nconst RIGHT_SIDE = 'RIGHT_SIDE';\n\n/**\n * @external AST\n * @see https://esprima.readthedocs.io/en/latest/syntax-tree-format.html\n */\n\n/**\n * One of the rules of `grammar.pegjs`\n * @typedef {PlainObject} SelectorAST\n * @see grammar.pegjs\n*/\n\n/**\n * The `sequence` production of `grammar.pegjs`\n * @typedef {PlainObject} SelectorSequenceAST\n*/\n\n/**\n * Get the value of a property which may be multiple levels down\n * in the object.\n * @param {?PlainObject} obj\n * @param {string[]} keys\n * @returns {undefined|boolean|string|number|external:AST}\n */\nfunction getPath(obj, keys) {\n    for (let i = 0; i < keys.length; ++i) {\n        if (obj == null) { return obj; }\n        obj = obj[keys[i]];\n    }\n    return obj;\n}\n\n/**\n * Determine whether `node` can be reached by following `path`,\n * starting at `ancestor`.\n * @param {?external:AST} node\n * @param {?external:AST} ancestor\n * @param {string[]} path\n * @param {Integer} fromPathIndex\n * @returns {boolean}\n */\nfunction inPath(node, ancestor, path, fromPathIndex) {\n    let current = ancestor;\n    for (let i = fromPathIndex; i < path.length; ++i) {\n        if (current == null) {\n            return false;\n        }\n        const field = current[path[i]];\n        if (Array.isArray(field)) {\n            for (let k = 0; k < field.length; ++k) {\n                if (inPath(node, field[k], path, i + 1)) {\n                    return true;\n                }\n            }\n            return false;\n        }\n        current = field;\n    }\n    return node === current;\n}\n\n/**\n * A generated matcher function for a selector.\n * @callback SelectorMatcher\n * @param {?SelectorAST} selector\n * @param {external:AST[]} [ancestry=[]]\n * @param {ESQueryOptions} [options]\n * @returns {void}\n*/\n\n/**\n * A WeakMap for holding cached matcher functions for selectors.\n * @type {WeakMap<SelectorAST, SelectorMatcher>}\n*/\nconst MATCHER_CACHE = typeof WeakMap === 'function' ? new WeakMap : null;\n\n/**\n * Look up a matcher function for `selector` in the cache.\n * If it does not exist, generate it with `generateMatcher` and add it to the cache.\n * In engines without WeakMap, the caching is skipped and matchers are generated with every call.\n * @param {?SelectorAST} selector\n * @returns {SelectorMatcher}\n */\nfunction getMatcher(selector) {\n    if (selector == null) {\n        return () => true;\n    }\n\n    if (MATCHER_CACHE != null) {\n        let matcher = MATCHER_CACHE.get(selector);\n        if (matcher != null) {\n            return matcher;\n        }\n        matcher = generateMatcher(selector);\n        MATCHER_CACHE.set(selector, matcher);\n        return matcher;\n    }\n\n    return generateMatcher(selector);\n}\n\n/**\n * Create a matcher function for `selector`,\n * @param {?SelectorAST} selector\n * @returns {SelectorMatcher}\n */\nfunction generateMatcher(selector) {\n    switch(selector.type) {\n        case 'wildcard':\n            return () => true;\n\n        case 'identifier': {\n            const value = selector.value.toLowerCase();\n            return (node, ancestry, options) => {\n                const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n                return value === node[nodeTypeKey].toLowerCase();\n            };\n        }\n\n        case 'exactNode':\n            return (node, ancestry) => {\n                return ancestry.length === 0;\n            };\n\n        case 'field': {\n            const path = selector.name.split('.');\n            return (node, ancestry) => {\n                const ancestor = ancestry[path.length - 1];\n                return inPath(node, ancestor, path, 0);\n            };\n        }\n\n        case 'matches': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (matchers[i](node, ancestry, options)) { return true; }\n                }\n                return false;\n            };\n        }\n\n        case 'compound': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (!matchers[i](node, ancestry, options)) { return false; }\n                }\n                return true;\n            };\n        }\n\n        case 'not': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (matchers[i](node, ancestry, options)) { return false; }\n                }\n                return true;\n            };\n        }\n\n        case 'has': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                let result = false;\n\n                const a = [];\n                estraverse.traverse(node, {\n                    enter (node, parent) {\n                        if (parent != null) { a.unshift(parent); }\n\n                        for (let i = 0; i < matchers.length; ++i) {\n                            if (matchers[i](node, a, options)) {\n                                result = true;\n                                this.break();\n                                return;\n                            }\n                        }\n                    },\n                    leave () { a.shift(); },\n                    keys: options && options.visitorKeys,\n                    fallback: options && options.fallback || 'iteration'\n                });\n\n                return result;\n            };\n        }\n\n        case 'child': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) => {\n                if (ancestry.length > 0 && right(node, ancestry, options)) {\n                    return left(ancestry[0], ancestry.slice(1), options);\n                }\n                return false;\n            };\n        }\n\n        case 'descendant': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) => {\n                if (right(node, ancestry, options)) {\n                    for (let i = 0, l = ancestry.length; i < l; ++i) {\n                        if (left(ancestry[i], ancestry.slice(i + 1), options)) {\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            };\n        }\n\n        case 'attribute': {\n            const path = selector.name.split('.');\n            switch (selector.operator) {\n                case void 0:\n                    return (node) => getPath(node, path) != null;\n                case '=':\n                    switch (selector.value.type) {\n                        case 'regexp':\n                            return (node) => {\n                                const p = getPath(node, path);\n                                return typeof p === 'string' && selector.value.value.test(p);\n                            };\n                        case 'literal': {\n                            const literal = `${selector.value.value}`;\n                            return (node) => literal === `${getPath(node, path)}`;\n                        }\n                        case 'type':\n                            return (node) => selector.value.value === typeof getPath(node, path);\n                    }\n                    throw new Error(`Unknown selector value type: ${selector.value.type}`);\n                case '!=':\n                    switch (selector.value.type) {\n                        case 'regexp':\n                            return (node) => !selector.value.value.test(getPath(node, path));\n                        case 'literal': {\n                            const literal = `${selector.value.value}`;\n                            return (node) => literal !== `${getPath(node, path)}`;\n                        }\n                        case 'type':\n                            return (node) => selector.value.value !== typeof getPath(node, path);\n                    }\n                    throw new Error(`Unknown selector value type: ${selector.value.type}`);\n                case '<=':\n                    return (node) => getPath(node, path) <= selector.value.value;\n                case '<':\n                    return (node) => getPath(node, path) < selector.value.value;\n                case '>':\n                    return (node) => getPath(node, path) > selector.value.value;\n                case '>=':\n                    return (node) => getPath(node, path) >= selector.value.value;\n            }\n            throw new Error(`Unknown operator: ${selector.operator}`);\n        }\n\n        case 'sibling': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    sibling(node, left, ancestry, LEFT_SIDE, options) ||\n                    selector.left.subject &&\n                    left(node, ancestry, options) &&\n                    sibling(node, right, ancestry, RIGHT_SIDE, options);\n        }\n\n        case 'adjacent': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    adjacent(node, left, ancestry, LEFT_SIDE, options) ||\n                    selector.right.subject &&\n                    left(node, ancestry, options) &&\n                    adjacent(node, right, ancestry, RIGHT_SIDE, options);\n        }\n\n        case 'nth-child': {\n            const nth = selector.index.value;\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    nthChild(node, ancestry, nth, options);\n        }\n\n        case 'nth-last-child': {\n            const nth = -selector.index.value;\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    nthChild(node, ancestry, nth, options);\n        }\n\n        case 'class': {\n            \n            const name = selector.name.toLowerCase();\n\n            return (node, ancestry, options) => {\n                \n                if (options && options.matchClass) {\n                    return options.matchClass(selector.name, node, ancestry);\n                }\n                \n                if (options && options.nodeTypeKey) return false;    \n\n                switch(name){\n                    case 'statement':\n                        if(node.type.slice(-9) === 'Statement') return true;\n                        // fallthrough: interface Declaration <: Statement { }\n                    case 'declaration':\n                        return node.type.slice(-11) === 'Declaration';\n                    case 'pattern':\n                        if(node.type.slice(-7) === 'Pattern') return true;\n                        // fallthrough: interface Expression <: Node, Pattern { }\n                    case 'expression':\n                        return node.type.slice(-10) === 'Expression' ||\n                            node.type.slice(-7) === 'Literal' ||\n                            (\n                                node.type === 'Identifier' &&\n                                (ancestry.length === 0 || ancestry[0].type !== 'MetaProperty')\n                            ) ||\n                            node.type === 'MetaProperty';\n                    case 'function':\n                        return node.type === 'FunctionDeclaration' ||\n                            node.type === 'FunctionExpression' ||\n                            node.type === 'ArrowFunctionExpression';\n                }\n                throw new Error(`Unknown class name: ${selector.name}`);\n            };\n        }\n    }\n\n    throw new Error(`Unknown selector type: ${selector.type}`);\n}\n\n/**\n * @callback TraverseOptionFallback\n * @param {external:AST} node The given node.\n * @returns {string[]} An array of visitor keys for the given node.\n */\n\n/**\n * @callback ClassMatcher\n * @param {string} className The name of the class to match.\n * @param {external:AST} node The node to match against.\n * @param {Array<external:AST>} ancestry The ancestry of the node.\n * @returns {boolean} True if the node matches the class, false if not.\n */\n\n/**\n * @typedef {object} ESQueryOptions\n * @property {string} [nodeTypeKey=\"type\"] By passing `nodeTypeKey`, we can allow other ASTs to use ESQuery.\n * @property { { [nodeType: string]: string[] } } [visitorKeys] By passing `visitorKeys` mapping, we can extend the properties of the nodes that traverse the node.\n * @property {TraverseOptionFallback} [fallback] By passing `fallback` option, we can control the properties of traversing nodes when encountering unknown nodes.\n * @property {ClassMatcher} [matchClass] By passing `matchClass` option, we can customize the interpretation of classes.\n */\n\n/**\n * Given a `node` and its ancestors, determine if `node` is matched\n * by `selector`.\n * @param {?external:AST} node\n * @param {?SelectorAST} selector\n * @param {external:AST[]} [ancestry=[]]\n * @param {ESQueryOptions} [options]\n * @throws {Error} Unknowns (operator, class name, selector type, or\n * selector value type)\n * @returns {boolean}\n */\nfunction matches(node, selector, ancestry, options) {\n    if (!selector) { return true; }\n    if (!node) { return false; }\n    if (!ancestry) { ancestry = []; }\n\n    return getMatcher(selector)(node, ancestry, options);\n}\n\n/**\n * Get visitor keys of a given node.\n * @param {external:AST} node The AST node to get keys.\n * @param {ESQueryOptions|undefined} options\n * @returns {string[]} Visitor keys of the node.\n */\nfunction getVisitorKeys(node, options) {\n    const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n\n    const nodeType = node[nodeTypeKey];\n    if (options && options.visitorKeys && options.visitorKeys[nodeType]) {\n        return options.visitorKeys[nodeType];\n    }\n    if (estraverse.VisitorKeys[nodeType]) {\n        return estraverse.VisitorKeys[nodeType];\n    }\n    if (options && typeof options.fallback === 'function') {\n        return options.fallback(node);\n    }\n    // 'iteration' fallback\n    return Object.keys(node).filter(function (key) {\n        return key !== nodeTypeKey;\n    });\n}\n\n\n/**\n * Check whether the given value is an ASTNode or not.\n * @param {any} node The value to check.\n * @param {ESQueryOptions|undefined} options The options to use.\n * @returns {boolean} `true` if the value is an ASTNode.\n */\nfunction isNode(node, options) {\n    const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n    return node !== null && typeof node === 'object' && typeof node[nodeTypeKey] === 'string';\n}\n\n/**\n * Determines if the given node has a sibling that matches the\n * given selector matcher.\n * @param {external:AST} node\n * @param {SelectorMatcher} matcher\n * @param {external:AST[]} ancestry\n * @param {Side} side\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction sibling(node, matcher, ancestry, side, options) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const startIndex = listProp.indexOf(node);\n            if (startIndex < 0) { continue; }\n            let lowerBound, upperBound;\n            if (side === LEFT_SIDE) {\n                lowerBound = 0;\n                upperBound = startIndex;\n            } else {\n                lowerBound = startIndex + 1;\n                upperBound = listProp.length;\n            }\n            for (let k = lowerBound; k < upperBound; ++k) {\n                if (isNode(listProp[k], options) && matcher(listProp[k], ancestry, options)) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * Determines if the given node has an adjacent sibling that matches\n * the given selector matcher.\n * @param {external:AST} node\n * @param {SelectorMatcher} matcher\n * @param {external:AST[]} ancestry\n * @param {Side} side\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction adjacent(node, matcher, ancestry, side, options) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const idx = listProp.indexOf(node);\n            if (idx < 0) { continue; }\n            if (side === LEFT_SIDE && idx > 0 && isNode(listProp[idx - 1], options) && matcher(listProp[idx - 1], ancestry, options)) {\n                return true;\n            }\n            if (side === RIGHT_SIDE && idx < listProp.length - 1 && isNode(listProp[idx + 1], options) &&  matcher(listProp[idx + 1], ancestry, options)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * Determines if the given node is the `nth` child.\n * If `nth` is negative then the position is counted\n * from the end of the list of children.\n * @param {external:AST} node\n * @param {external:AST[]} ancestry\n * @param {Integer} nth\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction nthChild(node, ancestry, nth, options) {\n    if (nth === 0) { return false; }\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)){\n            const idx = nth < 0 ? listProp.length + nth : nth - 1;\n            if (idx >= 0 && idx < listProp.length && listProp[idx] === node) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * For each selector node marked as a subject, find the portion of the\n * selector that the subject must match.\n * @param {SelectorAST} selector\n * @param {SelectorAST} [ancestor] Defaults to `selector`\n * @returns {SelectorAST[]}\n */\nfunction subjects(selector, ancestor) {\n    if (selector == null || typeof selector != 'object') { return []; }\n    if (ancestor == null) { ancestor = selector; }\n    const results = selector.subject ? [ancestor] : [];\n    const keys = Object.keys(selector);\n    for (let i = 0; i < keys.length; ++i) {\n        const p = keys[i];\n        const sel = selector[p];\n        results.push(...subjects(sel, p === 'left' ? sel : ancestor));\n    }\n    return results;\n}\n\n/**\n* @callback TraverseVisitor\n* @param {?external:AST} node\n* @param {?external:AST} parent\n* @param {external:AST[]} ancestry\n*/\n\n/**\n * From a JS AST and a selector AST, collect all JS AST nodes that\n * match the selector.\n * @param {external:AST} ast\n * @param {?SelectorAST} selector\n * @param {TraverseVisitor} visitor\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction traverse(ast, selector, visitor, options) {\n    if (!selector) { return; }\n    const ancestry = [];\n    const matcher = getMatcher(selector);\n    const altSubjects = subjects(selector).map(getMatcher);\n    estraverse.traverse(ast, {\n        enter (node, parent) {\n            if (parent != null) { ancestry.unshift(parent); }\n            if (matcher(node, ancestry, options)) {\n                if (altSubjects.length) {\n                    for (let i = 0, l = altSubjects.length; i < l; ++i) {\n                        if (altSubjects[i](node, ancestry, options)) {\n                            visitor(node, parent, ancestry);\n                        }\n                        for (let k = 0, m = ancestry.length; k < m; ++k) {\n                            const succeedingAncestry = ancestry.slice(k + 1);\n                            if (altSubjects[i](ancestry[k], succeedingAncestry, options)) {\n                                visitor(ancestry[k], parent, succeedingAncestry);\n                            }\n                        }\n                    }\n                } else {\n                    visitor(node, parent, ancestry);\n                }\n            }\n        },\n        leave () { ancestry.shift(); },\n        keys: options && options.visitorKeys,\n        fallback: options && options.fallback || 'iteration'\n    });\n}\n\n\n/**\n * From a JS AST and a selector AST, collect all JS AST nodes that\n * match the selector.\n * @param {external:AST} ast\n * @param {?SelectorAST} selector\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction match(ast, selector, options) {\n    const results = [];\n    traverse(ast, selector, function (node) {\n        results.push(node);\n    }, options);\n    return results;\n}\n\n/**\n * Parse a selector string and return its AST.\n * @param {string} selector\n * @returns {SelectorAST}\n */\nfunction parse(selector) {\n    return parser.parse(selector);\n}\n\n/**\n * Query the code AST using the selector string.\n * @param {external:AST} ast\n * @param {string} selector\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction query(ast, selector, options) {\n    return match(ast, parse(selector), options);\n}\n\nquery.parse = parse;\nquery.match = match;\nquery.traverse = traverse;\nquery.matches = matches;\nquery.query = query;\n\nexport default query;\n"], "names": ["module", "exports", "peg$SyntaxError", "message", "expected", "found", "location", "this", "name", "Error", "captureStackTrace", "child", "parent", "ctor", "constructor", "prototype", "peg$subclass", "buildMessage", "DESCRIBE_EXPECTATION_FNS", "literal", "expectation", "literalEscape", "text", "class", "i", "escapedParts", "parts", "length", "Array", "classEscape", "inverted", "any", "end", "other", "description", "hex", "ch", "charCodeAt", "toString", "toUpperCase", "s", "replace", "j", "descriptions", "type", "sort", "slice", "join", "describeExpected", "describeFound", "SyntaxError", "parse", "input", "options", "peg$result", "peg$FAILED", "peg$startRuleFunctions", "start", "peg$parsestart", "peg$startRuleFunction", "peg$c3", "peg$literalExpectation", "peg$c4", "peg$c5", "peg$classExpectation", "peg$c8", "peg$c11", "peg$c14", "peg$c18", "peg$c19", "ss", "concat", "map", "peg$c23", "peg$c26", "peg$c29", "peg$c32", "peg$c34", "peg$c36", "peg$c37", "peg$c39", "peg$c40", "a", "peg$c41", "peg$c42", "peg$c44", "peg$c46", "op", "value", "operator", "peg$c49", "peg$c50", "peg$c51", "peg$c53", "peg$c54", "peg$c55", "b", "peg$c56", "d", "match", "peg$c58", "peg$c59", "peg$c60", "peg$c61", "peg$c62", "peg$c66", "peg$c67", "peg$c68", "peg$c70", "peg$c72", "peg$c73", "peg$c75", "peg$c76", "peg$c77", "peg$c81", "peg$c84", "peg$c87", "peg$c90", "peg$c93", "peg$c96", "peg$c99", "peg$c102", "peg$currPos", "peg$posDetailsCache", "line", "column", "peg$maxFailPos", "peg$maxFailExpected", "peg$resultsCache", "startRule", "ignoreCase", "peg$computePosDetails", "pos", "p", "details", "peg$computeLocation", "startPos", "endPos", "startPosDetails", "endPosDetails", "offset", "peg$fail", "push", "s0", "s1", "s2", "key", "cached", "nextPos", "result", "peg$parse_", "peg$parseselectors", "selectors", "peg$c1", "peg$parseidentifierName", "test", "char<PERSON>t", "peg$parsebinaryOp", "s3", "s4", "s5", "s6", "s7", "peg$parseselector", "peg$parsehasSelector", "left", "right", "peg$parsesequence", "reduce", "memo", "rhs", "subject", "as", "peg$parseatom", "peg$parsewildcard", "peg$parseidentifier", "peg$parseattrName", "peg$parseattrEqOps", "substr", "peg$parsetype", "flgs", "peg$parseflags", "RegExp", "peg$parseregex", "peg$parseattrOps", "peg$parsestring", "leadingDecimals", "apply", "parseFloat", "peg$parsenumber", "peg$parsepath", "peg$parseattrValue", "peg$parseattr", "peg$parsefield", "peg$parsenegation", "peg$parsematches", "peg$parsehasSelectors", "peg$parsehas", "nth", "peg$parsefirstChild", "nthLast", "peg$parselastChild", "parseInt", "peg$parsenthChild", "peg$parsenthLastChild", "peg$parseclass", "n", "index", "factory", "<PERSON><PERSON><PERSON>", "obj", "keys", "MATCHER_CACHE", "WeakMap", "getMatcher", "selector", "matcher", "get", "generateMatcher", "set", "toLowerCase", "node", "ancestry", "nodeTypeKey", "path", "split", "inPath", "ancestor", "fromPathIndex", "current", "field", "isArray", "k", "matchers", "estraverse", "traverse", "enter", "unshift", "leave", "shift", "visitorKeys", "fallback", "l", "sibling", "adjacent", "nthChild", "matchClass", "getVisitorKeys", "nodeType", "VisitorKeys", "Object", "filter", "isNode", "_typeof", "side", "listProp", "startIndex", "indexOf", "lowerBound", "upperBound", "idx", "ast", "visitor", "altSubjects", "subjects", "results", "sel", "m", "succeedingAncestry", "parser", "query", "matches"], "mappings": "mnEAQ2CA,EAAOC,UAC9CD,UAEK,WASP,SAASE,EAAgBC,EAASC,EAAUC,EAAOC,GACjDC,KAAKJ,QAAWA,EAChBI,KAAKH,SAAWA,EAChBG,KAAKF,MAAWA,EAChBE,KAAKD,SAAWA,EAChBC,KAAKC,KAAW,cAEuB,mBAA5BC,MAAMC,mBACfD,MAAMC,kBAAkBH,KAAML,GAqmFlC,OAnnFA,SAAsBS,EAAOC,GAC3B,SAASC,IAASN,KAAKO,YAAcH,EACrCE,EAAKE,UAAYH,EAAOG,UACxBJ,EAAMI,UAAY,IAAIF,EAexBG,CAAad,EAAiBO,OAE9BP,EAAgBe,aAAe,SAASb,EAAUC,GAChD,IAAIa,EAA2B,CACzBC,QAAS,SAASC,GAChB,MAAO,IAAOC,EAAcD,EAAYE,MAAQ,KAGlDC,MAAS,SAASH,GAChB,IACII,EADAC,EAAe,GAGnB,IAAKD,EAAI,EAAGA,EAAIJ,EAAYM,MAAMC,OAAQH,IACxCC,GAAgBL,EAAYM,MAAMF,aAAcI,MAC5CC,EAAYT,EAAYM,MAAMF,GAAG,IAAM,IAAMK,EAAYT,EAAYM,MAAMF,GAAG,IAC9EK,EAAYT,EAAYM,MAAMF,IAGpC,MAAO,KAAOJ,EAAYU,SAAW,IAAM,IAAML,EAAe,KAGlEM,IAAK,SAASX,GACZ,MAAO,iBAGTY,IAAK,SAASZ,GACZ,MAAO,gBAGTa,MAAO,SAASb,GACd,OAAOA,EAAYc,cAI3B,SAASC,EAAIC,GACX,OAAOA,EAAGC,WAAW,GAAGC,SAAS,IAAIC,cAGvC,SAASlB,EAAcmB,GACrB,OAAOA,EACJC,QAAQ,MAAO,QACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,gBAAyB,SAASL,GAAM,MAAO,OAASD,EAAIC,MACpEK,QAAQ,yBAAyB,SAASL,GAAM,MAAO,MAASD,EAAIC,MAGzE,SAASP,EAAYW,GACnB,OAAOA,EACJC,QAAQ,MAAO,QACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,gBAAyB,SAASL,GAAM,MAAO,OAASD,EAAIC,MACpEK,QAAQ,yBAAyB,SAASL,GAAM,MAAO,MAASD,EAAIC,MA6CzE,MAAO,YAtCP,SAA0BhC,GACxB,IACIoB,EAAGkB,EANoBtB,EAKvBuB,EAAe,IAAIf,MAAMxB,EAASuB,QAGtC,IAAKH,EAAI,EAAGA,EAAIpB,EAASuB,OAAQH,IAC/BmB,EAAanB,IATYJ,EASahB,EAASoB,GAR1CN,EAAyBE,EAAYwB,MAAMxB,IAalD,GAFAuB,EAAaE,OAETF,EAAahB,OAAS,EAAG,CAC3B,IAAKH,EAAI,EAAGkB,EAAI,EAAGlB,EAAImB,EAAahB,OAAQH,IACtCmB,EAAanB,EAAI,KAAOmB,EAAanB,KACvCmB,EAAaD,GAAKC,EAAanB,GAC/BkB,KAGJC,EAAahB,OAASe,EAGxB,OAAQC,EAAahB,QACnB,KAAK,EACH,OAAOgB,EAAa,GAEtB,KAAK,EACH,OAAOA,EAAa,GAAK,OAASA,EAAa,GAEjD,QACE,OAAOA,EAAaG,MAAM,GAAI,GAAGC,KAAK,MAClC,QACAJ,EAAaA,EAAahB,OAAS,IAQxBqB,CAAiB5C,GAAY,QAJlD,SAAuBC,GACrB,OAAOA,EAAQ,IAAOgB,EAAchB,GAAS,IAAO,eAGM4C,CAAc5C,GAAS,WAu/E9E,CACL6C,YAAahD,EACbiD,MAt/EF,SAAmBC,EAAOC,GACxBA,OAAsB,IAAZA,EAAqBA,EAAU,OAwJrCC,EAwH8BlD,EAAUC,EAAOC,EA9Q/CiD,EAAa,GAEbC,EAAyB,CAAEC,MAAOC,IAClCC,EAAyBD,GAOzBE,EAASC,GAAuB,KAAK,GACrCC,EAAS,uBACTC,EAASC,GAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAAM,GAAM,GAGjHC,EAASJ,GAAuB,KAAK,GAGrCK,EAAUL,GAAuB,KAAK,GAGtCM,EAAUN,GAAuB,KAAK,GAItCO,EAAUP,GAAuB,KAAK,GACtCQ,EAAU,SAAS7B,EAAG8B,GACpB,MAAO,CAAC9B,GAAG+B,OAAOD,EAAGE,KAAI,SAAUhC,GAAK,OAAOA,EAAE,QAYnDiC,EAAUZ,GAAuB,KAAK,GAOtCa,EAAUb,GAAuB,KAAK,GAGtCc,EAAUd,GAAuB,KAAK,GAGtCe,EAAUf,GAAuB,KAAK,GAEtCgB,EAAUhB,GAAuB,KAAK,GAEtCiB,EAAU,SACVC,EAAUf,GAAqB,CAAC,IAAK,IAAK,MAAM,GAAO,GAEvDgB,EAAUnB,GAAuB,KAAK,GACtCoB,EAAU,SAASC,GAAK,OAAQA,GAAK,IAAM,KAC3CC,EAAU,QACVC,EAAUpB,GAAqB,CAAC,IAAK,MAAM,GAAO,GAElDqB,EAAUxB,GAAuB,KAAK,GAItCyB,EAAU,SAAS9E,EAAM+E,EAAIC,GACvB,MAAO,CAAE5C,KAAM,YAAapC,KAAMA,EAAMiF,SAAUF,EAAIC,MAAOA,IAInEE,EAAU7B,GAAuB,KAAM,GACvC8B,EAAU,UACVC,EAAU5B,GAAqB,CAAC,KAAM,MAAO,GAAM,GAEnD6B,EAAUhC,GAAuB,MAAM,GACvCiC,EAmHK,CAAElD,KAAM,OAlHbmD,EAAU,SAASb,EAAGc,GAAK,OAAOd,EAAIc,GACtCC,EAAU,SAASC,GACX,MAAO,CAAEtD,KAAM,UAAW4C,OA83EfhD,EA93EkC0D,EAAEnD,KAAK,IA+3ErDP,EAAEC,QAAQ,UAAU,SAAS0D,EAAO/D,GACzC,OAAOA,GACL,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,QAAS,OAAOA,QATtB,IAAqBI,GA33EnB4D,EAAUvC,GAAuB,KAAK,GACtCwC,EAAU,UACVC,EAAUtC,GAAqB,CAAC,KAAM,MAAM,GAAM,GAClDuC,EAAU,SACVC,EAAUxC,GAAqB,CAAC,CAAC,IAAK,OAAO,GAAO,GAQpDyC,EAAU5C,GAAuB,SAAS,GAC1C6C,EAAU,SACVC,EAAU3C,GAAqB,CAAC,IAAK,MAAM,GAAM,GAEjD4C,EAAU/C,GAAuB,KAAK,GAEtCgD,EAAU,UACVC,EAAU9C,GAAqB,CAAC,IAAK,IAAK,IAAK,MAAM,GAAO,GAE5D+C,EAAUlD,GAAuB,KAAK,GACtCmD,EAAU,SACVC,EAAUjD,GAAqB,CAAC,MAAM,GAAM,GAQ5CkD,EAAUrD,GAAuB,SAAS,GAG1CsD,EAAUtD,GAAuB,aAAa,GAG9CuD,GAAUvD,GAAuB,SAAS,GAG1CwD,GAAUxD,GAAuB,gBAAgB,GAGjDyD,GAAUzD,GAAuB,eAAe,GAGhD0D,GAAU1D,GAAuB,eAAe,GAGhD2D,GAAU3D,GAAuB,oBAAoB,GAGrD4D,GAAW5D,GAAuB,KAAK,GAKvC6D,GAAuB,EAEvBC,GAAuB,CAAC,CAAEC,KAAM,EAAGC,OAAQ,IAC3CC,GAAuB,EACvBC,GAAuB,GAGvBC,GAAmB,GAIvB,GAAI,cAAe3E,EAAS,CAC1B,KAAMA,EAAQ4E,aAAazE,GACzB,MAAM,IAAI/C,MAAM,mCAAqC4C,EAAQ4E,UAAY,MAG3EtE,EAAwBH,EAAuBH,EAAQ4E,WA2BzD,SAASpE,GAAuBvC,EAAM4G,GACpC,MAAO,CAAEtF,KAAM,UAAWtB,KAAMA,EAAM4G,WAAYA,GAGpD,SAASlE,GAAqBtC,EAAOI,EAAUoG,GAC7C,MAAO,CAAEtF,KAAM,QAASlB,MAAOA,EAAOI,SAAUA,EAAUoG,WAAYA,GAexE,SAASC,GAAsBC,GAC7B,IAAwCC,EAApCC,EAAUX,GAAoBS,GAElC,GAAIE,EACF,OAAOA,EAGP,IADAD,EAAID,EAAM,GACFT,GAAoBU,IAC1BA,IASF,IALAC,EAAU,CACRV,MAFFU,EAAUX,GAAoBU,IAEZT,KAChBC,OAAQS,EAAQT,QAGXQ,EAAID,GACmB,KAAxBhF,EAAMf,WAAWgG,IACnBC,EAAQV,OACRU,EAAQT,OAAS,GAEjBS,EAAQT,SAGVQ,IAIF,OADAV,GAAoBS,GAAOE,EACpBA,EAIX,SAASC,GAAoBC,EAAUC,GACrC,IAAIC,EAAkBP,GAAsBK,GACxCG,EAAkBR,GAAsBM,GAE5C,MAAO,CACLhF,MAAO,CACLmF,OAAQJ,EACRZ,KAAQc,EAAgBd,KACxBC,OAAQa,EAAgBb,QAE1B7F,IAAK,CACH4G,OAAQH,EACRb,KAAQe,EAAcf,KACtBC,OAAQc,EAAcd,SAK5B,SAASgB,GAASzI,GACZsH,GAAcI,KAEdJ,GAAcI,KAChBA,GAAiBJ,GACjBK,GAAsB,IAGxBA,GAAoBe,KAAK1I,IAgB3B,SAASsD,KACP,IAAIqF,EAAIC,EAAIC,EAnRQ3E,EAqRhB4E,EAAuB,GAAdxB,GAAmB,EAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,IACLsB,EAAKM,QACM/F,IACT0F,EAAKM,QACMhG,GACJ+F,OACM/F,EAGTwF,EADAC,EArSqB,KADP1E,EAsSF2E,GArSFtH,OAAe2C,EAAG,GAAK,CAAE1B,KAAM,UAAW4G,UAAWlF,IAgTnEoD,GAAcqB,EACdA,EAAKxF,GAEHwF,IAAOxF,IACTwF,EAAKrB,IACLsB,EAAKM,QACM/F,IAETyF,OAAKS,GAEPV,EAAKC,GAGPhB,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GAGT,SAASO,KACP,IAAIP,EAAIC,EAEJE,EAAuB,GAAdxB,GAAmB,EAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAWhB,IARAN,EAAK,GACiC,KAAlC3F,EAAMf,WAAWqF,KACnBsB,EA7US,IA8UTtB,OAEAsB,EAAKzF,EACwBsF,GAASjF,IAEjCoF,IAAOzF,GACZwF,EAAGD,KAAKE,GAC8B,KAAlC5F,EAAMf,WAAWqF,KACnBsB,EAtVO,IAuVPtB,OAEAsB,EAAKzF,EACwBsF,GAASjF,IAM1C,OAFAoE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAGT,SAASW,KACP,IAAIX,EAAIC,EAAIC,EAERC,EAAuB,GAAdxB,GAAmB,EAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAYhB,GARAL,EAAK,GACDlF,EAAO6F,KAAKvG,EAAMwG,OAAOlC,MAC3BuB,EAAK7F,EAAMwG,OAAOlC,IAClBA,OAEAuB,EAAK1F,EACwBsF,GAAS9E,IAEpCkF,IAAO1F,EACT,KAAO0F,IAAO1F,GACZyF,EAAGF,KAAKG,GACJnF,EAAO6F,KAAKvG,EAAMwG,OAAOlC,MAC3BuB,EAAK7F,EAAMwG,OAAOlC,IAClBA,OAEAuB,EAAK1F,EACwBsF,GAAS9E,SAI1CiF,EAAKzF,EAUP,OARIyF,IAAOzF,IAETyF,EAAYA,EApYoBjG,KAAK,KAsYvCgG,EAAKC,EAELhB,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAGT,SAASc,KACP,IAAId,EAAIC,EAAIC,EAERC,EAAuB,GAAdxB,GAAmB,EAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,IACLsB,EAAKM,QACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBuB,EA5ZO,IA6ZPvB,OAEAuB,EAAK1F,EACwBsF,GAAS5E,IAEpCgF,IAAO1F,GACJ+F,OACM/F,EAGTwF,EADAC,EApayB,SA2a3BtB,GAAcqB,EACdA,EAAKxF,KAGPmE,GAAcqB,EACdA,EAAKxF,GAEHwF,IAAOxF,IACTwF,EAAKrB,IACLsB,EAAKM,QACM/F,GAC6B,MAAlCH,EAAMf,WAAWqF,KACnBuB,EAtbM,IAubNvB,OAEAuB,EAAK1F,EACwBsF,GAAS3E,IAEpC+E,IAAO1F,GACJ+F,OACM/F,EAGTwF,EADAC,EA9bwB,WAqc1BtB,GAAcqB,EACdA,EAAKxF,KAGPmE,GAAcqB,EACdA,EAAKxF,GAEHwF,IAAOxF,IACTwF,EAAKrB,IACLsB,EAAKM,QACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBuB,EAhdI,IAidJvB,OAEAuB,EAAK1F,EACwBsF,GAAS1E,IAEpC8E,IAAO1F,GACJ+F,OACM/F,EAGTwF,EADAC,EAxdsB,YA+dxBtB,GAAcqB,EACdA,EAAKxF,KAGPmE,GAAcqB,EACdA,EAAKxF,GAEHwF,IAAOxF,IACTwF,EAAKrB,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsB,EAtfG,IAufHtB,OAEAsB,EAAKzF,EACwBsF,GAASjF,IAEpCoF,IAAOzF,IACT0F,EAAKK,QACM/F,EAGTwF,EADAC,EAlfsB,cAyfxBtB,GAAcqB,EACdA,EAAKxF,MAMbyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GA0GT,SAASQ,KACP,IAAIR,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAAIC,EAAIC,EAE5BhB,EAAuB,GAAdxB,GAAmB,EAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAKhB,GAFAN,EAAKrB,IACLsB,EAAKmB,QACM5G,EAAY,CAmCrB,IAlCA0F,EAAK,GACLa,EAAKpC,IACLqC,EAAKT,QACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EA/nBM,IAgoBNtC,OAEAsC,EAAKzG,EACwBsF,GAASzE,IAEpC4F,IAAOzG,IACT0G,EAAKX,QACM/F,IACT2G,EAAKC,QACM5G,EAETuG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBxC,GAAcoC,EACdA,EAAKvG,KAGPmE,GAAcoC,EACdA,EAAKvG,GAEAuG,IAAOvG,GACZ0F,EAAGH,KAAKgB,GACRA,EAAKpC,IACLqC,EAAKT,QACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EAlqBI,IAmqBJtC,OAEAsC,EAAKzG,EACwBsF,GAASzE,IAEpC4F,IAAOzG,IACT0G,EAAKX,QACM/F,IACT2G,EAAKC,QACM5G,EAETuG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBxC,GAAcoC,EACdA,EAAKvG,KAGPmE,GAAcoC,EACdA,EAAKvG,GAGL0F,IAAO1F,EAGTwF,EADAC,EAAK3E,EAAQ2E,EAAIC,IAGjBvB,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAGT,SAASqB,KACP,IAAIrB,EAAIC,EAAIC,EA9sBS1D,EAAI/C,EAgtBrB0G,EAAuB,GAAdxB,GAAmB,EAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,IACLsB,EAAKa,QACMtG,IACTyF,EAAK,MAEHA,IAAOzF,IACT0F,EAAKkB,QACM5G,GAhuBYf,EAkuBJyG,EACjBF,EADAC,GAluBiBzD,EAkuBJyD,GAhuBJ,CAAEpG,KAAM2C,EAAI8E,KAAM,CAAEzH,KAAM,aAAe0H,MAAO9H,GADvCA,IAwuBpBkF,GAAcqB,EACdA,EAAKxF,GAGPyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GAGT,SAASoB,KACP,IAAIpB,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EA/uBH9E,EAivBjBgE,EAAuB,GAAdxB,GAAmB,EAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAKhB,GAFAN,EAAKrB,IACLsB,EAAKuB,QACMhH,EAAY,CAiBrB,IAhBA0F,EAAK,GACLa,EAAKpC,IACLqC,EAAKF,QACMtG,IACTyG,EAAKO,QACMhH,EAETuG,EADAC,EAAK,CAACA,EAAIC,IAOZtC,GAAcoC,EACdA,EAAKvG,GAEAuG,IAAOvG,GACZ0F,EAAGH,KAAKgB,GACRA,EAAKpC,IACLqC,EAAKF,QACMtG,IACTyG,EAAKO,QACMhH,EAETuG,EADAC,EAAK,CAACA,EAAIC,IAOZtC,GAAcoC,EACdA,EAAKvG,GAGL0F,IAAO1F,GA/xBQ2B,EAiyBJ8D,EACbD,EADAC,EAAiBC,EAhyBJuB,QAAO,SAAUC,EAAMC,GAChC,MAAO,CAAE9H,KAAM8H,EAAI,GAAIL,KAAMI,EAAMH,MAAOI,EAAI,MAC7CxF,KAiyBLwC,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAGT,SAASwB,KACP,IAAIxB,EAAIC,EAAIC,EAAIa,EA3yBKa,EAASC,EAClB5E,EA4yBRkD,EAAuB,GAAdxB,GAAmB,EAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAchB,GAXAN,EAAKrB,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsB,EA1zBU,IA2zBVtB,OAEAsB,EAAKzF,EACwBsF,GAASpE,IAEpCuE,IAAOzF,IACTyF,EAAK,MAEHA,IAAOzF,EAAY,CAGrB,GAFA0F,EAAK,IACLa,EAAKe,QACMtH,EACT,KAAOuG,IAAOvG,GACZ0F,EAAGH,KAAKgB,GACRA,EAAKe,UAGP5B,EAAK1F,EAEH0F,IAAO1F,GA50BQoH,EA80BJ3B,EA70BLhD,EAAkB,KADA4E,EA80BT3B,GA70BFtH,OAAeiJ,EAAG,GAAK,CAAEhI,KAAM,WAAY4G,UAAWoB,GAChED,IAAS3E,EAAE2E,SAAU,GA60B1B5B,EADAC,EA30BShD,IA80BT0B,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAGT,SAAS8B,KACP,IAAI9B,EAEAG,EAAuB,GAAdxB,GAAmB,EAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,UAGhBN,EAwCF,WACE,IAAIA,EAAIC,EAEJE,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAIsB,KAAlCjG,EAAMf,WAAWqF,KACnBsB,EA35BU,IA45BVtB,OAEAsB,EAAKzF,EACwBsF,GAASnE,IAEpCsE,IAAOzF,IAETyF,EAj6B+B,CAAEpG,KAAM,WAAY4C,MAi6BtCwD,IAEfD,EAAKC,EAELhB,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GApEF+B,MACMvH,IACTwF,EAqEJ,WACE,IAAIA,EAAIC,EAAIC,EAERC,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsB,EAv7BU,IAw7BVtB,OAEAsB,EAAKzF,EACwBsF,GAASlE,IAEpCqE,IAAOzF,IACTyF,EAAK,MAEHA,IAAOzF,IACT0F,EAAKS,QACMnG,EAGTwF,EADAC,EAl8B6B,CAAEpG,KAAM,aAAc4C,MAk8BtCyD,IAOfvB,GAAcqB,EACdA,EAAKxF,GAGPyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GA7GAgC,MACMxH,IACTwF,EA8GN,WACE,IAAIA,EAAIC,EAAQc,EAAQE,EAEpBd,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsB,EA/9BU,IAg+BVtB,OAEAsB,EAAKzF,EACwBsF,GAASjE,IAEpCoE,IAAOzF,GACJ+F,OACM/F,IACTuG,EAmON,WACE,IAAIf,EAAIC,EAAQc,EAAQE,EAEpBd,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,IACLsB,EAAKgC,QACMzH,GACJ+F,OACM/F,IACTuG,EAjJN,WACE,IAAIf,EAAIC,EAAIC,EAERC,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsB,EAtmCU,IAumCVtB,OAEAsB,EAAKzF,EACwBsF,GAASpE,IAEpCuE,IAAOzF,IACTyF,EAAK,MAEHA,IAAOzF,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBuB,EA7lCQ,IA8lCRvB,OAEAuB,EAAK1F,EACwBsF,GAAS7D,IAEpCiE,IAAO1F,GAETyF,EAAK/D,EAAQ+D,GACbD,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,KAGPmE,GAAcqB,EACdA,EAAKxF,GAGPyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GAmGEkC,MACM1H,GACJ+F,OACM/F,IACTyG,EA+bV,WACE,IAAIjB,EAAIC,EAAQc,EAAIC,EAAIC,EAEpBd,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAWhB,GARAN,EAAKrB,GA/nDO,UAgoDRtE,EAAM8H,OAAOxD,GAAa,IAC5BsB,EAjoDU,QAkoDVtB,IAAe,IAEfsB,EAAKzF,EACwBsF,GAASpC,IAEpCuC,IAAOzF,EAET,GADK+F,OACM/F,EAAY,CASrB,GARAuG,EAAK,GACDpD,EAAQiD,KAAKvG,EAAMwG,OAAOlC,MAC5BqC,EAAK3G,EAAMwG,OAAOlC,IAClBA,OAEAqC,EAAKxG,EACwBsF,GAASlC,IAEpCoD,IAAOxG,EACT,KAAOwG,IAAOxG,GACZuG,EAAGhB,KAAKiB,GACJrD,EAAQiD,KAAKvG,EAAMwG,OAAOlC,MAC5BqC,EAAK3G,EAAMwG,OAAOlC,IAClBA,OAEAqC,EAAKxG,EACwBsF,GAASlC,SAI1CmD,EAAKvG,EAEHuG,IAAOvG,IACTwG,EAAKT,QACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EAhqDE,IAiqDFtC,OAEAsC,EAAKzG,EACwBsF,GAASjC,IAEpCoD,IAAOzG,GAETyF,EAtqDuB,CAAEpG,KAAM,OAAQ4C,MAsqD1BsE,EAtqDmC/G,KAAK,KAuqDrDgG,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,KAOTmE,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,OAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAjhBMoC,MACM5H,IACTyG,EA0jBZ,WACE,IAAIjB,EAAIC,EAAIC,EAAIa,EAAIC,EApuDIqB,EAsuDpBlC,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAWhB,GARAN,EAAKrB,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsB,EArvDU,IAsvDVtB,OAEAsB,EAAKzF,EACwBsF,GAAS9B,IAEpCiC,IAAOzF,EAAY,CASrB,GARA0F,EAAK,GACDjC,EAAQ2C,KAAKvG,EAAMwG,OAAOlC,MAC5BoC,EAAK1G,EAAMwG,OAAOlC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAAS5B,IAEpC6C,IAAOvG,EACT,KAAOuG,IAAOvG,GACZ0F,EAAGH,KAAKgB,GACJ9C,EAAQ2C,KAAKvG,EAAMwG,OAAOlC,MAC5BoC,EAAK1G,EAAMwG,OAAOlC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAAS5B,SAI1CgC,EAAK1F,EAEH0F,IAAO1F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBoC,EApxDM,IAqxDNpC,OAEAoC,EAAKvG,EACwBsF,GAAS9B,IAEpC+C,IAAOvG,IACTwG,EA5FR,WACE,IAAIhB,EAAIC,EAEJE,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAWhB,GARAN,EAAK,GACDlC,EAAQ8C,KAAKvG,EAAMwG,OAAOlC,MAC5BsB,EAAK5F,EAAMwG,OAAOlC,IAClBA,OAEAsB,EAAKzF,EACwBsF,GAAS/B,IAEpCkC,IAAOzF,EACT,KAAOyF,IAAOzF,GACZwF,EAAGD,KAAKE,GACJnC,EAAQ8C,KAAKvG,EAAMwG,OAAOlC,MAC5BsB,EAAK5F,EAAMwG,OAAOlC,IAClBA,OAEAsB,EAAKzF,EACwBsF,GAAS/B,SAI1CiC,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAuDIsC,MACM9H,IACTwG,EAAK,MAEHA,IAAOxG,GA3xDO6H,EA6xDCrB,EAAjBf,EA7xD+B,CAC/BpG,KAAM,SAAU4C,MAAO,IAAI8F,OA4xDdrC,EA5xDuBlG,KAAK,IAAKqI,EAAOA,EAAKrI,KAAK,IAAM,KA6xDrEgG,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,KAGPmE,GAAcqB,EACdA,EAAKxF,KAGPmE,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAzoBQwC,IAEHvB,IAAOzG,GAETyF,EAAK1D,EAAQ0D,EAAIc,EAAIE,GACrBjB,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,KAebmE,GAAcqB,EACdA,EAAKxF,GAEHwF,IAAOxF,IACTwF,EAAKrB,IACLsB,EAAKgC,QACMzH,GACJ+F,OACM/F,IACTuG,EAjPR,WACE,IAAIf,EAAIC,EAAIC,EAERC,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,GACD5C,EAAQ6E,KAAKvG,EAAMwG,OAAOlC,MAC5BsB,EAAK5F,EAAMwG,OAAOlC,IAClBA,OAEAsB,EAAKzF,EACwBsF,GAAS9D,IAEpCiE,IAAOzF,IACTyF,EAAK,MAEHA,IAAOzF,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBuB,EAniCQ,IAoiCRvB,OAEAuB,EAAK1F,EACwBsF,GAAS7D,IAEpCiE,IAAO1F,GAETyF,EAAK/D,EAAQ+D,GACbD,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,KAGPmE,GAAcqB,EACdA,EAAKxF,GAEHwF,IAAOxF,IACL4B,EAAQwE,KAAKvG,EAAMwG,OAAOlC,MAC5BqB,EAAK3F,EAAMwG,OAAOlC,IAClBA,OAEAqB,EAAKxF,EACwBsF,GAASzD,KAI1C4C,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GA0LIyC,MACMjI,GACJ+F,OACM/F,IACTyG,EA+CZ,WACE,IAAIjB,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAEpBd,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAWhB,GARAN,EAAKrB,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsB,EA9yCU,IA+yCVtB,OAEAsB,EAAKzF,EACwBsF,GAASnD,IAEpCsD,IAAOzF,EAAY,CAuCrB,IAtCA0F,EAAK,GACDtD,EAAQgE,KAAKvG,EAAMwG,OAAOlC,MAC5BoC,EAAK1G,EAAMwG,OAAOlC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAASjD,IAEpCkE,IAAOvG,IACTuG,EAAKpC,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBqC,EA5zCM,KA6zCNrC,OAEAqC,EAAKxG,EACwBsF,GAAShD,IAEpCkE,IAAOxG,GACLH,EAAMzB,OAAS+F,IACjBsC,EAAK5G,EAAMwG,OAAOlC,IAClBA,OAEAsC,EAAKzG,EACwBsF,GAAS/C,IAEpCkE,IAAOzG,GAETwG,EAAKhE,EAAQgE,EAAIC,GACjBF,EAAKC,IAELrC,GAAcoC,EACdA,EAAKvG,KAGPmE,GAAcoC,EACdA,EAAKvG,IAGFuG,IAAOvG,GACZ0F,EAAGH,KAAKgB,GACJnE,EAAQgE,KAAKvG,EAAMwG,OAAOlC,MAC5BoC,EAAK1G,EAAMwG,OAAOlC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAASjD,IAEpCkE,IAAOvG,IACTuG,EAAKpC,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBqC,EAn2CI,KAo2CJrC,OAEAqC,EAAKxG,EACwBsF,GAAShD,IAEpCkE,IAAOxG,GACLH,EAAMzB,OAAS+F,IACjBsC,EAAK5G,EAAMwG,OAAOlC,IAClBA,OAEAsC,EAAKzG,EACwBsF,GAAS/C,IAEpCkE,IAAOzG,GAETwG,EAAKhE,EAAQgE,EAAIC,GACjBF,EAAKC,IAELrC,GAAcoC,EACdA,EAAKvG,KAGPmE,GAAcoC,EACdA,EAAKvG,IAIP0F,IAAO1F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBoC,EAr4CM,IAs4CNpC,OAEAoC,EAAKvG,EACwBsF,GAASnD,IAEpCoE,IAAOvG,GAETyF,EAAK/C,EAAQgD,GACbF,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,KAGPmE,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,EAEP,GAAIwF,IAAOxF,EAST,GARAwF,EAAKrB,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsB,EAn5CQ,IAo5CRtB,OAEAsB,EAAKzF,EACwBsF,GAASzC,IAEpC4C,IAAOzF,EAAY,CAuCrB,IAtCA0F,EAAK,GACD5C,EAAQsD,KAAKvG,EAAMwG,OAAOlC,MAC5BoC,EAAK1G,EAAMwG,OAAOlC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAASvC,IAEpCwD,IAAOvG,IACTuG,EAAKpC,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBqC,EA56CI,KA66CJrC,OAEAqC,EAAKxG,EACwBsF,GAAShD,IAEpCkE,IAAOxG,GACLH,EAAMzB,OAAS+F,IACjBsC,EAAK5G,EAAMwG,OAAOlC,IAClBA,OAEAsC,EAAKzG,EACwBsF,GAAS/C,IAEpCkE,IAAOzG,GAETwG,EAAKhE,EAAQgE,EAAIC,GACjBF,EAAKC,IAELrC,GAAcoC,EACdA,EAAKvG,KAGPmE,GAAcoC,EACdA,EAAKvG,IAGFuG,IAAOvG,GACZ0F,EAAGH,KAAKgB,GACJzD,EAAQsD,KAAKvG,EAAMwG,OAAOlC,MAC5BoC,EAAK1G,EAAMwG,OAAOlC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAASvC,IAEpCwD,IAAOvG,IACTuG,EAAKpC,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBqC,EAn9CE,KAo9CFrC,OAEAqC,EAAKxG,EACwBsF,GAAShD,IAEpCkE,IAAOxG,GACLH,EAAMzB,OAAS+F,IACjBsC,EAAK5G,EAAMwG,OAAOlC,IAClBA,OAEAsC,EAAKzG,EACwBsF,GAAS/C,IAEpCkE,IAAOzG,GAETwG,EAAKhE,EAAQgE,EAAIC,GACjBF,EAAKC,IAELrC,GAAcoC,EACdA,EAAKvG,KAGPmE,GAAcoC,EACdA,EAAKvG,IAIP0F,IAAO1F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBoC,EA1+CI,IA2+CJpC,OAEAoC,EAAKvG,EACwBsF,GAASzC,IAEpC0D,IAAOvG,GAETyF,EAAK/C,EAAQgD,GACbF,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,KAGPmE,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,EAMT,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EA9RQ0C,MACMlI,IACTyG,EA+Rd,WACE,IAAIjB,EAAIC,EAAIC,EAAIa,EAlgDK5E,EAAGc,EAER0F,EAkgDZxC,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAahB,IAVAN,EAAKrB,GACLsB,EAAKtB,GACLuB,EAAK,GACD1C,EAAQoD,KAAKvG,EAAMwG,OAAOlC,MAC5BoC,EAAK1G,EAAMwG,OAAOlC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAASrC,IAEjCsD,IAAOvG,GACZ0F,EAAGH,KAAKgB,GACJvD,EAAQoD,KAAKvG,EAAMwG,OAAOlC,MAC5BoC,EAAK1G,EAAMwG,OAAOlC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAASrC,IAyB1C,GAtBIyC,IAAO1F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBoC,EA7jDQ,IA8jDRpC,OAEAoC,EAAKvG,EACwBsF,GAASxD,IAEpCyE,IAAOvG,EAETyF,EADAC,EAAK,CAACA,EAAIa,IAGVpC,GAAcsB,EACdA,EAAKzF,KAGPmE,GAAcsB,EACdA,EAAKzF,GAEHyF,IAAOzF,IACTyF,EAAK,MAEHA,IAAOzF,EAAY,CASrB,GARA0F,EAAK,GACD1C,EAAQoD,KAAKvG,EAAMwG,OAAOlC,MAC5BoC,EAAK1G,EAAMwG,OAAOlC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAASrC,IAEpCsD,IAAOvG,EACT,KAAOuG,IAAOvG,GACZ0F,EAAGH,KAAKgB,GACJvD,EAAQoD,KAAKvG,EAAMwG,OAAOlC,MAC5BoC,EAAK1G,EAAMwG,OAAOlC,IAClBA,OAEAoC,EAAKvG,EACwBsF,GAASrC,SAI1CyC,EAAK1F,EAEH0F,IAAO1F,GA9kDWyC,EAglDHiD,EA9kDLyC,GAFKxG,EAglDJ8D,GA9kDqB,GAAGzE,OAAOoH,MAAM,GAAIzG,GAAGnC,KAAK,IAAM,GA8kDpEiG,EA7kDa,CAAEpG,KAAM,UAAW4C,MAAOoG,WAAWF,EAAkB1F,EAAEjD,KAAK,MA8kD3EgG,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EA3XU8C,MACMtI,IACTyG,EA4XhB,WACE,IAAIjB,EAAIC,EAEJE,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,UAIhBL,EAAKU,QACMnG,IAETyF,EA3mD+B,CAAEpG,KAAM,UAAW4C,MA2mDrCwD,IAEfD,EAAKC,EAELhB,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GAlZY+C,IAGL9B,IAAOzG,GAETyF,EAAK1D,EAAQ0D,EAAIc,EAAIE,GACrBjB,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,KAebmE,GAAcqB,EACdA,EAAKxF,GAEHwF,IAAOxF,IACTwF,EAAKrB,IACLsB,EAAKgC,QACMzH,IAETyF,EAtxC8B,CAAEpG,KAAM,YAAapC,KAsxCtCwI,IAEfD,EAAKC,IAIThB,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GA1UEgD,MACMxI,GACJ+F,OACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EA3+BE,IA4+BFtC,OAEAsC,EAAKzG,EACwBsF,GAAShE,IAEpCmF,IAAOzG,EAGTwF,EADAC,EAAac,GAGbpC,GAAcqB,EACdA,EAAKxF,KAebmE,GAAcqB,EACdA,EAAKxF,GAGPyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GA3KEiD,MACMzI,IACTwF,EAygCR,WACE,IAAIA,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAAIC,EAnzDPzI,EAqzDjB0H,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAWhB,GARAN,EAAKrB,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsB,EAh3DU,IAi3DVtB,OAEAsB,EAAKzF,EACwBsF,GAASxD,IAEpC2D,IAAOzF,EAET,IADA0F,EAAKS,QACMnG,EAAY,CAuBrB,IAtBAuG,EAAK,GACLC,EAAKrC,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsC,EA53DM,IA63DNtC,OAEAsC,EAAKzG,EACwBsF,GAASxD,IAEpC2E,IAAOzG,IACT0G,EAAKP,QACMnG,EAETwG,EADAC,EAAK,CAACA,EAAIC,IAOZvC,GAAcqC,EACdA,EAAKxG,GAEAwG,IAAOxG,GACZuG,EAAGhB,KAAKiB,GACRA,EAAKrC,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsC,EAn5DI,IAo5DJtC,OAEAsC,EAAKzG,EACwBsF,GAASxD,IAEpC2E,IAAOzG,IACT0G,EAAKP,QACMnG,EAETwG,EADAC,EAAK,CAACA,EAAIC,IAOZvC,GAAcqC,EACdA,EAAKxG,GAGLuG,IAAOvG,GAv3DM/B,EAy3DFyH,EAAbD,EAx3DK,CAAEpG,KAAM,QAASpC,KAw3DLsJ,EAx3DcU,QAAO,SAASC,EAAMpC,GAAI,OAAOoC,EAAOpC,EAAE,GAAKA,EAAE,KAAO7G,IAy3DvFuH,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,OAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAjmCIkD,MACM1I,IACTwF,EAkmCV,WACE,IAAIA,EAAIC,EAAQc,EAAQE,EAEpBd,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,GAt5DO,UAu5DRtE,EAAM8H,OAAOxD,GAAa,IAC5BsB,EAx5DU,QAy5DVtB,IAAe,IAEfsB,EAAKzF,EACwBsF,GAAS3B,IAEpC8B,IAAOzF,GACJ+F,OACM/F,IACTuG,EAAKP,QACMhG,GACJ+F,OACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EAr7DE,IAs7DFtC,OAEAsC,EAAKzG,EACwBsF,GAASjC,IAEpCoD,IAAOzG,EAGTwF,EADAC,EA56DwB,CAAEpG,KAAM,MAAO4G,UA46D1BM,IAGbpC,GAAcqB,EACdA,EAAKxF,KAebmE,GAAcqB,EACdA,EAAKxF,GAGPyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GA/pCMmD,MACM3I,IACTwF,EAgqCZ,WACE,IAAIA,EAAIC,EAAQc,EAAQE,EAEpBd,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,GAn9DO,cAo9DRtE,EAAM8H,OAAOxD,GAAa,IAC5BsB,EAr9DU,YAs9DVtB,IAAe,IAEfsB,EAAKzF,EACwBsF,GAAS1B,IAEpC6B,IAAOzF,GACJ+F,OACM/F,IACTuG,EAAKP,QACMhG,GACJ+F,OACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EAr/DE,IAs/DFtC,OAEAsC,EAAKzG,EACwBsF,GAASjC,IAEpCoD,IAAOzG,EAGTwF,EADAC,EAz+DwB,CAAEpG,KAAM,UAAW4G,UAy+D9BM,IAGbpC,GAAcqB,EACdA,EAAKxF,KAebmE,GAAcqB,EACdA,EAAKxF,GAGPyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GA7tCQoD,MACM5I,IACTwF,EA8tCd,WACE,IAAIA,EAAIC,EAAQc,EAAQE,EAEpBd,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,GAhhEO,UAihERtE,EAAM8H,OAAOxD,GAAa,IAC5BsB,EAlhEU,QAmhEVtB,IAAe,IAEfsB,EAAKzF,EACwBsF,GAASzB,KAEpC4B,IAAOzF,GACJ+F,OACM/F,IACTuG,EAvnDN,WACE,IAAIf,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAAIC,EAAIC,EAE5BhB,EAAuB,GAAdxB,GAAmB,EAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAKhB,GAFAN,EAAKrB,IACLsB,EAAKoB,QACM7G,EAAY,CAmCrB,IAlCA0F,EAAK,GACLa,EAAKpC,IACLqC,EAAKT,QACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EAxhBM,IAyhBNtC,OAEAsC,EAAKzG,EACwBsF,GAASzE,IAEpC4F,IAAOzG,IACT0G,EAAKX,QACM/F,IACT2G,EAAKE,QACM7G,EAETuG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBxC,GAAcoC,EACdA,EAAKvG,KAGPmE,GAAcoC,EACdA,EAAKvG,GAEAuG,IAAOvG,GACZ0F,EAAGH,KAAKgB,GACRA,EAAKpC,IACLqC,EAAKT,QACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EA3jBI,IA4jBJtC,OAEAsC,EAAKzG,EACwBsF,GAASzE,IAEpC4F,IAAOzG,IACT0G,EAAKX,QACM/F,IACT2G,EAAKE,QACM7G,EAETuG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBxC,GAAcoC,EACdA,EAAKvG,KAGPmE,GAAcoC,EACdA,EAAKvG,GAGL0F,IAAO1F,EAGTwF,EADAC,EAAK3E,EAAQ2E,EAAIC,IAGjBvB,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAmhDEqD,MACM7I,GACJ+F,OACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EArjEE,IAsjEFtC,OAEAsC,EAAKzG,EACwBsF,GAASjC,IAEpCoD,IAAOzG,EAGTwF,EADAC,EAtiEwB,CAAEpG,KAAM,MAAO4G,UAsiE1BM,IAGbpC,GAAcqB,EACdA,EAAKxF,KAebmE,GAAcqB,EACdA,EAAKxF,GAGPyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GA3xCUsD,MACM9I,IACTwF,EA4xChB,WACE,IAAIA,EAAIC,EAEJE,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SA1kEJ,iBA8kERjG,EAAM8H,OAAOxD,GAAa,KAC5BsB,EA/kEU,eAglEVtB,IAAe,KAEfsB,EAAKzF,EACwBsF,GAASxB,KAEpC2B,IAAOzF,IAETyF,EArlE8BsD,GAAI,IAulEpCvD,EAAKC,EAELhB,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GAxzCYwD,MACMhJ,IACTwF,EAyzClB,WACE,IAAIA,EAAIC,EAEJE,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAtmEJ,gBA0mERjG,EAAM8H,OAAOxD,GAAa,KAC5BsB,EA3mEU,cA4mEVtB,IAAe,KAEfsB,EAAKzF,EACwBsF,GAASvB,KAEpC0B,IAAOzF,IAETyF,EAjnE8BwD,GAAQ,IAmnExCzD,EAAKC,EAELhB,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GAr1Cc0D,MACMlJ,IACTwF,EAs1CpB,WACE,IAAIA,EAAIC,EAAQc,EAAIC,EAAIC,EAEpBd,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAWhB,GARAN,EAAKrB,GAroEO,gBAsoERtE,EAAM8H,OAAOxD,GAAa,KAC5BsB,EAvoEU,cAwoEVtB,IAAe,KAEfsB,EAAKzF,EACwBsF,GAAStB,KAEpCyB,IAAOzF,EAET,GADK+F,OACM/F,EAAY,CASrB,GARAuG,EAAK,GACDvD,EAAQoD,KAAKvG,EAAMwG,OAAOlC,MAC5BqC,EAAK3G,EAAMwG,OAAOlC,IAClBA,OAEAqC,EAAKxG,EACwBsF,GAASrC,IAEpCuD,IAAOxG,EACT,KAAOwG,IAAOxG,GACZuG,EAAGhB,KAAKiB,GACJxD,EAAQoD,KAAKvG,EAAMwG,OAAOlC,MAC5BqC,EAAK3G,EAAMwG,OAAOlC,IAClBA,OAEAqC,EAAKxG,EACwBsF,GAASrC,SAI1CsD,EAAKvG,EAEHuG,IAAOvG,IACTwG,EAAKT,QACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EAxsEE,IAysEFtC,OAEAsC,EAAKzG,EACwBsF,GAASjC,IAEpCoD,IAAOzG,GAETyF,EAhrEuBsD,GAAII,SAgrEd5C,EAhrEyB/G,KAAK,IAAK,KAirEhDgG,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,KAOTmE,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,OAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAx6CgB4D,MACMpJ,IACTwF,EAy6CtB,WACE,IAAIA,EAAIC,EAAQc,EAAIC,EAAIC,EAEpBd,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAWhB,GARAN,EAAKrB,GAvtEO,qBAwtERtE,EAAM8H,OAAOxD,GAAa,KAC5BsB,EAztEU,mBA0tEVtB,IAAe,KAEfsB,EAAKzF,EACwBsF,GAASrB,KAEpCwB,IAAOzF,EAET,GADK+F,OACM/F,EAAY,CASrB,GARAuG,EAAK,GACDvD,EAAQoD,KAAKvG,EAAMwG,OAAOlC,MAC5BqC,EAAK3G,EAAMwG,OAAOlC,IAClBA,OAEAqC,EAAKxG,EACwBsF,GAASrC,IAEpCuD,IAAOxG,EACT,KAAOwG,IAAOxG,GACZuG,EAAGhB,KAAKiB,GACJxD,EAAQoD,KAAKvG,EAAMwG,OAAOlC,MAC5BqC,EAAK3G,EAAMwG,OAAOlC,IAClBA,OAEAqC,EAAKxG,EACwBsF,GAASrC,SAI1CsD,EAAKvG,EAEHuG,IAAOvG,IACTwG,EAAKT,QACM/F,GAC6B,KAAlCH,EAAMf,WAAWqF,KACnBsC,EA7xEE,IA8xEFtC,OAEAsC,EAAKzG,EACwBsF,GAASjC,IAEpCoD,IAAOzG,GAETyF,EAlwEwBwD,GAAQE,SAkwElB5C,EAlwE6B/G,KAAK,IAAK,KAmwErDgG,EAAKC,IAELtB,GAAcqB,EACdA,EAAKxF,KAOTmE,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,OAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EA3/CkB6D,MACMrJ,IACTwF,EA4/CxB,WACE,IAAIA,EAAIC,EAAIC,EAERC,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,OAAIC,GACFzB,GAAcyB,EAAOC,QAEdD,EAAOE,SAGhBN,EAAKrB,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBsB,EA3yEW,IA4yEXtB,OAEAsB,EAAKzF,EACwBsF,GAASpB,KAEpCuB,IAAOzF,IACT0F,EAAKS,QACMnG,EAGTwF,EADAC,EAlzEO,CAAEpG,KAAM,QAASpC,KAkzEVyI,IAOhBvB,GAAcqB,EACdA,EAAKxF,GAGPyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GAjiDoB8D,IAa3B7E,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,GAwPT,SAASiC,KACP,IAAIjC,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EA/mCH9E,EAAG0F,EAinCpB1B,EAAuB,GAAdxB,GAAmB,GAC5ByB,EAASnB,GAAiBkB,GAE9B,GAAIC,EAGF,OAFAzB,GAAcyB,EAAOC,QAEdD,EAAOE,OAKhB,GAFAN,EAAKrB,IACLsB,EAAKU,QACMnG,EAAY,CAuBrB,IAtBA0F,EAAK,GACLa,EAAKpC,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBqC,EAloCQ,IAmoCRrC,OAEAqC,EAAKxG,EACwBsF,GAASxD,IAEpC0E,IAAOxG,IACTyG,EAAKN,QACMnG,EAETuG,EADAC,EAAK,CAACA,EAAIC,IAOZtC,GAAcoC,EACdA,EAAKvG,GAEAuG,IAAOvG,GACZ0F,EAAGH,KAAKgB,GACRA,EAAKpC,GACiC,KAAlCtE,EAAMf,WAAWqF,KACnBqC,EAzpCM,IA0pCNrC,OAEAqC,EAAKxG,EACwBsF,GAASxD,IAEpC0E,IAAOxG,IACTyG,EAAKN,QACMnG,EAETuG,EADAC,EAAK,CAACA,EAAIC,IAOZtC,GAAcoC,EACdA,EAAKvG,GAGL0F,IAAO1F,GA3qCQ2B,EA6qCJ8D,EA7qCO4B,EA6qCH3B,EACjBF,EADAC,EA5qCS,GAAGzE,OAAOoH,MAAM,CAACzG,GAAI0F,GAAI7H,KAAK,MA+qCvC2E,GAAcqB,EACdA,EAAKxF,QAGPmE,GAAcqB,EACdA,EAAKxF,EAKP,OAFAyE,GAAiBkB,GAAO,CAAEE,QAAS1B,GAAa2B,OAAQN,GAEjDA,EAktCP,SAASuD,GAAIQ,GAAK,MAAO,CAAElK,KAAM,YAAamK,MAAO,CAAEnK,KAAM,UAAW4C,MAAOsH,IAC/E,SAASN,GAAQM,GAAK,MAAO,CAAElK,KAAM,iBAAkBmK,MAAO,CAAEnK,KAAM,UAAW4C,MAAOsH,IAkB1F,IAFAxJ,EAAaK,OAEMJ,GAAcmE,KAAgBtE,EAAMzB,OACrD,OAAO2B,EAMP,MAJIA,IAAeC,GAAcmE,GAActE,EAAMzB,QACnDkH,GA/xEK,CAAEjG,KAAM,QAyEiBxC,EA0tE9B2H,GA1tEwC1H,EA2tExCyH,GAAiB1E,EAAMzB,OAASyB,EAAMwG,OAAO9B,IAAkB,KA3tEhBxH,EA4tE/CwH,GAAiB1E,EAAMzB,OACnB4G,GAAoBT,GAAgBA,GAAiB,GACrDS,GAAoBT,GAAgBA,IA7tEnC,IAAI5H,EACTA,EAAgBe,aAAab,EAAUC,GACvCD,EACAC,EACAC,KA1Za0M,OCyBrB,SAASC,EAAQC,EAAKC,GAClB,IAAK,IAAI3L,EAAI,EAAGA,EAAI2L,EAAKxL,SAAUH,EAAG,CAClC,GAAW,MAAP0L,EAAe,OAAOA,EAC1BA,EAAMA,EAAIC,EAAK3L,IAEnB,OAAO0L,EA6CX,IAAME,EAAmC,mBAAZC,QAAyB,IAAIA,QAAU,KASpE,SAASC,EAAWC,GAChB,GAAgB,MAAZA,EACA,OAAO,WAAA,OAAM,GAGjB,GAAqB,MAAjBH,EAAuB,CACvB,IAAII,EAAUJ,EAAcK,IAAIF,GAChC,OAAe,MAAXC,IAGJA,EAAUE,EAAgBH,GAC1BH,EAAcO,IAAIJ,EAAUC,IAHjBA,EAOf,OAAOE,EAAgBH,GAQ3B,SAASG,EAAgBH,GACrB,OAAOA,EAAS3K,MACZ,IAAK,WACD,OAAO,WAAA,OAAM,GAEjB,IAAK,aACD,IAAM4C,EAAQ+H,EAAS/H,MAAMoI,cAC7B,OAAO,SAACC,EAAMC,EAAUzK,GACpB,IAAM0K,EAAe1K,GAAWA,EAAQ0K,aAAgB,OACxD,OAAOvI,IAAUqI,EAAKE,GAAaH,eAI3C,IAAK,YACD,OAAO,SAACC,EAAMC,GACV,OAA2B,IAApBA,EAASnM,QAGxB,IAAK,QACD,IAAMqM,EAAOT,EAAS/M,KAAKyN,MAAM,KACjC,OAAO,SAACJ,EAAMC,GAEV,OAvFhB,SAASI,EAAOL,EAAMM,EAAUH,EAAMI,GAElC,IADA,IAAIC,EAAUF,EACL3M,EAAI4M,EAAe5M,EAAIwM,EAAKrM,SAAUH,EAAG,CAC9C,GAAe,MAAX6M,EACA,OAAO,EAEX,IAAMC,EAAQD,EAAQL,EAAKxM,IAC3B,GAAII,MAAM2M,QAAQD,GAAQ,CACtB,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAM3M,SAAU6M,EAChC,GAAIN,EAAOL,EAAMS,EAAME,GAAIR,EAAMxM,EAAI,GACjC,OAAO,EAGf,OAAO,EAEX6M,EAAUC,EAEd,OAAOT,IAASQ,EAsEGH,CAAOL,EADGC,EAASE,EAAKrM,OAAS,GACVqM,EAAM,IAI5C,IAAK,UACD,IAAMS,EAAWlB,EAAS/D,UAAUhF,IAAI8I,GACxC,OAAO,SAACO,EAAMC,EAAUzK,GACpB,IAAK,IAAI7B,EAAI,EAAGA,EAAIiN,EAAS9M,SAAUH,EACnC,GAAIiN,EAASjN,GAAGqM,EAAMC,EAAUzK,GAAY,OAAO,EAEvD,OAAO,GAIf,IAAK,WACD,IAAMoL,EAAWlB,EAAS/D,UAAUhF,IAAI8I,GACxC,OAAO,SAACO,EAAMC,EAAUzK,GACpB,IAAK,IAAI7B,EAAI,EAAGA,EAAIiN,EAAS9M,SAAUH,EACnC,IAAKiN,EAASjN,GAAGqM,EAAMC,EAAUzK,GAAY,OAAO,EAExD,OAAO,GAIf,IAAK,MACD,IAAMoL,EAAWlB,EAAS/D,UAAUhF,IAAI8I,GACxC,OAAO,SAACO,EAAMC,EAAUzK,GACpB,IAAK,IAAI7B,EAAI,EAAGA,EAAIiN,EAAS9M,SAAUH,EACnC,GAAIiN,EAASjN,GAAGqM,EAAMC,EAAUzK,GAAY,OAAO,EAEvD,OAAO,GAIf,IAAK,MACD,IAAMoL,EAAWlB,EAAS/D,UAAUhF,IAAI8I,GACxC,OAAO,SAACO,EAAMC,EAAUzK,GACpB,IAAIgG,GAAS,EAEPnE,EAAI,GAkBV,OAjBAwJ,EAAWC,SAASd,EAAM,CACtBe,eAAOf,EAAMjN,GACK,MAAVA,GAAkBsE,EAAE2J,QAAQjO,GAEhC,IAAK,IAAIY,EAAI,EAAGA,EAAIiN,EAAS9M,SAAUH,EACnC,GAAIiN,EAASjN,GAAGqM,EAAM3I,EAAG7B,GAGrB,OAFAgG,GAAS,OACT9I,cAKZuO,iBAAW5J,EAAE6J,SACb5B,KAAM9J,GAAWA,EAAQ2L,YACzBC,SAAU5L,GAAWA,EAAQ4L,UAAY,cAGtC5F,GAIf,IAAK,QACD,IAAMgB,EAAOiD,EAAWC,EAASlD,MAC3BC,EAAQgD,EAAWC,EAASjD,OAClC,OAAO,SAACuD,EAAMC,EAAUzK,GACpB,SAAIyK,EAASnM,OAAS,GAAK2I,EAAMuD,EAAMC,EAAUzK,KACtCgH,EAAKyD,EAAS,GAAIA,EAAShL,MAAM,GAAIO,IAMxD,IAAK,aACD,IAAMgH,EAAOiD,EAAWC,EAASlD,MAC3BC,EAAQgD,EAAWC,EAASjD,OAClC,OAAO,SAACuD,EAAMC,EAAUzK,GACpB,GAAIiH,EAAMuD,EAAMC,EAAUzK,GACtB,IAAK,IAAI7B,EAAI,EAAG0N,EAAIpB,EAASnM,OAAQH,EAAI0N,IAAK1N,EAC1C,GAAI6I,EAAKyD,EAAStM,GAAIsM,EAAShL,MAAMtB,EAAI,GAAI6B,GACzC,OAAO,EAInB,OAAO,GAIf,IAAK,YACD,IAAM2K,EAAOT,EAAS/M,KAAKyN,MAAM,KACjC,OAAQV,EAAS9H,UACb,UAAK,EACD,OAAO,SAACoI,GAAI,OAA4B,MAAvBZ,EAAQY,EAAMG,IACnC,IAAK,IACD,OAAQT,EAAS/H,MAAM5C,MACnB,IAAK,SACD,OAAO,SAACiL,GACJ,IAAMxF,EAAI4E,EAAQY,EAAMG,GACxB,MAAoB,iBAAN3F,GAAkBkF,EAAS/H,MAAMA,MAAMmE,KAAKtB,IAElE,IAAK,UACD,IAAMlH,YAAaoM,EAAS/H,MAAMA,OAClC,OAAO,SAACqI,GAAI,OAAK1M,cAAe8L,EAAQY,EAAMG,KAElD,IAAK,OACD,OAAO,SAACH,GAAI,OAAKN,EAAS/H,MAAMA,UAAiByH,EAAQY,EAAMG,KAEvE,MAAM,IAAIvN,6CAAsC8M,EAAS/H,MAAM5C,OACnE,IAAK,KACD,OAAQ2K,EAAS/H,MAAM5C,MACnB,IAAK,SACD,OAAO,SAACiL,GAAI,OAAMN,EAAS/H,MAAMA,MAAMmE,KAAKsD,EAAQY,EAAMG,KAC9D,IAAK,UACD,IAAM7M,YAAaoM,EAAS/H,MAAMA,OAClC,OAAO,SAACqI,GAAI,OAAK1M,cAAe8L,EAAQY,EAAMG,KAElD,IAAK,OACD,OAAO,SAACH,GAAI,OAAKN,EAAS/H,MAAMA,UAAiByH,EAAQY,EAAMG,KAEvE,MAAM,IAAIvN,6CAAsC8M,EAAS/H,MAAM5C,OACnE,IAAK,KACD,OAAO,SAACiL,GAAI,OAAKZ,EAAQY,EAAMG,IAAST,EAAS/H,MAAMA,OAC3D,IAAK,IACD,OAAO,SAACqI,GAAI,OAAKZ,EAAQY,EAAMG,GAAQT,EAAS/H,MAAMA,OAC1D,IAAK,IACD,OAAO,SAACqI,GAAI,OAAKZ,EAAQY,EAAMG,GAAQT,EAAS/H,MAAMA,OAC1D,IAAK,KACD,OAAO,SAACqI,GAAI,OAAKZ,EAAQY,EAAMG,IAAST,EAAS/H,MAAMA,OAE/D,MAAM,IAAI/E,kCAA2B8M,EAAS9H,WAGlD,IAAK,UACD,IAAM4E,EAAOiD,EAAWC,EAASlD,MAC3BC,EAAQgD,EAAWC,EAASjD,OAClC,OAAO,SAACuD,EAAMC,EAAUzK,GAAO,OAC3BiH,EAAMuD,EAAMC,EAAUzK,IAClB8L,EAAQtB,EAAMxD,EAAMyD,EA1QtB,YA0Q2CzK,IACzCkK,EAASlD,KAAKM,SACdN,EAAKwD,EAAMC,EAAUzK,IACrB8L,EAAQtB,EAAMvD,EAAOwD,EA5QtB,aA4Q4CzK,IAGvD,IAAK,WACD,IAAMgH,EAAOiD,EAAWC,EAASlD,MAC3BC,EAAQgD,EAAWC,EAASjD,OAClC,OAAO,SAACuD,EAAMC,EAAUzK,GAAO,OAC3BiH,EAAMuD,EAAMC,EAAUzK,IAClB+L,EAASvB,EAAMxD,EAAMyD,EArRvB,YAqR4CzK,IAC1CkK,EAASjD,MAAMK,SACfN,EAAKwD,EAAMC,EAAUzK,IACrB+L,EAASvB,EAAMvD,EAAOwD,EAvRvB,aAuR6CzK,IAGxD,IAAK,YACD,IAAMiJ,EAAMiB,EAASR,MAAMvH,MACrB8E,EAAQgD,EAAWC,EAASjD,OAClC,OAAO,SAACuD,EAAMC,EAAUzK,GAAO,OAC3BiH,EAAMuD,EAAMC,EAAUzK,IAClBgM,EAASxB,EAAMC,EAAUxB,EAAKjJ,IAG1C,IAAK,iBACD,IAAMiJ,GAAOiB,EAASR,MAAMvH,MACtB8E,EAAQgD,EAAWC,EAASjD,OAClC,OAAO,SAACuD,EAAMC,EAAUzK,GAAO,OAC3BiH,EAAMuD,EAAMC,EAAUzK,IAClBgM,EAASxB,EAAMC,EAAUxB,EAAKjJ,IAG1C,IAAK,QAED,IAAM7C,EAAO+M,EAAS/M,KAAKoN,cAE3B,OAAO,SAACC,EAAMC,EAAUzK,GAEpB,GAAIA,GAAWA,EAAQiM,WACnB,OAAOjM,EAAQiM,WAAW/B,EAAS/M,KAAMqN,EAAMC,GAGnD,GAAIzK,GAAWA,EAAQ0K,YAAa,OAAO,EAE3C,OAAOvN,GACH,IAAK,YACD,GAA2B,cAAxBqN,EAAKjL,KAAKE,OAAO,GAAoB,OAAO,EAEnD,IAAK,cACD,MAAgC,gBAAzB+K,EAAKjL,KAAKE,OAAO,IAC5B,IAAK,UACD,GAA2B,YAAxB+K,EAAKjL,KAAKE,OAAO,GAAkB,OAAO,EAEjD,IAAK,aACD,MAAgC,eAAzB+K,EAAKjL,KAAKE,OAAO,KACI,YAAxB+K,EAAKjL,KAAKE,OAAO,IAEC,eAAd+K,EAAKjL,OACgB,IAApBkL,EAASnM,QAAqC,iBAArBmM,EAAS,GAAGlL,OAE5B,iBAAdiL,EAAKjL,KACb,IAAK,WACD,MAAqB,wBAAdiL,EAAKjL,MACM,uBAAdiL,EAAKjL,MACS,4BAAdiL,EAAKjL,KAEjB,MAAM,IAAInC,oCAA6B8M,EAAS/M,QAK5D,MAAM,IAAIC,uCAAgC8M,EAAS3K,OAkDvD,SAAS2M,EAAe1B,EAAMxK,GAC1B,IAAM0K,EAAe1K,GAAWA,EAAQ0K,aAAgB,OAElDyB,EAAW3B,EAAKE,GACtB,OAAI1K,GAAWA,EAAQ2L,aAAe3L,EAAQ2L,YAAYQ,GAC/CnM,EAAQ2L,YAAYQ,GAE3Bd,EAAWe,YAAYD,GAChBd,EAAWe,YAAYD,GAE9BnM,GAAuC,mBAArBA,EAAQ4L,SACnB5L,EAAQ4L,SAASpB,GAGrB6B,OAAOvC,KAAKU,GAAM8B,QAAO,SAAUzG,GACtC,OAAOA,IAAQ6E,KAWvB,SAAS6B,EAAO/B,EAAMxK,GAClB,IAAM0K,EAAe1K,GAAWA,EAAQ0K,aAAgB,OACxD,OAAgB,OAATF,GAAiC,WAAhBgC,EAAOhC,IAAkD,iBAAtBA,EAAKE,GAapE,SAASoB,EAAQtB,EAAML,EAASM,EAAUgC,EAAMzM,GAC5C,IAAOzC,IAAUkN,QACjB,IAAKlN,EAAU,OAAO,EAEtB,IADA,IAAMuM,EAAOoC,EAAe3O,EAAQyC,GAC3B7B,EAAI,EAAGA,EAAI2L,EAAKxL,SAAUH,EAAG,CAClC,IAAMuO,EAAWnP,EAAOuM,EAAK3L,IAC7B,GAAII,MAAM2M,QAAQwB,GAAW,CACzB,IAAMC,EAAaD,EAASE,QAAQpC,GACpC,GAAImC,EAAa,EAAK,SACtB,IAAIE,SAAYC,SAtbV,cAubFL,GACAI,EAAa,EACbC,EAAaH,IAEbE,EAAaF,EAAa,EAC1BG,EAAaJ,EAASpO,QAE1B,IAAK,IAAI6M,EAAI0B,EAAY1B,EAAI2B,IAAc3B,EACvC,GAAIoB,EAAOG,EAASvB,GAAInL,IAAYmK,EAAQuC,EAASvB,GAAIV,EAAUzK,GAC/D,OAAO,GAKvB,OAAO,EAaX,SAAS+L,EAASvB,EAAML,EAASM,EAAUgC,EAAMzM,GAC7C,IAAOzC,IAAUkN,QACjB,IAAKlN,EAAU,OAAO,EAEtB,IADA,IAAMuM,EAAOoC,EAAe3O,EAAQyC,GAC3B7B,EAAI,EAAGA,EAAI2L,EAAKxL,SAAUH,EAAG,CAClC,IAAMuO,EAAWnP,EAAOuM,EAAK3L,IAC7B,GAAII,MAAM2M,QAAQwB,GAAW,CACzB,IAAMK,EAAML,EAASE,QAAQpC,GAC7B,GAAIuC,EAAM,EAAK,SACf,GA3dM,cA2dFN,GAAsBM,EAAM,GAAKR,EAAOG,EAASK,EAAM,GAAI/M,IAAYmK,EAAQuC,EAASK,EAAM,GAAItC,EAAUzK,GAC5G,OAAO,EAEX,GA7dO,eA6dHyM,GAAuBM,EAAML,EAASpO,OAAS,GAAKiO,EAAOG,EAASK,EAAM,GAAI/M,IAAamK,EAAQuC,EAASK,EAAM,GAAItC,EAAUzK,GAChI,OAAO,GAInB,OAAO,EAaX,SAASgM,EAASxB,EAAMC,EAAUxB,EAAKjJ,GACnC,GAAY,IAARiJ,EAAa,OAAO,EACxB,IAAO1L,IAAUkN,QACjB,IAAKlN,EAAU,OAAO,EAEtB,IADA,IAAMuM,EAAOoC,EAAe3O,EAAQyC,GAC3B7B,EAAI,EAAGA,EAAI2L,EAAKxL,SAAUH,EAAG,CAClC,IAAMuO,EAAWnP,EAAOuM,EAAK3L,IAC7B,GAAII,MAAM2M,QAAQwB,GAAU,CACxB,IAAMK,EAAM9D,EAAM,EAAIyD,EAASpO,OAAS2K,EAAMA,EAAM,EACpD,GAAI8D,GAAO,GAAKA,EAAML,EAASpO,QAAUoO,EAASK,KAASvC,EACvD,OAAO,GAInB,OAAO,EAuCX,SAASc,EAAS0B,EAAK9C,EAAU+C,EAASjN,GACtC,GAAKkK,EAAL,CACA,IAAMO,EAAW,GACXN,EAAUF,EAAWC,GACrBgD,EAjCV,SAASC,EAASjD,EAAUY,GACxB,GAAgB,MAAZZ,GAAuC,UAAnBsC,EAAOtC,GAAwB,MAAO,GAC9C,MAAZY,IAAoBA,EAAWZ,GAGnC,IAFA,IAAMkD,EAAUlD,EAAS5C,QAAU,CAACwD,GAAY,GAC1ChB,EAAOuC,OAAOvC,KAAKI,GAChB/L,EAAI,EAAGA,EAAI2L,EAAKxL,SAAUH,EAAG,CAClC,IAAM6G,EAAI8E,EAAK3L,GACTkP,EAAMnD,EAASlF,GACrBoI,EAAQ3H,WAAR2H,IAAgBD,EAASE,EAAW,SAANrI,EAAeqI,EAAMvC,KAEvD,OAAOsC,EAuBaD,CAASjD,GAAU/I,IAAI8I,GAC3CoB,EAAWC,SAAS0B,EAAK,CACrBzB,eAAOf,EAAMjN,GAET,GADc,MAAVA,GAAkBkN,EAASe,QAAQjO,GACnC4M,EAAQK,EAAMC,EAAUzK,GACxB,GAAIkN,EAAY5O,OACZ,IAAK,IAAIH,EAAI,EAAG0N,EAAIqB,EAAY5O,OAAQH,EAAI0N,IAAK1N,EAAG,CAC5C+O,EAAY/O,GAAGqM,EAAMC,EAAUzK,IAC/BiN,EAAQzC,EAAMjN,EAAQkN,GAE1B,IAAK,IAAIU,EAAI,EAAGmC,EAAI7C,EAASnM,OAAQ6M,EAAImC,IAAKnC,EAAG,CAC7C,IAAMoC,EAAqB9C,EAAShL,MAAM0L,EAAI,GAC1C+B,EAAY/O,GAAGsM,EAASU,GAAIoC,EAAoBvN,IAChDiN,EAAQxC,EAASU,GAAI5N,EAAQgQ,SAKzCN,EAAQzC,EAAMjN,EAAQkN,IAIlCgB,iBAAWhB,EAASiB,SACpB5B,KAAM9J,GAAWA,EAAQ2L,YACzBC,SAAU5L,GAAWA,EAAQ4L,UAAY,eAajD,SAAS9I,EAAMkK,EAAK9C,EAAUlK,GAC1B,IAAMoN,EAAU,GAIhB,OAHA9B,EAAS0B,EAAK9C,GAAU,SAAUM,GAC9B4C,EAAQ3H,KAAK+E,KACdxK,GACIoN,EAQX,SAAStN,EAAMoK,GACX,OAAOsD,EAAO1N,MAAMoK,GAUxB,SAASuD,EAAMT,EAAK9C,EAAUlK,GAC1B,OAAO8C,EAAMkK,EAAKlN,EAAMoK,GAAWlK,UAGvCyN,EAAM3N,MAAQA,EACd2N,EAAM3K,MAAQA,EACd2K,EAAMnC,SAAWA,EACjBmC,EAAMC,QAvPN,SAAiBlD,EAAMN,EAAUO,EAAUzK,GACvC,OAAKkK,KACAM,IACAC,IAAYA,EAAW,IAErBR,EAAWC,EAAXD,CAAqBO,EAAMC,EAAUzK,KAmPhDyN,EAAMA,MAAQA"}