import{_ as a,u as s}from"./index-bcbc0702.js";import{B as e,C as l,D as t,F as i,G as u,u as r,I as n,K as c,L as d,R as o,$ as p,a3 as _,a6 as h,a9 as f}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const v={class:"register-container"},g={class:"register-card"},y={class:"auth-types"},m={class:"auth-icon"},z={class:"auth-icon"},k={class:"auth-icon"},C={class:"auth-icon"},j={class:"register-footer"},x=a({__name:"Register",setup(a){const x=s(),I=a=>{x.push(`/auth/${a}`)};return(a,s)=>{const x=c,R=d,S=o("router-link");return e(),l("div",v,[t("div",g,[s[18]||(s[18]=t("div",{class:"register-header"},[t("h1",null,"身份认证"),t("p",null,"请选择您的身份类型进行认证")],-1)),t("div",y,[t("div",{class:"auth-type-card",onClick:s[0]||(s[0]=a=>I("buaa"))},[t("div",m,[i(x,{size:"40"},{default:u(()=>[i(r(p))]),_:1})]),s[5]||(s[5]=t("h3",null,"本校学生",-1)),s[6]||(s[6]=t("p",null,"北航在校学生通过SSO认证",-1)),i(R,{type:"primary",size:"large"},{default:u(()=>s[4]||(s[4]=[n("开始认证",-1)])),_:1,__:[4]})]),t("div",{class:"auth-type-card",onClick:s[1]||(s[1]=a=>I("freshman"))},[t("div",z,[i(x,{size:"40"},{default:u(()=>[i(r(_))]),_:1})]),s[8]||(s[8]=t("h3",null,"新生认证",-1)),s[9]||(s[9]=t("p",null,"2025级新生录取查询认证",-1)),i(R,{type:"success",size:"large"},{default:u(()=>s[7]||(s[7]=[n("开始认证",-1)])),_:1,__:[7]})]),t("div",{class:"auth-type-card",onClick:s[2]||(s[2]=a=>I("external"))},[t("div",k,[i(x,{size:"40"},{default:u(()=>[i(r(h))]),_:1})]),s[11]||(s[11]=t("h3",null,"外校学生",-1)),s[12]||(s[12]=t("p",null,"其他高校学生申请认证",-1)),i(R,{type:"warning",size:"large"},{default:u(()=>s[10]||(s[10]=[n("开始认证",-1)])),_:1,__:[10]})]),t("div",{class:"auth-type-card",onClick:s[3]||(s[3]=a=>I("invite"))},[t("div",C,[i(x,{size:"40"},{default:u(()=>[i(r(f))]),_:1})]),s[14]||(s[14]=t("h3",null,"邀请码认证",-1)),s[15]||(s[15]=t("p",null,"使用邀请码进行认证",-1)),i(R,{type:"info",size:"large"},{default:u(()=>s[13]||(s[13]=[n("开始认证",-1)])),_:1,__:[13]})])]),t("div",j,[t("p",null,[s[17]||(s[17]=n("已有账号？",-1)),i(S,{to:"/login"},{default:u(()=>s[16]||(s[16]=[n("立即登录",-1)])),_:1,__:[16]})])])])])}}},[["__scopeId","data-v-e48296e6"]]);export{x as default};
