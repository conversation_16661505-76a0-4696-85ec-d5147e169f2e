{"version": 3, "file": "select-dropdown.mjs", "sources": ["../../../../../../packages/components/select-v2/src/select-dropdown.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  inject,\n  ref,\n  toRaw,\n  unref,\n  watch,\n} from 'vue'\nimport { get } from 'lodash-unified'\nimport { isIOS, isObject, isUndefined } from '@element-plus/utils'\nimport {\n  DynamicSizeList,\n  FixedSizeList,\n} from '@element-plus/components/virtual-list'\nimport { useNamespace } from '@element-plus/hooks'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport GroupItem from './group-item.vue'\nimport OptionItem from './option-item.vue'\nimport { useProps } from './useProps'\nimport { selectV2InjectionKey } from './token'\n\nimport type {\n  DynamicSizeListInstance,\n  FixedSizeListInstance,\n  ItemProps,\n} from '@element-plus/components/virtual-list'\nimport type { Option, OptionItemProps } from './select.types'\nimport type {\n  ComponentPublicInstance,\n  ComputedRef,\n  ExtractPropTypes,\n  Ref,\n} from 'vue'\n\nconst props = {\n  loading: Boolean,\n  data: {\n    type: Array,\n    required: true as const,\n  },\n  hoveringIndex: Number,\n  width: Number,\n}\ninterface SelectDropdownExposed {\n  listRef: Ref<FixedSizeListInstance | DynamicSizeListInstance | undefined>\n  isSized: ComputedRef<boolean>\n  isItemDisabled: (modelValue: any[] | any, selected: boolean) => boolean\n  isItemHovering: (target: number) => boolean\n  isItemSelected: (modelValue: any[] | any, target: Option) => boolean\n  scrollToItem: (index: number) => void\n  resetScrollTop: () => void\n}\nexport type SelectDropdownInstance = ComponentPublicInstance<\n  ExtractPropTypes<typeof props>,\n  SelectDropdownExposed\n>\nexport default defineComponent({\n  name: 'ElSelectDropdown',\n  props,\n  setup(props, { slots, expose }) {\n    const select = inject(selectV2InjectionKey)!\n    const ns = useNamespace('select')\n    const { getLabel, getValue, getDisabled } = useProps(select.props)\n\n    const cachedHeights = ref<Array<number>>([])\n\n    const listRef = ref<FixedSizeListInstance | DynamicSizeListInstance>()\n\n    const size = computed(() => props.data.length)\n    watch(\n      () => size.value,\n      () => {\n        select.tooltipRef.value?.updatePopper?.()\n      }\n    )\n\n    const isSized = computed(() =>\n      isUndefined(select.props.estimatedOptionHeight)\n    )\n    const listProps = computed(() => {\n      if (isSized.value) {\n        return {\n          itemSize: select.props.itemHeight,\n        }\n      }\n\n      return {\n        estimatedSize: select.props.estimatedOptionHeight,\n        itemSize: (idx: number) => cachedHeights.value[idx],\n      }\n    })\n\n    const contains = (arr: Array<any> = [], target: any) => {\n      const {\n        props: { valueKey },\n      } = select\n\n      if (!isObject(target)) {\n        return arr.includes(target)\n      }\n\n      return (\n        arr &&\n        arr.some((item) => {\n          return toRaw(get(item, valueKey)) === get(target, valueKey)\n        })\n      )\n    }\n    const isEqual = (selected: unknown, target: unknown) => {\n      if (!isObject(target)) {\n        return selected === target\n      } else {\n        const { valueKey } = select.props\n        return get(selected, valueKey) === get(target, valueKey)\n      }\n    }\n\n    const isItemSelected: SelectDropdownExposed['isItemSelected'] = (\n      modelValue,\n      target\n    ) => {\n      if (select.props.multiple) {\n        return contains(modelValue, getValue(target))\n      }\n      return isEqual(modelValue, getValue(target))\n    }\n\n    const isItemDisabled: SelectDropdownExposed['isItemDisabled'] = (\n      modelValue,\n      selected\n    ) => {\n      const { disabled, multiple, multipleLimit } = select.props\n      return (\n        disabled ||\n        (!selected &&\n          (multiple\n            ? multipleLimit > 0 && modelValue.length >= multipleLimit\n            : false))\n      )\n    }\n\n    const isItemHovering: SelectDropdownExposed['isItemHovering'] = (target) =>\n      props.hoveringIndex === target\n\n    const scrollToItem: SelectDropdownExposed['scrollToItem'] = (index) => {\n      const list = listRef.value\n      if (list) {\n        list.scrollToItem(index)\n      }\n    }\n\n    const resetScrollTop: SelectDropdownExposed['resetScrollTop'] = () => {\n      const list = listRef.value\n      if (list) {\n        list.resetScrollTop()\n      }\n    }\n    const exposed: SelectDropdownExposed = {\n      listRef,\n      isSized,\n\n      isItemDisabled,\n      isItemHovering,\n      isItemSelected,\n      scrollToItem,\n      resetScrollTop,\n    }\n    expose(exposed)\n\n    const Item = (itemProps: ItemProps<any>) => {\n      const { index, data, style } = itemProps\n      const sized = unref(isSized)\n      const { itemSize, estimatedSize } = unref(listProps)\n      const { modelValue } = select.props\n      const { onSelect, onHover } = select\n      const item = data[index]\n      if (item.type === 'Group') {\n        return (\n          <GroupItem\n            item={item}\n            style={style}\n            height={sized ? (itemSize as number) : estimatedSize}\n          />\n        )\n      }\n\n      const isSelected = isItemSelected(modelValue, item)\n      const isDisabled = isItemDisabled(modelValue, isSelected)\n      const isHovering = isItemHovering(index)\n      return (\n        <OptionItem\n          {...itemProps}\n          selected={isSelected}\n          disabled={getDisabled(item) || isDisabled}\n          created={!!item.created}\n          hovering={isHovering}\n          item={item}\n          onSelect={onSelect}\n          onHover={onHover}\n        >\n          {{\n            default: (props: OptionItemProps) =>\n              slots.default?.(props) || <span>{getLabel(item)}</span>,\n          }}\n        </OptionItem>\n      )\n    }\n\n    // computed\n    const { onKeyboardNavigate, onKeyboardSelect } = select\n\n    const onForward = () => {\n      onKeyboardNavigate('forward')\n    }\n\n    const onBackward = () => {\n      onKeyboardNavigate('backward')\n    }\n\n    const onEscOrTab = () => {\n      // The following line actually doesn't work. Fixing it may introduce a small breaking change for some users, so just comment it out for now.\n      // select.expanded = false\n    }\n\n    const onKeydown = (e: KeyboardEvent) => {\n      const { code } = e\n      const { tab, esc, down, up, enter, numpadEnter } = EVENT_CODE\n      if ([esc, down, up, enter, numpadEnter].includes(code)) {\n        e.preventDefault()\n        e.stopPropagation()\n      }\n\n      switch (code) {\n        case tab:\n        case esc:\n          onEscOrTab()\n          break\n        case down:\n          onForward()\n          break\n        case up:\n          onBackward()\n          break\n        case enter:\n        case numpadEnter:\n          onKeyboardSelect()\n          break\n      }\n    }\n\n    return () => {\n      const { data, width } = props\n      const { height, multiple, scrollbarAlwaysOn } = select.props\n      const isScrollbarAlwaysOn = computed(() => {\n        // fix https://github.com/element-plus/element-plus/issues/19127\n        return isIOS ? true : scrollbarAlwaysOn\n      })\n\n      const List = unref(isSized) ? FixedSizeList : DynamicSizeList\n\n      return (\n        <div\n          class={[ns.b('dropdown'), ns.is('multiple', multiple)]}\n          style={{\n            width: `${width}px`,\n          }}\n        >\n          {slots.header?.()}\n          {slots.loading?.() || slots.empty?.() || (\n            <List\n              ref={listRef}\n              {...unref(listProps)}\n              className={ns.be('dropdown', 'list')}\n              scrollbarAlwaysOn={isScrollbarAlwaysOn.value}\n              data={data}\n              height={height}\n              width={width}\n              total={data.length}\n              // @ts-ignore - dts problem\n              onKeydown={onKeydown}\n            >\n              {{\n                default: (props: ItemProps<any>) => <Item {...props} />,\n              }}\n            </List>\n          )}\n          {slots.footer?.()}\n        </div>\n      )\n    }\n  },\n})\n"], "names": ["props", "loading", "Boolean", "data", "type", "Array", "required", "hoveringIndex", "Number", "width", "defineComponent", "name", "slots", "expose", "select", "ns", "get<PERSON><PERSON><PERSON>", "getValue", "getDisabled", "cachedHeights", "ref", "listRef", "size", "computed", "watch", "tooltipRef", "value", "isSized", "isUndefined", "estimatedOptionHeight", "listProps", "itemSize", "itemHeight", "estimatedSize", "contains", "valueKey", "isObject", "target", "arr", "item", "isEqual", "selected", "get", "isItemSelected", "modelValue", "isItemDisabled", "multipleLimit", "isItemHovering", "scrollToItem", "resetScrollTop", "list", "<PERSON><PERSON>", "itemProps", "style", "_createVNode", "unref", "_mergeProps", "onSelect", "onHover", "sized", "onKeyboardSelect", "onForward", "onBackward", "onEscOrTab", "onKeydown", "code", "esc", "down", "numpadEnter", "up", "e", "enter", "scrollbarAlwaysOn"], "mappings": ";;;;;;;;;;;;;;AAmCA,MAAMA,KAAK,GAAG;AACZC,EAAAA,OAAO,EAAEC,OADG;AAEZC,EAAAA,IAAI,EAAE;AACJC,IAAAA,IAAI,EAAEC,KADF;AAEJC,IAAAA,QAAQ,EAAE,IAAA;GAJA;AAMZC,EAAAA,aAAa,EAAEC,MANH;AAOZC,EAAAA,KAAK,EAAED,MAAAA;AAPK,CAAd,CAAA;AAsBA,mBAAeE,eAAe,CAAC;AAC7BC,EAAAA,IAAI,EAAE,kBADuB;EAE7BX,KAF6B;;IAGxB;IAAUY,MAAF;AAASC,GAAAA,EAAAA;AAAT,IAAmB,MAAA,MAAA,GAAA,MAAA,CAAA,oBAAA,CAAA,CAAA;AAC9B,IAAA,MAAMC,EAAM,GAAA;AACZ,IAAA,MAAMC;MACA,QAAA;MAAEC,QAAF;MAAYC,WAAZ;AAAsBC,KAAAA,GAAAA,QAAAA,CAAAA,MAAAA,CAAAA,KAAAA,CAAAA,CAAAA;AAAtB,IAAA,MAA8C,aAAO,MAAP,CAApD,EAAA,CAAA,CAAA;AAEA,IAAA,MAAMC,OAAa,GAAA,GAAA,EAAA,CAAGC;IAEtB,MAAMC,IAAAA,GAAO,QAAb,CAAA,MAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;IAEA,KAAMC,CAAAA,MAAOC,IAAAA,CAAAA,KAAS,EAAA;AACtBC,MAAAA,IACE,EAAMF,EAAAA,EAAAA,CAAAA;AAEJR,MAAAA,CAAAA,EAAAA,GAAM,CAACW,EAAAA,GAAAA,MAAWC,CAAAA,UAAlB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AACD,KAJE,CAAL,CAAA;AAOA,IAAA,MAAMC,OAAO,GAAGJ,QAAQ,CAAC,MACvBK,WAAW,CAACd,MAAM,CAACd,KAAP,CAAa6B,qBAAd,CADW,CAAxB,CAAA;AAGA,IAAA,MAAMC,SAAS,GAAGP,QAAQ,CAAC,MAAM;MAC/B,IAAII,OAAO,CAACD,KAAZ,EAAmB;QACjB,OAAO;AACLK,UAAAA,QAAQ,EAAEjB,MAAM,CAACd,KAAP,CAAagC,UAAAA;SADzB,CAAA;AAGD,OAAA;;QAEM,aAAA,EAAA,MAAA,CAAA,KAAA,CAAA,qBAAA;AACLC,QAAAA,QAAAA,EAAAA,CAAAA,GAAenB,KAAAA,aADV,CAAA,KAAA,CAAA,GAAA,CAAA;AAELiB,OAAAA,CAAAA;MAFK,CAAP;AAID,IAAA,MAXD,QAAA,GAAA,CAAA,GAAA,GAAA,EAAA,EAAA,MAAA,KAAA;;QAaMG,KAAAA,EAAAA;UACE,QAAA;AACJlC,SAAAA;AAASmC,OAAAA,GAAAA,MAAAA,CAAAA;AAAF,MAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA;AADH,QAAA,OAAN,GAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;;AAIA,MAAA,OAAKC,GAAAA,IAASC,GAAAA,CAAAA,IAAS,CAAA,CAAA,IAAA,KAAA;AACrB,QAAA,OAAOC,KAAA,CAAA,GAAA,CAAA,cAAP,CAAA,CAAA,KAAA,GAAA,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA;AACD,OAAA,CAAA,CAAA;;AAED,IAAA,MAAA,OAEEA,GAAAA,CAAAA,QAAUC,QAAS,KAAA;AACjB,MAAA,IAAA,CAAA,QAAY,CAAA,SAAI;AACjB,QAJH,OAAA,QAAA,KAAA,MAAA,CAAA;OATF,MAAA;;AAgBA,UAAMC,QAAU;AACd,SAAA,GAAKJ,MAAAA,CAAQ,KAACC,CAAAA;QACZ,OAAOI,GAAAA,CAAAA,UAAP,QAAA,CAAA,KAAA,GAAA,CAAA,MAAA,EAAA,QAAA,CAAA,CAAA;AACD,OAFD;;AAGUN,IAAAA,MAAAA,cAAAA,GAAAA,CAAAA,UAAAA,EAAAA,MAAAA,KAAAA;UAAarB,MAAAA,CAAAA,KAArB,CAAA,QAAA,EAAA;AACA,QAAA,OAAO4B,QAAG,CAAA,UAAA,EAAA,QAAyBA,CAAAA,MAAIL,CAAAA,CAAD,CAASF;AAChD,OAAA;MANH,OAAA,OAAA,CAAA,UAAA,EAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA;;AASA,IAAA,MAAMQ,cAAuD,GAAG,CAC9DC,UAD8D,EAE9DP,QACG,KAAA;AACH,MAAA,MAAU;QACR,QAAOH;AACR,QAAA,QAAA;;OACMM,GAAAA,MAAAA,CAAAA,KAAO,CAAA;MAPhB,OAAA,QAAA,IAAA,CAAA,QAAA,KAAA,QAAA,GAAA,aAAA,GAAA,CAAA,IAAA,UAAA,CAAA,MAAA,IAAA,aAAA,GAAA,KAAA,CAAA,CAAA;;AAUA,IAAA,MAAMK,cAAuD,GAAG,CAC9DD,MAD8D,KAAA,oBAG3D,KAAA,MAAA,CAAA;UACG,YAAA,GAAA,CAAA,KAAA,KAAA;YAAA,IAAA,GAAA,OAAA,CAAA,KAAA,CAAA;UAAA,IAAA,EAAA;AAAsBE,QAAAA,IAAAA,CAAAA,YAAAA,CAAAA,KAAAA,CAAAA,CAAAA;OAAkBhC;AAC9C,KAAA,CAAA;IAOD,MAZD,cAAA,GAAA,MAAA;;MAcMiC,IAAAA,IAAAA,EAAAA;;OAGAC;AACJ,KAAA,CAAA;;AACA,MAAA;aACOA;AACN,MAAA,cAAA;MAJH,cAAA;;MAOMC,YAAAA;AACJ,MAAA,cAAoB;;AACpB,IAAA,MAAIC,QAAM,CAAA,CAAA;AACRA,IAAAA,MAAAA,IAAA,GAAA,CAAA,SAAA,KAAA;AACD,MAAA,MAAA;QAJH,KAAA;;AAMA,QAAA;OAAuC,GAAA,SAAA,CAAA;MAErCvB,MAFqC,KAAA,GAAA,KAAA,CAAA,OAAA,CAAA,CAAA;MAIrCkB,MAJqC;QAAA,QAAA;QAAA,aAAA;OAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAQrCI,MAAAA,MAAAA;QARF,UAAA;OAUM,GAAA,YAAN,CAAA;;QAEME,QAAQC;QACN,OAAA;UAAA,MAAA,CAAA;YAAA,IAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAeC,MAAAA,IAAAA,IAAAA,CAAAA,IAAAA,KAAAA,OAAAA,EAAAA;AAAf,QAAA,OAANC,WAAA,CAAA,SAAA,EAAA;AACA,UAAA,MAAW,EAAA,IAAQ;UACb,OAAA,EAAA,KAAA;UAAA,QAAA,EAAA,KAAA,GAAA,QAAA,GAAA,aAAA;AAAYrB,SAAAA,EAAAA,IAAAA,CAAAA,CAAAA;OAAkBsB;MACpC,MAAM,UAAA,GAAA,cAAA,CAAA,UAAA,EAAA,IAAA,CAAA,CAAA;AAAEX,MAAAA,MAAAA,UAAAA,GAAAA,cAAAA,CAAAA,UAAAA,EAAAA,UAAAA,CAAAA,CAAAA;MAAF,MAAiB9B,UAAvB,GAAA,cAAA,CAAA,KAAA,CAAA,CAAA;MACA,OAAMwC,WAAA,CAAA,UAAA,EAAAE,UAAA,CAAA,SAAA,EAAA;QAAEC,UAAF,EAAA,UAAA;AAAYC,QAAAA,UAAAA,EAAAA,WAAAA,CAAAA,IAAAA,CAAAA,IAAAA,UAAAA;AAAZ,QAAA,SAAN,EAAA,CAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,UAAU,EAAO,UAAjB;;AACA,QAAA,UAAI,EAAA;AACF,QAAA,SAAA,EAAA,OAAA;AAAA,OAAA,CAAA,EAAA;AAAA,QAAA,OAAA,EAAA,CAAA,MAAA,KAAA;UAAA,IAIYC,EAAAA,CAAAA;AAJZ,UAAA,OAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,MAAA,CAAA,KAAAL,WAAA,CAAA,MAAA,EAAA,IAAA,EAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAOD,SAAA;;AAED,KAAA,CAAA;AACA,IAAA,MAAA;AACA,MAAA,kBAAgB;AAChB,MAAA,gBAAA;AAAA,KAAA,GAAA,MAAA,CAAA;AAAA,IAAA,MAAA,SAAA,GAIyB,MAAA;AAJzB,MAAA,kBAKmB,CAAA,SALnB,CAAA,CAAA;AAAA,KAAA,CAAA;AAAA,IAAA,MAAA,UAAA,GAAA,MAAA;AAAA,MAAA,kBAAA,CAAA,UAAA,CAAA,CAAA;;AAAA,IAAA,MAAA,SAAA,GAAA,CAAA,CAAA,KAAA;AAiBD,MAnJ6B,MAqJ9B;;;MACM,MAAA;QAAA,GAAA;AAAsBM,QAAAA,GAAAA;AAAtB,QAA2C9C,IAAjD;;QAEM+C,KAAAA;QACc,WAAA;OADpB,GAAA,UAAA,CAAA;;QAIMC,CAAAA,CAAAA,cAAmB,EAAA,CAAA;QACL,CAAA,CAAA,eAAC;OADrB;;QAIMC,KAAAA,GAAAA,CAAAA;AAEJ,QAAA,KAAA,GAAA;;QAGIC,KAAAA,IAAAA;UACE,SAAA,EAAA,CAAA;AAAEC,UAAAA,MAAAA;AAAF,QAAA,KAAN,EAAA;UACM,UAAA,EAAA,CAAA;UAAA,MAAA;QAAOC,KAAP,KAAA,CAAA;QAAYC,KAAZ,WAAA;UAAA,gBAAA,EAAA,CAAA;UAAA,MAAA;AAA6BC,OAAAA;AAA7B,KAAA,CAAA;;AACN,MAAA,IAAI,EAACF,EAAD,EAAMC,EAAAA,EAAN,EAAYE,EAAZ,CAAA;AACFC,MAAAA,MAAA;AACAA,QAAAA,IAAA;AACD,QAAA,KAAA;;AAED,MAAA,MAAA;AACE,QAAA,MAAA;AACA,QAAA,QAAA;yBACY;AACV,OAAA,GAAA,MAAA,CAAA,KAAA,CAAA;;AACF,QAAA,OAAA,KAAA,GAAA,IAAA,GAAA,iBAAA,CAAA;;AAEE,MAAA,MAAA,IAAA,GAAA,KAAA,CAAA,OAAA,CAAA,GAAA,aAAA,GAAA,eAAA,CAAA;;AACF,QAAA,OAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;eACY,EAAA;AACV,UAAA,KAAA,EAAA,CAAA,EAAA,KAAA,CAAA,EAAA,CAAA;;AACF,OAAA,EAAA,CAAA,CAAA,EAAKC,GAAL,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,IAAAjB,WAAA,CAAA,IAAA,EAAAE,UAAA,CAAA;AACA,QAAA,KAAKY,EAAL,OAAA;cACkB,CAAA,SAAA,CAAA,EAAA;AAChB,QAAA,WAAA,EAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,MAAA,CAAA;AAdJ,QAAA,mBAAA,EAAA,mBAAA,CAAA,KAAA;QARF,MAAA,EAAA,IAAA;;AA0BA,QAAA,OAAa,EAAA,KAAA;QACL,OAAA,EAAA,IAAA,CAAA,MAAA;QAAEjE,WAAF,EAAA,SAAA;AAAQM,OAAAA,CAAAA,EAAAA;AAAR,QAAA,OAAN,EAAA,CAAA,MAAA,KAAA6C,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,IAAA,CAAA;OACM,CAAA,EAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;;;AAAoBkB,CAAAA,CAAAA;;;;"}