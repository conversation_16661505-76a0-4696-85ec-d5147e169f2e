{"version": 3, "file": "select2.js", "sources": ["../../../../../../packages/components/select/src/select.ts"], "sourcesContent": ["import { placements } from '@popperjs/core'\nimport { scrollbarEmits } from '@element-plus/components/scrollbar'\nimport {\n  useAriaProps,\n  useEmptyValuesProps,\n  useSizeProp,\n} from '@element-plus/hooks'\nimport {\n  EmitFn,\n  buildProps,\n  definePropType,\n  iconPropType,\n} from '@element-plus/utils'\nimport { useTooltipContentProps } from '@element-plus/components/tooltip'\nimport { ArrowDown, CircleClose } from '@element-plus/icons-vue'\nimport { tagProps } from '@element-plus/components/tag'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Select from './select.vue'\nimport type {\n  Options,\n  Placement,\n  PopperEffect,\n} from '@element-plus/components/popper'\nimport type { OptionValue } from './type'\n\nexport const selectProps = buildProps({\n  /**\n   * @description the name attribute of select input\n   */\n  name: String,\n  /**\n   * @description native input id\n   */\n  id: String,\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: definePropType<OptionValue | OptionValue[] | null>([\n      Array,\n      String,\n      Number,\n      Boolean,\n      Object,\n    ]),\n    default: undefined,\n  },\n  /**\n   * @description the autocomplete attribute of select input\n   */\n  autocomplete: {\n    type: String,\n    default: 'off',\n  },\n  /**\n   * @description for non-filterable Select, this prop decides if the option menu pops up when the input is focused\n   */\n  automaticDropdown: Boolean,\n  /**\n   * @description size of Input\n   */\n  size: useSizeProp,\n  /**\n   * @description tooltip theme, built-in theme: `dark` / `light`\n   */\n  effect: {\n    type: definePropType<PopperEffect>(String),\n    default: 'light',\n  },\n  /**\n   * @description whether Select is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether select can be cleared\n   */\n  clearable: Boolean,\n  /**\n   * @description whether Select is filterable\n   */\n  filterable: Boolean,\n  /**\n   * @description whether creating new items is allowed. To use this, `filterable` must be true\n   */\n  allowCreate: Boolean,\n  /**\n   * @description whether Select is loading data from server\n   */\n  loading: Boolean,\n  /**\n   * @description custom class name for Select's dropdown\n   */\n  popperClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description [popper.js](https://popper.js.org/docs/v2/) parameters\n   */\n  popperOptions: {\n    type: definePropType<Partial<Options>>(Object),\n    default: () => ({} as Partial<Options>),\n  },\n  /**\n   * @description whether options are loaded from server\n   */\n  remote: Boolean,\n  /**\n   * @description displayed text while loading data from server, default is 'Loading'\n   */\n  loadingText: String,\n  /**\n   * @description displayed text when no data matches the filtering query, you can also use slot `empty`, default is 'No matching data'\n   */\n  noMatchText: String,\n  /**\n   * @description displayed text when there is no options, you can also use slot `empty`, default is 'No data'\n   */\n  noDataText: String,\n  /**\n   * @description function that gets called when the input value changes. Its parameter is the current input value. To use this, `filterable` must be true\n   */\n  remoteMethod: {\n    type: definePropType<(query: string) => void>(Function),\n  },\n  /**\n   * @description custom filter method, the first parameter is the current input value. To use this, `filterable` must be true\n   */\n  filterMethod: {\n    type: definePropType<(query: string) => void>(Function),\n  },\n  /**\n   * @description whether multiple-select is activated\n   */\n  multiple: Boolean,\n  /**\n   * @description maximum number of options user can select when `multiple` is `true`. No limit when set to 0\n   */\n  multipleLimit: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description placeholder, default is 'Select'\n   */\n  placeholder: {\n    type: String,\n  },\n  /**\n   * @description select first matching option on enter key. Use with `filterable` or `remote`\n   */\n  defaultFirstOption: Boolean,\n  /**\n   * @description when `multiple` and `filter` is true, whether to reserve current keyword after selecting an option\n   */\n  reserveKeyword: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description unique identity key name for value, required when value is an object\n   */\n  valueKey: {\n    type: String,\n    default: 'value',\n  },\n  /**\n   * @description whether to collapse tags to a text when multiple selecting\n   */\n  collapseTags: Boolean,\n  /**\n   * @description whether show all selected tags when mouse hover text of collapse-tags. To use this, `collapse-tags` must be true\n   */\n  collapseTagsTooltip: Boolean,\n  /**\n   * @description the max tags number to be shown. To use this, `collapse-tags` must be true\n   */\n  maxCollapseTags: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description whether select dropdown is teleported, if `true` it will be teleported to where `append-to` sets\n   */\n  teleported: useTooltipContentProps.teleported,\n  /**\n   * @description when select dropdown is inactive and `persistent` is `false`, select dropdown will be destroyed\n   */\n  persistent: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description custom clear icon component\n   */\n  clearIcon: {\n    type: iconPropType,\n    default: CircleClose,\n  },\n  /**\n   * @description whether the width of the dropdown is the same as the input\n   */\n  fitInputWidth: Boolean,\n  /**\n   * @description custom suffix icon component\n   */\n  suffixIcon: {\n    type: iconPropType,\n    default: ArrowDown,\n  },\n  /**\n   * @description tag type\n   */\n  // eslint-disable-next-line vue/require-prop-types\n  tagType: { ...tagProps.type, default: 'info' },\n  /**\n   * @description tag effect\n   */\n  tagEffect: { ...tagProps.effect, default: 'light' },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description in remote search method show suffix icon\n   */\n  remoteShowSuffix: Boolean,\n  /**\n   * @description determines whether the arrow is displayed\n   */\n  showArrow: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description offset of the dropdown\n   */\n  offset: {\n    type: Number,\n    default: 12,\n  },\n  /**\n   * @description position of dropdown\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: placements,\n    default: 'bottom-start',\n  },\n  /**\n   * @description list of possible positions for dropdown\n   */\n  fallbackPlacements: {\n    type: definePropType<Placement[]>(Array),\n    default: ['bottom-start', 'top-start', 'right', 'left'],\n  },\n  /**\n   * @description tabindex for input\n   */\n  tabindex: {\n    type: [String, Number],\n    default: 0,\n  },\n  /**\n   * @description which element the selection dropdown appends to\n   */\n  appendTo: useTooltipContentProps.appendTo,\n  options: {\n    type: definePropType<Record<string, any>[]>(Array),\n  },\n  props: {\n    type: definePropType<SelectOptionProps>(Object),\n  },\n  ...useEmptyValuesProps,\n  ...useAriaProps(['ariaLabel']),\n})\n/* eslint-disable @typescript-eslint/no-unused-vars */\nexport const selectEmits = {\n  [UPDATE_MODEL_EVENT]: (val: SelectProps['modelValue']) => true,\n  [CHANGE_EVENT]: (val: SelectProps['modelValue']) => true,\n  'popup-scroll': scrollbarEmits.scroll,\n  'remove-tag': (val: unknown) => true,\n  'visible-change': (visible: boolean) => true,\n  focus: (evt: FocusEvent) => evt instanceof FocusEvent,\n  blur: (evt: FocusEvent) => evt instanceof FocusEvent,\n  clear: () => true,\n}\n/* eslint-enable @typescript-eslint/no-unused-vars */\n\nexport type SelectProps = ExtractPropTypes<typeof selectProps>\nexport type SelectPropsPublic = __ExtractPublicPropTypes<typeof selectProps>\nexport type SelectEmits = EmitFn<typeof selectEmits>\nexport type SelectInstance = InstanceType<typeof Select> & unknown\nexport type SelectOptionProps = {\n  value?: string\n  label?: string\n  disabled?: string\n}\n"], "names": ["buildProps", "definePropType", "useSizeProp", "useTooltipContentProps", "iconPropType", "CircleClose", "ArrowDown", "tagProps", "placements", "useEmptyValuesProps", "useAriaProps", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "scrollbarEmits"], "mappings": ";;;;;;;;;;;;;;;;AAgBY,MAAC,WAAW,GAAGA,kBAAU,CAAC;AACtC,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,EAAE,EAAE,MAAM;AACZ,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEC,sBAAc,CAAC;AACzB,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,KAAK,CAAC;AACN,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,iBAAiB,EAAE,OAAO;AAC5B,EAAE,IAAI,EAAEC,iBAAW;AACnB,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAED,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,WAAW,EAAE,OAAO;AACtB,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,kBAAkB,EAAE,OAAO;AAC7B,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,mBAAmB,EAAE,OAAO;AAC9B,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,UAAU,EAAEE,8BAAsB,CAAC,UAAU;AAC/C,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEC,iBAAY;AACtB,IAAI,OAAO,EAAEC,oBAAW;AACxB,GAAG;AACH,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAED,iBAAY;AACtB,IAAI,OAAO,EAAEE,kBAAS;AACtB,GAAG;AACH,EAAE,OAAO,EAAE,EAAE,GAAGC,YAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE;AAChD,EAAE,SAAS,EAAE,EAAE,GAAGA,YAAQ,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;AACrD,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,gBAAgB,EAAE,OAAO;AAC3B,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEN,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,EAAEO,eAAU;AACtB,IAAI,OAAO,EAAE,cAAc;AAC3B,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAEP,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC;AAC3D,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,QAAQ,EAAEE,8BAAsB,CAAC,QAAQ;AAC3C,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEF,sBAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,GAAGQ,2BAAmB;AACxB,EAAE,GAAGC,oBAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,WAAW,GAAG;AAC3B,EAAE,CAACC,wBAAkB,GAAG,CAAC,GAAG,KAAK,IAAI;AACrC,EAAE,CAACC,kBAAY,GAAG,CAAC,GAAG,KAAK,IAAI;AAC/B,EAAE,cAAc,EAAEC,wBAAc,CAAC,MAAM;AACvC,EAAE,YAAY,EAAE,CAAC,GAAG,KAAK,IAAI;AAC7B,EAAE,gBAAgB,EAAE,CAAC,OAAO,KAAK,IAAI;AACrC,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC1C,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB;;;;;"}