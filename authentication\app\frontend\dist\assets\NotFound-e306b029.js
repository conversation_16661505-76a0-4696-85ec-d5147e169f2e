import{_ as t,u as s}from"./index-bcbc0702.js";/* empty css                   *//* empty css                  */import{B as a,C as l,D as e,F as u,G as n,L as o,ao as i,ap as r,I as _,u as d,K as c,Q as p,$ as f,a3 as h,a2 as m}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const k={class:"not-found-container"},x={class:"not-found-content"},g={class:"result-actions"},v={class:"help-links"},C={class:"links-grid"},j=t({__name:"NotFound",setup(t){const j=s(),$=()=>{j.push("/")},w=()=>{window.history.length>1?j.go(-1):j.push("/")};return(t,s)=>{const j=o,b=i,y=r,z=c;return a(),l("div",k,[e("div",x,[u(b,{icon:"warning",title:"404","sub-title":"抱歉，您访问的页面不存在"},{extra:n(()=>[e("div",g,[u(j,{type:"primary",size:"large",onClick:$},{default:n(()=>s[4]||(s[4]=[_(" 返回首页 ",-1)])),_:1,__:[4]}),u(j,{size:"large",onClick:w},{default:n(()=>s[5]||(s[5]=[_(" 返回上页 ",-1)])),_:1,__:[5]})])]),_:1}),e("div",v,[u(y,null,{default:n(()=>s[6]||(s[6]=[_("常用链接",-1)])),_:1,__:[6]}),e("div",C,[u(j,{text:"",onClick:s[0]||(s[0]=s=>t.$router.push("/"))},{default:n(()=>[u(z,null,{default:n(()=>[u(d(p))]),_:1}),s[7]||(s[7]=_(" 首页 ",-1))]),_:1,__:[7]}),u(j,{text:"",onClick:s[1]||(s[1]=s=>t.$router.push("/auth/buaa"))},{default:n(()=>[u(z,null,{default:n(()=>[u(d(f))]),_:1}),s[8]||(s[8]=_(" 本校学生认证 ",-1))]),_:1,__:[8]}),u(j,{text:"",onClick:s[2]||(s[2]=s=>t.$router.push("/auth/freshman"))},{default:n(()=>[u(z,null,{default:n(()=>[u(d(h))]),_:1}),s[9]||(s[9]=_(" 新生认证 ",-1))]),_:1,__:[9]}),u(j,{text:"",onClick:s[3]||(s[3]=s=>t.$router.push("/admin/login"))},{default:n(()=>[u(z,null,{default:n(()=>[u(d(m))]),_:1}),s[10]||(s[10]=_(" 管理员登录 ",-1))]),_:1,__:[10]})])])])])}}},[["__scopeId","data-v-96d0b165"]]);export{j as default};
