import { logger } from '@/utils/logger'
import axios from 'axios'
import crypto from 'crypto'
import { v4 as uuidv4 } from 'uuid'

export class NumberAuthService {
  private static instance: NumberAuthService

  private accessKeyId: string
  private accessKeySecret: string
  private enabled: boolean
  private mock: boolean

  private constructor() {
    this.accessKeyId = process.env['ALIYUN_ACCESS_KEY_ID'] || ''
    this.accessKeySecret = process.env['ALIYUN_ACCESS_KEY_SECRET'] || ''
    this.enabled = (process.env['NUMBERAUTH_ENABLED'] || 'false').toLowerCase() === 'true'
    this.mock = (process.env['NUMBERAUTH_MOCK'] || 'true').toLowerCase() === 'true'
  }

  public static getInstance(): NumberAuthService {
    if (!NumberAuthService.instance) NumberAuthService.instance = new NumberAuthService()
    return NumberAuthService.instance
  }

  /**
   * 一键登录-取号：调用阿里云 Dypns GetMobile（2017-05-25）
   * 文档：公共参数签名（阿里云通用 RPC）
   */
  public async getMobile(accessCode: string): Promise<{ success: boolean; phone?: string; message?: string }> {
    try {
      if (!accessCode || accessCode.trim() === '') {
        return { success: false, message: '缺少 accessCode' }
      }

      if (!this.enabled || !this.accessKeyId || !this.accessKeySecret) {
        logger.warn('NumberAuth not configured, using mock fallback')
        if (this.mock) {
          return { success: true, phone: '13800138000' }
        }
        return { success: false, message: 'NumberAuth 未配置' }
      }

      const endpoint = 'https://dypnsapi.aliyuncs.com'
      const method = 'GET'
      // 公共参数 + 业务参数
      const params: Record<string, string> = {
        Action: 'GetMobile',
        Version: '2017-05-25',
        Format: 'JSON',
        AccessKeyId: this.accessKeyId,
        SignatureMethod: 'HMAC-SHA1',
        SignatureVersion: '1.0',
        SignatureNonce: uuidv4(),
        Timestamp: new Date().toISOString(),
        AccessCode: accessCode,
      }

      const signature = this.signRPC(method, params)
      const finalParams = { ...params, Signature: signature }

      const response = await axios.get(endpoint, { params: finalParams, timeout: 5000 })
      return this.parsePhoneResponse(response.data)
    } catch (err: any) {
      // 不打印密钥
      logger.error('NumberAuth getMobile failed:', err?.response?.data || err?.message || err)
      return { success: false, message: 'NumberAuth 调用失败' }
    }
  }

  /**
   * 一键登录-取号：spToken 交换手机号（GetPhoneWithToken）
   */
  public async getMobileBySpToken(spToken: string): Promise<{ success: boolean; phone?: string; message?: string }> {
    try {
      if (!spToken || spToken.trim() === '') {
        return { success: false, message: '缺少 spToken' }
      }
      if (!this.enabled || !this.accessKeyId || !this.accessKeySecret) {
        logger.warn('NumberAuth not configured, using mock fallback')
        if (this.mock) return { success: true, phone: '13800138000' }
        return { success: false, message: 'NumberAuth 未配置' }
      }

      const endpoint = 'https://dypnsapi.aliyuncs.com'
      const method: 'GET' = 'GET'
      const params: Record<string, string> = {
        Action: 'GetPhoneWithToken',
        Version: '2017-05-25',
        Format: 'JSON',
        AccessKeyId: this.accessKeyId,
        SignatureMethod: 'HMAC-SHA1',
        SignatureVersion: '1.0',
        SignatureNonce: uuidv4(),
        Timestamp: new Date().toISOString(),
        SpToken: spToken,
      }
      const signature = this.signRPC(method, params)
      const finalParams = { ...params, Signature: signature }
      const response = await axios.get(endpoint, { params: finalParams, timeout: 5000 })
      return this.parsePhoneResponse(response.data)
    } catch (err: any) {
      logger.error('NumberAuth getMobileBySpToken failed:', err?.response?.data || err?.message || err)
      return { success: false, message: 'NumberAuth 调用失败' }
    }
  }

  private parsePhoneResponse(data: any): { success: boolean; phone?: string; message?: string } {
    const code = data?.Code || data?.code
    if (code && String(code).toUpperCase() !== 'OK') {
      const msg = data?.Message || data?.message || '取号失败'
      return { success: false, message: msg }
    }
    const dto = data?.GetMobileResultDTO || data?.GetPhoneWithTokenResultDTO || data?.getMobileResultDTO || data?.getPhoneWithTokenResultDTO || {}
    const phone = dto.Mobile || dto.mobile || data?.Mobile || data?.mobile || data?.PhoneNumber || data?.phoneNumber || data?.PhoneNo || data?.phoneNo
    if (phone && typeof phone === 'string') return { success: true, phone }
    return { success: false, message: '取号响应无手机号字段' }
  }

  private percentEncode(str: string): string {
    return encodeURIComponent(str)
      .replace(/\+/g, '%20')
      .replace(/\*/g, '%2A')
      .replace(/%7E/g, '~')
  }

  private signRPC(method: 'GET' | 'POST', params: Record<string, string>): string {
    const sortedKeys = Object.keys(params).sort()
    const canonicalized = sortedKeys
      .map((k) => `${this.percentEncode(k)}=${this.percentEncode(params[k])}`)
      .join('&')
    const stringToSign = `${method}&${this.percentEncode('/')}&${this.percentEncode(canonicalized)}`
    const key = `${this.accessKeySecret}&`
    const hmac = crypto.createHmac('sha1', key)
    hmac.update(stringToSign)
    return hmac.digest('base64')
  }
}

export const numberAuthService = NumberAuthService.getInstance()

