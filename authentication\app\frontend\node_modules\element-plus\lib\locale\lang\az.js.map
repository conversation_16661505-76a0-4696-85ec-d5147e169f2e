{"version": 3, "file": "az.js", "sources": ["../../../../../packages/locale/lang/az.ts"], "sourcesContent": ["export default {\n  name: 'az',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Təsdiqlə',\n      clear: 'Təmizlə',\n    },\n    datepicker: {\n      now: '<PERSON>ndi',\n      today: '<PERSON><PERSON>ü<PERSON>',\n      cancel: '<PERSON><PERSON><PERSON>',\n      clear: 'Təmizlə',\n      confirm: 'Təsdiqlə',\n      selectDate: 'Tarix seç',\n      selectTime: 'Saat seç',\n      startDate: '<PERSON>şlanğıc Tarixi',\n      startTime: '<PERSON>şlanğıc Saatı',\n      endDate: 'Bitm<PERSON> Tarixi',\n      endTime: 'Bitmə Saatı',\n      prevYear: 'Öncəki il',\n      nextYear: 'Sonrakı il',\n      prevMonth: 'Öncəki ay',\n      nextMonth: 'Sonrakı ay',\n      year: '',\n      month1: 'Yanvar',\n      month2: 'Fevral',\n      month3: 'Mart',\n      month4: 'Aprel',\n      month5: 'May',\n      month6: 'İyun',\n      month7: 'İyul',\n      month8: 'Avqust',\n      month9: '<PERSON><PERSON><PERSON><PERSON>',\n      month10: 'Oktyabr',\n      month11: 'Noyabr',\n      month12: 'Dekabr',\n      week: 'həftə',\n      weeks: {\n        sun: 'Baz',\n        mon: 'B.e',\n        tue: 'Ç.a',\n        wed: 'Çər',\n        thu: 'C.a',\n        fri: 'Cüm',\n        sat: 'Şən',\n      },\n      months: {\n        jan: 'Yan',\n        feb: 'Fev',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'İyn',\n        jul: 'İyl',\n        aug: 'Avq',\n        sep: 'Sen',\n        oct: 'Okt',\n        nov: 'Noy',\n        dec: 'Dek',\n      },\n    },\n    select: {\n      loading: 'Yüklənir',\n      noMatch: 'Nəticə tapılmadı',\n      noData: 'Məlumat yoxdur',\n      placeholder: 'Seç',\n    },\n    mention: {\n      loading: 'Yüklənir',\n    },\n    cascader: {\n      noMatch: 'Nəticə tapılmadı',\n      loading: 'Yüklənir',\n      placeholder: 'Seç',\n      noData: 'Məlumat yoxdur',\n    },\n    pagination: {\n      goto: 'Get',\n      pagesize: '/səhifə',\n      total: 'Toplam {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mesaj',\n      confirm: 'Təsdiqlə',\n      cancel: 'İmtina',\n      error: 'Səhv',\n    },\n    upload: {\n      deleteTip: 'Sürüşdürmədən sonra sil',\n      delete: 'Sil',\n      preview: 'Ön izlə',\n      continue: 'Davam et',\n    },\n    table: {\n      emptyText: 'Məlumat yoxdur',\n      confirmFilter: 'Təsdiqlə',\n      resetFilter: 'Sıfırla',\n      clearFilter: 'Bütün',\n      sumText: 'Cəmi',\n    },\n    tree: {\n      emptyText: 'Məlumat yoxdur',\n    },\n    transfer: {\n      noMatch: 'Nəticə tapılmadı',\n      noData: 'Məlumat yoxdur',\n      titles: ['Siyahı 1', 'Siyahı 2'],\n      filterPlaceholder: 'Kəlimələri daxil et',\n      noCheckedFormat: '{total} ədəd',\n      hasCheckedFormat: '{checked}/{total} seçildi',\n    },\n    image: {\n      error: 'SƏHV', // to be translated\n    },\n    pageHeader: {\n      title: 'Geri', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Bəli', // to be translated\n      cancelButtonText: 'Xeyr', // to be translated\n    },\n    empty: {\n      description: 'Məlumat yoxdur',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,WAAW;AACtB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM,SAAS,EAAE,iCAAiC;AAClD,MAAM,SAAS,EAAE,qCAAqC;AACtD,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,aAAa;AAC1B,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,eAAe;AAC5B,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,MAAM,EAAE,qBAAqB;AACnC,MAAM,WAAW,EAAE,QAAQ;AAC3B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,kBAAkB;AACjC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,MAAM,EAAE,qBAAqB;AACnC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,KAAK,EAAE,WAAW;AACxB,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,iDAAiD;AAClE,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,OAAO,EAAE,iBAAiB;AAChC,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,aAAa,EAAE,oBAAoB;AACzC,MAAM,WAAW,EAAE,mBAAmB;AACtC,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,OAAO,EAAE,WAAW;AAC1B,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,qBAAqB;AACtC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,MAAM,EAAE,qBAAqB;AACnC,MAAM,MAAM,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;AAChD,MAAM,iBAAiB,EAAE,oCAAoC;AAC7D,MAAM,eAAe,EAAE,wBAAwB;AAC/C,MAAM,gBAAgB,EAAE,8BAA8B;AACtD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,WAAW;AACxB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,WAAW;AACpC,MAAM,gBAAgB,EAAE,MAAM;AAC9B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,WAAW,EAAE,qBAAqB;AACxC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}