{"version": 3, "file": "auto-resizer.mjs", "sources": ["../../../../../../packages/components/table-v2/src/auto-resizer.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\ntype AutoResizeHandler = (event: { height: number; width: number }) => void\n\nexport const autoResizerProps = buildProps({\n  disableWidth: <PERSON><PERSON><PERSON>,\n  disableHeight: <PERSON><PERSON>an,\n  onResize: {\n    type: definePropType<AutoResizeHandler>(Function),\n  },\n} as const)\n\nexport type AutoResizerProps = ExtractPropTypes<typeof autoResizerProps>\nexport type AutoResizerPropsPublic = __ExtractPublicPropTypes<\n  typeof autoResizerProps\n>\n"], "names": [], "mappings": ";;AACY,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,CAAC;;;;"}