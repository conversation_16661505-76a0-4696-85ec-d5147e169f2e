'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../utils/vue/props/runtime.js');

const popperTriggerProps = runtime.buildProps({
  virtualRef: {
    type: runtime.definePropType(Object)
  },
  virtualTriggering: Boolean,
  onMouseenter: {
    type: runtime.definePropType(Function)
  },
  onMouseleave: {
    type: runtime.definePropType(Function)
  },
  onClick: {
    type: runtime.definePropType(Function)
  },
  onKeydown: {
    type: runtime.definePropType(Function)
  },
  onFocus: {
    type: runtime.definePropType(Function)
  },
  onBlur: {
    type: runtime.definePropType(Function)
  },
  onContextmenu: {
    type: runtime.definePropType(Function)
  },
  id: String,
  open: Bo<PERSON><PERSON>
});
const usePopperTriggerProps = popperTriggerProps;

exports.popperTriggerProps = popperTriggerProps;
exports.usePopperTriggerProps = usePopperTriggerProps;
//# sourceMappingURL=trigger.js.map
