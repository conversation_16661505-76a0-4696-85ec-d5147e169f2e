(function(e,t){"object"===typeof exports&&"object"===typeof module?module.exports=t(require("vue"),require("agGrid")):"function"===typeof define&&define.amd?define([,"agGrid"],t):"object"===typeof exports?exports["ag-grid-vue3"]=t(require("vue"),require("agGrid")):e["ag-grid-vue3"]=t(e["Vue"],e["agGrid"])})("undefined"!==typeof self?self:this,(function(e,t){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s="fae3")}({"8bbf":function(t,n){t.exports=e},"8bd4":function(e,n){e.exports=t},"9ff4":function(e,t,n){"use strict";(function(e){function r(e,t){const n=Object.create(null),r=e.split(",");for(let o=0;o<r.length;o++)n[r[o]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return _})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return j})),n.d(t,"e",(function(){return a})),n.d(t,"f",(function(){return c})),n.d(t,"g",(function(){return d})),n.d(t,"h",(function(){return b})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return f})),n.d(t,"k",(function(){return p})),n.d(t,"l",(function(){return r})),n.d(t,"m",(function(){return m}));const o=()=>{},s=Object.assign,i=Object.prototype.hasOwnProperty,a=(e,t)=>i.call(e,t),c=Array.isArray,u=e=>"[object Map]"===g(e),d=e=>"function"===typeof e,l=e=>"string"===typeof e,p=e=>"symbol"===typeof e,f=e=>null!==e&&"object"===typeof e,h=Object.prototype.toString,g=e=>h.call(e),m=e=>g(e).slice(8,-1),b=e=>l(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,w=e=>{const t=Object.create(null);return n=>{const r=t[n];return r||(t[n]=e(n))}},y=/-(\w)/g,O=(w(e=>e.replace(y,(e,t)=>t?t.toUpperCase():"")),/\B([A-Z])/g),v=(w(e=>e.replace(O,"-$1").toLowerCase()),w(e=>e.charAt(0).toUpperCase()+e.slice(1))),j=(w(e=>{const t=e?"on"+v(e):"";return t}),(e,t)=>!Object.is(e,t)),_=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})}}).call(this,n("c8ba"))},c8ba:function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(r){"object"===typeof window&&(n=window)}e.exports=n},fae3:function(e,t,n){"use strict";if(n.r(t),n.d(t,"AgGridVue",(function(){return Ie})),"undefined"!==typeof window){var r=window.document.currentScript,o=r&&r.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);o&&(n.p=o[1])}var s=n("8bbf"),i=n("9ff4");const a=e=>{const t=new Set(e);return t.w=0,t.n=0,t},c=e=>(e.w&p)>0,u=e=>(e.n&p)>0,d=new WeakMap;let l=0,p=1;const f=30;let h;const g=Symbol(""),m=Symbol("");let b=!0;const w=[];function y(){w.push(b),b=!1}function O(){const e=w.pop();b=void 0===e||e}function v(e,t,n){if(b&&h){let t=d.get(e);t||d.set(e,t=new Map);let r=t.get(n);r||t.set(n,r=a());const o=void 0;j(r,o)}}function j(e,t){let n=!1;l<=f?u(e)||(e.n|=p,n=!c(e)):n=!e.has(h),n&&(e.add(h),h.deps.push(e))}function _(e,t,n,r,o,s){const c=d.get(e);if(!c)return;let u=[];if("clear"===t)u=[...c.values()];else if("length"===n&&Object(i["f"])(e)){const e=Number(r);c.forEach((t,n)=>{("length"===n||!Object(i["k"])(n)&&n>=e)&&u.push(t)})}else switch(void 0!==n&&u.push(c.get(n)),t){case"add":Object(i["f"])(e)?Object(i["h"])(n)&&u.push(c.get("length")):(u.push(c.get(g)),Object(i["i"])(e)&&u.push(c.get(m)));break;case"delete":Object(i["f"])(e)||(u.push(c.get(g)),Object(i["i"])(e)&&u.push(c.get(m)));break;case"set":Object(i["i"])(e)&&u.push(c.get(g));break}if(1===u.length)u[0]&&C(u[0]);else{const e=[];for(const t of u)t&&e.push(...t);C(a(e))}}function C(e,t){const n=Object(i["f"])(e)?e:[...e];for(const r of n)r.computed&&R(r,t);for(const r of n)r.computed||R(r,t)}function R(e,t){(e!==h||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const x=Object(i["l"])("__proto__,__v_isRef,__isVue"),k=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(i["k"])),P=M();function M(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...e){const n=fe(this);for(let t=0,o=this.length;t<o;t++)v(n,"get",t+"");const r=n[t](...e);return-1===r||!1===r?n[t](...e.map(fe)):r}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...e){y();const n=fe(this)[t].apply(this,e);return O(),n}}),e}function E(e){const t=fe(this);return v(t,"has",e),t.hasOwnProperty(e)}class F{constructor(e=!1,t=!1){this._isReadonly=e,this._shallow=t}get(e,t,n){const r=this._isReadonly,o=this._shallow;if("__v_isReactive"===t)return!r;if("__v_isReadonly"===t)return r;if("__v_isShallow"===t)return o;if("__v_raw"===t&&n===(r?o?se:oe:o?re:ne).get(e))return e;const s=Object(i["f"])(e);if(!r){if(s&&Object(i["e"])(P,t))return Reflect.get(P,t,n);if("hasOwnProperty"===t)return E}const a=Reflect.get(e,t,n);return(Object(i["k"])(t)?k.has(t):x(t))?a:(r||v(e,"get",t),o?a:be(a)?s&&Object(i["h"])(t)?a:a.value:Object(i["j"])(a)?r?ue(a):ce(a):a)}}class I extends F{constructor(e=!1){super(!1,e)}set(e,t,n,r){let o=e[t];if(le(o)&&be(o)&&!be(n))return!1;if(!this._shallow&&(pe(n)||le(n)||(o=fe(o),n=fe(n)),!Object(i["f"])(e)&&be(o)&&!be(n)))return o.value=n,!0;const s=Object(i["f"])(e)&&Object(i["h"])(t)?Number(t)<e.length:Object(i["e"])(e,t),a=Reflect.set(e,t,n,r);return e===fe(r)&&(s?Object(i["d"])(n,o)&&_(e,"set",t,n,o):_(e,"add",t,n)),a}deleteProperty(e,t){const n=Object(i["e"])(e,t),r=e[t],o=Reflect.deleteProperty(e,t);return o&&n&&_(e,"delete",t,void 0,r),o}has(e,t){const n=Reflect.has(e,t);return Object(i["k"])(t)&&k.has(t)||v(e,"has",t),n}ownKeys(e){return v(e,"iterate",Object(i["f"])(e)?"length":g),Reflect.ownKeys(e)}}class D extends F{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const S=new I,$=new D,A=e=>e,V=e=>Reflect.getPrototypeOf(e);function G(e,t,n=!1,r=!1){e=e["__v_raw"];const o=fe(e),s=fe(t);n||(Object(i["d"])(t,s)&&v(o,"get",t),v(o,"get",s));const{has:a}=V(o),c=r?A:n?me:ge;return a.call(o,t)?c(e.get(t)):a.call(o,s)?c(e.get(s)):void(e!==o&&e.get(t))}function U(e,t=!1){const n=this["__v_raw"],r=fe(n),o=fe(e);return t||(Object(i["d"])(e,o)&&v(r,"has",e),v(r,"has",o)),e===o?n.has(e):n.has(e)||n.has(o)}function T(e,t=!1){return e=e["__v_raw"],!t&&v(fe(e),"iterate",g),Reflect.get(e,"size",e)}function z(e){e=fe(e);const t=fe(this),n=V(t),r=n.has.call(t,e);return r||(t.add(e),_(t,"add",e,e)),this}function B(e,t){t=fe(t);const n=fe(this),{has:r,get:o}=V(n);let s=r.call(n,e);s||(e=fe(e),s=r.call(n,e));const a=o.call(n,e);return n.set(e,t),s?Object(i["d"])(t,a)&&_(n,"set",e,t,a):_(n,"add",e,t),this}function L(e){const t=fe(this),{has:n,get:r}=V(t);let o=n.call(t,e);o||(e=fe(e),o=n.call(t,e));const s=r?r.call(t,e):void 0,i=t.delete(e);return o&&_(t,"delete",e,void 0,s),i}function N(){const e=fe(this),t=0!==e.size,n=void 0,r=e.clear();return t&&_(e,"clear",void 0,void 0,n),r}function W(e,t){return function(n,r){const o=this,s=o["__v_raw"],i=fe(s),a=t?A:e?me:ge;return!e&&v(i,"iterate",g),s.forEach((e,t)=>n.call(r,a(e),a(t),o))}}function q(e,t,n){return function(...r){const o=this["__v_raw"],s=fe(o),a=Object(i["i"])(s),c="entries"===e||e===Symbol.iterator&&a,u="keys"===e&&a,d=o[e](...r),l=n?A:t?me:ge;return!t&&v(s,"iterate",u?m:g),{next(){const{value:e,done:t}=d.next();return t?{value:e,done:t}:{value:c?[l(e[0]),l(e[1])]:l(e),done:t}},[Symbol.iterator](){return this}}}}function K(e){return function(...t){return"delete"!==e&&this}}function Z(){const e={get(e){return G(this,e)},get size(){return T(this)},has:U,add:z,set:B,delete:L,clear:N,forEach:W(!1,!1)},t={get(e){return G(this,e,!1,!0)},get size(){return T(this)},has:U,add:z,set:B,delete:L,clear:N,forEach:W(!1,!0)},n={get(e){return G(this,e,!0)},get size(){return T(this,!0)},has(e){return U.call(this,e,!0)},add:K("add"),set:K("set"),delete:K("delete"),clear:K("clear"),forEach:W(!0,!1)},r={get(e){return G(this,e,!0,!0)},get size(){return T(this,!0)},has(e){return U.call(this,e,!0)},add:K("add"),set:K("set"),delete:K("delete"),clear:K("clear"),forEach:W(!0,!0)},o=["keys","values","entries",Symbol.iterator];return o.forEach(o=>{e[o]=q(o,!1,!1),n[o]=q(o,!0,!1),t[o]=q(o,!1,!0),r[o]=q(o,!0,!0)}),[e,n,t,r]}const[Y,H,J,Q]=Z();function X(e,t){const n=t?e?Q:J:e?H:Y;return(t,r,o)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(Object(i["e"])(n,r)&&r in t?n:t,r,o)}const ee={get:X(!1,!1)},te={get:X(!0,!1)};const ne=new WeakMap,re=new WeakMap,oe=new WeakMap,se=new WeakMap;function ie(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ae(e){return e["__v_skip"]||!Object.isExtensible(e)?0:ie(Object(i["m"])(e))}function ce(e){return le(e)?e:de(e,!1,S,ee,ne)}function ue(e){return de(e,!0,$,te,oe)}function de(e,t,n,r,o){if(!Object(i["j"])(e))return e;if(e["__v_raw"]&&(!t||!e["__v_isReactive"]))return e;const s=o.get(e);if(s)return s;const a=ae(e);if(0===a)return e;const c=new Proxy(e,2===a?r:n);return o.set(e,c),c}function le(e){return!(!e||!e["__v_isReadonly"])}function pe(e){return!(!e||!e["__v_isShallow"])}function fe(e){const t=e&&e["__v_raw"];return t?fe(t):e}function he(e){return Object(i["b"])(e,"__v_skip",!0),e}const ge=e=>Object(i["j"])(e)?ce(e):e,me=e=>Object(i["j"])(e)?ue(e):e;function be(e){return!(!e||!0!==e.__v_isRef)}var we=n("8bd4");class ye{static getComponentDefinition(e,t){let n;return n="string"===typeof e?this.searchForComponentInstance(t,e):{extends:Object(s["defineComponent"])(Object.assign({},e))},n||console.error(`Could not find component with name of ${e}. Is it in Vue.components?`),n.extends?(n.extends.setup&&(n.setup=n.extends.setup),n.extends.props=this.addParamsToProps(n.extends.props)):n.props=this.addParamsToProps(n.props),n}static addParamsToProps(e){return!e||Array.isArray(e)&&-1===e.indexOf("params")?e=["params",...e||[]]:"object"!==typeof e||e.params||(e["params"]={type:Object}),e}static createAndMountComponent(e,t,n,r){const o=ye.getComponentDefinition(e,n);if(!o)return;const{vNode:s,destroy:i,el:a}=this.mount(o,{params:Object.freeze(t)},n,r||{});return{componentInstance:s.component.proxy,element:a,destroy:i}}static mount(e,t,n,r){let o=Object(s["createVNode"])(e,t);o.appContext=n.$.appContext,o.appContext.provides=Object.assign(Object.assign(Object.assign({},r),o.appContext.provides?o.appContext.provides:{}),n.$parent.$options.provide?n.$parent.$options.provide:{});let i=document.createElement("div");Object(s["render"])(o,i);const a=()=>{i&&Object(s["render"])(null,i),i=null,o=null};return{vNode:o,destroy:a,el:i}}static searchForComponentInstance(e,t,n=10,r=!1){let o=null,s=e.$parent,i=0;while(!o&&s&&s.$options&&++i<n){const e=s;o=e.$options&&e.$options.components?e.$options.components[t]:null,s=s.$parent}if(!o){const n=e.$.appContext.components;n&&n[t]&&(o=n[t])}return o||r?o:(console.error(`Could not find component with name of ${t}. Is it in Vue.components?`),null)}}class Oe extends we["BaseComponentWrapper"]{constructor(e,t){super(),this.parent=e,Oe.provides||(Oe.provides=t)}createWrapper(e){const t=this;class n extends ve{init(e){super.init(e)}hasMethod(e){return null!=r.getFrameworkComponentInstance()[e]}callMethod(e,t){const n=this.getFrameworkComponentInstance(),o=r.getFrameworkComponentInstance();return o[e].apply(n,t)}addMethod(e,t){r[e]=t}overrideProcessing(e){return t.parent.autoParamsRefresh&&"refresh"===e}processMethod(e,t){return"refresh"===e&&(this.getFrameworkComponentInstance().params=t[0]),this.hasMethod(e)?this.callMethod(e,t):"refresh"===e}createComponent(n){return t.createComponent(e,n)}}const r=new n;return r}createComponent(e,t){return ye.createAndMountComponent(e,t,this.parent,Oe.provides)}createMethodProxy(e,t,n){return function(){return e.overrideProcessing(t)?e.processMethod(t,arguments):e.hasMethod(t)?e.callMethod(t,arguments):(n&&console.warn("AG Grid: Framework component is missing the method "+t+"()"),null)}}destroy(){this.parent=null}}class ve{getGui(){return this.element}destroy(){this.getFrameworkComponentInstance()&&"function"===typeof this.getFrameworkComponentInstance().destroy&&this.getFrameworkComponentInstance().destroy(),this.unmount()}getFrameworkComponentInstance(){return this.componentInstance}init(e){const{componentInstance:t,element:n,destroy:r}=this.createComponent(e);this.componentInstance=t,this.unmount=r,this.element=n.firstElementChild}}const je=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),_e=e=>`on${e.charAt(0).toUpperCase()}${e.substring(1,e.length)}`,Ce=()=>{const e={},t=we["ComponentUtil"].PUBLIC_EVENTS.map(e=>_e(je(e)));t.forEach(t=>e[t]=void 0);const n={};return we["ComponentUtil"].ALL_PROPERTIES.filter(e=>"gridOptions"!=e).forEach(t=>{e[t]={},n[t]={handler(e,n){this.processChanges(t,e,n)},deep:"popupParent"!==t&&"context"!==t}}),[e,n]};class Re extends we["VanillaFrameworkOverrides"]{constructor(e){super(),this.parent=e}frameworkComponent(e,t){let n=ye.searchForComponentInstance(this.parent,e,10,!0)?e:null;if(!n&&t&&t[e]){const r=t[e];n=ye.searchForComponentInstance(this.parent,r,10,!0)?r:null}return n}isFrameworkComponent(e){return"object"===typeof e}}const xe=new Set(["rowDataChanged","rowDataUpdated","cellValueChanged","rowValueChanged"]),ke=new Set([we["Events"].EVENT_GRID_PRE_DESTROYED]),Pe="onUpdate:modelValue",Me="update:modelValue",[Ee,Fe]=Ce(),Ie=Object(s["defineComponent"])({render(){return Object(s["h"])("div")},props:Object.assign({gridOptions:{type:Object,default:()=>({})},autoParamsRefresh:{type:Boolean,default:()=>!1},componentDependencies:{type:Array,default:()=>[]},plugins:[],modules:{type:Array,default:()=>[]},modelValue:{type:Array,default:void 0,required:!1}},Ee),data(){return{gridCreated:!1,isDestroyed:!1,gridReadyFired:!1,emitRowModel:void 0}},watch:Object.assign({modelValue:{handler(e,t){this.processChanges("rowData",e,t)},deep:!0}},Fe),methods:{globalEventListenerFactory(e){return(t,n)=>{if(this.isDestroyed)return;"gridReady"===t&&(this.gridReadyFired=!0);const r=ke.has(t);r&&!e||!r&&e||this.updateModelIfUsed(t)}},processChanges(e,t,n){if(this.gridCreated){if(this.skipChange(e,t,n))return;const r={};r[e]={currentValue:"rowData"===e?Object.isFrozen(t)?t:he(fe(t)):t,previousValue:n},we["ComponentUtil"].processOnChange(r,this.gridOptions.api)}},checkForBindingConflicts(){const e=this;(e.rowData||this.gridOptions.rowData)&&e.modelValue&&console.warn("AG Grid: Using both rowData and v-model. rowData will be ignored.")},getRowData(){const e=[];return this.gridOptions.api.forEachNode(t=>{e.push(t.data)}),e},updateModelIfUsed(e){this.gridReadyFired&&this.$attrs[Pe]&&xe.has(e)&&this.emitRowModel&&this.emitRowModel()},getRowDataBasedOnBindings(){const e=this,t=e.modelValue;return t||(e.rowData?e.rowData:e.gridOptions.rowData)},getProvides(){let e=Object(s["getCurrentInstance"])(),t={};while(e)e&&e.provides&&(t=Object.assign(Object.assign({},t),e.provides)),e=e.parent;return t},skipChange(e,t,n){if(this.gridReadyFired&&"rowData"===e&&this.$attrs[Pe]){if(t===n)return!0;if(t&&n){const e=t,r=n;if(e.length===r.length){for(let t=0;t<e.length;t++)if(e[t]!==r[t])return!1;return!0}}}return!1},debounce(e,t){let n;return()=>{const r=function(){e()};window.clearTimeout(n),n=window.setTimeout(r,t)}}},mounted(){this.emitRowModel=this.debounce(()=>{this.$emit(Me,Object.freeze(this.getRowData()))},20);const e=this.getProvides(),t=new Oe(this,e),n=he(we["ComponentUtil"].copyAttributesToGridOptions(fe(this.gridOptions),this,!0));this.checkForBindingConflicts();const r=this.getRowDataBasedOnBindings();n.rowData=r?Object.isFrozen(r)?r:he(fe(r)):r;const o={globalEventListener:this.globalEventListenerFactory().bind(this),globalSyncEventListener:this.globalEventListenerFactory(!0).bind(this),frameworkOverrides:new Re(this),providedBeanInstances:{frameworkComponentWrapper:t},modules:this.modules};new we["Grid"](this.$el,n,o),this.gridCreated=!0},unmounted(){this.gridCreated&&(this.gridOptions.api&&this.gridOptions.api.destroy(),this.isDestroyed=!0)}})}})}));
//# sourceMappingURL=ag-grid-vue3.umd.min.js.map