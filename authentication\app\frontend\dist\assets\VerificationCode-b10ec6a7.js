import{_ as e}from"./index-bcbc0702.js";/* empty css                     *//* empty css                 */import{aa as a,r as o,d as l,A as n,aw as i,R as s,B as t,C as r,D as d,H as c,J as u,F as f,G as m,I as p,ax as v,E as g,K as y,ai as k,aj as _,L as w,ak as C}from"./element-plus-3ab68b46.js";const b={class:"verification-code"},h={class:"verification-info"},I={key:0},j={key:1},x={key:2,class:"countdown"},V={class:"verification-actions"};const z=e({name:"VerificationCode",components:{Key:a},props:{email:{type:String,default:""},phone:{type:String,default:""}},emits:["verified","back"],setup(e,{emit:a}){const s=o(null),t=o(!1),r=o(60);let d=null;const c=l({code:""}),u=()=>{r.value=60,d=setInterval(()=>{r.value--,r.value<=0&&(clearInterval(d),d=null)},1e3)};return n(()=>{u()}),i(()=>{d&&clearInterval(d)}),{formRef:s,loading:t,countdown:r,form:c,rules:{code:[{required:!0,message:"请输入验证码",trigger:"blur"},{pattern:/^\d{6}$/,message:"验证码必须是6位数字",trigger:"blur"}]},handleInput:e=>{c.code=e.replace(/\D/g,"")},verifyCode:async()=>{if(s.value)try{await s.value.validate(),t.value=!0,await new Promise(e=>setTimeout(e,1e3)),"123456"===c.code?(g.success("验证成功"),a("verified")):(g.error("验证码错误，请重新输入"),c.code="")}catch(e){}finally{t.value=!1}},resendCode:async()=>{if(!(r.value>0))try{t.value=!0,await new Promise(e=>setTimeout(e,1e3)),g.success("验证码已重新发送"),u()}catch(e){g.error("发送失败，请稍后重试")}finally{t.value=!1}},goBack:()=>{a("back")}}}},[["render",function(e,a,o,l,n,i){const g=s("Key"),z=y,S=k,B=_,K=w,R=C;return t(),r("div",b,[d("div",h,[o.email?(t(),r("p",I,"验证码已发送至："+c(o.email),1)):u("",!0),o.phone?(t(),r("p",j,"验证码已发送至："+c(o.phone),1)):u("",!0),l.countdown>0?(t(),r("p",x,c(l.countdown)+"秒后可重新发送",1)):u("",!0)]),f(R,{model:l.form,rules:l.rules,ref:"formRef",onSubmit:v(l.verifyCode,["prevent"])},{default:m(()=>[f(B,{prop:"code"},{default:m(()=>[f(S,{modelValue:l.form.code,"onUpdate:modelValue":a[0]||(a[0]=e=>l.form.code=e),placeholder:"请输入6位验证码",size:"large",maxlength:"6",disabled:l.loading,onInput:l.handleInput},{prefix:m(()=>[f(z,null,{default:m(()=>[f(g)]),_:1})]),_:1},8,["modelValue","disabled","onInput"])]),_:1}),d("div",V,[f(K,{type:"primary",size:"large",loading:l.loading,onClick:l.verifyCode,disabled:6!==l.form.code.length},{default:m(()=>a[1]||(a[1]=[p(" 验证 ",-1)])),_:1,__:[1]},8,["loading","onClick","disabled"]),f(K,{size:"large",disabled:l.countdown>0,onClick:l.resendCode},{default:m(()=>[p(" 重新发送"+c(l.countdown>0?`(${l.countdown})`:""),1)]),_:1},8,["disabled","onClick"]),f(K,{size:"large",onClick:l.goBack},{default:m(()=>a[2]||(a[2]=[p(" 返回修改 ",-1)])),_:1,__:[2]},8,["onClick"])])]),_:1},8,["model","rules","onSubmit"])])}],["__scopeId","data-v-21f59101"]]);export{z as V};
