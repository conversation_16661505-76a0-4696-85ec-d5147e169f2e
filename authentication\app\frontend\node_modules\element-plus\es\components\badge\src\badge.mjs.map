{"version": 3, "file": "badge.mjs", "sources": ["../../../../../../packages/components/badge/src/badge.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type {\n  ExtractPropTypes,\n  StyleValue,\n  __ExtractPublicPropTypes,\n} from 'vue'\n\nexport const badgeProps = buildProps({\n  /**\n   * @description display value.\n   */\n  value: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description maximum value, shows `{max}+` when exceeded. Only works if value is a number.\n   */\n  max: {\n    type: Number,\n    default: 99,\n  },\n  /**\n   * @description if a little dot is displayed.\n   */\n  isDot: Boolean,\n  /**\n   * @description hidden badge.\n   */\n  hidden: Boolean,\n  /**\n   * @description badge type.\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'warning', 'info', 'danger'],\n    default: 'danger',\n  },\n  /**\n   * @description whether to show badge when value is zero.\n   */\n  showZero: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description customize dot background color\n   */\n  color: String,\n  /**\n   * @description CSS style of badge\n   */\n  badgeStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n  },\n  /**\n   * @description set offset of the badge\n   */\n  offset: {\n    type: definePropType<[number, number]>(Array),\n    default: [0, 0],\n  },\n  /**\n   * @description custom class name of badge\n   */\n  badgeClass: {\n    type: String,\n  },\n} as const)\nexport type BadgeProps = ExtractPropTypes<typeof badgeProps>\nexport type BadgePropsPublic = __ExtractPublicPropTypes<typeof badgeProps>\n"], "names": [], "mappings": ";;AACY,MAAC,UAAU,GAAG,UAAU,CAAC;AACrC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,MAAM,EAAE,OAAO;AACjB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC;AAC/D,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACnB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,CAAC;;;;"}