{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/cascader/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Cascader from './src/cascader.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElCascader: SFCWithInstall<typeof Cascader> = withInstall(Cascader)\n\nexport default ElCascader\n\nexport * from './src/cascader'\nexport * from './src/instances'\n"], "names": ["withInstall", "<PERSON>r"], "mappings": ";;;;;;;;AAEY,MAAC,UAAU,GAAGA,mBAAW,CAACC,qBAAQ;;;;;;;"}