{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-prevent-global/index.ts"], "sourcesContent": ["import { watch } from 'vue'\nimport { useEventListener } from '@vueuse/core'\n\nimport type { Ref } from 'vue'\n\nexport const usePreventGlobal = <E extends keyof DocumentEventMap>(\n  indicator: Ref<boolean>,\n  evt: E,\n  cb: (e: DocumentEventMap[E]) => boolean\n) => {\n  const prevent = (e: DocumentEventMap[E]) => {\n    if (cb(e)) e.stopImmediatePropagation()\n  }\n  let stop: (() => void) | undefined = undefined\n  watch(\n    () => indicator.value,\n    (val) => {\n      if (val) {\n        stop = useEventListener(document, evt, prevent, true)\n      } else {\n        stop?.()\n      }\n    },\n    { immediate: true }\n  )\n}\n"], "names": [], "mappings": ";;;AAEY,MAAC,gBAAgB,GAAG,CAAC,SAAS,EAAE,GAAG,EAAE,EAAE,KAAK;AACxD,EAAE,MAAM,OAAO,GAAG,CAAC,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;AACb,MAAM,CAAC,CAAC,wBAAwB,EAAE,CAAC;AACnC,GAAG,CAAC;AACJ,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;AACpB,EAAE,KAAK,CAAC,MAAM,SAAS,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK;AACxC,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC5D,KAAK,MAAM;AACX,MAAM,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC;AACrC,KAAK;AACL,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1B;;;;"}