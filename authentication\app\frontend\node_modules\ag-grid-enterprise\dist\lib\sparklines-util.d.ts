export * from './util/value';
export * from './util/id';
export * from './util/padding';
export * from './util/json';
export * from './util/angle';
export { extent, normalisedExtent } from './util/array';
export { toFixed, isEqual as isNumberEqual } from './util/number';
export { tickFormat } from './util/numberFormat';
export { interpolate as interpolateString } from './util/string';
export * from './util/sanitize';
import ticks from './util/ticks';
export { ticks };
export { Color } from './util/color';
export type { MeasuredLabel, PointLabelDatum } from './util/labelPlacement';
export * from './util/logger';
//# sourceMappingURL=sparklines-util.d.ts.map