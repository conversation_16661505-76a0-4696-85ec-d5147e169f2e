{"version": 3, "file": "useDragNode.js", "sources": ["../../../../../../../packages/components/tree/src/model/useDragNode.ts"], "sourcesContent": ["import { provide, ref } from 'vue'\nimport { addClass, isFunction, removeClass } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { InjectionKey, Ref, SetupContext } from 'vue'\nimport type {\n  AllowDragFunction,\n  AllowDropFunction,\n  FakeNode,\n  NodeDropType,\n} from '../tree.type'\nimport type TreeStore from './tree-store'\nimport type Node from './node'\n\ninterface TreeNode {\n  node: Node\n  $el?: HTMLElement\n}\n\ninterface DragOptions {\n  event: DragEvent\n  treeNode: TreeNode\n}\n\ninterface Props {\n  props: {\n    allowDrag?: AllowDragFunction\n    allowDrop?: AllowDropFunction\n  }\n  ctx: SetupContext<string[]>\n  el$: Ref<HTMLElement | null>\n  dropIndicator$: Ref<HTMLElement | null>\n  store: Ref<TreeStore>\n}\n\nexport interface DragEvents {\n  treeNodeDragStart: (options: DragOptions) => void\n  treeNodeDragOver: (options: DragOptions) => void\n  treeNodeDragEnd: (event: DragEvent) => void\n}\n\nexport const dragEventsKey: InjectionKey<DragEvents> = Symbol('dragEvents')\n\nexport function useDragNodeHandler({\n  props,\n  ctx,\n  el$,\n  dropIndicator$,\n  store,\n}: Props) {\n  const ns = useNamespace('tree')\n  const dragState = ref<{\n    allowDrop: boolean\n    dropType: NodeDropType | null\n    draggingNode: TreeNode | null\n    showDropIndicator: boolean\n    dropNode: TreeNode | null\n  }>({\n    showDropIndicator: false,\n    draggingNode: null,\n    dropNode: null,\n    allowDrop: true,\n    dropType: null,\n  })\n\n  const treeNodeDragStart = ({ event, treeNode }: DragOptions) => {\n    if (!event.dataTransfer) return\n    if (isFunction(props.allowDrag) && !props.allowDrag(treeNode.node)) {\n      event.preventDefault()\n      return false\n    }\n    event.dataTransfer.effectAllowed = 'move'\n\n    // wrap in try catch to address IE's error when first param is 'text/plain'\n    try {\n      // setData is required for draggable to work in FireFox\n      // the content has to be '' so dragging a node out of the tree won't open a new tab in FireFox\n      event.dataTransfer.setData('text/plain', '')\n    } catch {}\n    dragState.value.draggingNode = treeNode\n    ctx.emit('node-drag-start', treeNode.node, event)\n  }\n\n  const treeNodeDragOver = ({ event, treeNode }: DragOptions) => {\n    if (!event.dataTransfer) return\n    const dropNode = treeNode\n    const oldDropNode = dragState.value.dropNode\n    if (oldDropNode && oldDropNode.node.id !== dropNode.node.id) {\n      removeClass(oldDropNode.$el!, ns.is('drop-inner'))\n    }\n    const draggingNode = dragState.value.draggingNode\n    if (!draggingNode || !dropNode) return\n\n    let dropPrev = true\n    let dropInner = true\n    let dropNext = true\n    let userAllowDropInner = true\n    if (isFunction(props.allowDrop)) {\n      dropPrev = props.allowDrop(draggingNode.node, dropNode.node, 'prev')\n      userAllowDropInner = dropInner = props.allowDrop(\n        draggingNode.node,\n        dropNode.node,\n        'inner'\n      )\n      dropNext = props.allowDrop(draggingNode.node, dropNode.node, 'next')\n    }\n    event.dataTransfer.dropEffect =\n      dropInner || dropPrev || dropNext ? 'move' : 'none'\n    if (\n      (dropPrev || dropInner || dropNext) &&\n      oldDropNode?.node.id !== dropNode.node.id\n    ) {\n      if (oldDropNode) {\n        ctx.emit('node-drag-leave', draggingNode.node, oldDropNode.node, event)\n      }\n      ctx.emit('node-drag-enter', draggingNode.node, dropNode.node, event)\n    }\n\n    if (dropPrev || dropInner || dropNext) {\n      dragState.value.dropNode = dropNode\n    } else {\n      // Reset dragState.value.dropNode to null when allowDrop is transfer from true to false.(For issue #14704)\n      dragState.value.dropNode = null\n    }\n\n    if (dropNode.node.nextSibling === draggingNode.node) {\n      dropNext = false\n    }\n    if (dropNode.node.previousSibling === draggingNode.node) {\n      dropPrev = false\n    }\n    if (dropNode.node.contains(draggingNode.node, false)) {\n      dropInner = false\n    }\n    if (\n      draggingNode.node === dropNode.node ||\n      draggingNode.node.contains(dropNode.node)\n    ) {\n      dropPrev = false\n      dropInner = false\n      dropNext = false\n    }\n    const dropEl = dropNode.$el!\n\n    // find target node without children, just calc content node height\n    const targetPosition = dropEl\n      .querySelector(`.${ns.be('node', 'content')}`)!\n      .getBoundingClientRect()\n    const treePosition = el$.value!.getBoundingClientRect()\n\n    let dropType: NodeDropType\n    const prevPercent = dropPrev ? (dropInner ? 0.25 : dropNext ? 0.45 : 1) : -1\n    const nextPercent = dropNext ? (dropInner ? 0.75 : dropPrev ? 0.55 : 0) : 1\n\n    let indicatorTop = -9999\n    const distance = event.clientY - targetPosition.top\n    if (distance < targetPosition.height * prevPercent) {\n      dropType = 'before'\n    } else if (distance > targetPosition.height * nextPercent) {\n      dropType = 'after'\n    } else if (dropInner) {\n      dropType = 'inner'\n    } else {\n      dropType = 'none'\n    }\n\n    const iconPosition = dropEl\n      .querySelector(`.${ns.be('node', 'expand-icon')}`)!\n      .getBoundingClientRect()\n    const dropIndicator = dropIndicator$.value\n    if (dropType === 'before') {\n      indicatorTop = iconPosition.top - treePosition.top\n    } else if (dropType === 'after') {\n      indicatorTop = iconPosition.bottom - treePosition.top\n    }\n    dropIndicator!.style.top = `${indicatorTop}px`\n    dropIndicator!.style.left = `${iconPosition.right - treePosition.left}px`\n\n    if (dropType === 'inner') {\n      addClass(dropEl, ns.is('drop-inner'))\n    } else {\n      removeClass(dropEl, ns.is('drop-inner'))\n    }\n\n    dragState.value.showDropIndicator =\n      dropType === 'before' || dropType === 'after'\n    dragState.value.allowDrop =\n      dragState.value.showDropIndicator || userAllowDropInner\n    dragState.value.dropType = dropType\n    ctx.emit('node-drag-over', draggingNode.node, dropNode.node, event)\n  }\n\n  const treeNodeDragEnd = (event: DragEvent) => {\n    const { draggingNode, dropType, dropNode } = dragState.value\n    event.preventDefault()\n\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1911486\n    if (event.dataTransfer) {\n      event.dataTransfer.dropEffect = 'move'\n    }\n\n    if (draggingNode?.node.data && dropNode) {\n      const draggingNodeCopy: FakeNode = { data: draggingNode.node.data }\n      if (dropType !== 'none') {\n        draggingNode.node.remove()\n      }\n      if (dropType === 'before') {\n        dropNode.node.parent?.insertBefore(draggingNodeCopy, dropNode.node)\n      } else if (dropType === 'after') {\n        dropNode.node.parent?.insertAfter(draggingNodeCopy, dropNode.node)\n      } else if (dropType === 'inner') {\n        dropNode.node.insertChild(draggingNodeCopy)\n      }\n      if (dropType !== 'none') {\n        store.value.registerNode(draggingNodeCopy as any)\n        if (store.value.key) {\n          //restore checkbox state after dragging\n          draggingNode.node.eachNode((node) => {\n            store.value.nodesMap[node.data[store.value.key]]?.setChecked(\n              node.checked,\n              !store.value.checkStrictly\n            )\n          })\n        }\n      }\n\n      removeClass(dropNode.$el!, ns.is('drop-inner'))\n\n      ctx.emit(\n        'node-drag-end',\n        draggingNode.node,\n        dropNode.node,\n        dropType,\n        event\n      )\n      if (dropType !== 'none') {\n        ctx.emit('node-drop', draggingNode.node, dropNode.node, dropType, event)\n      }\n    }\n    if (draggingNode && !dropNode) {\n      ctx.emit('node-drag-end', draggingNode.node, null, dropType, event)\n    }\n\n    dragState.value.showDropIndicator = false\n    dragState.value.draggingNode = null\n    dragState.value.dropNode = null\n    dragState.value.allowDrop = true\n  }\n\n  provide(dragEventsKey, {\n    treeNodeDragStart,\n    treeNodeDragOver,\n    treeNodeDragEnd,\n  })\n\n  return {\n    dragState,\n  }\n}\n"], "names": ["useNamespace", "ref", "isFunction", "removeClass", "addClass", "provide"], "mappings": ";;;;;;;;;AAGY,MAAC,aAAa,GAAG,MAAM,CAAC,YAAY,EAAE;AAC3C,SAAS,kBAAkB,CAAC;AACnC,EAAE,KAAK;AACP,EAAE,GAAG;AACL,EAAE,GAAG;AACL,EAAE,cAAc;AAChB,EAAE,KAAK;AACP,CAAC,EAAE;AACH,EAAE,MAAM,EAAE,GAAGA,kBAAY,CAAC,MAAM,CAAC,CAAC;AAClC,EAAE,MAAM,SAAS,GAAGC,OAAG,CAAC;AACxB,IAAI,iBAAiB,EAAE,KAAK;AAC5B,IAAI,YAAY,EAAE,IAAI;AACtB,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,SAAS,EAAE,IAAI;AACnB,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,iBAAiB,GAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK;AACrD,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY;AAC3B,MAAM,OAAO;AACb,IAAI,IAAIC,iBAAU,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACxE,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC;AAC7B,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,KAAK,CAAC,YAAY,CAAC,aAAa,GAAG,MAAM,CAAC;AAC9C,IAAI,IAAI;AACR,MAAM,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AACnD,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,KAAK;AACL,IAAI,SAAS,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC;AAC5C,IAAI,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACtD,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK;AACpD,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY;AAC3B,MAAM,OAAO;AACb,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC;AAC9B,IAAI,MAAM,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC;AACjD,IAAI,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,KAAK,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE;AACjE,MAAMC,iBAAW,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,YAAY,CAAC;AACtD,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,QAAQ;AAClC,MAAM,OAAO;AACb,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,kBAAkB,GAAG,IAAI,CAAC;AAClC,IAAI,IAAID,iBAAU,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;AACrC,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC3E,MAAM,kBAAkB,GAAG,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAClG,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC3E,KAAK;AACL,IAAI,KAAK,CAAC,YAAY,CAAC,UAAU,GAAG,SAAS,IAAI,QAAQ,IAAI,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;AACxF,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,QAAQ,KAAK,CAAC,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE;AAC1H,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAChF,OAAO;AACP,MAAM,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC3E,KAAK;AACL,IAAI,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,EAAE;AAC3C,MAAM,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC1C,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AACtC,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,KAAK,YAAY,CAAC,IAAI,EAAE;AACzD,MAAM,QAAQ,GAAG,KAAK,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,eAAe,KAAK,YAAY,CAAC,IAAI,EAAE;AAC7D,MAAM,QAAQ,GAAG,KAAK,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;AAC1D,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,YAAY,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC1F,MAAM,QAAQ,GAAG,KAAK,CAAC;AACvB,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,MAAM,QAAQ,GAAG,KAAK,CAAC;AACvB,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC;AAChC,IAAI,MAAM,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;AACxG,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;AAC3D,IAAI,IAAI,QAAQ,CAAC;AACjB,IAAI,MAAM,WAAW,GAAG,QAAQ,GAAG,SAAS,GAAG,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/E,IAAI,MAAM,WAAW,GAAG,QAAQ,GAAG,SAAS,GAAG,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9E,IAAI,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC;AAC7B,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC;AACxD,IAAI,IAAI,QAAQ,GAAG,cAAc,CAAC,MAAM,GAAG,WAAW,EAAE;AACxD,MAAM,QAAQ,GAAG,QAAQ,CAAC;AAC1B,KAAK,MAAM,IAAI,QAAQ,GAAG,cAAc,CAAC,MAAM,GAAG,WAAW,EAAE;AAC/D,MAAM,QAAQ,GAAG,OAAO,CAAC;AACzB,KAAK,MAAM,IAAI,SAAS,EAAE;AAC1B,MAAM,QAAQ,GAAG,OAAO,CAAC;AACzB,KAAK,MAAM;AACX,MAAM,QAAQ,GAAG,MAAM,CAAC;AACxB,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC;AAC1G,IAAI,MAAM,aAAa,GAAG,cAAc,CAAC,KAAK,CAAC;AAC/C,IAAI,IAAI,QAAQ,KAAK,QAAQ,EAAE;AAC/B,MAAM,YAAY,GAAG,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC;AACzD,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,EAAE;AACrC,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC;AAC5D,KAAK;AACL,IAAI,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7E,IAAI,IAAI,QAAQ,KAAK,OAAO,EAAE;AAC9B,MAAME,cAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;AAC5C,KAAK,MAAM;AACX,MAAMD,iBAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,SAAS,CAAC,KAAK,CAAC,iBAAiB,GAAG,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,OAAO,CAAC;AACtF,IAAI,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,iBAAiB,IAAI,kBAAkB,CAAC;AACxF,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACxC,IAAI,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACxE,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,KAAK,KAAK;AACrC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC;AACjE,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;AAC3B,IAAI,IAAI,KAAK,CAAC,YAAY,EAAE;AAC5B,MAAM,KAAK,CAAC,YAAY,CAAC,UAAU,GAAG,MAAM,CAAC;AAC7C,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC9E,MAAM,MAAM,gBAAgB,GAAG,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;AAChE,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;AAC/B,QAAQ,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AACnC,OAAO;AACP,MAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACjC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;AACxG,OAAO,MAAM,IAAI,QAAQ,KAAK,OAAO,EAAE;AACvC,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,gBAAgB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvG,OAAO,MAAM,IAAI,QAAQ,KAAK,OAAO,EAAE;AACvC,QAAQ,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;AACpD,OAAO;AACP,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;AAC/B,QAAQ,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;AACnD,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE;AAC7B,UAAU,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK;AAC/C,YAAY,IAAI,GAAG,CAAC;AACpB,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACjJ,WAAW,CAAC,CAAC;AACb,SAAS;AACT,OAAO;AACP,MAAMA,iBAAW,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;AACrD,MAAM,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACnF,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;AAC/B,QAAQ,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACjF,OAAO;AACP,KAAK;AACL,IAAI,IAAI,YAAY,IAAI,CAAC,QAAQ,EAAE;AACnC,MAAM,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC1E,KAAK;AACL,IAAI,SAAS,CAAC,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;AAC9C,IAAI,SAAS,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;AACxC,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AACpC,IAAI,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;AACrC,GAAG,CAAC;AACJ,EAAEE,WAAO,CAAC,aAAa,EAAE;AACzB,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,SAAS;AACb,GAAG,CAAC;AACJ;;;;;"}