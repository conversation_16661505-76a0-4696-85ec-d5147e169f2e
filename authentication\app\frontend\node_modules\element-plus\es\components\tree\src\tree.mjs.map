{"version": 3, "file": "tree.mjs", "sources": ["../../../../../../packages/components/tree/src/tree.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"el$\"\n    :class=\"[\n      ns.b(),\n      ns.is('dragging', !!dragState.draggingNode),\n      ns.is('drop-not-allow', !dragState.allowDrop),\n      ns.is('drop-inner', dragState.dropType === 'inner'),\n      { [ns.m('highlight-current')]: highlightCurrent },\n    ]\"\n    role=\"tree\"\n  >\n    <el-tree-node\n      v-for=\"child in root.childNodes\"\n      :key=\"getNodeKey(child)\"\n      :node=\"child\"\n      :props=\"props\"\n      :accordion=\"accordion\"\n      :render-after-expand=\"renderAfterExpand\"\n      :show-checkbox=\"showCheckbox\"\n      :render-content=\"renderContent\"\n      @node-expand=\"handleNodeExpand\"\n    />\n    <div v-if=\"isEmpty\" :class=\"ns.e('empty-block')\">\n      <slot name=\"empty\">\n        <span :class=\"ns.e('empty-text')\">\n          {{ emptyText ?? t('el.tree.emptyText') }}\n        </span>\n      </slot>\n    </div>\n    <div\n      v-show=\"dragState.showDropIndicator\"\n      ref=\"dropIndicator$\"\n      :class=\"ns.e('drop-indicator')\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  inject,\n  provide,\n  ref,\n  watch,\n} from 'vue'\nimport { definePropType, iconPropType } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { formItemContextKey } from '@element-plus/components/form'\nimport { selectKey } from '@element-plus/components/select/src/token'\nimport TreeStore from './model/tree-store'\nimport { getNodeKey as getNodeKeyUtil, handleCurrentChange } from './model/util'\nimport ElTreeNode from './tree-node.vue'\nimport { useNodeExpandEventBroadcast } from './model/useNodeExpandEventBroadcast'\nimport { useDragNodeHandler } from './model/useDragNode'\nimport { useKeydown } from './model/useKeydown'\nimport { ROOT_TREE_INJECTION_KEY } from './tokens'\nimport { isEqual } from 'lodash-unified'\n\nimport type Node from './model/node'\nimport type { ComponentInternalInstance, PropType } from 'vue'\nimport type { Nullable } from '@element-plus/utils'\nimport type {\n  AllowDragFunction,\n  AllowDropFunction,\n  FilterValue,\n  RenderContentFunction,\n  TreeComponentProps,\n  TreeData,\n  TreeKey,\n  TreeNodeData,\n} from './tree.type'\n\nexport default defineComponent({\n  name: 'ElTree',\n  components: { ElTreeNode },\n  props: {\n    data: {\n      type: definePropType<TreeData>(Array),\n      default: () => [],\n    },\n    emptyText: {\n      type: String,\n    },\n    renderAfterExpand: {\n      type: Boolean,\n      default: true,\n    },\n    nodeKey: String,\n    checkStrictly: Boolean,\n    defaultExpandAll: Boolean,\n    expandOnClickNode: {\n      type: Boolean,\n      default: true,\n    },\n    checkOnClickNode: Boolean,\n    checkOnClickLeaf: {\n      type: Boolean,\n      default: true,\n    },\n    checkDescendants: Boolean,\n    autoExpandParent: {\n      type: Boolean,\n      default: true,\n    },\n    defaultCheckedKeys: Array as PropType<\n      TreeComponentProps['defaultCheckedKeys']\n    >,\n    defaultExpandedKeys: Array as PropType<\n      TreeComponentProps['defaultExpandedKeys']\n    >,\n    currentNodeKey: [String, Number] as PropType<string | number>,\n    renderContent: {\n      type: definePropType<RenderContentFunction>(Function),\n    },\n    showCheckbox: Boolean,\n    draggable: Boolean,\n    allowDrag: {\n      type: definePropType<AllowDragFunction>(Function),\n    },\n    allowDrop: {\n      type: definePropType<AllowDropFunction>(Function),\n    },\n    props: {\n      type: Object as PropType<TreeComponentProps['props']>,\n      default: () => ({\n        children: 'children',\n        label: 'label',\n        disabled: 'disabled',\n      }),\n    },\n    lazy: Boolean,\n    highlightCurrent: Boolean,\n    load: Function as PropType<TreeComponentProps['load']>,\n    filterNodeMethod: Function as PropType<\n      TreeComponentProps['filterNodeMethod']\n    >,\n    accordion: Boolean,\n    indent: {\n      type: Number,\n      default: 18,\n    },\n    icon: {\n      type: iconPropType,\n    },\n  },\n  emits: [\n    'check-change',\n    'current-change',\n    'node-click',\n    'node-contextmenu',\n    'node-collapse',\n    'node-expand',\n    'check',\n    'node-drag-start',\n    'node-drag-end',\n    'node-drop',\n    'node-drag-leave',\n    'node-drag-enter',\n    'node-drag-over',\n  ] as string[],\n  setup(props, ctx) {\n    const { t } = useLocale()\n    const ns = useNamespace('tree')\n    const selectInfo = inject(selectKey, null)\n\n    const store = ref<TreeStore>(\n      new TreeStore({\n        key: props.nodeKey,\n        data: props.data,\n        lazy: props.lazy,\n        props: props.props,\n        load: props.load,\n        currentNodeKey: props.currentNodeKey,\n        checkStrictly: props.checkStrictly,\n        checkDescendants: props.checkDescendants,\n        defaultCheckedKeys: props.defaultCheckedKeys,\n        defaultExpandedKeys: props.defaultExpandedKeys,\n        autoExpandParent: props.autoExpandParent,\n        defaultExpandAll: props.defaultExpandAll,\n        filterNodeMethod: props.filterNodeMethod,\n      })\n    )\n\n    store.value.initialize()\n\n    const root = ref<Node>(store.value.root)\n    const currentNode = ref<Node | null>(null)\n    const el$ = ref<Nullable<HTMLElement>>(null)\n    const dropIndicator$ = ref<Nullable<HTMLElement>>(null)\n\n    const { broadcastExpanded } = useNodeExpandEventBroadcast(props)\n\n    const { dragState } = useDragNodeHandler({\n      props,\n      ctx,\n      el$,\n      dropIndicator$,\n      store,\n    })\n\n    useKeydown({ el$ }, store)\n\n    const isEmpty = computed(() => {\n      const { childNodes } = root.value\n      const hasFilteredOptions = selectInfo\n        ? (selectInfo as any).hasFilteredOptions !== 0\n        : false\n      return (\n        (!childNodes ||\n          childNodes.length === 0 ||\n          childNodes.every(({ visible }) => !visible)) &&\n        !hasFilteredOptions\n      )\n    })\n\n    watch(\n      () => props.currentNodeKey,\n      (newVal) => {\n        store.value.setCurrentNodeKey(newVal ?? null)\n      }\n    )\n\n    watch(\n      () => props.defaultCheckedKeys,\n      (newVal, oldVal) => {\n        if (isEqual(newVal, oldVal)) return\n\n        store.value.setDefaultCheckedKey(newVal ?? [])\n      }\n    )\n\n    watch(\n      () => props.defaultExpandedKeys,\n      (newVal) => {\n        store.value.setDefaultExpandedKeys(newVal ?? [])\n      }\n    )\n\n    watch(\n      () => props.data,\n      (newVal) => {\n        store.value.setData(newVal)\n      },\n      { deep: true }\n    )\n\n    watch(\n      () => props.checkStrictly,\n      (newVal) => {\n        store.value.checkStrictly = newVal\n      }\n    )\n\n    const filter = (value: FilterValue) => {\n      if (!props.filterNodeMethod)\n        throw new Error('[Tree] filterNodeMethod is required when filter')\n      store.value.filter(value)\n    }\n\n    const getNodeKey = (node: Node) => {\n      return getNodeKeyUtil(props.nodeKey, node.data)\n    }\n\n    const getNodePath = (data: TreeKey | TreeNodeData) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in getNodePath')\n      const node = store.value.getNode(data)\n      if (!node) return []\n      const path = [node.data]\n      let parent = node.parent\n      while (parent && parent !== root.value) {\n        path.push(parent.data)\n        parent = parent.parent\n      }\n      return path.reverse()\n    }\n\n    const getCheckedNodes = (\n      leafOnly?: boolean,\n      includeHalfChecked?: boolean\n    ): TreeNodeData[] => {\n      return store.value.getCheckedNodes(leafOnly, includeHalfChecked)\n    }\n\n    const getCheckedKeys = (leafOnly?: boolean): TreeKey[] => {\n      return store.value.getCheckedKeys(leafOnly)\n    }\n\n    const getCurrentNode = () => {\n      const currentNode = store.value.getCurrentNode()\n      return currentNode ? currentNode.data : null\n    }\n\n    const getCurrentKey = (): TreeKey | null => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in getCurrentKey')\n      const currentNode = getCurrentNode()\n      return currentNode ? currentNode[props.nodeKey] : null\n    }\n\n    const setCheckedNodes = (nodes: Node[], leafOnly?: boolean) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCheckedNodes')\n      store.value.setCheckedNodes(nodes, leafOnly)\n    }\n\n    const setCheckedKeys = (keys: TreeKey[], leafOnly?: boolean) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCheckedKeys')\n      store.value.setCheckedKeys(keys, leafOnly)\n    }\n\n    const setChecked = (\n      data: TreeKey | TreeNodeData,\n      checked: boolean,\n      deep: boolean\n    ) => {\n      store.value.setChecked(data, checked, deep)\n    }\n\n    const getHalfCheckedNodes = (): TreeNodeData[] => {\n      return store.value.getHalfCheckedNodes()\n    }\n\n    const getHalfCheckedKeys = (): TreeKey[] => {\n      return store.value.getHalfCheckedKeys()\n    }\n\n    const setCurrentNode = (node: Node, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCurrentNode')\n\n      handleCurrentChange(store, ctx.emit, () => {\n        broadcastExpanded(node)\n        store.value.setUserCurrentNode(node, shouldAutoExpandParent)\n      })\n    }\n\n    const setCurrentKey = (key?: TreeKey, shouldAutoExpandParent = true) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in setCurrentKey')\n\n      handleCurrentChange(store, ctx.emit, () => {\n        broadcastExpanded()\n        store.value.setCurrentNodeKey(key ?? null, shouldAutoExpandParent)\n      })\n    }\n\n    const getNode = (data: TreeKey | TreeNodeData): Node => {\n      return store.value.getNode(data)\n    }\n\n    const remove = (data: TreeNodeData | Node) => {\n      store.value.remove(data)\n    }\n\n    const append = (\n      data: TreeNodeData,\n      parentNode: TreeNodeData | TreeKey | Node\n    ) => {\n      store.value.append(data, parentNode)\n    }\n\n    const insertBefore = (\n      data: TreeNodeData,\n      refNode: TreeKey | TreeNodeData | Node\n    ) => {\n      store.value.insertBefore(data, refNode)\n    }\n\n    const insertAfter = (\n      data: TreeNodeData,\n      refNode: TreeKey | TreeNodeData | Node\n    ) => {\n      store.value.insertAfter(data, refNode)\n    }\n\n    const handleNodeExpand = (\n      nodeData: TreeNodeData,\n      node: Node,\n      instance: ComponentInternalInstance\n    ) => {\n      broadcastExpanded(node)\n      ctx.emit('node-expand', nodeData, node, instance)\n    }\n\n    const updateKeyChildren = (key: TreeKey, data: TreeData) => {\n      if (!props.nodeKey)\n        throw new Error('[Tree] nodeKey is required in updateKeyChild')\n      store.value.updateChildren(key, data)\n    }\n\n    provide(ROOT_TREE_INJECTION_KEY, {\n      ctx,\n      props,\n      store,\n      root,\n      currentNode,\n      instance: getCurrentInstance(),\n    })\n\n    provide(formItemContextKey, undefined)\n\n    return {\n      ns,\n      // ref\n      store,\n      root,\n      currentNode,\n      dragState,\n      el$,\n      dropIndicator$,\n\n      // computed\n      isEmpty,\n\n      // methods\n      filter,\n      getNodeKey,\n      getNodePath,\n      getCheckedNodes,\n      getCheckedKeys,\n      getCurrentNode,\n      getCurrentKey,\n      setCheckedNodes,\n      setCheckedKeys,\n      setChecked,\n      getHalfCheckedNodes,\n      getHalfCheckedKeys,\n      setCurrentNode,\n      setCurrentKey,\n      t,\n      getNode,\n      remove,\n      append,\n      insertBefore,\n      insertAfter,\n      handleNodeExpand,\n      updateKeyChildren,\n    }\n  },\n})\n</script>\n"], "names": ["getNodeKeyUtil", "currentNode", "getNodeKey", "_resolveComponent", "_openBlock", "_createElementBlock", "_normalizeClass", "_Fragment", "_renderList", "_createBlock", "_renderSlot", "_createElementVNode", "_createCommentVNode", "_withDirectives", "_vShow"], "mappings": ";;;;;;;;;;;;;;;;;AA2EA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,QAAA;AAAA,EACN,UAAA,EAAY,EAAE,UAAW,EAAA;AAAA,EACzB,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAA,EAAM,eAAyB,KAAK,CAAA;AAAA,MACpC,OAAA,EAAS,MAAM,EAAC;AAAA,KAClB;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAM,EAAA,MAAA;AAAA,KACR;AAAA,IACA,iBAAmB,EAAA;AAAA,MACjB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,OAAS,EAAA,MAAA;AAAA,IACT,aAAe,EAAA,OAAA;AAAA,IACf,gBAAkB,EAAA,OAAA;AAAA,IAClB,iBAAmB,EAAA;AAAA,MACjB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,gBAAkB,EAAA,OAAA;AAAA,IAClB,gBAAkB,EAAA;AAAA,MAChB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,gBAAkB,EAAA,OAAA;AAAA,IAClB,gBAAkB,EAAA;AAAA,MAChB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,IACA,kBAAoB,EAAA,KAAA;AAAA,IAGpB,mBAAqB,EAAA,KAAA;AAAA,IAGrB,cAAA,EAAgB,CAAC,MAAA,EAAQ,MAAM,CAAA;AAAA,IAC/B,aAAe,EAAA;AAAA,MACb,IAAA,EAAM,eAAsC,QAAQ,CAAA;AAAA,KACtD;AAAA,IACA,YAAc,EAAA,OAAA;AAAA,IACd,SAAW,EAAA,OAAA;AAAA,IACX,SAAW,EAAA;AAAA,MACT,IAAA,EAAM,eAAkC,QAAQ,CAAA;AAAA,KAClD;AAAA,IACA,SAAW,EAAA;AAAA,MACT,IAAA,EAAM,eAAkC,QAAQ,CAAA;AAAA,KAClD;AAAA,IACA,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,SAAS,OAAO;AAAA,QACd,QAAU,EAAA,UAAA;AAAA,QACV,KAAO,EAAA,OAAA;AAAA,QACP,QAAU,EAAA,UAAA;AAAA,OACZ,CAAA;AAAA,KACF;AAAA,IACA,IAAM,EAAA,OAAA;AAAA,IACN,gBAAkB,EAAA,OAAA;AAAA,IAClB,IAAM,EAAA,QAAA;AAAA,IACN,gBAAkB,EAAA,QAAA;AAAA,IAGlB,SAAW,EAAA,OAAA;AAAA,IACX,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,MAAA;AAAA,MACN,OAAS,EAAA,EAAA;AAAA,KACX;AAAA,IACA,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,YAAA;AAAA,KACR;AAAA,GACF;AAAA,EACA,KAAO,EAAA;AAAA,IACL,cAAA;AAAA,IACA,gBAAA;AAAA,IACA,YAAA;AAAA,IACA,kBAAA;AAAA,IACA,eAAA;AAAA,IACA,aAAA;AAAA,IACA,OAAA;AAAA,IACA,iBAAA;AAAA,IACA,eAAA;AAAA,IACA,WAAA;AAAA,IACA,iBAAA;AAAA,IACA,iBAAA;AAAA,IACA,gBAAA;AAAA,GACF;AAAA,EACA,KAAA,CAAM,OAAO,GAAK,EAAA;AAChB,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA,UAAA,GAAa,MAAO,CAAA,SAAA,EAAW,IAAI,CAAA,CAAA;AAEzC,IAAA,MAAM,KAAQ,GAAA,GAAA,CAAA,IAAA,SAAA,CAAA;AAAA,MACZ,KAAc,KAAA,CAAA,OAAA;AAAA,MAAA,WACD,CAAA,IAAA;AAAA,MAAA,WACC,CAAA,IAAA;AAAA,MAAA,YACA,CAAA,KAAA;AAAA,MAAA,WACC,CAAA,IAAA;AAAA,MAAA,cACD,EAAA,KAAA,CAAA,cAAA;AAAA,MAAA,oBACU,CAAA,aAAA;AAAA,MAAA,kBACD,KAAA,CAAA,gBAAA;AAAA,MAAA,oBACH,KAAM,CAAA,kBAAA;AAAA,MAAA,0BACE,CAAA,mBAAA;AAAA,MAAA,uBACL,CAAM,gBAAA;AAAA,MAAA,uBACH,CAAA,gBAAA;AAAA,MAAA,uBACA,CAAA,gBAAA;AAAA,KAAA,CAAA,CAAA,CACxB;AAAwB,IAAA,KACzB,CAAA,KAAA,CAAA,UAAA,EAAA,CAAA;AAAA,IACH,MAAA,IAAA,GAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAEA,IAAA,MAAM,WAAiB,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AAEvB,IAAA,MAAM,GAAO,GAAA,GAAA,CAAA,IAAgB,CAAA,CAAA;AAC7B,IAAM,MAAA,cAAc,MAAqB,CAAA,IAAA,CAAA,CAAA;AACzC,IAAM,MAAA,EAAA,iBAAqC,EAAA,GAAA,2BAAA,CAAA,KAAA,CAAA,CAAA;AAC3C,IAAM,MAAA,EAAA,SAAA,EAAA,GAAA,kBAAgD,CAAA;AAEtD,MAAA,KAAQ;AAER,MAAM,GAAA;AAAmC,MACvC,GAAA;AAAA,MACA,cAAA;AAAA,MACA,KAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,UAAA,CAAA,EAAA,GAAA,EAAA,EAAA,KAAA,CAAA,CAAA;AAAA,IACF,MAAC,OAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAW,MAAA,EAAA,UAAS,EAAK,GAAA,IAAA,CAAA,KAAA,CAAA;AAEzB,MAAM,MAAA,kBAAyB,GAAA,UAAA,GAAA,UAAA,CAAA,kBAAA,KAAA,CAAA,GAAA,KAAA,CAAA;AAC7B,MAAM,OAAA,CAAE,CAAW,UAAA,IAAI,UAAK,CAAA,MAAA,KAAA,CAAA,IAAA,UAAA,CAAA,KAAA,CAAA,CAAA,EAAA,OAAA,EAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,kBAAA,CAAA;AAC5B,KAAA,CAAA,CAAA;AAGA,IAAA,KAAA,CAAA,MACI,KAAA,CAAA,cACW,EAAA,CAAA,MAAA,KAAA;AAEZ,MAEJ,KAAA,CAAA,KAAA,CAAA,iBAAA,CAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,IAAA,CAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAAA,IAAA,YACc,KAAA,CAAA,kBAAA,EAAA,CAAA,MAAA,EAAA,MAAA,KAAA;AAAA,MACZ,IAAY,OAAA,CAAA,MAAA,EAAA,MAAA,CAAA;AACV,QAAM,OAAA;AAAsC,MAC9C,KAAA,CAAA,KAAA,CAAA,oBAAA,CAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,EAAA,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAEA,IAAA,KAAA,CAAA,MAAA,KAAA,CAAA,mBAAA,EAAA,CAAA,MAAA,KAAA;AAAA,MACE,MAAM,KAAM,CAAA,sBAAA,CAAA,MAAA,IAAA,IAAA,GAAA,MAAA,GAAA,EAAA,CAAA,CAAA;AAAA,KACZ,CAAA,CAAC;AACC,IAAI,KAAA,CAAA,MAAA,KAAgB,CAAA,IAAA,EAAA,CAAA,MAAS,KAAA;AAE7B,MAAA,KAAA,CAAA,KAAY,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA;AAAiC,KAC/C,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAAA,IACF,KAAA,CAAA,MAAA,KAAA,CAAA,aAAA,EAAA,CAAA,MAAA,KAAA;AAEA,MAAA,KAAA,CAAA,KAAA,CAAA,aAAA,GAAA,MAAA,CAAA;AAAA,KAAA,CACE;AAAY,IAAA,MACA,MAAA,GAAA,CAAA,KAAA,KAAA;AACV,MAAA,IAAA,CAAA,KAAY,CAAA,gBAAA;AAAmC,QACjD,MAAA,IAAA,KAAA,CAAA,iDAAA,CAAA,CAAA;AAAA,MACF,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AAAA,IAAA,kBACc,GAAA,CAAA,IAAA,KAAA;AAAA,MACZ,OAAYA,UAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AACV,KAAM,CAAA;AAAoB,IAC5B,MAAA,WAAA,GAAA,CAAA,IAAA,KAAA;AAAA,MACA,UAAa,CAAA,OAAA;AAAA,QACf,MAAA,IAAA,KAAA,CAAA,2CAAA,CAAA,CAAA;AAEA,MAAA,MAAA,IAAA,GAAA,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MACE,SAAY;AAAA,QACA,OAAA,EAAA,CAAA;AACV,MAAA,MAAA,QAAY,IAAgB,CAAA,IAAA,CAAA,CAAA;AAAA,MAC9B,IAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA;AAAA,MACF,OAAA,MAAA,IAAA,MAAA,KAAA,IAAA,CAAA,KAAA,EAAA;AAEA,QAAM,IAAA,CAAA,IAAA,CAAA,MAAiC,CAAA,IAAA,CAAA,CAAA;AACrC,QAAA,MAAW,GAAA,MAAA,CAAA,MAAA,CAAA;AACT,OAAM;AACR,MAAM,OAAA,IAAA,CAAM,OAAO,EAAK,CAAA;AAAA,KAC1B,CAAA;AAEA,IAAM,MAAA,eAA6B,GAAA,CAAA,QAAA,EAAA,kBAAA,KAAA;AACjC,MAAA,OAAO,KAAe,CAAA,KAAA,CAAA,eAAe,CAAA,QAAS,EAAA,kBAAA,CAAA,CAAA;AAAA,KAChD,CAAA;AAEA,IAAM,MAAA,cAAc,GAAkC,CAAA,QAAA,KAAA;AACpD,MAAA,OAAW,KAAA,CAAA,KAAA,CAAA,cAAA,CAAA,QAAA,CAAA,CAAA;AACT,KAAM,CAAA;AACR,IAAA,MAAA,cAAa,GAAY,MAAA;AACzB,MAAI,MAAO,YAAO,GAAC,KAAA,CAAA,KAAA,CAAA,cAAA,EAAA,CAAA;AACnB,MAAM,OAAA,YAAa,GAAI,YAAA,CAAA,IAAA,GAAA,IAAA,CAAA;AACvB,KAAA,CAAA;AACA,IAAO,MAAA,aAAU,GAAW,MAAA;AAC1B,MAAK,IAAA,CAAA,KAAA,CAAA;AACL,QAAA,MAAA,IAAgB,KAAA,CAAA,6CAAA,CAAA,CAAA;AAAA,MAClB,MAAA,YAAA,GAAA,cAAA,EAAA,CAAA;AACA,MAAA,OAAO,YAAa,GAAA,YAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,IAAA,CAAA;AAAA,KACtB,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CACtB,KAAA,EAAA,QAEmB,KAAA;AACnB,MAAA,IAAA,CAAA,KAAa,CAAA,OAAA;AAAkD,QACjE,MAAA,IAAA,KAAA,CAAA,+CAAA,CAAA,CAAA;AAEA,MAAM,KAAA,CAAA,KAAA,CAAA,eAAoD,CAAA,KAAA,EAAA,QAAA,CAAA,CAAA;AACxD,KAAO,CAAA;AAAmC,IAC5C,MAAA,cAAA,GAAA,CAAA,IAAA,EAAA,QAAA,KAAA;AAEA,MAAA,IAAM;AACJ,QAAMC,MAAAA,IAAAA,KAAAA,CAAAA,8CAAyC,CAAA,CAAA;AAC/C,MAAOA,KAAAA,CAAAA,KAAAA,CAAAA,6BAAiC,CAAA,CAAA;AAAA,KAC1C,CAAA;AAEA,IAAA,MAAM,kBAAsC,EAAA,OAAA,EAAA,IAAA,KAAA;AAC1C,MAAA,KAAK,CAAM,KAAA,CAAA,UAAA,CAAA,IAAA,EAAA,OAAA,EAAA,IAAA,CAAA,CAAA;AACT,KAAM,CAAA;AACR,IAAA,MAAA,mBAAmC,GAAA,MAAA;AACnC,MAAA,OAAOA,KAAcA,CAAAA,KAAAA,CAAAA,mBAAkB,EAAA,CAAA;AAAW,KACpD,CAAA;AAEA,IAAM,MAAA,kBAAkB,GAAC,MAAsC;AAC7D,MAAA,OAAW,KAAA,CAAA,KAAA,CAAA,kBAAA,EAAA,CAAA;AACT,KAAM,CAAA;AACR,IAAM,MAAA,cAAsB,GAAA,CAAA,IAAA,EAAA,sBAAe,GAAA,IAAA,KAAA;AAAA,MAC7C,IAAA,CAAA,KAAA,CAAA,OAAA;AAEA,QAAM,MAAA,IAAA,KAAA,CAAA,8CAA0D,CAAA,CAAA;AAC9D,MAAA,mBAAW,CAAA,KAAA,EAAA,GAAA,CAAA,IAAA,EAAA,MAAA;AACT,QAAM,iBAAwD,CAAA,IAAA,CAAA,CAAA;AAChE,QAAM,KAAA,CAAA,KAAqB,CAAA,kBAAA,CAAM,IAAQ,EAAA,sBAAA,CAAA,CAAA;AAAA,OAC3C,CAAA,CAAA;AAEA,KAAA,CAAA;AAKE,IAAA,MAAA,aAAY,GAAA,CAAA,GAAiB,EAAA,sBAAa,GAAA,IAAA,KAAA;AAAA,MAC5C,IAAA,CAAA,KAAA,CAAA,OAAA;AAEA,QAAA,6DAAkD,CAAA,CAAA;AAChD,MAAO,mBAAY,CAAoB,KAAA,EAAA,GAAA,CAAA,IAAA,EAAA,MAAA;AAAA,QACzC,iBAAA,EAAA,CAAA;AAEA,QAAA,6BAA4C,CAAA,GAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,EAAA,sBAAA,CAAA,CAAA;AAC1C,OAAO,CAAA,CAAA;AAA+B,KACxC,CAAA;AAEA,IAAA,MAAM,OAAiB,GAAA,CAAA,IAAA,KAAa;AAClC,MAAA,OAAW,KAAA,CAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AACT,KAAM,CAAA;AAER,IAAoB,MAAA,MAAA,GAAA,CAAA,IAAA,KAAA;AAClB,MAAA,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAsB,CAAA,CAAA;AACtB,KAAM,CAAA;AAAqD,IAAA,MAC5D,MAAA,GAAA,CAAA,IAAA,EAAA,UAAA,KAAA;AAAA,MACH,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,EAAA,UAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAI,YAAO,GAAA,CAAA,IAAA,EAAA,OAAA,KAAA;AACT,MAAM,KAAA,CAAA,KAAA,CAAI,YAAmD,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;AAE/D,KAAoB,CAAA;AAClB,IAAkB,MAAA,WAAA,GAAA,CAAA,IAAA,EAAA,OAAA,KAAA;AAClB,MAAA,KAAA,CAAA,KAAY,CAAA,WAAA,CAAA,IAAA,EAAA,OAAyB,CAAA,CAAA;AAA4B,KAAA,CACnE;AAAC,IACH,MAAA,gBAAA,GAAA,CAAA,QAAA,EAAA,IAAA,EAAA,QAAA,KAAA;AAEA,MAAM,iBAAkD,CAAA,IAAA,CAAA,CAAA;AACtD,MAAO,GAAA,CAAA,IAAA,CAAA,aAAY,EAAA,QAAY,EAAA,IAAA,EAAA,QAAA,CAAA,CAAA;AAAA,KACjC,CAAA;AAEA,IAAM,MAAA,iBAAwC,GAAA,CAAA,GAAA,EAAA,IAAA,KAAA;AAC5C,MAAM,IAAA,CAAA,KAAA,CAAA;AAAiB,QACzB,MAAA,IAAA,KAAA,CAAA,8CAAA,CAAA,CAAA;AAEA,MAAM,KAAA,CAAA,KAAA,CAAA,cAGD,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACH,KAAM,CAAA;AAA6B,IACrC,OAAA,CAAA,uBAAA,EAAA;AAEA,MAAM,GAAA;AAIJ,MAAM,KAAA;AAAgC,MACxC,KAAA;AAEA,MAAM,IAAA;AAIJ,MAAM,WAAA;AAA+B,MACvC,QAAA,EAAA,kBAAA,EAAA;AAEA,KAAA,CAAA,CAAA;AAKE,IAAA,OAAA,CAAA,kBAAsB,EAAA,KAAA,CAAA,CAAA,CAAA;AACtB,IAAA,OAAS;AAAuC,MAClD,EAAA;AAEA,MAAM,KAAA;AACJ,MAAA,IAAI;AACF,MAAM,WAAA;AACR,MAAM,SAAA;AAA8B,MACtC,GAAA;AAEA,MAAA,cAAiC;AAAA,MAC/B,OAAA;AAAA,MACA,MAAA;AAAA,kBACAC,YAAA;AAAA,MACA,WAAA;AAAA,MACA,eAAA;AAAA,MACA,cAA6B;AAAA,MAC9B,cAAA;AAED,MAAA;AAEA,MAAO,eAAA;AAAA,MACL,cAAA;AAAA,MAAA,UAAA;AAAA,MAEA,mBAAA;AAAA,MACA,kBAAA;AAAA,MACA,cAAA;AAAA,MACA,aAAA;AAAA,MACA,CAAA;AAAA,MACA,OAAA;AAAA,MAAA,MAAA;AAAA,MAGA,MAAA;AAAA,MAAA,YAAA;AAAA,MAGA,WAAA;AAAA,MACA,gBAAA;AAAA,MACA,iBAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACA,SACA,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACA,MAAA,uBAAA,GAAAC,gBAAA,CAAA,cAAA,CAAA,CAAA;AAAA,EACA,OAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,IACA,GAAA,EAAA,KAAA;AAAA,IACA,KAAA,EAAAC,cAAA,CAAA;AAAA,MACA,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA;AAAA,MACA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,YAAA,CAAA;AAAA,MACA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,gBAAA,EAAA,CAAA,IAAA,CAAA,SAAA,CAAA,SAAA,CAAA;AAAA,MACA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,YAAA,EAAA,IAAA,CAAA,SAAA,CAAA,QAAA,KAAA,OAAA,CAAA;AAAA,MACA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,IAAA,CAAA,gBAAA,EAAA;AAAA,KACA,CAAA;AAAA,IACA,IAAA,EAAA,MAAA;AAAA,GACA,EAAA;AAAA,KACAF,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAE,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,KAAA,KAAA;AAAA,MACA,OAAAJ,SAAA,EAAA,EAAAK,WAAA,CAAA,uBAAA,EAAA;AAAA,QACF,GAAA,EAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA;AAAA,QACF,IAAA,EAAA,KAAA;AACF,QAAC,KAAA,EAAA,IAAA,CAAA,KAAA;;;;AA3bC,QAAA,gBAAA,EAAA,IAAA,CAAA,aAAA;AAAA,QAkCM,YAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,OAAA,EAAA,IAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,OAAA,EAAA,WAAA,EAAA,qBAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AAAA,KAAA,CAjCJ,EAAI,GAAA,CAAA;AAAA,IAAA,IACE,CAAA,OAAA,IAAAL,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,MAAA;AAAc,MAAA,KAAa,EAAAC,cAAiB,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,aAAsB,CAAA,CAAA;AAAA,KAAA,EAAA;AAAqD,MAAAI,UAAY,CAAA,IAAiB,CAAA,MAAA,EAAA,OAAA,EAAA,EAAA,EAAA,MAAkB;AAAA,QAAwB,IAAA,EAAA,CAAA;AAA4C,QAAA,OAAA;UAO3OC,kBAAA,CAAA,MAAA,EAAA;AAAA,YAAA,KAAA,EAAAL,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA;;AAEL,SAAA,CAAA;AAAA,OAUE,CAAA;AAAA,KAAA,EAAA,CAAA,CAAA,IAAAM,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,IATgBC,cAAA,CAAAF,kBAAe,CAAA,KAAnB,EAAA;;AASZ,MARC,KAAA,EAAAL,mBAAgB,CAAK,EAAA,CAAA,CAAA,CAAA,gBAAA,CAAA,CAAA;AAAA,KAAA,EAAA,IAAA,EACf,CAAA,CAAA,EAAA;AAAA,MAAA,CAAAQ,KACC,EAAA,IAAA,CAAA,SAAA,CAAA,iBAAA,CAAA;AAAA,KAAA,CAAA;AACI,GAAA,EAAA,CAAA,CAAA,CAAA;AACU,CAAA;AAEL,WACH,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,UAAA,CAAA,CAAA,CAAA;;;;"}