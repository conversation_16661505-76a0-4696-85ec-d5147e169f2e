import{_ as e,u as a,b as l,r as s}from"./index-bcbc0702.js";/* empty css                     *//* empty css                 *//* empty css                 */import{r as t,d as i,A as o,B as r,al as n,G as u,I as d,J as p,C as c,F as m,am as _,u as g,D as v,H as f,E as h,z as b,an as y,K as V,ai as w,aj as q,L as k,ak as I,a9 as x,ah as A}from"./element-plus-3ab68b46.js";import{V as z}from"./VerificationCode-b10ec6a7.js";import{A as E}from"./AuthLayout-1aa16efb.js";import"./utils-c6a461b2.js";const j={key:1,class:"step-content"},T={class:"form-actions"},Q={key:0,class:"invite-info"},C={key:2,class:"step-content"},U={class:"form-actions"},D={key:3,class:"step-content"},S={key:4,class:"step-content"},L={class:"submit-review"},R={class:"review-info"},O={class:"form-actions"},P=e({__name:"InviteAuth",setup(e){const P=a(),B=["验证邀请码","填写信息","邮箱验证","提交审核"],$=t(0),F=t(!1),K=t(null),N=i({code:"",qq:""}),G=i({real_name:"",school:"",student_id:"",email:"",phone:""}),H=t(),J=t(),M={code:[{required:!0,message:"请输入邀请码",trigger:"blur"},{min:6,max:32,message:"邀请码长度为6-32位",trigger:"blur"}],qq:[{required:!0,message:"请输入QQ号",trigger:"blur"},{pattern:/^[1-9][0-9]{4,10}$/,message:"QQ号格式不正确",trigger:"blur"}]},W={real_name:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{min:2,max:20,message:"姓名长度为2-20个字符",trigger:"blur"}],school:[{required:!0,message:"请输入学校名称",trigger:"blur"},{min:2,max:50,message:"学校名称长度为2-50个字符",trigger:"blur"}],student_id:[{required:!0,message:"请输入学号",trigger:"blur"},{min:6,max:20,message:"学号长度为6-20个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"邮箱格式不正确",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}]},X=t(!1);o(async()=>{const{useUserSessionStore:e}=await l(()=>import("./userSession-03354358.js"),["assets/userSession-03354358.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css"]);if(e().hasValidEditToken){X.value=!0;try{const{applicationApi:e}=await l(()=>import("./application-7212cdc3.js"),["assets/application-7212cdc3.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css","assets/userSession-03354358.js"]),a=await e.getDetail();(null==a?void 0:a.success)&&a.data&&(G.real_name=a.data.realName||"",G.school=a.data.school||"",G.student_id=a.data.studentId||"",G.email=a.data.email||"",G.phone=a.data.phone||"")}catch(a){}}});const Y=async()=>{if(!H.value)return;if(await H.value.validate().catch(()=>!1)){F.value=!0;try{const e=await s.get(`/api/invites/verify/${encodeURIComponent(N.code)}`);e.success?(K.value=e.data,h.success("邀请码验证成功"),$.value=1):h.error(e.message||"邀请码验证失败")}catch(e){h.error("验证失败，请稍后重试")}finally{F.value=!1}}},Z=async()=>{if(1===$.value){if(!J.value)return;if(!(await J.value.validate().catch(()=>!1)))return}$.value++},ee=()=>{$.value>0&&$.value--},ae=e=>{h.success("验证成功"),$.value=3},le=async()=>{try{if(await b.confirm(X.value?"确认保存修改？":"确认提交认证申请？提交后将进入人工审核流程。","确认提交",{confirmButtonText:"确认提交",cancelButtonText:"取消",type:"warning"}),F.value=!0,X.value){const{applicationApi:e}=await l(()=>import("./application-7212cdc3.js"),["assets/application-7212cdc3.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css","assets/userSession-03354358.js"]),a={realName:G.real_name,school:G.school,studentId:G.student_id,email:G.email,phone:G.phone,uploadedImages:previewImage.value?[previewImage.value]:void 0,extraInfo:previewImage.value?{capture:{timestamp:Date.now(),source:"edit"}}:void 0},s=await e.edit(a);if(!(null==s?void 0:s.success))throw new Error((null==s?void 0:s.message)||"保存失败");return h.success("编辑已保存"),void P.push({name:"ApplicationStatus"})}const e=await(await l(()=>import("./auth-36869992.js"),["assets/auth-36869992.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css"])).authApi.submitInviteAuth({invite_code:N.code,qq:N.qq,real_name:G.real_name,school:G.school,student_id:G.student_id,email:G.email,phone:G.phone});if(e.success){if(e.editToken&&e.editTokenExpiresIn){const{useUserSessionStore:a}=await l(()=>import("./userSession-03354358.js"),["assets/userSession-03354358.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css"]);a().setEditToken(e.editToken,e.editTokenExpiresIn)}h.success("认证申请提交成功"),P.push({name:"Success",query:{type:"invite"}})}else h.error(e.message||"提交失败")}catch(e){"cancel"!==e&&h.error("提交失败，请稍后重试")}finally{F.value=!1}};return(e,a)=>{const l=y,s=V,t=w,i=q,o=k,h=I;return r(),n(E,{title:"邀请码认证",subtitle:"Invitation Code Authentication","page-title":"邀请码认证","page-description":"通过已认证用户的邀请码进行身份认证","icon-component":"Ticket",features:["邀请码快速认证","已认证用户担保","简化认证流程"],steps:B,"current-step":$.value,"show-steps":!0},{default:u(()=>[X.value?(r(),n(l,{key:0,type:"info",closable:!1,"show-icon":"",style:{"margin-bottom":"12px"}},{default:u(()=>a[8]||(a[8]=[d(" 当前为编辑模式：QQ不可修改，保存后将更新待审核申请。 ",-1)])),_:1,__:[8]})):p("",!0),0===$.value?(r(),c("div",j,[m(h,{model:N,rules:M,ref_key:"inviteFormRef",ref:H,"label-width":"100px"},{default:u(()=>[m(i,{label:"邀请码",prop:"code"},{default:u(()=>[m(t,{modelValue:N.code,"onUpdate:modelValue":a[0]||(a[0]=e=>N.code=e),placeholder:"请输入邀请码",size:"large",disabled:F.value,onKeyup:_(Y,["enter"])},{prefix:u(()=>[m(s,null,{default:u(()=>[m(g(x))]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),m(i,{label:"QQ号",prop:"qq"},{default:u(()=>[m(t,{modelValue:N.qq,"onUpdate:modelValue":a[1]||(a[1]=e=>N.qq=e),placeholder:"请输入您的QQ号",size:"large",disabled:F.value||X.value,onKeyup:_(Y,["enter"])},{prefix:u(()=>[m(s,null,{default:u(()=>[m(g(A))]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),v("div",T,[m(o,{type:"primary",size:"large",loading:F.value,onClick:Y},{default:u(()=>a[9]||(a[9]=[d(" 验证邀请码 ",-1)])),_:1,__:[9]},8,["loading"]),m(o,{size:"large",onClick:a[2]||(a[2]=a=>e.$router.push("/"))},{default:u(()=>a[10]||(a[10]=[d(" 返回首页 ",-1)])),_:1,__:[10]})])]),_:1},8,["model"]),K.value?(r(),c("div",Q,[m(l,{title:"邀请码信息",type:"success",closable:!1,"show-icon":""},{default:u(()=>{return[v("p",null,[a[11]||(a[11]=v("strong",null,"邀请人：",-1)),d(f(K.value.creator_name),1)]),v("p",null,[a[12]||(a[12]=v("strong",null,"剩余使用次数：",-1)),d(f(K.value.remaining_uses),1)]),v("p",null,[a[13]||(a[13]=v("strong",null,"有效期至：",-1)),d(f((e=K.value.expire_at,e?new Date(e).toLocaleString("zh-CN"):"")),1)])];var e}),_:1})])):p("",!0)])):p("",!0),1===$.value?(r(),c("div",C,[m(h,{model:G,rules:W,ref_key:"userFormRef",ref:J,"label-width":"100px"},{default:u(()=>[m(i,{label:"真实姓名",prop:"real_name"},{default:u(()=>[m(t,{modelValue:G.real_name,"onUpdate:modelValue":a[3]||(a[3]=e=>G.real_name=e),placeholder:"请输入真实姓名",size:"large",disabled:F.value},null,8,["modelValue","disabled"])]),_:1}),m(i,{label:"学校",prop:"school"},{default:u(()=>[m(t,{modelValue:G.school,"onUpdate:modelValue":a[4]||(a[4]=e=>G.school=e),placeholder:"请输入学校名称",size:"large",disabled:F.value},null,8,["modelValue","disabled"])]),_:1}),m(i,{label:"学号",prop:"student_id"},{default:u(()=>[m(t,{modelValue:G.student_id,"onUpdate:modelValue":a[5]||(a[5]=e=>G.student_id=e),placeholder:"请输入学号",size:"large",disabled:F.value},null,8,["modelValue","disabled"])]),_:1}),m(i,{label:"邮箱",prop:"email"},{default:u(()=>[m(t,{modelValue:G.email,"onUpdate:modelValue":a[6]||(a[6]=e=>G.email=e),placeholder:"请输入邮箱地址",size:"large",disabled:F.value},null,8,["modelValue","disabled"])]),_:1}),m(i,{label:"手机号",prop:"phone"},{default:u(()=>[m(t,{modelValue:G.phone,"onUpdate:modelValue":a[7]||(a[7]=e=>G.phone=e),placeholder:"请输入手机号",size:"large",disabled:F.value},null,8,["modelValue","disabled"])]),_:1}),v("div",U,[m(o,{type:"primary",size:"large",loading:F.value,onClick:Z},{default:u(()=>a[14]||(a[14]=[d(" 下一步 ",-1)])),_:1,__:[14]},8,["loading"]),m(o,{size:"large",onClick:ee},{default:u(()=>a[15]||(a[15]=[d(" 上一步 ",-1)])),_:1,__:[15]})])]),_:1},8,["model"])])):p("",!0),2===$.value?(r(),c("div",D,[m(z,{email:G.email,phone:G.phone,onVerified:ae,onBack:ee},null,8,["email","phone"])])):p("",!0),3===$.value?(r(),c("div",S,[v("div",L,[m(l,{title:"信息确认",type:"info",closable:!1,"show-icon":""},{default:u(()=>[v("div",R,[v("p",null,[a[16]||(a[16]=v("strong",null,"QQ号：",-1)),d(f(N.qq),1)]),v("p",null,[a[17]||(a[17]=v("strong",null,"真实姓名：",-1)),d(f(G.real_name),1)]),v("p",null,[a[18]||(a[18]=v("strong",null,"学校：",-1)),d(f(G.school),1)]),v("p",null,[a[19]||(a[19]=v("strong",null,"学号：",-1)),d(f(G.student_id),1)]),v("p",null,[a[20]||(a[20]=v("strong",null,"邮箱：",-1)),d(f(G.email),1)]),v("p",null,[a[21]||(a[21]=v("strong",null,"手机号：",-1)),d(f(G.phone),1)])])]),_:1}),v("div",O,[m(o,{type:"primary",size:"large",loading:F.value,onClick:le},{default:u(()=>[d(f(X.value?"保存修改":"提交认证申请"),1)]),_:1},8,["loading"]),m(o,{size:"large",onClick:ee},{default:u(()=>a[22]||(a[22]=[d(" 返回修改 ",-1)])),_:1,__:[22]})])])])):p("",!0)]),_:1},8,["current-step"])}}},[["__scopeId","data-v-8f55072b"]]);export{P as default};
