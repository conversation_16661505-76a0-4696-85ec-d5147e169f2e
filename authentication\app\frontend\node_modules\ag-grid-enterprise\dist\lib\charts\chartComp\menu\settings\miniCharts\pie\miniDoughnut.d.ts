import { MiniC<PERSON> } from "../miniChart";
import { ChartType } from "ag-grid-community";
export declare class <PERSON><PERSON><PERSON>nut extends Mini<PERSON>hart {
    static chartType: ChartType;
    private readonly sectors;
    constructor(container: HTMLElement, fills: string[], strokes: string[], centerRadiusScaler?: number, tooltipName?: string);
    updateColors(fills: string[], strokes: string[]): void;
}
