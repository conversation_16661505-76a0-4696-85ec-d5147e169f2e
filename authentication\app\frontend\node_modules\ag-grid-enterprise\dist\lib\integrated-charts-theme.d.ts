import type { ChartTheme as ChartThemeType } from './chart/themes/chartTheme';
export { getChartTheme } from './chart/mapping/themes';
export { ChartTheme, EXTENDS_AXES_DEFAULTS, EXTENDS_AXES_LABEL_DEFAULTS, EXTENDS_AXES_LINE_DEFAULTS, EXTENDS_SERIES_DEFAULTS, OVERRIDE_SERIES_LABEL_DEFAULTS, DEFAULT_FONT_FAMILY, } from './chart/themes/chartTheme';
export declare const themes: Record<"undefined" | import("./main").AgChartThemeName | "null", ChartThemeType>;
//# sourceMappingURL=integrated-charts-theme.d.ts.map