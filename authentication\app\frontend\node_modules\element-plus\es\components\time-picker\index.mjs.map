{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/time-picker/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport TimePicker from './src/time-picker'\nimport CommonPicker from './src/common/picker.vue'\nimport TimePickPanel from './src/time-picker-com/panel-time-pick.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport * from './src/utils'\nexport * from './src/constants'\nexport * from './src/common/props'\n\nexport const ElTimePicker: SFCWithInstall<typeof TimePicker> =\n  withInstall(TimePicker)\n\nexport { CommonPicker, TimePickPanel }\nexport default ElTimePicker\n"], "names": [], "mappings": ";;;;;;;;AAOY,MAAC,YAAY,GAAG,WAAW,CAAC,UAAU;;;;"}