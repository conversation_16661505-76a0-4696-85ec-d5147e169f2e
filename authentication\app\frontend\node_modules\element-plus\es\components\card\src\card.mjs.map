{"version": 3, "file": "card.mjs", "sources": ["../../../../../../packages/components/card/src/card.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type {\n  ExtractPropTypes,\n  InjectionKey,\n  StyleValue,\n  __ExtractPublicPropTypes,\n} from 'vue'\n\nexport const cardProps = buildProps({\n  /**\n   * @description title of the card. Also accepts a DOM passed by `slot#header`\n   */\n  header: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description content of footer. Also accepts a DOM passed by `slot#footer`\n   */\n  footer: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description CSS style of card body\n   */\n  bodyStyle: {\n    type: definePropType<StyleValue>([String, Object, Array]),\n    default: '',\n  },\n  /**\n   * @description custom class name of card footer\n   */\n  headerClass: String,\n  /**\n   * @description custom class name of card body\n   */\n  bodyClass: String,\n  /**\n   * @description custom class name of card footer\n   */\n  footerClass: String,\n  /**\n   * @description when to show card shadows\n   */\n  shadow: {\n    type: String,\n    values: ['always', 'hover', 'never'],\n    default: undefined,\n  },\n} as const)\nexport type CardProps = ExtractPropTypes<typeof cardProps>\nexport type CardPropsPublic = __ExtractPublicPropTypes<typeof cardProps>\nexport interface CardConfigContext {\n  shadow?: string\n}\n\nexport const cardContextKey: InjectionKey<CardConfigContext> =\n  Symbol('cardContextKey')\n"], "names": [], "mappings": ";;AACY,MAAC,SAAS,GAAG,UAAU,CAAC;AACpC,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;AACxC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,cAAc,GAAG,MAAM,CAAC,gBAAgB;;;;"}