{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/tag/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Tag from './src/tag.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTag: SFCWithInstall<typeof Tag> = withInstall(Tag)\nexport default ElTag\n\nexport * from './src/tag'\n"], "names": ["withInstall", "Tag"], "mappings": ";;;;;;;;AAEY,MAAC,KAAK,GAAGA,mBAAW,CAACC,gBAAG;;;;;;;"}