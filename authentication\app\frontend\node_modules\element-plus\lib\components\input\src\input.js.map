{"version": 3, "file": "input.js", "sources": ["../../../../../../packages/components/input/src/input.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  iconPropType,\n  isString,\n  mutable,\n} from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { useAriaProps, useSizeProp } from '@element-plus/hooks'\n\nimport type {\n  ExtractPropTypes,\n  HTMLAttributes,\n  StyleValue,\n  __ExtractPublicPropTypes,\n} from 'vue'\n\nexport type InputAutoSize = { minRows?: number; maxRows?: number } | boolean\n\nexport const inputProps = buildProps({\n  /**\n   * @description native input id\n   */\n  id: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description input box size\n   */\n  size: useSizeProp,\n  /**\n   * @description whether to disable\n   */\n  disabled: <PERSON>olean,\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: definePropType<string | number | null | undefined>([\n      String,\n      Number,\n      Object,\n    ]),\n    default: '',\n  },\n  /**\n   * @description same as `maxlength` in native input\n   */\n  maxlength: {\n    type: [String, Number],\n  },\n  /**\n   * @description same as `minlength` in native input\n   */\n  minlength: {\n    type: [String, Number],\n  },\n  /**\n   * @description type of input\n   */\n  type: {\n    type: String,\n    default: 'text',\n  },\n  /**\n   * @description control the resizability\n   */\n  resize: {\n    type: String,\n    values: ['none', 'both', 'horizontal', 'vertical'],\n  },\n  /**\n   * @description whether textarea has an adaptive height\n   */\n  autosize: {\n    type: definePropType<InputAutoSize>([Boolean, Object]),\n    default: false,\n  },\n  /**\n   * @description native input autocomplete\n   */\n  autocomplete: {\n    type: String,\n    default: 'off',\n  },\n  /**\n   * @description format content\n   */\n  formatter: {\n    type: Function,\n  },\n  /**\n   * @description parse content\n   */\n  parser: {\n    type: Function,\n  },\n  /**\n   * @description placeholder\n   */\n  placeholder: {\n    type: String,\n  },\n  /**\n   * @description native input form\n   */\n  form: {\n    type: String,\n  },\n  /**\n   * @description native input readonly\n   */\n  readonly: Boolean,\n  /**\n   * @description whether to show clear button\n   */\n  clearable: Boolean,\n  /**\n   * @description toggleable password input\n   */\n  showPassword: Boolean,\n  /**\n   * @description word count\n   */\n  showWordLimit: Boolean,\n  /**\n   * @description suffix icon\n   */\n  suffixIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description prefix icon\n   */\n  prefixIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description container role, internal properties provided for use by the picker component\n   */\n  containerRole: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description input tabindex\n   */\n  tabindex: {\n    type: [String, Number],\n    default: 0,\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description input or textarea element style\n   */\n  inputStyle: {\n    type: definePropType<StyleValue>([Object, Array, String]),\n    default: () => mutable({} as const),\n  },\n  /**\n   * @description native input autofocus\n   */\n  autofocus: Boolean,\n  rows: {\n    type: Number,\n    default: 2,\n  },\n  ...useAriaProps(['ariaLabel']),\n  /**\n   * @description native input mode for virtual keyboards\n   */\n  inputmode: {\n    type: definePropType<HTMLAttributes['inputmode']>(String),\n    default: undefined,\n  },\n  /**\n   * @description same as `name` in native input\n   */\n  name: String,\n} as const)\nexport type InputProps = ExtractPropTypes<typeof inputProps>\nexport type InputPropsPublic = __ExtractPublicPropTypes<typeof inputProps>\n\nexport const inputEmits = {\n  [UPDATE_MODEL_EVENT]: (value: string) => isString(value),\n  input: (value: string) => isString(value),\n  change: (value: string) => isString(value),\n  focus: (evt: FocusEvent) => evt instanceof FocusEvent,\n  blur: (evt: FocusEvent) => evt instanceof FocusEvent,\n  clear: () => true,\n  mouseleave: (evt: MouseEvent) => evt instanceof MouseEvent,\n  mouseenter: (evt: MouseEvent) => evt instanceof MouseEvent,\n  // NOTE: when autofill by browser, the keydown event is instanceof Event, not KeyboardEvent\n  // relative bug report https://github.com/element-plus/element-plus/issues/6665\n  keydown: (evt: KeyboardEvent | Event) => evt instanceof Event,\n  compositionstart: (evt: CompositionEvent) => evt instanceof CompositionEvent,\n  compositionupdate: (evt: CompositionEvent) => evt instanceof CompositionEvent,\n  compositionend: (evt: CompositionEvent) => evt instanceof CompositionEvent,\n}\nexport type InputEmits = typeof inputEmits\n"], "names": ["buildProps", "useSizeProp", "definePropType", "iconPropType", "mutable", "useAriaProps", "UPDATE_MODEL_EVENT", "isString"], "mappings": ";;;;;;;;;;;;AASY,MAAC,UAAU,GAAGA,kBAAU,CAAC;AACrC,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,EAAEC,iBAAW;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEC,sBAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,KAAK,CAAC;AACN,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC;AACtD,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,QAAQ;AAClB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,QAAQ;AAClB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,aAAa,EAAE,OAAO;AACxB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEC,iBAAY;AACtB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEA,iBAAY;AACtB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,MAAME,kBAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,GAAGC,oBAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEH,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,EAAE,MAAM;AACd,CAAC,EAAE;AACS,MAAC,UAAU,GAAG;AAC1B,EAAE,CAACI,wBAAkB,GAAG,CAAC,KAAK,KAAKC,eAAQ,CAAC,KAAK,CAAC;AAClD,EAAE,KAAK,EAAE,CAAC,KAAK,KAAKA,eAAQ,CAAC,KAAK,CAAC;AACnC,EAAE,MAAM,EAAE,CAAC,KAAK,KAAKA,eAAQ,CAAC,KAAK,CAAC;AACpC,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC1C,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB,EAAE,UAAU,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAChD,EAAE,UAAU,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAChD,EAAE,OAAO,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,KAAK;AACxC,EAAE,gBAAgB,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,gBAAgB;AAC5D,EAAE,iBAAiB,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,gBAAgB;AAC7D,EAAE,cAAc,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,gBAAgB;AAC1D;;;;;"}