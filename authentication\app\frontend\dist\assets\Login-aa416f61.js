import{_ as e,u as a,c as s,d as l}from"./index-bcbc0702.js";/* empty css                *//* empty css                 *//* empty css                   *//* empty css                     *//* empty css                    *//* empty css                 */import{r as t,d as r,A as o,aw as d,B as i,C as u,D as n,F as m,G as p,u as c,am as g,al as _,I as f,J as v,H as h,E as b,K as w,ai as y,aj as V,aX as I,aY as j,L as x,ak as C,ap as k,an as A,X as D,a2 as E,a0 as L,aa as R,aZ as U,a_ as q,a5 as N}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const O={class:"admin-login-container"},T={class:"login-form-container"},z={class:"login-header"},K={class:"totp-help"},F={class:"login-options"},Q={class:"login-help"},S={class:"help-content"},B={class:"back-home"},G={class:"login-stats"},X={class:"stats-item"},$={class:"stats-item"},H={class:"stats-item"},J=e({__name:"Login",setup(e){const J=a(),P=s(),Y=l(),Z=t(!1),M=t(!1),W=t(""),ee=r({username:"",password:"",totp:"",remember:!1}),ae=t(),se={username:[{required:!0,message:"请输入管理员账号",trigger:"blur"},{min:3,max:20,message:"账号长度为3-20个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:50,message:"密码长度为6-50个字符",trigger:"blur"}],totp:[{required:!0,message:"请输入动态验证码",trigger:"blur"},{pattern:/^\d{6}$/,message:"动态验证码为6位数字",trigger:"blur"}]};let le=null;const te=()=>{const e=new Date;W.value=e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},re=async()=>{if(!ae.value)return;if(await ae.value.validate().catch(()=>!1)){Z.value=!0;try{const e={username:ee.username,password:ee.password};ee.totp&&(e.totp=ee.totp);const a=await Y.login(e);if(a.success){b.success("登录成功");const e=P.query.redirect||"/admin";J.push(e)}else b.error(a.error||"登录失败")}catch(e){let a="登录失败，请稍后重试";if(e.response&&e.response.data){const s=e.response.data;if("INVALID_CREDENTIALS"===s.error_code)a="用户名或密码错误";else if("ACCOUNT_LOCKED"===s.error_code)a=s.message||"账户已被锁定";else if("ACCOUNT_DISABLED"===s.error_code)a="账户已被禁用";else if("TOTP_REQUIRED"===s.error_code)a="请提供二次验证码";else if("VALIDATION_ERROR"===s.error_code)if(s.details){const e=s.details;a=e.username&&e.username.length>0?e.username[0]:e.password&&e.password.length>0?e.password[0]:e.totp&&e.totp.length>0?e.totp[0]:s.message||"请求参数错误"}else a=s.message||"请求参数错误";else s.message&&(a=s.message)}else e.message&&!e.message.includes("Request failed with status code")&&(a=e.message);b.error(a)}finally{Z.value=!1}}};return o(()=>{te(),le=setInterval(te,1e3)}),d(()=>{le&&clearInterval(le)}),(e,a)=>{const s=w,l=y,t=V,r=I,o=j,d=x,b=C,J=k,P=A,Y=D;return i(),u("div",O,[a[14]||(a[14]=n("div",{class:"login-background"},[n("div",{class:"background-overlay"})],-1)),n("div",T,[m(Y,{class:"login-card",shadow:"always"},{header:p(()=>[n("div",z,[m(s,{size:"32",color:"#409EFF"},{default:p(()=>[m(c(E))]),_:1}),a[5]||(a[5]=n("h2",null,"管理员登录",-1)),a[6]||(a[6]=n("p",null,"北航QQ身份认证系统管理后台",-1))])]),default:p(()=>[m(b,{model:ee,rules:se,ref_key:"loginFormRef",ref:ae,"label-width":"0",size:"large"},{default:p(()=>[m(t,{prop:"username"},{default:p(()=>[m(l,{modelValue:ee.username,"onUpdate:modelValue":a[0]||(a[0]=e=>ee.username=e),placeholder:"请输入管理员账号",disabled:Z.value,onKeyup:g(re,["enter"])},{prefix:p(()=>[m(s,null,{default:p(()=>[m(c(L))]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),m(t,{prop:"password"},{default:p(()=>[m(l,{modelValue:ee.password,"onUpdate:modelValue":a[1]||(a[1]=e=>ee.password=e),type:"password",placeholder:"请输入密码",disabled:Z.value,"show-password":"",onKeyup:g(re,["enter"])},{prefix:p(()=>[m(s,null,{default:p(()=>[m(c(E))]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),M.value?(i(),_(t,{key:0,prop:"totp"},{default:p(()=>[m(l,{modelValue:ee.totp,"onUpdate:modelValue":a[2]||(a[2]=e=>ee.totp=e),placeholder:"请输入6位动态验证码",disabled:Z.value,maxlength:"6",onKeyup:g(re,["enter"])},{prefix:p(()=>[m(s,null,{default:p(()=>[m(c(R))]),_:1})]),_:1},8,["modelValue","disabled"]),n("div",K,[m(r,{size:"small",type:"info"},{default:p(()=>a[7]||(a[7]=[f(" 请使用Google Authenticator或类似应用生成的6位验证码 ",-1)])),_:1,__:[7]})])]),_:1})):v("",!0),m(t,null,{default:p(()=>[n("div",F,[m(o,{modelValue:ee.remember,"onUpdate:modelValue":a[3]||(a[3]=e=>ee.remember=e),disabled:Z.value},{default:p(()=>a[8]||(a[8]=[f(" 记住登录状态 ",-1)])),_:1,__:[8]},8,["modelValue","disabled"])])]),_:1}),m(t,null,{default:p(()=>[m(d,{type:"primary",size:"large",loading:Z.value,onClick:re,class:"login-button"},{default:p(()=>[f(h(Z.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),n("div",Q,[m(J,null,{default:p(()=>a[9]||(a[9]=[f("登录帮助",-1)])),_:1,__:[9]}),n("div",S,[m(P,{title:"安全提示",type:"info",closable:!1,"show-icon":""},{default:p(()=>a[10]||(a[10]=[n("ul",null,[n("li",null,"管理员账号仅限授权人员使用"),n("li",null,"请妥善保管您的登录凭据"),n("li",null,"如遇登录问题，请联系系统管理员"),n("li",null,"系统会记录所有登录行为")],-1)])),_:1})])]),n("div",B,[m(d,{text:"",onClick:a[4]||(a[4]=a=>e.$router.push("/"))},{default:p(()=>[m(s,null,{default:p(()=>[m(c(U))]),_:1}),a[11]||(a[11]=f(" 返回首页 ",-1))]),_:1,__:[11]})])]),_:1})]),n("div",G,[n("div",X,[m(s,null,{default:p(()=>[m(c(q))]),_:1}),a[12]||(a[12]=n("span",null,"系统运行正常",-1))]),n("div",$,[m(s,null,{default:p(()=>[m(c(E))]),_:1}),a[13]||(a[13]=n("span",null,"安全防护已启用",-1))]),n("div",H,[m(s,null,{default:p(()=>[m(c(N))]),_:1}),n("span",null,h(W.value),1)])])])}}},[["__scopeId","data-v-fa4ae6a3"]]);export{J as default};
