import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("ecm.tencentcloudapi.com", "2019-07-19", clientConfig);
    }
    async ReplaceSecurityGroupPolicy(req, cb) {
        return this.request("ReplaceSecurityGroupPolicy", req, cb);
    }
    async DescribeConfig(req, cb) {
        return this.request("DescribeConfig", req, cb);
    }
    async DescribeTaskResult(req, cb) {
        return this.request("DescribeTaskResult", req, cb);
    }
    async ModifyModuleName(req, cb) {
        return this.request("ModifyModuleName", req, cb);
    }
    async DescribeRouteTables(req, cb) {
        return this.request("DescribeRouteTables", req, cb);
    }
    async CreateRouteTable(req, cb) {
        return this.request("CreateRouteTable", req, cb);
    }
    async BatchDeregisterTargets(req, cb) {
        return this.request("BatchDeregisterTargets", req, cb);
    }
    async ModifyInstancesAttribute(req, cb) {
        return this.request("ModifyInstancesAttribute", req, cb);
    }
    async ResetRoutes(req, cb) {
        return this.request("ResetRoutes", req, cb);
    }
    async AssignIpv6CidrBlocks(req, cb) {
        return this.request("AssignIpv6CidrBlocks", req, cb);
    }
    async ModifyPrivateIpAddressesAttribute(req, cb) {
        return this.request("ModifyPrivateIpAddressesAttribute", req, cb);
    }
    async RunInstances(req, cb) {
        return this.request("RunInstances", req, cb);
    }
    async AssignPrivateIpAddresses(req, cb) {
        return this.request("AssignPrivateIpAddresses", req, cb);
    }
    async DescribeAddresses(req, cb) {
        return this.request("DescribeAddresses", req, cb);
    }
    async ModifyModuleImage(req, cb) {
        return this.request("ModifyModuleImage", req, cb);
    }
    async DescribeListeners(req, cb) {
        return this.request("DescribeListeners", req, cb);
    }
    async CreateImage(req, cb) {
        return this.request("CreateImage", req, cb);
    }
    async DescribeNode(req, cb) {
        return this.request("DescribeNode", req, cb);
    }
    async DescribeCustomImageTask(req, cb) {
        return this.request("DescribeCustomImageTask", req, cb);
    }
    async AssignIpv6CidrBlock(req, cb) {
        return this.request("AssignIpv6CidrBlock", req, cb);
    }
    async DescribeLoadBalancers(req, cb) {
        return this.request("DescribeLoadBalancers", req, cb);
    }
    async DescribeLoadBalanceTaskStatus(req, cb) {
        return this.request("DescribeLoadBalanceTaskStatus", req, cb);
    }
    async DescribeVpcs(req, cb) {
        return this.request("DescribeVpcs", req, cb);
    }
    async BatchModifyTargetWeight(req, cb) {
        return this.request("BatchModifyTargetWeight", req, cb);
    }
    async DescribeImage(req, cb) {
        return this.request("DescribeImage", req, cb);
    }
    async DisassociateSecurityGroups(req, cb) {
        return this.request("DisassociateSecurityGroups", req, cb);
    }
    async MigrateNetworkInterface(req, cb) {
        return this.request("MigrateNetworkInterface", req, cb);
    }
    async ModifyAddressesBandwidth(req, cb) {
        return this.request("ModifyAddressesBandwidth", req, cb);
    }
    async CreateSubnet(req, cb) {
        return this.request("CreateSubnet", req, cb);
    }
    async ResetInstancesMaxBandwidth(req, cb) {
        return this.request("ResetInstancesMaxBandwidth", req, cb);
    }
    async DescribeModule(req, cb) {
        return this.request("DescribeModule", req, cb);
    }
    async StartInstances(req, cb) {
        return this.request("StartInstances", req, cb);
    }
    async DeleteListener(req, cb) {
        return this.request("DeleteListener", req, cb);
    }
    async DisassociateAddress(req, cb) {
        return this.request("DisassociateAddress", req, cb);
    }
    async CreateVpc(req, cb) {
        return this.request("CreateVpc", req, cb);
    }
    async ModifyLoadBalancerAttributes(req, cb) {
        return this.request("ModifyLoadBalancerAttributes", req, cb);
    }
    async AttachDisks(req, cb) {
        return this.request("AttachDisks", req, cb);
    }
    async AssignIpv6SubnetCidrBlock(req, cb) {
        return this.request("AssignIpv6SubnetCidrBlock", req, cb);
    }
    async SetLoadBalancerSecurityGroups(req, cb) {
        return this.request("SetLoadBalancerSecurityGroups", req, cb);
    }
    async AllocateAddresses(req, cb) {
        return this.request("AllocateAddresses", req, cb);
    }
    async ModifyModuleDisableWanIp(req, cb) {
        return this.request("ModifyModuleDisableWanIp", req, cb);
    }
    async ModifyListener(req, cb) {
        return this.request("ModifyListener", req, cb);
    }
    async DisassociateInstancesKeyPairs(req, cb) {
        return this.request("DisassociateInstancesKeyPairs", req, cb);
    }
    async AssociateAddress(req, cb) {
        return this.request("AssociateAddress", req, cb);
    }
    async DeleteSubnet(req, cb) {
        return this.request("DeleteSubnet", req, cb);
    }
    async ModifyIpv6AddressesBandwidth(req, cb) {
        return this.request("ModifyIpv6AddressesBandwidth", req, cb);
    }
    async AllocateIpv6AddressesBandwidth(req, cb) {
        return this.request("AllocateIpv6AddressesBandwidth", req, cb);
    }
    async BatchRegisterTargets(req, cb) {
        return this.request("BatchRegisterTargets", req, cb);
    }
    async DescribeInstancesDeniedActions(req, cb) {
        return this.request("DescribeInstancesDeniedActions", req, cb);
    }
    async CreateNetworkInterface(req, cb) {
        return this.request("CreateNetworkInterface", req, cb);
    }
    async StopInstances(req, cb) {
        return this.request("StopInstances", req, cb);
    }
    async DescribeImportImageOs(req, cb) {
        return this.request("DescribeImportImageOs", req, cb);
    }
    async DescribeMonthPeakNetwork(req, cb) {
        return this.request("DescribeMonthPeakNetwork", req, cb);
    }
    async DetachNetworkInterface(req, cb) {
        return this.request("DetachNetworkInterface", req, cb);
    }
    async ResetInstances(req, cb) {
        return this.request("ResetInstances", req, cb);
    }
    async TerminateDisks(req, cb) {
        return this.request("TerminateDisks", req, cb);
    }
    async ModifyTargetPort(req, cb) {
        return this.request("ModifyTargetPort", req, cb);
    }
    async DeleteNetworkInterface(req, cb) {
        return this.request("DeleteNetworkInterface", req, cb);
    }
    async DeleteModule(req, cb) {
        return this.request("DeleteModule", req, cb);
    }
    async ReleaseIpv6Addresses(req, cb) {
        return this.request("ReleaseIpv6Addresses", req, cb);
    }
    async DescribeTargetHealth(req, cb) {
        return this.request("DescribeTargetHealth", req, cb);
    }
    async ReplaceRoutes(req, cb) {
        return this.request("ReplaceRoutes", req, cb);
    }
    async UnassignIpv6SubnetCidrBlock(req, cb) {
        return this.request("UnassignIpv6SubnetCidrBlock", req, cb);
    }
    async DescribeRouteConflicts(req, cb) {
        return this.request("DescribeRouteConflicts", req, cb);
    }
    async DetachDisks(req, cb) {
        return this.request("DetachDisks", req, cb);
    }
    async DisableRoutes(req, cb) {
        return this.request("DisableRoutes", req, cb);
    }
    async ReplaceRouteTableAssociation(req, cb) {
        return this.request("ReplaceRouteTableAssociation", req, cb);
    }
    async DescribePriceRunInstance(req, cb) {
        return this.request("DescribePriceRunInstance", req, cb);
    }
    async ModifyIpv6AddressesAttribute(req, cb) {
        return this.request("ModifyIpv6AddressesAttribute", req, cb);
    }
    async DescribeBaseOverview(req, cb) {
        return this.request("DescribeBaseOverview", req, cb);
    }
    async AttachNetworkInterface(req, cb) {
        return this.request("AttachNetworkInterface", req, cb);
    }
    async DeleteSecurityGroupPolicies(req, cb) {
        return this.request("DeleteSecurityGroupPolicies", req, cb);
    }
    async DescribeRegionIpv6Addresses(req, cb) {
        return this.request("DescribeRegionIpv6Addresses", req, cb);
    }
    async CreateKeyPair(req, cb) {
        return this.request("CreateKeyPair", req, cb);
    }
    async ModifyModuleConfig(req, cb) {
        return this.request("ModifyModuleConfig", req, cb);
    }
    async ModifySecurityGroupAttribute(req, cb) {
        return this.request("ModifySecurityGroupAttribute", req, cb);
    }
    async DescribeModuleDetail(req, cb) {
        return this.request("DescribeModuleDetail", req, cb);
    }
    async DeleteLoadBalancer(req, cb) {
        return this.request("DeleteLoadBalancer", req, cb);
    }
    async DescribeInstanceTypeConfig(req, cb) {
        return this.request("DescribeInstanceTypeConfig", req, cb);
    }
    async DeleteRoutes(req, cb) {
        return this.request("DeleteRoutes", req, cb);
    }
    async ModifySecurityGroupPolicies(req, cb) {
        return this.request("ModifySecurityGroupPolicies", req, cb);
    }
    async ModifySubnetAttribute(req, cb) {
        return this.request("ModifySubnetAttribute", req, cb);
    }
    async DescribePackingQuotaGroup(req, cb) {
        return this.request("DescribePackingQuotaGroup", req, cb);
    }
    async DescribeNetworkInterfaces(req, cb) {
        return this.request("DescribeNetworkInterfaces", req, cb);
    }
    async CreateDisks(req, cb) {
        return this.request("CreateDisks", req, cb);
    }
    async AssociateSecurityGroups(req, cb) {
        return this.request("AssociateSecurityGroups", req, cb);
    }
    async ModifyImageAttribute(req, cb) {
        return this.request("ModifyImageAttribute", req, cb);
    }
    async DescribeTaskStatus(req, cb) {
        return this.request("DescribeTaskStatus", req, cb);
    }
    async DescribePeakNetworkOverview(req, cb) {
        return this.request("DescribePeakNetworkOverview", req, cb);
    }
    async CreateSecurityGroup(req, cb) {
        return this.request("CreateSecurityGroup", req, cb);
    }
    async ImportCustomImage(req, cb) {
        return this.request("ImportCustomImage", req, cb);
    }
    async DescribeSnapshots(req, cb) {
        return this.request("DescribeSnapshots", req, cb);
    }
    async TerminateInstances(req, cb) {
        return this.request("TerminateInstances", req, cb);
    }
    async DescribeInstanceVncUrl(req, cb) {
        return this.request("DescribeInstanceVncUrl", req, cb);
    }
    async DeleteRouteTable(req, cb) {
        return this.request("DeleteRouteTable", req, cb);
    }
    async DescribeSecurityGroupLimits(req, cb) {
        return this.request("DescribeSecurityGroupLimits", req, cb);
    }
    async SetSecurityGroupForLoadbalancers(req, cb) {
        return this.request("SetSecurityGroupForLoadbalancers", req, cb);
    }
    async ModifyDefaultSubnet(req, cb) {
        return this.request("ModifyDefaultSubnet", req, cb);
    }
    async RebootInstances(req, cb) {
        return this.request("RebootInstances", req, cb);
    }
    async AssignIpv6Addresses(req, cb) {
        return this.request("AssignIpv6Addresses", req, cb);
    }
    async MigratePrivateIpAddress(req, cb) {
        return this.request("MigratePrivateIpAddress", req, cb);
    }
    async CreateLoadBalancer(req, cb) {
        return this.request("CreateLoadBalancer", req, cb);
    }
    async ModifyModuleNetwork(req, cb) {
        return this.request("ModifyModuleNetwork", req, cb);
    }
    async DescribeHaVips(req, cb) {
        return this.request("DescribeHaVips", req, cb);
    }
    async ImportImage(req, cb) {
        return this.request("ImportImage", req, cb);
    }
    async DeleteHaVip(req, cb) {
        return this.request("DeleteHaVip", req, cb);
    }
    async QueryVpcTaskResult(req, cb) {
        return this.request("QueryVpcTaskResult", req, cb);
    }
    async DescribeAddressQuota(req, cb) {
        return this.request("DescribeAddressQuota", req, cb);
    }
    async DeleteLoadBalancerListeners(req, cb) {
        return this.request("DeleteLoadBalancerListeners", req, cb);
    }
    async DeleteVpc(req, cb) {
        return this.request("DeleteVpc", req, cb);
    }
    async DescribeSubnets(req, cb) {
        return this.request("DescribeSubnets", req, cb);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async DescribeSecurityGroups(req, cb) {
        return this.request("DescribeSecurityGroups", req, cb);
    }
    async DescribeSecurityGroupPolicies(req, cb) {
        return this.request("DescribeSecurityGroupPolicies", req, cb);
    }
    async DescribeDefaultSubnet(req, cb) {
        return this.request("DescribeDefaultSubnet", req, cb);
    }
    async ResetInstancesPassword(req, cb) {
        return this.request("ResetInstancesPassword", req, cb);
    }
    async CreateModule(req, cb) {
        return this.request("CreateModule", req, cb);
    }
    async ReleaseIpv6AddressesBandwidth(req, cb) {
        return this.request("ReleaseIpv6AddressesBandwidth", req, cb);
    }
    async EnableRoutes(req, cb) {
        return this.request("EnableRoutes", req, cb);
    }
    async CreateHaVip(req, cb) {
        return this.request("CreateHaVip", req, cb);
    }
    async DescribeDisks(req, cb) {
        return this.request("DescribeDisks", req, cb);
    }
    async DeleteSecurityGroup(req, cb) {
        return this.request("DeleteSecurityGroup", req, cb);
    }
    async CreateListener(req, cb) {
        return this.request("CreateListener", req, cb);
    }
    async DeleteSnapshots(req, cb) {
        return this.request("DeleteSnapshots", req, cb);
    }
    async ModifyRouteTableAttribute(req, cb) {
        return this.request("ModifyRouteTableAttribute", req, cb);
    }
    async ModifyTargetWeight(req, cb) {
        return this.request("ModifyTargetWeight", req, cb);
    }
    async ModifyModuleSecurityGroups(req, cb) {
        return this.request("ModifyModuleSecurityGroups", req, cb);
    }
    async CreateRoutes(req, cb) {
        return this.request("CreateRoutes", req, cb);
    }
    async ModifyHaVipAttribute(req, cb) {
        return this.request("ModifyHaVipAttribute", req, cb);
    }
    async ReleaseAddresses(req, cb) {
        return this.request("ReleaseAddresses", req, cb);
    }
    async ModifyModuleIpDirect(req, cb) {
        return this.request("ModifyModuleIpDirect", req, cb);
    }
    async DescribeTargets(req, cb) {
        return this.request("DescribeTargets", req, cb);
    }
    async ModifyVpcAttribute(req, cb) {
        return this.request("ModifyVpcAttribute", req, cb);
    }
    async DescribeSecurityGroupAssociationStatistics(req, cb) {
        return this.request("DescribeSecurityGroupAssociationStatistics", req, cb);
    }
    async DescribePeakBaseOverview(req, cb) {
        return this.request("DescribePeakBaseOverview", req, cb);
    }
    async ModifyAddressAttribute(req, cb) {
        return this.request("ModifyAddressAttribute", req, cb);
    }
    async RemovePrivateIpAddresses(req, cb) {
        return this.request("RemovePrivateIpAddresses", req, cb);
    }
    async CreateSecurityGroupPolicies(req, cb) {
        return this.request("CreateSecurityGroupPolicies", req, cb);
    }
    async DeleteImage(req, cb) {
        return this.request("DeleteImage", req, cb);
    }
}
