/*! wmf.js (C) 2020-present SheetJS LLC -- https://sheetjs.com */
module.exports=function(r){var i={};function s(t){if(i[t])return i[t].exports;var e=i[t]={i:t,l:!1,exports:{}};return r[t].call(e.exports,e,e.exports,s),e.l=!0,e.exports}return s.m=r,s.c=i,s.d=function(t,e,r){s.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},s.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(s.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)s.d(r,i,function(t){return e[t]}.bind(null,i));return r},s.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return s.d(e,"a",e),e},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},s.p="",s(s.s=2)}([function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var r,c=!("undefined"==typeof Buffer||"undefined"==typeof process||void 0===process.versions||!process.versions.node);if(a.has_buf=c,a.Buffer_from=r,"undefined"!=typeof Buffer){var i=!Buffer.from;if(!i)try{Buffer.from("foo","utf8")}catch(t){i=!0}a.Buffer_from=r=i?function(t,e){return e?new Buffer(t,e):new Buffer(t)}:Buffer.from.bind(Buffer),Buffer.alloc||(Buffer.alloc=function(t){return new Buffer(t)}),Buffer.allocUnsafe||(Buffer.allocUnsafe=function(t){return new Buffer(t)})}a.new_raw_buf=function(t){return c?Buffer.alloc(t):new Array(t)},a.new_unsafe_buf=function(t){return c?Buffer.allocUnsafe(t):new Array(t)},a._chr=function(t){return String.fromCharCode(t)},a.chr0=/\u0000/g,a.chr1=/[\u0001-\u0006]/g;var l,s,u=function(t,e){return t[e]},d=function(t,e){return 256*t[e+1]+t[e]},_=function(t,e){var r=256*t[e+1]+t[e];return r<32768?r:-1*(65535-r+1)},p=function(t,e){return t[e+3]*(1<<24)+(t[e+2]<<16)+(t[e+1]<<8)+t[e]},E=function(t,e){return t[e+3]<<24|t[e+2]<<16|t[e+1]<<8|t[e]},g=function(t,e){return t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3]},b=function(t,e,r){for(var i=[],s=e;s<r;s+=2)i.push(String.fromCharCode(d(t,s)));return i.join("").replace(a.chr0,"")},n=a.__utf16le=b,v=function(t,e,r){for(var i=[],s=e;s<e+r;++s)i.push(("0"+t[s].toString(16)).slice(-2));return i.join("")},f=v,T=function(t,e,r){for(var i=[],s=e;s<r;s++)i.push(String.fromCharCode(u(t,s)));return i.join("")},h=T,S=function(t,e){var r=p(t,e);return 0<r?T(t,e+4,e+4+r-1):""},o=S,C=function(t,e){var r=p(t,e);return 0<r?T(t,e+4,e+4+r-1):""},M=C,y=function(t,e){var r=2*p(t,e);return 0<r?T(t,e+4,e+4+r-1):""},A=y;l=s=function(t,e){var r=p(t,e);return 0<r?b(t,e+4,e+4+r):""};function w(t,e){var r=p(t,e);return 0<r?T(t,e+4,e+4+r):""}function B(t,e){return function(t,e){for(var r=1-2*(t[e+7]>>>7),i=((127&t[e+7])<<4)+(t[e+6]>>>4&15),s=15&t[e+6],a=5;0<=a;--a)s=256*s+t[e+a];return 2047==i?0==s?1/0*r:NaN:(0==i?i=-1022:(i-=1023,s+=Math.pow(2,52)),r*Math.pow(2,i-52)*s)}(t,e)}var m=w,O=B;function k(t,e){var r,i,s,a,n,f="",h=0,o=[];switch(e){case"dbcs":if(n=this.l,c&&Buffer.isBuffer(this))f=this.slice(this.l,this.l+2*t).toString("utf16le");else for(a=0;a<t;++a)f+=String.fromCharCode(d(this,n)),n+=2;t*=2;break;case"utf8":f=T(this,this.l,this.l+t);break;case"utf16le":t*=2,f=b(this,this.l,this.l+t);break;case"wstr":return k.call(this,t,"dbcs");case"lpstr-ansi":f=S(this,this.l),t=4+p(this,this.l);break;case"lpstr-cp":f=C(this,this.l),t=4+p(this,this.l);break;case"lpwstr":f=y(this,this.l),t=4+2*p(this,this.l);break;case"lpp4":t=4+p(this,this.l),f=l(this,this.l),2&t&&(t+=2);break;case"8lpp4":t=4+p(this,this.l),f=m(this,this.l),3&t&&(t+=4-(3&t));break;case"cstr":for(t=0,f="";0!==(i=u(this,this.l+t++));)o.push(String.fromCharCode(i));f=o.join("");break;case"_wstr":for(t=0,f="";0!==(i=d(this,this.l+t));)o.push(String.fromCharCode(i)),t+=2;t+=2,f=o.join("");break;case"dbcs-cont":for(f="",n=this.l,a=0;a<t;++a){if(this.lens&&-1!==this.lens.indexOf(n))return i=u(this,n),this.l=n+1,s=k.call(this,t-a,i?"dbcs-cont":"sbcs-cont"),o.join("")+s;o.push(String.fromCharCode(d(this,n))),n+=2}f=o.join(""),t*=2;break;case"cpstr":case"sbcs-cont":for(f="",n=this.l,a=0;a!=t;++a){if(this.lens&&-1!==this.lens.indexOf(n))return i=u(this,n),this.l=n+1,s=k.call(this,t-a,i?"dbcs-cont":"sbcs-cont"),o.join("")+s;o.push(String.fromCharCode(u(this,n))),n+=1}f=o.join("");break;default:switch(t){case 1:return h=u(this,this.l),this.l++,h;case 2:return h=("i"===e?_:d)(this,this.l),this.l+=2,h;case 4:case-4:return"i"===e||0==(128&this[this.l+3])?(h=(0<t?E:g)(this,this.l),this.l+=4,h):(r=p(this,this.l),this.l+=4,r);case 8:case-8:if("f"===e)return r=8==t?O(this,this.l):O([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,r;t=8;case 16:f=v(this,this.l,t)}}return this.l+=t,f}c&&(a.__utf16le=b=function(t,e,r){return Buffer.isBuffer(t)?t.toString("utf16le",e,r).replace(a.chr0,""):n(t,e,r)},v=function(t,e,r){return Buffer.isBuffer(t)?t.toString("hex",e,e+r):f(t,e,r)},S=function(t,e){if(!Buffer.isBuffer(t))return o(t,e);var r=t.readUInt32LE(e);return 0<r?t.toString("utf8",e+4,e+4+r-1):""},C=function(t,e){if(!Buffer.isBuffer(t))return M(t,e);var r=t.readUInt32LE(e);return 0<r?t.toString("utf8",e+4,e+4+r-1):""},y=function(t,e){if(!Buffer.isBuffer(t))return A(t,e);var r=2*t.readUInt32LE(e);return t.toString("utf16le",e+4,e+4+r-1)},l=function(t,e){if(!Buffer.isBuffer(t))return s(t,e);var r=t.readUInt32LE(e);return t.toString("utf16le",e+4,e+4+r)},m=function(t,e){if(!Buffer.isBuffer(t))return w(t,e);var r=t.readUInt32LE(e);return t.toString("utf8",e+4,e+4+r)},T=function(t,e,r){return Buffer.isBuffer(t)?t.toString("utf8",e,r):h(t,e,r)},O=function(t,e){return Buffer.isBuffer(t)?t.readDoubleLE(e):B(t,e)}),a.ReadShift=k;function x(t,e,r){var i,s,a,n,f,h,o,c,l,u=0,d=0;if("dbcs"===r){if("string"!=typeof e)throw new Error("expected string");for(d=0;d!=e.length;++d)o=this,c=e.charCodeAt(d),l=this.l+2*d,o[l]=255&c,o[l+1]=c>>>8&255;u=2*e.length}else if("sbcs"===r){for(e=e.replace(/[^\x00-\x7F]/g,"_"),d=0;d!=e.length;++d)this[this.l+d]=255&e.charCodeAt(d);u=e.length}else{if("hex"===r){for(;d<t;++d)this[this.l++]=parseInt(e.slice(2*d,2*d+2),16)||0;return this}if("utf16le"===r){var _=Math.min(this.l+t,this.length);for(d=0;d<Math.min(e.length,t);++d){var p=e.charCodeAt(d);this[this.l++]=255&p,this[this.l++]=p>>8}for(;this.l<_;)this[this.l++]=0;return this}if("number"==typeof e)switch(t){case 1:u=1,this[this.l]=255&e;break;case 2:u=2,this[this.l]=255&e,e>>>=8,this[this.l+1]=255&e;break;case 3:u=3,this[this.l]=255&e,e>>>=8,this[this.l+1]=255&e,e>>>=8,this[this.l+2]=255&e;break;case 4:u=4,f=e,h=(n=this).l,n[h]=255&f,n[h+1]=f>>>8&255,n[h+2]=f>>>16&255,n[h+3]=f>>>24&255;break;case 8:if(u=8,"f"===r){!function(t,e,r){var i=(e<0||1/e==-1/0?1:0)<<7,s=0,a=0,n=i?-e:e;isFinite(n)?0==n?s=a=0:(s=Math.floor(Math.log(n)/Math.LN2),a=n*Math.pow(2,52-s),s<=-1023&&(!isFinite(a)||a<Math.pow(2,52))?s=-1022:(a-=Math.pow(2,52),s+=1023)):(s=2047,a=isNaN(e)?26985:0);for(var f=0;f<=5;++f,a/=256)t[r+f]=255&a;t[r+6]=(15&s)<<4|15&a,t[r+7]=s>>4|i}(this,e,this.l);break}case 16:break;case-4:u=4,s=e,a=(i=this).l,i[a]=255&s,i[a+1]=s>>8&255,i[a+2]=s>>16&255,i[a+3]=s>>24&255}}return this.l+=u,this}function I(t,e){var r=v(this,this.l,t.length>>1);if(r!==t)throw new Error(e+"Expected "+t+" saw "+r);this.l+=t.length>>1}a.WriteShift=x,a.CheckField=I;function P(t,e){t.l=e,t.read_shift=k,t.chk=I,t.write_shift=x}a.prep_blob=P;a.new_buf=function(t){var e=a.new_raw_buf(t);return P(e,0),e};var F=function(t){for(var e=!0,r=0;r<t.length;++r)Array.isArray(t[r])||(e=!1);if(e)return[].concat.apply([],t);var i=0,s=0;for(s=0;s<t.length;++s)i+=t[s].length;var a=new Uint8Array(i);for(i=s=0;s<t.length;i+=t[s].length,++s)a.set(t[s],i);return a};a.bconcat=F,c&&(a.bconcat=F=function(t){return Buffer.isBuffer(t[0])?Buffer.concat(t):[].concat.apply([],t)})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});function et(t){if(0==t.length)return null;it.prep_blob(t,0);var e,r=t.read_shift(4),i=0,s=0,a=0,n=0;s=12==r?(i=t.read_shift(2),t.read_shift(2)):(i=t.read_shift(4,"i"),t.read_shift(4,"i")),t.read_shift(2);var f={Width:i,Height:s,BitCount:e=t.read_shift(2)};return 12!=r&&(a=t.read_shift(4),n=t.read_shift(4),t.read_shift(4,"i"),t.read_shift(4,"i"),t.read_shift(4),t.read_shift(4),f.Compression=a,24==e&&3*s*i<n&&(i=f.Width=n/(3*s))),n==t.length-t.l&&(f.ImageData=t.slice(t.l,t.length),it.prep_blob(f.ImageData,0)),f}function rt(t,e){for(var r=0;r<t.length;++r)if(!t[r])return void(t[r]=e);t.push(e)}var it=r(0),st=r(4);e.get_actions_prepped_bytes=function(t){var e=[],r=t.read_shift(2);if(1!=r&&2!=r)throw"Header: Type "+r+" must be 1 or 2";if(9!=(r=t.read_shift(2)))throw"Header: HeaderSize "+r+" must be 9";if(256!=(r=t.read_shift(2))&&768!=r)throw"Header: Version "+r+" must be 0x0100 or 0x0300";t.l+=4;var i=t.read_shift(2),s=Array.from({length:i},function(){return null});t.l+=4,t.l+=2;for(var a=0,n=0,f=0,h=0,o=0,c=[],l=[],u={},d=-1;t.l<t.length;){r=t.read_shift(4);var _=t.l+2*r-4;a=t.read_shift(2);var p=st.WMFRecords[a];if(0==a)break;switch(a){case 1574:var E=t.read_shift(2);st.WMFEscapes[E];switch(E){case 15:var g=t.read_shift(2),b=t.read_shift(4);if(1128680791!=b)throw"Escape: Comment ID 0x"+b.toString(16)+" != 0x43464D57";if(1!=(b=t.read_shift(4)))throw"Escape: Comment Type 0x"+b.toString(16)+" != 0x00000001";if(65536!=(b=t.read_shift(4)))throw"Escape: Version 0x"+b.toString(16)+" != 0x00010000";t.read_shift(2);if(t.l+=4,0==n)f=t.read_shift(4);else{var v=t.read_shift(4);if(v!=f)throw"Escape: CommentRecordCount "+v+" != "+f}var T=t.read_shift(4),S=t.read_shift(4);if(0<n&&T+S!=h)throw"Escape: "+h+" != "+T+" + "+S;h=S;var C=t.read_shift(4);if(0==n){if(C!=T+S)throw"Escape: "+C+" != "+T+" + "+S;o=C}else if(o!=C)throw"Escape: "+o+" != "+C;if(g!=_-t.l+34)throw"Escape: Sizes "+g+" != "+(_-t.l)+" + 34";if(_-t.l!=T)throw"Escape: CRSize "+T+" != "+(_-t.l);if(c.push(t.slice(t.l,_)),++n==f){var M=it.bconcat(c);it.prep_blob(M,0)}break;default:throw"Escape: Unrecognized META_ESCAPE Type 0x"+E.toString(16)}break;case 2368:var y=r!=3+(a>>8),A=t.read_shift(4),w=t.read_shift(2,"i"),B=t.read_shift(2,"i");y||(t.l+=2);var m=t.read_shift(2,"i"),O=t.read_shift(2,"i"),k=t.read_shift(2,"i"),x={t:"cpy",src:[[B,O],[w,m]],dst:[t.read_shift(2,"i"),k],rop:A,s:Object.assign({},u)};if(y){var I=et(t.slice(t.l,_));x.data=I}e.push(x);break;case 2881:y=r!=3+(a>>8),A=t.read_shift(4);var P=t.read_shift(2,"i"),F=t.read_shift(2,"i");w=t.read_shift(2,"i"),B=t.read_shift(2,"i");y||(t.l+=2);var N=t.read_shift(2,"i"),R=t.read_shift(2,"i");k=t.read_shift(2,"i"),x={t:"str",src:[[B,F],[w,P]],dst:[[t.read_shift(2,"i"),R],[k,N]],rop:A,s:Object.assign({},u)};if(y){I=et(t.slice(t.l,_));x.data=I}e.push(x);break;case 2610:var L=t.read_shift(2),D=t.read_shift(2),j=t.read_shift(2);6&t.read_shift(2)&&(t.l+=8);var W=t.read_shift(j,"cpstr");t.l,e.push({t:"text",v:W,p:[D,L],s:Object.assign({},u)});break;case 805:case 804:for(var H=t.read_shift(2),U=[],z=0;z<H;++z)U.push([t.read_shift(2),t.read_shift(2)]);e.push({t:"poly",p:U,g:805!==a,s:Object.assign({},u)});break;case 1336:var G=t.read_shift(2),J=[],X=[];for(z=0;z<G;++z)X[z]=t.read_shift(2);for(z=0;z<X.length;++z){J[z]=[];for(var Y=0;Y<X[z];++Y)J[z].push([t.read_shift(2),t.read_shift(2)]);e.push({t:"poly",p:J[z],g:!0,s:Object.assign({},u)})}break;case 764:(V={}).Brush={Style:t.read_shift(2),Color:t.read_shift(4),Hatch:t.read_shift(2)},rt(s,V);break;case 763:var V={Font:{}},K=(m=t.read_shift(2,"i"),O=t.read_shift(2,"i"),t.read_shift(2,"i")),Z=(t.read_shift(2,"i"),t.read_shift(2,"i")),q=!!t.read_shift(1),Q=(t.read_shift(1),t.read_shift(1),t.read_shift(1),t.read_shift(1),t.read_shift(1),t.read_shift(1),t.read_shift(1),t.read_shift(32,"cstr"));V.Font.Name=Q,V.Font.Height=m,V.Font.Weight=Z,V.Font.Italic=q,V.Font.Angle=K/10,rt(s,V);break;case 762:(V={}).Pen={Style:t.read_shift(2),Width:255&t.read_shift(4),Color:t.read_shift(4)},rt(s,V);break;case 496:s[$=t.read_shift(2)]=null;break;case 300:t.read_shift(2);break;case 301:var $=t.read_shift(2);Object.assign(u,s[$]);break;case 1046:u.ClipRect=[[0,0],[0,0]],u.ClipRect[1][1]=t.read_shift(2),u.ClipRect[1][0]=t.read_shift(2),u.ClipRect[0][1]=t.read_shift(2),u.ClipRect[0][0]=t.read_shift(2);break;case 295:var tt=t.read_shift(2,"i");u=l[d=0<=tt?tt:d+tt];break;case 30:l.push(u),d=l.length-1,u=JSON.parse(JSON.stringify(u));break;case 258:u.BkMode=t.read_shift(2);break;case 259:u.MapMode=t.read_shift(2);break;case 262:u.PolyFillMode=t.read_shift(2);break;case 263:u.StretchMode=t.read_shift(2);break;case 302:u.TextAlignmentMode=t.read_shift(2);break;case 521:u.TextColor=t.read_shift(4);break;case 524:u.Extent=[0,0],u.Extent[1]=t.read_shift(2),u.Extent[0]=t.read_shift(2);break;case 523:u.Origin=[0,0],u.Origin[1]=t.read_shift(2),u.Origin[0]=t.read_shift(2);break;default:console.log(p)}t.l=_}if(0!==a)throw"Record: Last Record Type "+a+" is not EOF type";return e},e.image_size_prepped_bytes=function(t){var e=t.read_shift(2);if(1!=e&&2!=e)throw"Header: Type "+e+" must be 1 or 2";if(9!=(e=t.read_shift(2)))throw"Header: HeaderSize "+e+" must be 9";if(256!=(e=t.read_shift(2))&&768!=e)throw"Header: Version "+e+" must be 0x0100 or 0x0300";t.l=18;for(var r=0;t.l<t.length;){e=t.read_shift(4);var i=t.l+2*e-4;if(0==(r=t.read_shift(2)))break;if(524==r){var s=[NaN,NaN];return s[1]=t.read_shift(2),s[0]=t.read_shift(2),s}t.l=i}return[NaN,NaN]}},function(t,e,r){var i=r(3);t.exports=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=r(0),s=r(1),a=r(5);e.draw_canvas=a.draw_canvas,e.render_canvas=a.render_canvas,e.get_actions=function(t){return t instanceof ArrayBuffer?e.get_actions(new Uint8Array(t)):(i.prep_blob(t,0),s.get_actions_prepped_bytes(t))},e.image_size=function(t){return t instanceof ArrayBuffer?e.image_size(new Uint8Array(t)):(i.prep_blob(t,0),s.image_size_prepped_bytes(t))}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.WMFRecords={0:{n:"META_EOF"},1574:{n:"META_ESCAPE"},2368:{n:"META_DIBBITBLT"},2881:{n:"META_DIBSTRETCHBLT"},2610:{n:"META_EXTTEXTOUT"},805:{n:"META_POLYLINE"},804:{n:"META_POLYGON"},1336:{n:"META_POLYPOLYGON"},764:{n:"META_CREATEBRUSHINDIRECT"},763:{n:"META_CREATEFONTINDIRECT"},762:{n:"META_CREATEPENINDIRECT"},496:{n:"META_DELETEOBJECT"},300:{n:"META_SELECTCLIPREGION"},301:{n:"META_SELECTOBJECT"},1046:{n:"META_INTERSECTCLIPRECT"},53:{n:"META_REALIZEPALETTE"},295:{n:"META_RESTOREDC"},30:{n:"META_SAVEDC"},258:{n:"META_SETBKMODE"},259:{n:"META_SETMAPMODE"},55:{n:"META_SETPALENTRIES"},262:{n:"META_SETPOLYFILLMODE"},263:{n:"META_SETSTRETCHBLTMODE"},302:{n:"META_SETTEXTALIGN"},521:{n:"META_SETTEXTCOLOR"},524:{n:"META_SETWINDOWEXT"},523:{n:"META_SETWINDOWORG"},65535:{n:"META_SHEETJS"}},e.WMFEscapes={15:{n:"META_ESCAPE_ENHANCED_METAFILE"}}},function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i=e(0),s=e(1);n.css_color=function(t){return"#"+(255&t).toString(16).padStart(2,"0")+(t>>8&255).toString(16).padStart(2,"0")+(t>>16&255).toString(16).padStart(2,"0")},n.set_ctx_state=function(t,e){if(e){var r="";if(e.Font){e.Font.Italic&&(r+=" italic"),e.Font.Weight&&(r+=" "+(700==e.Font.Weight?"bold":400==e.Font.Weight?"":e.Font.Weight)),e.Font.Height<0?r+=" "+-e.Font.Height+"px":0<e.Font.Height&&(r+=" "+e.Font.Height+"px");var i=e.Font.Name||"";"System"==i&&(i="Calibri"),i&&(r+=" '"+i+"', sans-serif"),t.font=r.trim()}}},n.render_actions_to_context=function(t,a){t.forEach(function(t){switch(a.save(),n.set_ctx_state(a,t.s),t.t){case"poly":a.beginPath(),null!=t.s.Pen.Color&&(a.strokeStyle=n.css_color(t.s.Pen.Color)),0<t.s.Pen.Width&&(a.lineWidth=t.s.Pen.Width),null!=t.s.Brush.Color&&(a.fillStyle=n.css_color(t.s.Brush.Color)),a.moveTo(t.p[0][0],t.p[0][1]),t.p.slice(1).forEach(function(t){var e=t[0],r=t[1];a.lineTo(e,r)}),t.g&&a.closePath(),5!=t.s.Pen.Style&&a.stroke(),1!=t.s.Brush.Style&&a.fill();break;case"text":t.s&&t.s.TextColor&&(a.fillStyle=n.css_color(t.s.TextColor)),0!=t.s.Font.Angle?(a.translate(t.p[0],t.p[1]),a.rotate(-t.s.Font.Angle*Math.PI/180),a.fillText(t.v,0,0),a.translate(-t.p[0],-t.p[1])):a.fillText(t.v,t.p[0],t.p[1]);break;case"cpy":var e=a.getImageData(t.src[0][0],t.src[1][0],t.src[0][1],t.src[1][1]);a.putImageData(e,t.dst[0],t.dst[1]);break;case"str":if(t.data&&24==t.data.BitCount&&t.data.ImageData){for(var r=new Uint8ClampedArray(t.data.Width*t.data.Height*4),i=0;i<t.data.Width*t.data.Height;++i){var s=i%t.data.Width+t.data.Width*(t.data.Height-1-Math.floor(i/t.data.Width));r[4*i]=t.data.ImageData[3*s+2],r[4*i+1]=t.data.ImageData[3*s+1],r[4*i+2]=t.data.ImageData[3*s],r[4*i+3]=255}e=new ImageData(r,t.data.Width,t.data.Height);a.putImageData(e,t.dst[0][0],t.dst[1][0])}}a.restore()})},n.render_canvas=function(t,e){var r;t.forEach(function(t){r||t.s&&t.s.Extent&&t.s.Origin&&(e.width=t.s.Extent[0]-t.s.Origin[0],e.height=t.s.Extent[1]-t.s.Origin[1],(r=e.getContext("2d")).save(),r.fillStyle="rgb(255,255,255)",r.fillRect(0,0,t.s.Extent[0]-t.s.Origin[0],t.s.Extent[1]-t.s.Origin[1]),r.restore())}),r=r||e.getContext("2d"),n.render_actions_to_context(t,r)},n.draw_canvas=function(t,e){if(t instanceof ArrayBuffer)return n.draw_canvas(new Uint8Array(t),e);i.prep_blob(t,0);var r=s.get_actions_prepped_bytes(t);return n.render_canvas(r,e)}}]);