/*
  测试输出原始 JSON 的脚本（使用 ?raw=1）
*/

import axios from 'axios';

const API_BASE = 'http://127.0.0.1:5000';

async function testSSORaw() {
  console.log('开始测试 SSO API（原始JSON）...');
  try {
    const response = await axios.post(`${API_BASE}/api/auth/sso?raw=1`, {
      username: '23374368',
      password: '20050626Zxy.',
      userType: 'student',
      qq: '23374368'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 120000
    });

    console.log('SSO API 响应状态:', response.status);
    console.log('SSO API 响应数据:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('API 请求失败:');
      console.error('状态码:', error.response?.status);
      console.error('响应数据:', error.response?.data);
      console.error('错误信息:', error.message);
    } else {
      console.error('未知错误:', error);
    }
  }
}

testSSORaw();

