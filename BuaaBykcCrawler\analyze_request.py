#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import base64
import json
from crypto import aes_decrypt

def analyze_request_data():
    """分析您提供的请求数据"""
    
    # 您提供的请求参数
    ak = "VDBizWpuQAuTAKxiUNpsGz990hHYqlmbRYygtQxqkPFQQhFsQNAYVeaGjZDLhYBMb7KojGZdZqyEwEgG4qKNdEEJmf2JCGlZE5jOxURiDzdpff2FJPEixTqAsnVMJ1plotimCtQuADes3c4wDXBn/w6nP6aEpS9Eb0rOrslb2c0="
    sk = "eTEeEW1IcABTkQatu4Hv2UGcgEcji1kDOffYYr2w7Ew99WomaPlQlkkRGhAKfQb7bCNDt+z5ib2oAcY7zyDIBNqx03XlouUXb4u+mz+6uCbQ6/AE6KzBBOYtFpDUbzHgXu8aEiASFDl3cXtwYGq3qiaRA8vd6kBzkn+dgC9lat4="
    ts = "1756882265062"
    payload = "Y4lM12PjGEkZZbwQxd0+agdU75OLLJz5zkF0R+iLN+M="
    response = "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"
    
    print("=== 请求数据分析 ===")
    print(f"ak (RSA加密的AES密钥): {ak}")
    print(f"sk (RSA加密的数据签名): {sk}")
    print(f"ts (时间戳): {ts}")
    print(f"负载 (AES加密的数据): {payload}")
    print(f"响应长度: {len(response)} 字符")
    
    # 分析负载是否可能是AES密钥
    try:
        payload_decoded = base64.b64decode(payload)
        print(f"\n=== 负载分析 ===")
        print(f"负载解码后长度: {len(payload_decoded)} 字节")
        print(f"负载解码后内容: {payload_decoded}")
        
        # 检查是否是16字节（AES密钥长度）
        if len(payload_decoded) == 16:
            print("⚠️  负载长度正好是16字节，可能是AES密钥！")
            return payload_decoded
        else:
            print("❌ 负载长度不是16字节，不太可能是AES密钥")
            
    except Exception as e:
        print(f"❌ 负载解码失败: {e}")
    
    return None

def try_decrypt_with_payload_as_key():
    """尝试用负载作为AES密钥来解密响应"""
    
    payload = "Y4lM12PjGEkZZbwQxd0+agdU75OLLJz5zkF0R+iLN+M="
    response = "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"
    
    try:
        # 尝试用负载作为AES密钥
        potential_key = base64.b64decode(payload)
        response_data = base64.b64decode(response)
        
        print(f"\n=== 尝试解密 ===")
        print(f"使用密钥长度: {len(potential_key)} 字节")
        print(f"响应数据长度: {len(response_data)} 字节")
        
        # 尝试解密
        decrypted = aes_decrypt(response_data, potential_key)
        decrypted_str = decrypted.decode('utf-8')
        
        print(f"✅ 解密成功！")
        print(f"解密结果: {decrypted_str}")
        
        # 尝试解析为JSON
        try:
            json_data = json.loads(decrypted_str)
            print(f"✅ JSON解析成功！")
            print(f"JSON数据: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
        except:
            print("❌ 不是有效的JSON格式")
            
    except Exception as e:
        print(f"❌ 解密失败: {e}")

if __name__ == "__main__":
    # 分析请求数据
    potential_key = analyze_request_data()
    
    # 尝试解密
    try_decrypt_with_payload_as_key()
