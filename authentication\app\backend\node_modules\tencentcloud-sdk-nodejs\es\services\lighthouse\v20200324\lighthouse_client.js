import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("lighthouse.tencentcloudapi.com", "2020-03-24", clientConfig);
    }
    async ModifyFirewallTemplate(req, cb) {
        return this.request("ModifyFirewallTemplate", req, cb);
    }
    async DeleteBlueprints(req, cb) {
        return this.request("DeleteBlueprints", req, cb);
    }
    async CancelShareBlueprintAcrossAccounts(req, cb) {
        return this.request("CancelShareBlueprintAcrossAccounts", req, cb);
    }
    async CreateFirewallRules(req, cb) {
        return this.request("CreateFirewallRules", req, cb);
    }
    async ModifyInstancesAttribute(req, cb) {
        return this.request("ModifyInstancesAttribute", req, cb);
    }
    async DescribeCcnAttachedInstances(req, cb) {
        return this.request("DescribeCcnAttachedInstances", req, cb);
    }
    async DeleteKeyPairs(req, cb) {
        return this.request("DeleteKeyPairs", req, cb);
    }
    async ModifyDisksRenewFlag(req, cb) {
        return this.request("ModifyDisksRenewFlag", req, cb);
    }
    async RenameDockerContainer(req, cb) {
        return this.request("RenameDockerContainer", req, cb);
    }
    async ImportKeyPair(req, cb) {
        return this.request("ImportKeyPair", req, cb);
    }
    async DescribeImagesToShare(req, cb) {
        return this.request("DescribeImagesToShare", req, cb);
    }
    async InquirePriceCreateDisks(req, cb) {
        return this.request("InquirePriceCreateDisks", req, cb);
    }
    async TerminateInstances(req, cb) {
        return this.request("TerminateInstances", req, cb);
    }
    async DescribeBundleDiscount(req, cb) {
        return this.request("DescribeBundleDiscount", req, cb);
    }
    async ModifyBlueprintAttribute(req, cb) {
        return this.request("ModifyBlueprintAttribute", req, cb);
    }
    async RestartDockerContainers(req, cb) {
        return this.request("RestartDockerContainers", req, cb);
    }
    async DescribeFirewallTemplateRules(req, cb) {
        return this.request("DescribeFirewallTemplateRules", req, cb);
    }
    async ModifyDiskBackupsAttribute(req, cb) {
        return this.request("ModifyDiskBackupsAttribute", req, cb);
    }
    async DescribeDockerContainerConfiguration(req, cb) {
        return this.request("DescribeDockerContainerConfiguration", req, cb);
    }
    async InquirePriceRenewDisks(req, cb) {
        return this.request("InquirePriceRenewDisks", req, cb);
    }
    async ModifyDisksAttribute(req, cb) {
        return this.request("ModifyDisksAttribute", req, cb);
    }
    async RerunDockerContainer(req, cb) {
        return this.request("RerunDockerContainer", req, cb);
    }
    async DescribeDisksReturnable(req, cb) {
        return this.request("DescribeDisksReturnable", req, cb);
    }
    async ModifyFirewallRules(req, cb) {
        return this.request("ModifyFirewallRules", req, cb);
    }
    async ResizeDisks(req, cb) {
        return this.request("ResizeDisks", req, cb);
    }
    async CreateDiskBackup(req, cb) {
        return this.request("CreateDiskBackup", req, cb);
    }
    async DescribeDiskDiscount(req, cb) {
        return this.request("DescribeDiskDiscount", req, cb);
    }
    async DescribeFirewallTemplateQuota(req, cb) {
        return this.request("DescribeFirewallTemplateQuota", req, cb);
    }
    async DescribeAllScenes(req, cb) {
        return this.request("DescribeAllScenes", req, cb);
    }
    async CreateFirewallTemplate(req, cb) {
        return this.request("CreateFirewallTemplate", req, cb);
    }
    async AttachDisks(req, cb) {
        return this.request("AttachDisks", req, cb);
    }
    async DeleteDiskBackups(req, cb) {
        return this.request("DeleteDiskBackups", req, cb);
    }
    async AssociateInstancesKeyPairs(req, cb) {
        return this.request("AssociateInstancesKeyPairs", req, cb);
    }
    async DeleteFirewallTemplate(req, cb) {
        return this.request("DeleteFirewallTemplate", req, cb);
    }
    async DisassociateInstancesKeyPairs(req, cb) {
        return this.request("DisassociateInstancesKeyPairs", req, cb);
    }
    async DescribeBlueprints(req, cb) {
        return this.request("DescribeBlueprints", req, cb);
    }
    async ModifyDockerContainer(req, cb) {
        return this.request("ModifyDockerContainer", req, cb);
    }
    async DescribeInstancesDeniedActions(req, cb) {
        return this.request("DescribeInstancesDeniedActions", req, cb);
    }
    async InquirePriceCreateInstances(req, cb) {
        return this.request("InquirePriceCreateInstances", req, cb);
    }
    async CreateInstanceSnapshot(req, cb) {
        return this.request("CreateInstanceSnapshot", req, cb);
    }
    async ModifyDisksBackupQuota(req, cb) {
        return this.request("ModifyDisksBackupQuota", req, cb);
    }
    async DescribeRegions(req, cb) {
        return this.request("DescribeRegions", req, cb);
    }
    async ModifySnapshotAttribute(req, cb) {
        return this.request("ModifySnapshotAttribute", req, cb);
    }
    async DescribeDockerContainerDetail(req, cb) {
        return this.request("DescribeDockerContainerDetail", req, cb);
    }
    async DescribeBlueprintInstances(req, cb) {
        return this.request("DescribeBlueprintInstances", req, cb);
    }
    async DescribeDockerContainers(req, cb) {
        return this.request("DescribeDockerContainers", req, cb);
    }
    async SyncBlueprint(req, cb) {
        return this.request("SyncBlueprint", req, cb);
    }
    async TerminateDisks(req, cb) {
        return this.request("TerminateDisks", req, cb);
    }
    async CreateFirewallTemplateRules(req, cb) {
        return this.request("CreateFirewallTemplateRules", req, cb);
    }
    async DescribeSnapshotsDeniedActions(req, cb) {
        return this.request("DescribeSnapshotsDeniedActions", req, cb);
    }
    async IsolateInstances(req, cb) {
        return this.request("IsolateInstances", req, cb);
    }
    async CreateBlueprint(req, cb) {
        return this.request("CreateBlueprint", req, cb);
    }
    async DescribeDockerActivities(req, cb) {
        return this.request("DescribeDockerActivities", req, cb);
    }
    async DetachDisks(req, cb) {
        return this.request("DetachDisks", req, cb);
    }
    async ModifyImageSharePermission(req, cb) {
        return this.request("ModifyImageSharePermission", req, cb);
    }
    async StartDockerContainers(req, cb) {
        return this.request("StartDockerContainers", req, cb);
    }
    async DescribeModifyInstanceBundles(req, cb) {
        return this.request("DescribeModifyInstanceBundles", req, cb);
    }
    async CreateKeyPair(req, cb) {
        return this.request("CreateKeyPair", req, cb);
    }
    async DescribeBundles(req, cb) {
        return this.request("DescribeBundles", req, cb);
    }
    async ShareBlueprintAcrossAccounts(req, cb) {
        return this.request("ShareBlueprintAcrossAccounts", req, cb);
    }
    async DescribeInstancesTrafficPackages(req, cb) {
        return this.request("DescribeInstancesTrafficPackages", req, cb);
    }
    async DescribeDiskBackups(req, cb) {
        return this.request("DescribeDiskBackups", req, cb);
    }
    async AttachCcn(req, cb) {
        return this.request("AttachCcn", req, cb);
    }
    async ModifyInstancesRenewFlag(req, cb) {
        return this.request("ModifyInstancesRenewFlag", req, cb);
    }
    async ResetInstance(req, cb) {
        return this.request("ResetInstance", req, cb);
    }
    async DescribeFirewallRules(req, cb) {
        return this.request("DescribeFirewallRules", req, cb);
    }
    async DescribeGeneralResourceQuotas(req, cb) {
        return this.request("DescribeGeneralResourceQuotas", req, cb);
    }
    async CreateDisks(req, cb) {
        return this.request("CreateDisks", req, cb);
    }
    async ApplyFirewallTemplate(req, cb) {
        return this.request("ApplyFirewallTemplate", req, cb);
    }
    async DescribeSnapshots(req, cb) {
        return this.request("DescribeSnapshots", req, cb);
    }
    async DescribeDiskBackupsDeniedActions(req, cb) {
        return this.request("DescribeDiskBackupsDeniedActions", req, cb);
    }
    async RunDockerContainers(req, cb) {
        return this.request("RunDockerContainers", req, cb);
    }
    async DescribeInstanceVncUrl(req, cb) {
        return this.request("DescribeInstanceVncUrl", req, cb);
    }
    async StartInstances(req, cb) {
        return this.request("StartInstances", req, cb);
    }
    async DescribeKeyPairs(req, cb) {
        return this.request("DescribeKeyPairs", req, cb);
    }
    async DescribeDiskConfigs(req, cb) {
        return this.request("DescribeDiskConfigs", req, cb);
    }
    async ModifyFirewallRuleDescription(req, cb) {
        return this.request("ModifyFirewallRuleDescription", req, cb);
    }
    async ResetAttachCcn(req, cb) {
        return this.request("ResetAttachCcn", req, cb);
    }
    async RebootInstances(req, cb) {
        return this.request("RebootInstances", req, cb);
    }
    async ModifyInstancesBundle(req, cb) {
        return this.request("ModifyInstancesBundle", req, cb);
    }
    async StopInstances(req, cb) {
        return this.request("StopInstances", req, cb);
    }
    async StopDockerContainers(req, cb) {
        return this.request("StopDockerContainers", req, cb);
    }
    async ApplyDiskBackup(req, cb) {
        return this.request("ApplyDiskBackup", req, cb);
    }
    async DescribeInstancesDiskNum(req, cb) {
        return this.request("DescribeInstancesDiskNum", req, cb);
    }
    async DeleteFirewallTemplateRules(req, cb) {
        return this.request("DeleteFirewallTemplateRules", req, cb);
    }
    async DescribeFirewallTemplateRuleQuota(req, cb) {
        return this.request("DescribeFirewallTemplateRuleQuota", req, cb);
    }
    async DescribeInstancesReturnable(req, cb) {
        return this.request("DescribeInstancesReturnable", req, cb);
    }
    async DescribeFirewallRulesTemplate(req, cb) {
        return this.request("DescribeFirewallRulesTemplate", req, cb);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async RemoveDockerContainers(req, cb) {
        return this.request("RemoveDockerContainers", req, cb);
    }
    async IsolateDisks(req, cb) {
        return this.request("IsolateDisks", req, cb);
    }
    async InquirePriceCreateBlueprint(req, cb) {
        return this.request("InquirePriceCreateBlueprint", req, cb);
    }
    async DeleteFirewallRules(req, cb) {
        return this.request("DeleteFirewallRules", req, cb);
    }
    async ResetInstancesPassword(req, cb) {
        return this.request("ResetInstancesPassword", req, cb);
    }
    async DescribeDisksDeniedActions(req, cb) {
        return this.request("DescribeDisksDeniedActions", req, cb);
    }
    async DescribeZones(req, cb) {
        return this.request("DescribeZones", req, cb);
    }
    async DescribeDisks(req, cb) {
        return this.request("DescribeDisks", req, cb);
    }
    async InquirePriceRenewInstances(req, cb) {
        return this.request("InquirePriceRenewInstances", req, cb);
    }
    async DeleteSnapshots(req, cb) {
        return this.request("DeleteSnapshots", req, cb);
    }
    async DescribeFirewallTemplateApplyRecords(req, cb) {
        return this.request("DescribeFirewallTemplateApplyRecords", req, cb);
    }
    async ResetFirewallTemplateRules(req, cb) {
        return this.request("ResetFirewallTemplateRules", req, cb);
    }
    async DescribeResetInstanceBlueprints(req, cb) {
        return this.request("DescribeResetInstanceBlueprints", req, cb);
    }
    async ApplyInstanceSnapshot(req, cb) {
        return this.request("ApplyInstanceSnapshot", req, cb);
    }
    async DetachCcn(req, cb) {
        return this.request("DetachCcn", req, cb);
    }
    async RenewDisks(req, cb) {
        return this.request("RenewDisks", req, cb);
    }
    async RenewInstances(req, cb) {
        return this.request("RenewInstances", req, cb);
    }
    async DescribeScenes(req, cb) {
        return this.request("DescribeScenes", req, cb);
    }
    async CreateInstances(req, cb) {
        return this.request("CreateInstances", req, cb);
    }
    async ReplaceFirewallTemplateRule(req, cb) {
        return this.request("ReplaceFirewallTemplateRule", req, cb);
    }
    async DescribeFirewallTemplates(req, cb) {
        return this.request("DescribeFirewallTemplates", req, cb);
    }
}
