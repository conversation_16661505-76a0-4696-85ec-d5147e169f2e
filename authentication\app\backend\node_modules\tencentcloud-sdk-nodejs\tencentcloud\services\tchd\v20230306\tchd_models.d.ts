/**
 * DescribeEventStatistics返回参数结构体
 */
export interface DescribeEventStatisticsResponse {
    /**
     * 正常产品数
     */
    Data?: TotalStatus;
    /**
     * 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
     */
    RequestId?: string;
}
/**
 * DescribeEvents返回参数结构体
 */
export interface DescribeEventsResponse {
    /**
     * 事件详情列表
     */
    Data?: ProductEventList;
    /**
     * 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
     */
    RequestId?: string;
}
/**
 * 事件详情信息，包含：产品名称、地域名称、事件开始时间、事件结束时间、事件当前状态；
 */
export interface EventDetail {
    /**
     * 产品ID
     */
    ProductId?: string;
    /**
     * 产品名称
     */
    ProductName?: string;
    /**
     * 地域ID，非区域性地域返回non-regional
     */
    RegionId?: string;
    /**
     * 地域名称
     */
    RegionName?: string;
    /**
     * 事件开始时间
     */
    StartTime?: string;
    /**
     * 事件结束时间，当事件正在发生还未结束时，结束时间返回空
     */
    EndTime?: string;
    /**
     * 事件当前状态：提示、异常、正常
     */
    CurrentStatus?: string;
}
/**
 * 产品可用性事件详情列表
 */
export interface ProductEventList {
    /**
     * 事件详情列表
     */
    EventList?: Array<EventDetail>;
}
/**
 * 状态汇总
 */
export interface TotalStatus {
    /**
     * 正常状态的数目
     */
    NormalCount?: number;
    /**
     * 通知状态的数目
     */
    NotifyCount?: number;
    /**
     * 异常状态的数目
     */
    AbnormalCount?: number;
}
/**
 * DescribeEventStatistics请求参数结构体
 */
export interface DescribeEventStatisticsRequest {
    /**
     * 1. 查询非区域性产品事件时，地域ID指定为：non-regional
  2. 其他地域ID取值请参考：https://cloud.tencent.com/document/api/213/15692
     */
    RegionId: string;
    /**
     * 1. 不指定产品列表时将查询所有产品。
  2. 产品ID示例：cvm、lb、cdb、cdn、crs
     */
    ProductIds?: Array<string>;
}
/**
 * DescribeEvents请求参数结构体
 */
export interface DescribeEventsRequest {
    /**
     * 事件的发生日期
     */
    EventDate: string;
    /**
     * 1. 不指定产品列表时将查询所有产品。
  2. 产品ID示例：cvm、lb、cdb、cdn、crs
     */
    ProductIds?: Array<string>;
    /**
     * 1. 不指定地域列表时将查询所有地域。
  2. 查询非区域性产品事件时，地域ID指定为：non-regional
  3. 其他地域ID取值请参考：https://cloud.tencent.com/document/api/213/15692
     */
    RegionIds?: Array<string>;
}
