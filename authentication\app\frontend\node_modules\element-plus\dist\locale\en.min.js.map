{"version": 3, "file": "en.min.js", "sources": ["../../../../packages/locale/lang/en.ts"], "sourcesContent": ["export default {\n  name: 'en',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Clear',\n      defaultLabel: 'color picker',\n      description:\n        'current color is {color}. press enter to select a new color.',\n      alphaLabel: 'pick alpha value',\n    },\n    datepicker: {\n      now: 'Now',\n      today: 'Today',\n      cancel: 'Cancel',\n      clear: 'Clear',\n      confirm: 'OK',\n      dateTablePrompt:\n        'Use the arrow keys and enter to select the day of the month',\n      monthTablePrompt: 'Use the arrow keys and enter to select the month',\n      yearTablePrompt: 'Use the arrow keys and enter to select the year',\n      selectedDate: 'Selected date',\n      selectDate: 'Select date',\n      selectTime: 'Select time',\n      startDate: 'Start Date',\n      startTime: 'Start Time',\n      endDate: 'End Date',\n      endTime: 'End Time',\n      prevYear: 'Previous Year',\n      nextYear: 'Next Year',\n      prevMonth: 'Previous Month',\n      nextMonth: 'Next Month',\n      year: '',\n      month1: 'January',\n      month2: 'February',\n      month3: 'March',\n      month4: 'April',\n      month5: 'May',\n      month6: 'June',\n      month7: 'July',\n      month8: 'August',\n      month9: 'September',\n      month10: 'October',\n      month11: 'November',\n      month12: 'December',\n      week: 'week',\n      weeks: {\n        sun: 'Sun',\n        mon: 'Mon',\n        tue: 'Tue',\n        wed: 'Wed',\n        thu: 'Thu',\n        fri: 'Fri',\n        sat: 'Sat',\n      },\n      weeksFull: {\n        sun: 'Sunday',\n        mon: 'Monday',\n        tue: 'Tuesday',\n        wed: 'Wednesday',\n        thu: 'Thursday',\n        fri: 'Friday',\n        sat: 'Saturday',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Oct',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    inputNumber: {\n      decrease: 'decrease number',\n      increase: 'increase number',\n    },\n    select: {\n      loading: 'Loading',\n      noMatch: 'No matching data',\n      noData: 'No data',\n      placeholder: 'Select',\n    },\n    mention: {\n      loading: 'Loading',\n    },\n    dropdown: {\n      toggleDropdown: 'Toggle Dropdown',\n    },\n    cascader: {\n      noMatch: 'No matching data',\n      loading: 'Loading',\n      placeholder: 'Select',\n      noData: 'No data',\n    },\n    pagination: {\n      goto: 'Go to',\n      pagesize: '/page',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page',\n      prev: 'Go to previous page',\n      next: 'Go to next page',\n      currentPage: 'page {pager}',\n      prevPages: 'Previous {pager} pages',\n      nextPages: 'Next {pager} pages',\n      deprecationWarning:\n        'Deprecated usages detected, please refer to the el-pagination documentation for more details',\n    },\n    dialog: {\n      close: 'Close this dialog',\n    },\n    drawer: {\n      close: 'Close this dialog',\n    },\n    messagebox: {\n      title: 'Message',\n      confirm: 'OK',\n      cancel: 'Cancel',\n      error: 'Illegal input',\n      close: 'Close this dialog',\n    },\n    upload: {\n      deleteTip: 'press delete to remove',\n      delete: 'Delete',\n      preview: 'Preview',\n      continue: 'Continue',\n    },\n    slider: {\n      defaultLabel: 'slider between {min} and {max}',\n      defaultRangeStartLabel: 'pick start value',\n      defaultRangeEndLabel: 'pick end value',\n    },\n    table: {\n      emptyText: 'No Data',\n      confirmFilter: 'Confirm',\n      resetFilter: 'Reset',\n      clearFilter: 'All',\n      sumText: 'Sum',\n    },\n    tour: {\n      next: 'Next',\n      previous: 'Previous',\n      finish: 'Finish',\n    },\n    tree: {\n      emptyText: 'No Data',\n    },\n    transfer: {\n      noMatch: 'No matching data',\n      noData: 'No data',\n      titles: ['List 1', 'List 2'], // to be translated\n      filterPlaceholder: 'Enter keyword', // to be translated\n      noCheckedFormat: '{total} items', // to be translated\n      hasCheckedFormat: '{checked}/{total} checked', // to be translated\n    },\n    image: {\n      error: 'FAILED',\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes',\n      cancelButtonText: 'No',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left',\n      rightArrow: 'Carousel arrow right',\n      indicator: 'Carousel switch to index {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,8DAA8D,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,6DAA6D,CAAC,gBAAgB,CAAC,kDAAkD,CAAC,eAAe,CAAC,iDAAiD,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,8FAA8F,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,gCAAgC,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,eAAe,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}