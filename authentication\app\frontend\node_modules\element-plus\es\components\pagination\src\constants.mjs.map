{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/pagination/src/constants.ts"], "sourcesContent": ["import type { ComputedRef, InjectionKey, WritableComputedRef } from 'vue'\n\nexport interface ElPaginationContext {\n  currentPage?: WritableComputedRef<number>\n  pageCount?: ComputedRef<number>\n  disabled?: ComputedRef<boolean>\n  changeEvent?: (val: number) => void\n  handleSizeChange?: (val: number) => void\n}\n\nexport const elPaginationKey: InjectionKey<ElPaginationContext> =\n  Symbol('elPaginationKey')\n"], "names": [], "mappings": "AAAY,MAAC,eAAe,GAAG,MAAM,CAAC,iBAAiB;;;;"}