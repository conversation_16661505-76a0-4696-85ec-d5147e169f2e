import{_ as a,r as s}from"./index-bcbc0702.js";/* empty css                        *//* empty css               *//* empty css                */import{r as t,A as e,B as l,C as i,F as d,G as c,D as u,u as n,H as r,V as o,I as _,ae as v,af as p,E as f,K as y,X as m,Y as h,Z as g,L as b,a$ as k,a0 as C,a5 as $,b0 as w,a1 as z,b1 as j,O as D,aT as x,a4 as q}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const A={class:"dashboard"},F={class:"stat-content"},I={class:"stat-icon total-users"},L={class:"stat-info"},M={class:"stat-number"},U={class:"stat-content"},B={class:"stat-icon pending-users"},E={class:"stat-info"},G={class:"stat-number"},H={class:"stat-content"},K={class:"stat-icon today-registrations"},O={class:"stat-info"},R={class:"stat-number"},S={class:"stat-content"},T={class:"stat-info"},V={class:"card-header"},X={class:"chart-container"},Y={class:"chart-placeholder"},Z={class:"card-header"},J={class:"activity-list"},N={class:"activity-time"},P={class:"activity-content"},Q={class:"activity-action"},W={class:"activity-user"},aa={class:"quick-actions"},sa=a({__name:"Dashboard",setup(a){const sa=t({total_users:0,pending_users:0,today_registrations:0,system_status:"healthy"}),ta=t([]),ea=t("7d"),la=async()=>{try{const a=await s.get("/api/statistics/dashboard");if(a.success){const s=a.data.summary;sa.value={total_users:s.totalUsers||0,pending_users:s.pendingUsers||0,today_registrations:s.todayRegistrations||0,system_status:"healthy"},ta.value=a.data.recentActivities||[]}}catch(a){f.error("加载仪表板数据失败")}},ia=()=>{la(),f.success("活动数据已刷新")},da=a=>{ea.value=a,f.info(`切换到${a}视图`)},ca=a=>{const s=new Date(a),t=new Date-s;return t<6e4?"刚刚":t<36e5?`${Math.floor(t/6e4)}分钟前`:t<864e5?`${Math.floor(t/36e5)}小时前`:s.toLocaleDateString()};return e(()=>{la()}),(a,s)=>{const t=y,e=m,f=h,la=g,ua=b,na=k;return l(),i("div",A,[d(la,{gutter:20,class:"stats-row"},{default:c(()=>[d(f,{span:6},{default:c(()=>[d(e,{class:"stat-card"},{default:c(()=>[u("div",F,[u("div",I,[d(t,{size:"32"},{default:c(()=>[d(n(C))]),_:1})]),u("div",L,[u("div",M,r(sa.value.total_users),1),s[7]||(s[7]=u("div",{class:"stat-label"},"总用户数",-1))])])]),_:1})]),_:1}),d(f,{span:6},{default:c(()=>[d(e,{class:"stat-card"},{default:c(()=>[u("div",U,[u("div",B,[d(t,{size:"32"},{default:c(()=>[d(n($))]),_:1})]),u("div",E,[u("div",G,r(sa.value.pending_users),1),s[8]||(s[8]=u("div",{class:"stat-label"},"待审核用户",-1))])])]),_:1})]),_:1}),d(f,{span:6},{default:c(()=>[d(e,{class:"stat-card"},{default:c(()=>[u("div",H,[u("div",K,[d(t,{size:"32"},{default:c(()=>[d(n(w))]),_:1})]),u("div",O,[u("div",R,r(sa.value.today_registrations),1),s[9]||(s[9]=u("div",{class:"stat-label"},"今日注册",-1))])])]),_:1})]),_:1}),d(f,{span:6},{default:c(()=>[d(e,{class:"stat-card"},{default:c(()=>[u("div",S,[u("div",{class:o(["stat-icon system-status",sa.value.system_status])},[d(t,{size:"32"},{default:c(()=>[d(n(z))]),_:1})],2),u("div",T,[s[10]||(s[10]=u("div",{class:"stat-label"},"系统状态",-1)),u("div",{class:o(["stat-status",sa.value.system_status])},r("healthy"===sa.value.system_status?"正常":"异常"),3)])])]),_:1})]),_:1})]),_:1}),d(la,{gutter:20,class:"content-row"},{default:c(()=>[d(f,{span:16},{default:c(()=>[d(e,{class:"chart-card"},{header:c(()=>[u("div",V,[s[14]||(s[14]=u("span",null,"用户注册趋势",-1)),d(na,{size:"small"},{default:c(()=>[d(ua,{type:"7d"===ea.value?"primary":"",onClick:s[0]||(s[0]=a=>da("7d"))},{default:c(()=>s[11]||(s[11]=[_("7天",-1)])),_:1,__:[11]},8,["type"]),d(ua,{type:"30d"===ea.value?"primary":"",onClick:s[1]||(s[1]=a=>da("30d"))},{default:c(()=>s[12]||(s[12]=[_("30天",-1)])),_:1,__:[12]},8,["type"]),d(ua,{type:"90d"===ea.value?"primary":"",onClick:s[2]||(s[2]=a=>da("90d"))},{default:c(()=>s[13]||(s[13]=[_("90天",-1)])),_:1,__:[13]},8,["type"])]),_:1})])]),default:c(()=>[u("div",X,[u("div",Y,[d(t,{size:"64",color:"#ddd"},{default:c(()=>[d(n(j))]),_:1}),s[15]||(s[15]=u("p",null,"图表数据加载中...",-1))])])]),_:1})]),_:1}),d(f,{span:8},{default:c(()=>[d(e,{class:"activity-card"},{header:c(()=>[u("div",Z,[s[16]||(s[16]=u("span",null,"最近活动",-1)),d(ua,{text:"",size:"small",onClick:ia},{default:c(()=>[d(t,null,{default:c(()=>[d(n(D))]),_:1})]),_:1})])]),default:c(()=>[u("div",J,[(l(!0),i(v,null,p(ta.value,a=>(l(),i("div",{key:a.time,class:"activity-item"},[u("div",N,r(ca(a.time)),1),u("div",P,[u("span",Q,r(a.action),1),u("span",W,r(a.user),1)])]))),128))])]),_:1})]),_:1})]),_:1}),d(la,{gutter:20,class:"actions-row"},{default:c(()=>[d(f,{span:24},{default:c(()=>[d(e,{class:"actions-card"},{header:c(()=>s[17]||(s[17]=[u("span",null,"快捷操作",-1)])),default:c(()=>[u("div",aa,[d(ua,{type:"primary",onClick:s[3]||(s[3]=s=>a.$router.push("/admin/pending"))},{default:c(()=>[d(t,null,{default:c(()=>[d(n($))]),_:1}),s[18]||(s[18]=_(" 审核用户 ",-1))]),_:1,__:[18]}),d(ua,{type:"success",onClick:s[4]||(s[4]=s=>a.$router.push("/admin/users"))},{default:c(()=>[d(t,null,{default:c(()=>[d(n(C))]),_:1}),s[19]||(s[19]=_(" 用户管理 ",-1))]),_:1,__:[19]}),d(ua,{type:"warning",onClick:s[5]||(s[5]=s=>a.$router.push("/admin/config"))},{default:c(()=>[d(t,null,{default:c(()=>[d(n(x))]),_:1}),s[20]||(s[20]=_(" 系统配置 ",-1))]),_:1,__:[20]}),d(ua,{type:"info",onClick:s[6]||(s[6]=s=>a.$router.push("/admin/logs"))},{default:c(()=>[d(t,null,{default:c(()=>[d(n(q))]),_:1}),s[21]||(s[21]=_(" 查看日志 ",-1))]),_:1,__:[21]})])]),_:1})]),_:1})]),_:1})])}}},[["__scopeId","data-v-36e849d1"]]);export{sa as default};
