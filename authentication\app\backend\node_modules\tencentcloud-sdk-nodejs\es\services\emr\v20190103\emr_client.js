import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("emr.tencentcloudapi.com", "2019-01-03", clientConfig);
    }
    async ModifyUserManagerPwd(req, cb) {
        return this.request("ModifyUserManagerPwd", req, cb);
    }
    async CreateCluster(req, cb) {
        return this.request("CreateCluster", req, cb);
    }
    async ModifyYarnQueueV2(req, cb) {
        return this.request("ModifyYarnQueueV2", req, cb);
    }
    async DeployYarnConf(req, cb) {
        return this.request("DeployYarnConf", req, cb);
    }
    async DescribeCvmQuota(req, cb) {
        return this.request("DescribeCvmQuota", req, cb);
    }
    async TerminateInstance(req, cb) {
        return this.request("TerminateInstance", req, cb);
    }
    async DescribeHBaseTableOverview(req, cb) {
        return this.request("DescribeHBaseTableOverview", req, cb);
    }
    async DescribeServiceNodeInfos(req, cb) {
        return this.request("DescribeServiceNodeInfos", req, cb);
    }
    async ModifyYarnDeploy(req, cb) {
        return this.request("ModifyYarnDeploy", req, cb);
    }
    async DescribeNodeSpec(req, cb) {
        return this.request("DescribeNodeSpec", req, cb);
    }
    async DescribeKyuubiQueryInfo(req, cb) {
        return this.request("DescribeKyuubiQueryInfo", req, cb);
    }
    async DeleteAutoScaleStrategy(req, cb) {
        return this.request("DeleteAutoScaleStrategy", req, cb);
    }
    async ScaleOutCluster(req, cb) {
        return this.request("ScaleOutCluster", req, cb);
    }
    async DescribeInstanceRenewNodes(req, cb) {
        return this.request("DescribeInstanceRenewNodes", req, cb);
    }
    async DescribeResourceScheduleDiffDetail(req, cb) {
        return this.request("DescribeResourceScheduleDiffDetail", req, cb);
    }
    async DescribeJobFlow(req, cb) {
        return this.request("DescribeJobFlow", req, cb);
    }
    async DescribeHiveQueries(req, cb) {
        return this.request("DescribeHiveQueries", req, cb);
    }
    async DescribeTrinoQueryInfo(req, cb) {
        return this.request("DescribeTrinoQueryInfo", req, cb);
    }
    async DescribeInstancesList(req, cb) {
        return this.request("DescribeInstancesList", req, cb);
    }
    async ModifyUsersOfGroupSTD(req, cb) {
        return this.request("ModifyUsersOfGroupSTD", req, cb);
    }
    async ModifyResourcesTags(req, cb) {
        return this.request("ModifyResourcesTags", req, cb);
    }
    async RunJobFlow(req, cb) {
        return this.request("RunJobFlow", req, cb);
    }
    async DescribeImpalaQueries(req, cb) {
        return this.request("DescribeImpalaQueries", req, cb);
    }
    async DescribeYarnQueue(req, cb) {
        return this.request("DescribeYarnQueue", req, cb);
    }
    async DescribeAutoScaleRecords(req, cb) {
        return this.request("DescribeAutoScaleRecords", req, cb);
    }
    async ScaleOutInstance(req, cb) {
        return this.request("ScaleOutInstance", req, cb);
    }
    async AttachDisks(req, cb) {
        return this.request("AttachDisks", req, cb);
    }
    async SetNodeResourceConfigDefault(req, cb) {
        return this.request("SetNodeResourceConfigDefault", req, cb);
    }
    async DescribeStarRocksQueryInfo(req, cb) {
        return this.request("DescribeStarRocksQueryInfo", req, cb);
    }
    async DescribeNodeResourceConfigFast(req, cb) {
        return this.request("DescribeNodeResourceConfigFast", req, cb);
    }
    async DescribeDAGInfo(req, cb) {
        return this.request("DescribeDAGInfo", req, cb);
    }
    async DeleteUserManagerUserList(req, cb) {
        return this.request("DeleteUserManagerUserList", req, cb);
    }
    async ConvertPreToPostCluster(req, cb) {
        return this.request("ConvertPreToPostCluster", req, cb);
    }
    async TerminateSLInstance(req, cb) {
        return this.request("TerminateSLInstance", req, cb);
    }
    async ModifyPodNum(req, cb) {
        return this.request("ModifyPodNum", req, cb);
    }
    async CreateSLInstance(req, cb) {
        return this.request("CreateSLInstance", req, cb);
    }
    async DescribeSLInstanceList(req, cb) {
        return this.request("DescribeSLInstanceList", req, cb);
    }
    async ModifyAutoScaleStrategy(req, cb) {
        return this.request("ModifyAutoScaleStrategy", req, cb);
    }
    async SyncPodState(req, cb) {
        return this.request("SyncPodState", req, cb);
    }
    async ModifySLInstance(req, cb) {
        return this.request("ModifySLInstance", req, cb);
    }
    async CreateInstance(req, cb) {
        return this.request("CreateInstance", req, cb);
    }
    async InquiryPriceCreateInstance(req, cb) {
        return this.request("InquiryPriceCreateInstance", req, cb);
    }
    async AddMetricScaleStrategy(req, cb) {
        return this.request("AddMetricScaleStrategy", req, cb);
    }
    async ResizeDataDisks(req, cb) {
        return this.request("ResizeDataDisks", req, cb);
    }
    async StartStopServiceOrMonitor(req, cb) {
        return this.request("StartStopServiceOrMonitor", req, cb);
    }
    async AddNodeResourceConfig(req, cb) {
        return this.request("AddNodeResourceConfig", req, cb);
    }
    async TerminateTasks(req, cb) {
        return this.request("TerminateTasks", req, cb);
    }
    async DescribeNodeDataDisks(req, cb) {
        return this.request("DescribeNodeDataDisks", req, cb);
    }
    async DescribeGlobalConfig(req, cb) {
        return this.request("DescribeGlobalConfig", req, cb);
    }
    async DescribeClusterNodes(req, cb) {
        return this.request("DescribeClusterNodes", req, cb);
    }
    async DescribeYarnScheduleHistory(req, cb) {
        return this.request("DescribeYarnScheduleHistory", req, cb);
    }
    async DescribeInsightList(req, cb) {
        return this.request("DescribeInsightList", req, cb);
    }
    async ModifyInspectionSettings(req, cb) {
        return this.request("ModifyInspectionSettings", req, cb);
    }
    async InquiryPriceUpdateInstance(req, cb) {
        return this.request("InquiryPriceUpdateInstance", req, cb);
    }
    async CreateCloudInstance(req, cb) {
        return this.request("CreateCloudInstance", req, cb);
    }
    async InquiryPriceScaleOutInstance(req, cb) {
        return this.request("InquiryPriceScaleOutInstance", req, cb);
    }
    async DescribeEmrOverviewMetrics(req, cb) {
        return this.request("DescribeEmrOverviewMetrics", req, cb);
    }
    async ModifySLInstanceBasic(req, cb) {
        return this.request("ModifySLInstanceBasic", req, cb);
    }
    async TerminateClusterNodes(req, cb) {
        return this.request("TerminateClusterNodes", req, cb);
    }
    async ResetYarnConfig(req, cb) {
        return this.request("ResetYarnConfig", req, cb);
    }
    async ModifyUserGroup(req, cb) {
        return this.request("ModifyUserGroup", req, cb);
    }
    async DescribeEmrApplicationStatics(req, cb) {
        return this.request("DescribeEmrApplicationStatics", req, cb);
    }
    async DescribeSLInstance(req, cb) {
        return this.request("DescribeSLInstance", req, cb);
    }
    async DescribeSparkQueries(req, cb) {
        return this.request("DescribeSparkQueries", req, cb);
    }
    async ModifyGlobalConfig(req, cb) {
        return this.request("ModifyGlobalConfig", req, cb);
    }
    async DescribeAutoScaleStrategies(req, cb) {
        return this.request("DescribeAutoScaleStrategies", req, cb);
    }
    async DescribeAutoScaleGroupGlobalConf(req, cb) {
        return this.request("DescribeAutoScaleGroupGlobalConf", req, cb);
    }
    async DescribeServiceConfGroupInfos(req, cb) {
        return this.request("DescribeServiceConfGroupInfos", req, cb);
    }
    async DescribeInspectionTaskResult(req, cb) {
        return this.request("DescribeInspectionTaskResult", req, cb);
    }
    async DescribeYarnApplications(req, cb) {
        return this.request("DescribeYarnApplications", req, cb);
    }
    async DescribeUsersForUserManager(req, cb) {
        return this.request("DescribeUsersForUserManager", req, cb);
    }
    async InquiryPriceRenewInstance(req, cb) {
        return this.request("InquiryPriceRenewInstance", req, cb);
    }
    async DescribeGroupsSTD(req, cb) {
        return this.request("DescribeGroupsSTD", req, cb);
    }
    async DescribeHDFSStorageInfo(req, cb) {
        return this.request("DescribeHDFSStorageInfo", req, cb);
    }
    async InquirePriceRenewEmr(req, cb) {
        return this.request("InquirePriceRenewEmr", req, cb);
    }
    async DescribeClusterFlowStatusDetail(req, cb) {
        return this.request("DescribeClusterFlowStatusDetail", req, cb);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async CreateGroupsSTD(req, cb) {
        return this.request("CreateGroupsSTD", req, cb);
    }
    async AddUsersForUserManager(req, cb) {
        return this.request("AddUsersForUserManager", req, cb);
    }
    async DescribeSparkApplications(req, cb) {
        return this.request("DescribeSparkApplications", req, cb);
    }
    async ModifyAutoRenewFlag(req, cb) {
        return this.request("ModifyAutoRenewFlag", req, cb);
    }
    async ModifyResourceScheduler(req, cb) {
        return this.request("ModifyResourceScheduler", req, cb);
    }
    async DescribeResourceSchedule(req, cb) {
        return this.request("DescribeResourceSchedule", req, cb);
    }
    async ModifyResourceScheduleConfig(req, cb) {
        return this.request("ModifyResourceScheduleConfig", req, cb);
    }
    async ModifyResource(req, cb) {
        return this.request("ModifyResource", req, cb);
    }
    async DeleteGroupsSTD(req, cb) {
        return this.request("DeleteGroupsSTD", req, cb);
    }
    async ModifyResourcePools(req, cb) {
        return this.request("ModifyResourcePools", req, cb);
    }
    async DeleteNodeResourceConfig(req, cb) {
        return this.request("DeleteNodeResourceConfig", req, cb);
    }
    async ModifyInstanceBasic(req, cb) {
        return this.request("ModifyInstanceBasic", req, cb);
    }
}
