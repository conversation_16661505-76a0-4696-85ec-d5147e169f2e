# Contributing to AG Grid Enterprise

AG Grid Enterprise is copyright commercial software. If you provide a PR, you must also state that you agree to the following:

Retention of Intellectual Property Rights for AG Grid Enterprise component
==============


1.DEFINITIONS

“the Software” means the AG Grid Enterprise software as location the the repository
https://github.com/ag-grid/ag-grid-enterprise.

“Software Rights” means all intellectual property rights inherent in or relating to the Software,
which include, but are not limited to, all copyright, patent rights, all rights in relation to registered
and unregistered trademarks (including service marks) and confidential information (including trade secrets
and know-how)

2.RETENTION OF INTELLECTUAL PROPERTY RIGHTS

AG GRID LTD reserve ownership of all software rights for the software. Upon contributing to the software
you agree to grant AG GRID LTD all software rights free of charge. You acknowledge that all software rights
will belong to AG GRID LTD and will not seek payment. AG GRID LTD reserve the right to do with the software
what it wishes. AG GRID LTD is under no obligation as to what it does with the contributed software.

 - [Question or Problem?](#question)
 - [Issues and Bugs](#issue)
 - [Feature Requests](#feature)
 - [Submission Guidelines](#submit)
 - [Coding Rules](#rules)
 - [Commit Message Guidelines](#commit)
 - [Signing the CLA](#cla)

## <a name="question"></a> Got a Question or Problem?

Please, do not open issues for the general support questions as we want to keep GitHub issues for bug reports and feature requests. You've got much better chances of getting your question answered on [StackOverflow](https://stackoverflow.com/questions/tagged/ag-grid) where the questions should be tagged with tag `ag-grig`,
or on our [Forum](https://ag-grid.com/forum)

If you're using the Enterprise version of AG Grid (ag-grid-enterprise), then the [Members Forum](https://ag-grid.com/forum/forumdisplay.php?fid=5) is the best place to ask - you'll get a much quicker response there. <NAME_EMAIL> for access.

To save your and our time we will be systematically closing all the issues that are requests for general support (for AG Grid Community) and redirecting people to StackOverflow.

## <a name="issue"></a> Found a Bug?
If you find a bug in the source code, you can help us by
[submitting an issue](#submit-issue) to our [GitHub Repository][github].

## <a name="feature"></a> Missing a Feature?
You can *request* a new feature by [submitting an issue](#submit-issue) to our GitHub
Repository.

## <a name="submit"></a> Submission Guidelines

### <a name="submit-issue"></a> Submitting an Issue

Before you submit an issue, please search the issue tracker, maybe an issue for your problem already exists and the discussion might inform you of workarounds readily available.

We want to fix all the issues as soon as possible, but before fixing a bug we need to reproduce and confirm it. In order to reproduce bugs we will systematically ask you to provide a minimal reproduction scenario using http://plnkr.co. Having a live, reproducible scenario gives us wealth of important information without going back & forth to you with additional questions like:

- version of AG Grid Enterprise used
- 3rd-party libraries and their versions
- and most importantly - a use-case that fails

A minimal reproduce scenario using http://plnkr.co/ allows us to quickly confirm a bug (or point out coding problem) as well as confirm that we are fixing the right problem. If plunker is not a suitable way to demonstrate the problem (for example for issues related to our npm packaging), please create a standalone git repository demonstrating the problem.

We will be insisting on a minimal reproduce scenario in order to save maintainers time and ultimately be able to fix more bugs. Interestingly, from our experience users often find coding problems themselves while preparing a minimal plunk. We understand that sometimes it might be hard to extract essentials bits of code from a larger code-base but we really need to isolate the problem before we can fix it.

You can file new issues by filling out our [new issue form](https://github.com/ag-grid/ag-grid-enterprise/issues/new).

[github]: https://github.com/ag-grid/ag-grid-enterprise
[jsfiddle]: http://jsfiddle.net
[plunker]: http://plnkr.co/edit
[runnable]: http://runnable.com
[stackoverflow]: http://stackoverflow.com/questions/tagged/ag-grid


