{"version": 3, "file": "footer.mjs", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/footer.tsx"], "sourcesContent": ["import type { CSSProperties, FunctionalComponent } from 'vue'\n\ntype FooterRendererProps = {\n  class?: JSX.IntrinsicAttributes['class']\n  style: CSSProperties\n}\n\nconst Footer: FunctionalComponent<FooterRendererProps> = (props, { slots }) => {\n  return (\n    <div class={props.class} style={props.style}>\n      {slots.default?.()}\n    </div>\n  )\n}\n\nFooter.displayName = 'ElTableV2Footer'\n\nexport default Footer\n"], "names": ["slots", "_createVNode", "props", "class", "style", "default", "Footer"], "mappings": ";;;AAOA,EAAA,KAAsD;AAAaA,CAAAA,KAAAA;AAAF,EAAc,IAAA,EAAA,CAAA;AAC7E,EAAA,OAAAC,WAAA,CAAA,KAAA,EAAA;IAAA,OACcC,EAAAA,KAAK,CAACC,KADpB;AAAA,IAAA,OAAA,EACkCD,KAAK,CAACE,KAAAA;GACnCJ,EAAAA,CAAAA,CAAAA,EAAAA,GAAMK,KAAAA,CAAAA,OAFX,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAKD,CAND,CAAA;;AAQAC,eAAA,MAAqB;;;;"}