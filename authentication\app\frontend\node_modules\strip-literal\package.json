{"name": "strip-literal", "version": "2.1.1", "packageManager": "pnpm@9.14.2", "description": "Strip comments and string literals from JavaScript code", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/strip-literal#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/strip-literal.git"}, "bugs": {"url": "https://github.com/antfu/strip-literal/issues"}, "keywords": [], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint .", "prepublishOnly": "nr build", "release": "bumpp --commit --push --tag && npm publish", "start": "esmo src/index.ts", "test": "vitest", "bench": "vitest bench", "typecheck": "tsc --noEmit"}, "dependencies": {"js-tokens": "^9.0.1"}, "devDependencies": {"@antfu/eslint-config": "^3.9.2", "@antfu/ni": "^0.23.1", "@types/node": "^22.9.3", "bumpp": "^9.8.1", "eslint": "^9.15.0", "esmo": "^4.8.0", "pnpm": "^9.14.2", "rimraf": "^6.0.1", "three": "^0.170.0", "typescript": "^5.7.2", "unbuild": "^2.0.0", "vite": "^5.4.11", "vitest": "^2.1.5", "vue": "^3.5.13"}}