{"version": 3, "file": "tooltip.js", "sources": ["../../../../../../packages/components/tooltip-v2/src/tooltip.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { tooltipV2RootProps } from './root'\nimport { tooltipV2TriggerProps } from './trigger'\nimport { tooltipV2ArrowProps } from './arrow'\nimport { tooltipV2ContentProps } from './content'\n\nimport type {\n  ExtractPropTypes,\n  TransitionProps,\n  __ExtractPublicPropTypes,\n} from 'vue'\n\nexport const tooltipV2Props = buildProps({\n  ...tooltipV2RootProps,\n  ...tooltipV2ArrowProps,\n  ...tooltipV2TriggerProps,\n  ...tooltipV2ContentProps,\n  alwaysOn: Boolean,\n  fullTransition: Boolean,\n  transitionProps: {\n    type: definePropType<TransitionProps | null>(Object),\n    default: null,\n  },\n  teleported: Boolean,\n  to: {\n    type: definePropType<string | HTMLElement>([String, Object]),\n    default: 'body',\n  },\n} as const)\n\nexport type TooltipV2Props = ExtractPropTypes<typeof tooltipV2Props>\nexport type TooltipV2PropsPublic = __ExtractPublicPropTypes<\n  typeof tooltipV2Props\n>\n"], "names": ["buildProps", "tooltipV2RootProps", "tooltipV2ArrowProps", "tooltipV2TriggerProps", "tooltipV2ContentProps", "definePropType"], "mappings": ";;;;;;;;;;AAKY,MAAC,cAAc,GAAGA,kBAAU,CAAC;AACzC,EAAE,GAAGC,uBAAkB;AACvB,EAAE,GAAGC,yBAAmB;AACxB,EAAE,GAAGC,6BAAqB;AAC1B,EAAE,GAAGC,6BAAqB;AAC1B,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,cAAc,EAAE,OAAO;AACzB,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,CAAC;;;;"}