<template>
  <div class="step-phone">
    <el-form :model="form" ref="formRef" label-width="80px">
      <el-form-item label="手机号" prop="phone" :rules="phoneRules">
        <el-input v-model="form.phone" placeholder="请输入手机号" maxlength="20" />
      </el-form-item>
      <el-alert type="success" show-icon :closable="false" style="margin: 0 0 8px 0;">
        <template #title>优先使用“一键取号”，更快更安全；如失败，可改用短信验证码</template>
      </el-alert>
      <div class="actions primary">
        <el-button type="primary" :loading="oneClickLoading" @click="oneClickLogin">一键取号并完成验证</el-button>
      </div>

      <el-divider content-position="center">或</el-divider>

      <el-form-item label="验证码" prop="code" :rules="codeRules">
        <div class="code-row">
          <el-input v-model="form.code" placeholder="请输入验证码" maxlength="6" />
          <el-button :disabled="sending || countdown>0 || !validPhone" @click="sendCode" style="margin-left:8px;">
            {{ countdown>0 ? `${countdown}s` : (sending ? '发送中...' : '发送验证码') }}
          </el-button>
        </div>
      </el-form-item>

      <div class="actions">
        <el-button @click="$emit('prev-step')">上一步</el-button>
        <el-button type="primary" :loading="verifying" @click="verifyCode">完成验证</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import { loadNumberAuthSdk, getAccessCodeFromSdk, fetchMobileByAccessCode } from '@/utils/numberAuth'

const props = defineProps({
  defaultPhone: { type: String, default: '' }
})
const emit = defineEmits(['prev-step','verified'])

const formRef = ref(null)
const form = reactive({ phone: props.defaultPhone || '', code: '' })
const sending = ref(false)
const verifying = ref(false)
const oneClickLoading = ref(false)
const countdown = ref(0)
let timer = null

const phoneRules = [{ validator: (_r, v, cb) => {
  if (!v) return cb(new Error('请输入手机号'))
  if (!/^\+?\d{6,20}$/.test(v)) return cb(new Error('手机号格式不正确'))
  cb()
}, trigger: 'blur' }]

const codeRules = [{ validator: (_r, v, cb) => {
  if (!v) return cb(new Error('请输入验证码'))
  if (!/^\d{4,6}$/.test(v)) return cb(new Error('验证码格式不正确'))
  cb()
}, trigger: 'blur' }]

const validPhone = computed(() => /^\+?\d{6,20}$/.test(form.phone))

const startCountdown = () => {
  countdown.value = 60
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      timer = null
    }
  }, 1000)
}

const oneClickLogin = async () => {
  try {
    oneClickLoading.value = true
    await loadNumberAuthSdk()
    const accessCode = await getAccessCodeFromSdk()
    const res = await fetchMobileByAccessCode(accessCode)
    if (res.success && res.phone) {
      form.phone = res.phone
      ElMessage.success('已获取本机号码，已完成验证')
      emit('verified', { phone: form.phone })
    } else {
      ElMessage.warning(res.message || '一键取号失败，请使用短信验证码')
    }
  } catch (e) {
    ElMessage.error('一键取号失败，请使用短信验证码')
  } finally {
    oneClickLoading.value = false
  }
}

const sendCode = async () => {
  if (!validPhone.value) return ElMessage.error('请输入有效手机号')
  sending.value = true
  try {
    // 发送短信验证码
    const res = await request.post('/api/auth/sms', { phone: form.phone })
    if (res.success) {
      ElMessage.success('验证码已发送')
      if (!timer) startCountdown()
    } else {
      ElMessage.error(res.message || '发送失败')
    }
  } catch (e) {
    ElMessage.error(e.response?.data?.message || e.message || '发送失败')
  } finally {
    sending.value = false
  }
}

const verifyCode = async () => {
  try {
    await formRef.value.validate()
  } catch { return }
  verifying.value = true
  try {
    // 验证短信验证码
    const res = await request.post('/api/auth/verify-sms', { phone: form.phone, code: form.code })
    if (res.success) {
      ElMessage.success('验证成功')
      emit('verified', { phone: form.phone })
    } else {
      ElMessage.error(res.message || '验证码无效或已过期')
    }
  } catch (e) {
    ElMessage.error(e.response?.data?.message || e.message || '验证失败')
  } finally {
    verifying.value = false
  }
}

onUnmounted(() => { if (timer) clearInterval(timer) })
</script>

<style scoped>
.step-phone { max-width: 520px; margin: 0 auto; }
.code-row { display:flex; align-items:center; }
.actions { display:flex; gap:12px; justify-content:center; margin-top: 12px; }
.actions.primary { justify-content: center; margin: 8px 0; }
</style>

