import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("trp.tencentcloudapi.com", "2021-05-15", clientConfig);
    }
    async DescribeTraceCodeById(req, cb) {
        return this.request("DescribeTraceCodeById", req, cb);
    }
    async DescribeProducts(req, cb) {
        return this.request("DescribeProducts", req, cb);
    }
    async DescribeCustomRules(req, cb) {
        return this.request("DescribeCustomRules", req, cb);
    }
    async ModifyTraceCodeUnlink(req, cb) {
        return this.request("ModifyTraceCodeUnlink", req, cb);
    }
    async AuthorizedTransfer(req, cb) {
        return this.request("AuthorizedTransfer", req, cb);
    }
    async DescribeCustomRuleById(req, cb) {
        return this.request("DescribeCustomRuleById", req, cb);
    }
    async DescribeRawScanLogs(req, cb) {
        return this.request("DescribeRawScanLogs", req, cb);
    }
    async CreateTraceCodesAsync(req, cb) {
        return this.request("CreateTraceCodesAsync", req, cb);
    }
    async DescribeCodeBatchById(req, cb) {
        return this.request("DescribeCodeBatchById", req, cb);
    }
    async DescribeTraceCodes(req, cb) {
        return this.request("DescribeTraceCodes", req, cb);
    }
    async DescribePlanQRCodes(req, cb) {
        return this.request("DescribePlanQRCodes", req, cb);
    }
    async ModifyTraceDataRanks(req, cb) {
        return this.request("ModifyTraceDataRanks", req, cb);
    }
    async DescribeCodesByPack(req, cb) {
        return this.request("DescribeCodesByPack", req, cb);
    }
    async CreateChainBatch(req, cb) {
        return this.request("CreateChainBatch", req, cb);
    }
    async DescribeMerchantById(req, cb) {
        return this.request("DescribeMerchantById", req, cb);
    }
    async CreateProduct(req, cb) {
        return this.request("CreateProduct", req, cb);
    }
    async ModifyCustomRule(req, cb) {
        return this.request("ModifyCustomRule", req, cb);
    }
    async DescribeAgentCorps(req, cb) {
        return this.request("DescribeAgentCorps", req, cb);
    }
    async DescribeCorpQuotas(req, cb) {
        return this.request("DescribeCorpQuotas", req, cb);
    }
    async ReportBatchCallbackStatus(req, cb) {
        return this.request("ReportBatchCallbackStatus", req, cb);
    }
    async DescribePlanQRCodeScanRecords(req, cb) {
        return this.request("DescribePlanQRCodeScanRecords", req, cb);
    }
    async ModifyTraceData(req, cb) {
        return this.request("ModifyTraceData", req, cb);
    }
    async DescribeCodeBatches(req, cb) {
        return this.request("DescribeCodeBatches", req, cb);
    }
    async ModifyProduct(req, cb) {
        return this.request("ModifyProduct", req, cb);
    }
    async ModifyCustomRuleStatus(req, cb) {
        return this.request("ModifyCustomRuleStatus", req, cb);
    }
    async DescribeProductById(req, cb) {
        return this.request("DescribeProductById", req, cb);
    }
    async DescribeTmpToken(req, cb) {
        return this.request("DescribeTmpToken", req, cb);
    }
    async CreateTraceCodes(req, cb) {
        return this.request("CreateTraceCodes", req, cb);
    }
    async DeleteProduct(req, cb) {
        return this.request("DeleteProduct", req, cb);
    }
    async CreateTraceChain(req, cb) {
        return this.request("CreateTraceChain", req, cb);
    }
    async CreateCustomPack(req, cb) {
        return this.request("CreateCustomPack", req, cb);
    }
    async DescribeTraceDataById(req, cb) {
        return this.request("DescribeTraceDataById", req, cb);
    }
    async CreateCorporationOrder(req, cb) {
        return this.request("CreateCorporationOrder", req, cb);
    }
    async DeleteMerchant(req, cb) {
        return this.request("DeleteMerchant", req, cb);
    }
    async DescribeScanStats(req, cb) {
        return this.request("DescribeScanStats", req, cb);
    }
    async DescribeCodePackStatus(req, cb) {
        return this.request("DescribeCodePackStatus", req, cb);
    }
    async DescribeCodeBatchs(req, cb) {
        return this.request("DescribeCodeBatchs", req, cb);
    }
    async CreateCustomRule(req, cb) {
        return this.request("CreateCustomRule", req, cb);
    }
    async DescribeCodePackUrl(req, cb) {
        return this.request("DescribeCodePackUrl", req, cb);
    }
    async ModifyMerchant(req, cb) {
        return this.request("ModifyMerchant", req, cb);
    }
    async CreateCodeBatch(req, cb) {
        return this.request("CreateCodeBatch", req, cb);
    }
    async EffectFeedback(req, cb) {
        return this.request("EffectFeedback", req, cb);
    }
    async DeleteCodeBatch(req, cb) {
        return this.request("DeleteCodeBatch", req, cb);
    }
    async DeleteTraceData(req, cb) {
        return this.request("DeleteTraceData", req, cb);
    }
    async CreateTraceData(req, cb) {
        return this.request("CreateTraceData", req, cb);
    }
    async CreateCodePack(req, cb) {
        return this.request("CreateCodePack", req, cb);
    }
    async DescribeTraceDataList(req, cb) {
        return this.request("DescribeTraceDataList", req, cb);
    }
    async ModifyTraceCode(req, cb) {
        return this.request("ModifyTraceCode", req, cb);
    }
    async DescribeCodePacks(req, cb) {
        return this.request("DescribeCodePacks", req, cb);
    }
    async DescribeScanLogs(req, cb) {
        return this.request("DescribeScanLogs", req, cb);
    }
    async ModifyCodeBatch(req, cb) {
        return this.request("ModifyCodeBatch", req, cb);
    }
    async DescribeMerchants(req, cb) {
        return this.request("DescribeMerchants", req, cb);
    }
    async CreateMerchant(req, cb) {
        return this.request("CreateMerchant", req, cb);
    }
    async DescribeJobFileUrl(req, cb) {
        return this.request("DescribeJobFileUrl", req, cb);
    }
}
