import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("tdmq.tencentcloudapi.com", "2020-02-17", clientConfig);
    }
    async DeleteEnvironments(req, cb) {
        return this.request("DeleteEnvironments", req, cb);
    }
    async CreateCluster(req, cb) {
        return this.request("CreateCluster", req, cb);
    }
    async DescribeRocketMQConsumeStats(req, cb) {
        return this.request("DescribeRocketMQConsumeStats", req, cb);
    }
    async DeleteRoles(req, cb) {
        return this.request("DeleteRoles", req, cb);
    }
    async DescribeRabbitMQExchanges(req, cb) {
        return this.request("DescribeRabbitMQExchanges", req, cb);
    }
    async DescribeRocketMQMsgTrace(req, cb) {
        return this.request("DescribeRocketMQMsgTrace", req, cb);
    }
    async CreateRocketMQRole(req, cb) {
        return this.request("CreateRocketMQRole", req, cb);
    }
    async CreateCmqTopic(req, cb) {
        return this.request("CreateCmqTopic", req, cb);
    }
    async DeleteCmqQueue(req, cb) {
        return this.request("DeleteCmqQueue", req, cb);
    }
    async ModifyRabbitMQVirtualHost(req, cb) {
        return this.request("ModifyRabbitMQVirtualHost", req, cb);
    }
    async DescribeCmqTopics(req, cb) {
        return this.request("DescribeCmqTopics", req, cb);
    }
    async DeleteTopics(req, cb) {
        return this.request("DeleteTopics", req, cb);
    }
    async DeleteRocketMQGroup(req, cb) {
        return this.request("DeleteRocketMQGroup", req, cb);
    }
    async DeleteProCluster(req, cb) {
        return this.request("DeleteProCluster", req, cb);
    }
    async DeleteRocketMQRoles(req, cb) {
        return this.request("DeleteRocketMQRoles", req, cb);
    }
    async DescribeRocketMQConsumerConnections(req, cb) {
        return this.request("DescribeRocketMQConsumerConnections", req, cb);
    }
    async CreateRabbitMQUser(req, cb) {
        return this.request("CreateRabbitMQUser", req, cb);
    }
    async DescribeRabbitMQVipInstances(req, cb) {
        return this.request("DescribeRabbitMQVipInstances", req, cb);
    }
    async RewindCmqQueue(req, cb) {
        return this.request("RewindCmqQueue", req, cb);
    }
    async DescribeRocketMQTopicStats(req, cb) {
        return this.request("DescribeRocketMQTopicStats", req, cb);
    }
    async CreateTopic(req, cb) {
        return this.request("CreateTopic", req, cb);
    }
    async DescribeRocketMQNamespaces(req, cb) {
        return this.request("DescribeRocketMQNamespaces", req, cb);
    }
    async DescribeCmqQueues(req, cb) {
        return this.request("DescribeCmqQueues", req, cb);
    }
    async DescribeEnvironments(req, cb) {
        return this.request("DescribeEnvironments", req, cb);
    }
    async DescribeClusterDetail(req, cb) {
        return this.request("DescribeClusterDetail", req, cb);
    }
    async CreateRocketMQGroup(req, cb) {
        return this.request("CreateRocketMQGroup", req, cb);
    }
    async AcknowledgeMessage(req, cb) {
        return this.request("AcknowledgeMessage", req, cb);
    }
    async CreateEnvironment(req, cb) {
        return this.request("CreateEnvironment", req, cb);
    }
    async DescribeRabbitMQPermission(req, cb) {
        return this.request("DescribeRabbitMQPermission", req, cb);
    }
    async ModifyPublicNetworkSecurityPolicy(req, cb) {
        return this.request("ModifyPublicNetworkSecurityPolicy", req, cb);
    }
    async ModifyRocketMQEnvironmentRole(req, cb) {
        return this.request("ModifyRocketMQEnvironmentRole", req, cb);
    }
    async ModifyRocketMQTopic(req, cb) {
        return this.request("ModifyRocketMQTopic", req, cb);
    }
    async ExecuteDisasterRecovery(req, cb) {
        return this.request("ExecuteDisasterRecovery", req, cb);
    }
    async DescribeRocketMQTopicMsgs(req, cb) {
        return this.request("DescribeRocketMQTopicMsgs", req, cb);
    }
    async DescribeRocketMQProducers(req, cb) {
        return this.request("DescribeRocketMQProducers", req, cb);
    }
    async DescribeEnvironmentAttributes(req, cb) {
        return this.request("DescribeEnvironmentAttributes", req, cb);
    }
    async CreateCmqSubscribe(req, cb) {
        return this.request("CreateCmqSubscribe", req, cb);
    }
    async DescribePublisherSummary(req, cb) {
        return this.request("DescribePublisherSummary", req, cb);
    }
    async DeleteRocketMQNamespace(req, cb) {
        return this.request("DeleteRocketMQNamespace", req, cb);
    }
    async CreateRabbitMQVirtualHost(req, cb) {
        return this.request("CreateRabbitMQVirtualHost", req, cb);
    }
    async DeleteCmqTopic(req, cb) {
        return this.request("DeleteCmqTopic", req, cb);
    }
    async ModifyRocketMQInstance(req, cb) {
        return this.request("ModifyRocketMQInstance", req, cb);
    }
    async CreateCmqQueue(req, cb) {
        return this.request("CreateCmqQueue", req, cb);
    }
    async ModifyRocketMQGroup(req, cb) {
        return this.request("ModifyRocketMQGroup", req, cb);
    }
    async ModifyCmqTopicAttribute(req, cb) {
        return this.request("ModifyCmqTopicAttribute", req, cb);
    }
    async DescribeRocketMQMigratingTopicList(req, cb) {
        return this.request("DescribeRocketMQMigratingTopicList", req, cb);
    }
    async DescribeCmqSubscriptionDetail(req, cb) {
        return this.request("DescribeCmqSubscriptionDetail", req, cb);
    }
    async GetTopicList(req, cb) {
        return this.request("GetTopicList", req, cb);
    }
    async DeleteCluster(req, cb) {
        return this.request("DeleteCluster", req, cb);
    }
    async ResetRocketMQConsumerOffSet(req, cb) {
        return this.request("ResetRocketMQConsumerOffSet", req, cb);
    }
    async DescribeClusters(req, cb) {
        return this.request("DescribeClusters", req, cb);
    }
    async DeleteRabbitMQPermission(req, cb) {
        return this.request("DeleteRabbitMQPermission", req, cb);
    }
    async DescribeRocketMQSourceClusterTopicList(req, cb) {
        return this.request("DescribeRocketMQSourceClusterTopicList", req, cb);
    }
    async CreateRocketMQEnvironmentRole(req, cb) {
        return this.request("CreateRocketMQEnvironmentRole", req, cb);
    }
    async DescribeRocketMQConsumerConnectionDetail(req, cb) {
        return this.request("DescribeRocketMQConsumerConnectionDetail", req, cb);
    }
    async DescribeRabbitMQVipInstance(req, cb) {
        return this.request("DescribeRabbitMQVipInstance", req, cb);
    }
    async DescribeRocketMQTopicsByGroup(req, cb) {
        return this.request("DescribeRocketMQTopicsByGroup", req, cb);
    }
    async DescribeRocketMQPublicAccessMonitorData(req, cb) {
        return this.request("DescribeRocketMQPublicAccessMonitorData", req, cb);
    }
    async ImportRocketMQTopics(req, cb) {
        return this.request("ImportRocketMQTopics", req, cb);
    }
    async SetRocketMQPublicAccessPoint(req, cb) {
        return this.request("SetRocketMQPublicAccessPoint", req, cb);
    }
    async DescribeSubscriptions(req, cb) {
        return this.request("DescribeSubscriptions", req, cb);
    }
    async DescribeCmqTopicDetail(req, cb) {
        return this.request("DescribeCmqTopicDetail", req, cb);
    }
    async DescribeRocketMQVipInstanceDetail(req, cb) {
        return this.request("DescribeRocketMQVipInstanceDetail", req, cb);
    }
    async DescribeRocketMQRoles(req, cb) {
        return this.request("DescribeRocketMQRoles", req, cb);
    }
    async DescribeRabbitMQUser(req, cb) {
        return this.request("DescribeRabbitMQUser", req, cb);
    }
    async ModifyRocketMQCluster(req, cb) {
        return this.request("ModifyRocketMQCluster", req, cb);
    }
    async ModifyCluster(req, cb) {
        return this.request("ModifyCluster", req, cb);
    }
    async DescribePulsarProInstanceDetail(req, cb) {
        return this.request("DescribePulsarProInstanceDetail", req, cb);
    }
    async ModifyCmqSubscriptionAttribute(req, cb) {
        return this.request("ModifyCmqSubscriptionAttribute", req, cb);
    }
    async DescribeRocketMQSubscriptions(req, cb) {
        return this.request("DescribeRocketMQSubscriptions", req, cb);
    }
    async CreateSubscription(req, cb) {
        return this.request("CreateSubscription", req, cb);
    }
    async DescribeRocketMQCluster(req, cb) {
        return this.request("DescribeRocketMQCluster", req, cb);
    }
    async DeleteEnvironmentRoles(req, cb) {
        return this.request("DeleteEnvironmentRoles", req, cb);
    }
    async ResetMsgSubOffsetByTimestamp(req, cb) {
        return this.request("ResetMsgSubOffsetByTimestamp", req, cb);
    }
    async CreateRabbitMQBinding(req, cb) {
        return this.request("CreateRabbitMQBinding", req, cb);
    }
    async DescribeMsgTrace(req, cb) {
        return this.request("DescribeMsgTrace", req, cb);
    }
    async ModifyRocketMQRole(req, cb) {
        return this.request("ModifyRocketMQRole", req, cb);
    }
    async DescribeRabbitMQNodeList(req, cb) {
        return this.request("DescribeRabbitMQNodeList", req, cb);
    }
    async DescribeBindVpcs(req, cb) {
        return this.request("DescribeBindVpcs", req, cb);
    }
    async DeleteRocketMQTopic(req, cb) {
        return this.request("DeleteRocketMQTopic", req, cb);
    }
    async RetryRocketMQDlqMessage(req, cb) {
        return this.request("RetryRocketMQDlqMessage", req, cb);
    }
    async DescribeRocketMQSmoothMigrationTaskList(req, cb) {
        return this.request("DescribeRocketMQSmoothMigrationTaskList", req, cb);
    }
    async ExportRocketMQMessageDetail(req, cb) {
        return this.request("ExportRocketMQMessageDetail", req, cb);
    }
    async DescribeRabbitMQQueues(req, cb) {
        return this.request("DescribeRabbitMQQueues", req, cb);
    }
    async ModifyCmqQueueAttribute(req, cb) {
        return this.request("ModifyCmqQueueAttribute", req, cb);
    }
    async ModifyRocketMQInstanceSpec(req, cb) {
        return this.request("ModifyRocketMQInstanceSpec", req, cb);
    }
    async ImportRocketMQConsumerGroups(req, cb) {
        return this.request("ImportRocketMQConsumerGroups", req, cb);
    }
    async DescribeRocketMQVipInstances(req, cb) {
        return this.request("DescribeRocketMQVipInstances", req, cb);
    }
    async DescribeRocketMQTopUsages(req, cb) {
        return this.request("DescribeRocketMQTopUsages", req, cb);
    }
    async VerifyRocketMQConsume(req, cb) {
        return this.request("VerifyRocketMQConsume", req, cb);
    }
    async ModifyRabbitMQPermission(req, cb) {
        return this.request("ModifyRabbitMQPermission", req, cb);
    }
    async DescribeAMQPClusters(req, cb) {
        return this.request("DescribeAMQPClusters", req, cb);
    }
    async CreateRabbitMQVipInstance(req, cb) {
        return this.request("CreateRabbitMQVipInstance", req, cb);
    }
    async DescribeMqMsgTrace(req, cb) {
        return this.request("DescribeMqMsgTrace", req, cb);
    }
    async DescribeRocketMQGroups(req, cb) {
        return this.request("DescribeRocketMQGroups", req, cb);
    }
    async CreateRocketMQTopic(req, cb) {
        return this.request("CreateRocketMQTopic", req, cb);
    }
    async DescribeRocketMQEnvironmentRoles(req, cb) {
        return this.request("DescribeRocketMQEnvironmentRoles", req, cb);
    }
    async DeleteRocketMQCluster(req, cb) {
        return this.request("DeleteRocketMQCluster", req, cb);
    }
    async DeleteRabbitMQUser(req, cb) {
        return this.request("DeleteRabbitMQUser", req, cb);
    }
    async CreateProCluster(req, cb) {
        return this.request("CreateProCluster", req, cb);
    }
    async DescribeRabbitMQVirtualHost(req, cb) {
        return this.request("DescribeRabbitMQVirtualHost", req, cb);
    }
    async ReceiveMessage(req, cb) {
        return this.request("ReceiveMessage", req, cb);
    }
    async DeleteRabbitMQVirtualHost(req, cb) {
        return this.request("DeleteRabbitMQVirtualHost", req, cb);
    }
    async SendCmqMsg(req, cb) {
        return this.request("SendCmqMsg", req, cb);
    }
    async DescribeRocketMQSmoothMigrationTask(req, cb) {
        return this.request("DescribeRocketMQSmoothMigrationTask", req, cb);
    }
    async ModifyEnvironmentAttributes(req, cb) {
        return this.request("ModifyEnvironmentAttributes", req, cb);
    }
    async DescribeRoles(req, cb) {
        return this.request("DescribeRoles", req, cb);
    }
    async UnbindCmqDeadLetter(req, cb) {
        return this.request("UnbindCmqDeadLetter", req, cb);
    }
    async ModifyRabbitMQUser(req, cb) {
        return this.request("ModifyRabbitMQUser", req, cb);
    }
    async CreateRocketMQNamespace(req, cb) {
        return this.request("CreateRocketMQNamespace", req, cb);
    }
    async DescribeMsg(req, cb) {
        return this.request("DescribeMsg", req, cb);
    }
    async SendBatchMessages(req, cb) {
        return this.request("SendBatchMessages", req, cb);
    }
    async ModifyRabbitMQVipInstance(req, cb) {
        return this.request("ModifyRabbitMQVipInstance", req, cb);
    }
    async CreateEnvironmentRole(req, cb) {
        return this.request("CreateEnvironmentRole", req, cb);
    }
    async DescribeRocketMQTopics(req, cb) {
        return this.request("DescribeRocketMQTopics", req, cb);
    }
    async ModifyEnvironmentRole(req, cb) {
        return this.request("ModifyEnvironmentRole", req, cb);
    }
    async DescribeRocketMQClusters(req, cb) {
        return this.request("DescribeRocketMQClusters", req, cb);
    }
    async SendMessages(req, cb) {
        return this.request("SendMessages", req, cb);
    }
    async ModifyTopic(req, cb) {
        return this.request("ModifyTopic", req, cb);
    }
    async DescribeRabbitMQBindings(req, cb) {
        return this.request("DescribeRabbitMQBindings", req, cb);
    }
    async DescribeNodeHealthOpt(req, cb) {
        return this.request("DescribeNodeHealthOpt", req, cb);
    }
    async CreateRole(req, cb) {
        return this.request("CreateRole", req, cb);
    }
    async ModifyRocketMQNamespace(req, cb) {
        return this.request("ModifyRocketMQNamespace", req, cb);
    }
    async DescribeTopicMsgs(req, cb) {
        return this.request("DescribeTopicMsgs", req, cb);
    }
    async ClearCmqQueue(req, cb) {
        return this.request("ClearCmqQueue", req, cb);
    }
    async DescribePulsarProInstances(req, cb) {
        return this.request("DescribePulsarProInstances", req, cb);
    }
    async DescribePublishers(req, cb) {
        return this.request("DescribePublishers", req, cb);
    }
    async CreateRocketMQCluster(req, cb) {
        return this.request("CreateRocketMQCluster", req, cb);
    }
    async DescribeRabbitMQQueueDetail(req, cb) {
        return this.request("DescribeRabbitMQQueueDetail", req, cb);
    }
    async DescribeRocketMQMsg(req, cb) {
        return this.request("DescribeRocketMQMsg", req, cb);
    }
    async DescribeTopics(req, cb) {
        return this.request("DescribeTopics", req, cb);
    }
    async DescribeEnvironmentRoles(req, cb) {
        return this.request("DescribeEnvironmentRoles", req, cb);
    }
    async DeleteRabbitMQVipInstance(req, cb) {
        return this.request("DeleteRabbitMQVipInstance", req, cb);
    }
    async PublishCmqMsg(req, cb) {
        return this.request("PublishCmqMsg", req, cb);
    }
    async DescribeBindClusters(req, cb) {
        return this.request("DescribeBindClusters", req, cb);
    }
    async DescribeCmqQueueDetail(req, cb) {
        return this.request("DescribeCmqQueueDetail", req, cb);
    }
    async DescribeRocketMQSourceClusterGroupList(req, cb) {
        return this.request("DescribeRocketMQSourceClusterGroupList", req, cb);
    }
    async SendMsg(req, cb) {
        return this.request("SendMsg", req, cb);
    }
    async DeleteRocketMQVipInstance(req, cb) {
        return this.request("DeleteRocketMQVipInstance", req, cb);
    }
    async SendRocketMQMessage(req, cb) {
        return this.request("SendRocketMQMessage", req, cb);
    }
    async ClearCmqSubscriptionFilterTags(req, cb) {
        return this.request("ClearCmqSubscriptionFilterTags", req, cb);
    }
    async DeleteCmqSubscribe(req, cb) {
        return this.request("DeleteCmqSubscribe", req, cb);
    }
    async DescribeRocketMQPublicAccessPoint(req, cb) {
        return this.request("DescribeRocketMQPublicAccessPoint", req, cb);
    }
    async DeleteRabbitMQBinding(req, cb) {
        return this.request("DeleteRabbitMQBinding", req, cb);
    }
    async ModifyRole(req, cb) {
        return this.request("ModifyRole", req, cb);
    }
    async CreateRocketMQVipInstance(req, cb) {
        return this.request("CreateRocketMQVipInstance", req, cb);
    }
    async DeleteRocketMQEnvironmentRoles(req, cb) {
        return this.request("DeleteRocketMQEnvironmentRoles", req, cb);
    }
    async DescribeAllTenants(req, cb) {
        return this.request("DescribeAllTenants", req, cb);
    }
    async DescribeNamespaceBundlesOpt(req, cb) {
        return this.request("DescribeNamespaceBundlesOpt", req, cb);
    }
    async DeleteSubscriptions(req, cb) {
        return this.request("DeleteSubscriptions", req, cb);
    }
}
