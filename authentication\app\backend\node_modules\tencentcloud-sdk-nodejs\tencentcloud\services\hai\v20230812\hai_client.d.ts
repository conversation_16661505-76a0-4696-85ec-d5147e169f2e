import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { InquirePriceRunInstancesResponse, ResizeInstanceDiskResponse, DescribeServiceLoginSettingsResponse, ResetInstancesPasswordResponse, DescribeInstanceNetworkStatusResponse, TerminateInstancesRequest, TerminateInstancesResponse, DescribeApplicationsResponse, StartInstanceRequest, ResizeInstanceDiskRequest, StopInstanceRequest, DescribeMuskPromptsRequest, DescribeScenesResponse, DescribeApplicationsRequest, CreateApplicationRequest, ResetInstancesPasswordRequest, RunInstancesRequest, DescribeInstancesResponse, RunInstancesResponse, DescribeInstancesRequest, DescribeScenesRequest, DescribeRegionsResponse, InquirePriceRunInstancesRequest, StartInstanceResponse, CreateMuskPromptResponse, DescribeServiceLoginSettingsRequest, CreateApplicationResponse, CreateMuskPromptRequest, DescribeRegionsRequest, DescribeInstanceNetworkStatusRequest, StopInstanceResponse, DescribeMuskPromptsResponse } from "./hai_models";
/**
 * hai client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 本接口（DescribeInstances）用户查询实例
     */
    DescribeInstances(req: DescribeInstancesRequest, cb?: (error: string, rep: DescribeInstancesResponse) => void): Promise<DescribeInstancesResponse>;
    /**
     * 本接口（DescribeApplications）用于查询应用
     */
    DescribeApplications(req: DescribeApplicationsRequest, cb?: (error: string, rep: DescribeApplicationsResponse) => void): Promise<DescribeApplicationsResponse>;
    /**
     * 获取prompt任务列表
     */
    DescribeMuskPrompts(req: DescribeMuskPromptsRequest, cb?: (error: string, rep: DescribeMuskPromptsResponse) => void): Promise<DescribeMuskPromptsResponse>;
    /**
     * 本接口（DescribeScenes）用于查询场景
     */
    DescribeScenes(req: DescribeScenesRequest, cb?: (error: string, rep: DescribeScenesResponse) => void): Promise<DescribeScenesResponse>;
    /**
     * 本接口（ResizeInstanceDisk）用于对指定HAI实例进行扩容云硬盘操作。
     */
    ResizeInstanceDisk(req: ResizeInstanceDiskRequest, cb?: (error: string, rep: ResizeInstanceDiskResponse) => void): Promise<ResizeInstanceDiskResponse>;
    /**
     * 本接口（DescribeRegions）用于查询地域列表
     */
    DescribeRegions(req?: DescribeRegionsRequest, cb?: (error: string, rep: DescribeRegionsResponse) => void): Promise<DescribeRegionsResponse>;
    /**
     * 本接口 (RunInstances) 用于创建一个或多个指定配置的实例。
     */
    RunInstances(req: RunInstancesRequest, cb?: (error: string, rep: RunInstancesResponse) => void): Promise<RunInstancesResponse>;
    /**
     * 本接口 (StartInstance) 用于主动启动实例。
‘运行中’、‘预付费’的实例不支持启动实例
     */
    StartInstance(req: StartInstanceRequest, cb?: (error: string, rep: StartInstanceResponse) => void): Promise<StartInstanceResponse>;
    /**
     * 本接口 (TerminateInstances) 用于主动退还实例。
     */
    TerminateInstances(req: TerminateInstancesRequest, cb?: (error: string, rep: TerminateInstancesResponse) => void): Promise<TerminateInstancesResponse>;
    /**
     * 创建musk prompt 任务
     */
    CreateMuskPrompt(req: CreateMuskPromptRequest, cb?: (error: string, rep: CreateMuskPromptResponse) => void): Promise<CreateMuskPromptResponse>;
    /**
     * 本接口 (ResetInstancesPassword) 用于重置实例的用户密码。
     */
    ResetInstancesPassword(req: ResetInstancesPasswordRequest, cb?: (error: string, rep: ResetInstancesPasswordResponse) => void): Promise<ResetInstancesPasswordResponse>;
    /**
     * 本接口（DescribeInstanceNetworkStatus）用于查询实例的网络配置及消耗情况
     */
    DescribeInstanceNetworkStatus(req: DescribeInstanceNetworkStatusRequest, cb?: (error: string, rep: DescribeInstanceNetworkStatusResponse) => void): Promise<DescribeInstanceNetworkStatusResponse>;
    /**
     * 本接口（DescribeServiceLoginSettings）用于查询服务登录配置
     */
    DescribeServiceLoginSettings(req: DescribeServiceLoginSettingsRequest, cb?: (error: string, rep: DescribeServiceLoginSettingsResponse) => void): Promise<DescribeServiceLoginSettingsResponse>;
    /**
     * 本接口 (InquirePriceRunInstances) 用于实例询价。
     */
    InquirePriceRunInstances(req: InquirePriceRunInstancesRequest, cb?: (error: string, rep: InquirePriceRunInstancesResponse) => void): Promise<InquirePriceRunInstancesResponse>;
    /**
     * 本接口 (StopInstance) 用于主动关闭实例。
‘已关机’、‘预付费’的实例不支持关机
     */
    StopInstance(req: StopInstanceRequest, cb?: (error: string, rep: StopInstanceResponse) => void): Promise<StopInstanceResponse>;
    /**
     * 本接口（CreateApplication）用于对HAI实例制作自定义应用。
     */
    CreateApplication(req: CreateApplicationRequest, cb?: (error: string, rep: CreateApplicationResponse) => void): Promise<CreateApplicationResponse>;
}
