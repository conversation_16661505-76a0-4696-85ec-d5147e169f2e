{"version": 3, "file": "arrow.js", "sources": ["../../../../../../packages/components/popper/src/arrow.vue"], "sourcesContent": ["<template>\n  <span\n    ref=\"arrowRef\"\n    :class=\"ns.e('arrow')\"\n    :style=\"arrowStyle\"\n    data-popper-arrow\n  />\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, onBeforeUnmount } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { POPPER_CONTENT_INJECTION_KEY } from './constants'\n\ndefineOptions({\n  name: 'ElPopperArrow',\n  inheritAttrs: false,\n})\n\nconst ns = useNamespace('popper')\nconst { arrowRef, arrowStyle } = inject(\n  POPPER_CONTENT_INJECTION_KEY,\n  undefined\n)!\n\nonBeforeUnmount(() => {\n  arrowRef.value = undefined\n})\n\ndefineExpose({\n  /**\n   * @description Arrow element\n   */\n  arrowRef,\n})\n</script>\n"], "names": ["useNamespace", "inject", "POPPER_CONTENT_INJECTION_KEY", "onBeforeUnmount", "_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_normalizeStyle"], "mappings": ";;;;;;;;;uCAcc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;AAEA,IAAM,MAAA,EAAA,GAAKA,mBAAa,QAAQ,CAAA,CAAA;AAChC,IAAM,MAAA,EAAE,QAAU,EAAA,UAAA,EAAe,GAAAC,UAAA,CAAAC,sCAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IAC/BC,mBAAA,CAAA,MAAA;AAAA,MACA,QAAA,CAAA,KAAA,GAAA,KAAA,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAEA,IAAA,MAAA,CAAA;AACE,MAAA,QAAA;AAAiB,KAClB,CAAA,CAAA;AAED,IAAa,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,MAAA,EAAA;AAAA,QAAA,OAAA,EAAA,UAAA;AAAA,QAAA,GAAA,EAAA,QAAA;AAAA,QAIX,KAAA,EAAAC,kBAAA,CAAAC,SAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QACD,KAAA,EAAAC,kBAAA,CAAAD,SAAA,CAAA,UAAA,CAAA,CAAA;;;;;;;;;;"}