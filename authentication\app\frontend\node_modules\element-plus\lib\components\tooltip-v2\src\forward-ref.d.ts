import type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue';
export type RefSetter = (el: HTMLElement | null) => void;
export declare const forwardRefProps: {
    readonly setRef: {
        readonly type: import("vue").PropType<RefSetter>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onlyChild: BooleanConstructor;
};
export type ForwardRefProps = ExtractPropTypes<typeof forwardRefProps>;
export type ForwardRefPropsPublic = __ExtractPublicPropTypes<typeof forwardRefProps>;
declare const _default: import("vue").DefineComponent<{
    readonly setRef: {
        readonly type: import("vue").PropType<RefSetter>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onlyChild: BooleanConstructor;
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<ExtractPropTypes<{
    readonly setRef: {
        readonly type: import("vue").PropType<RefSetter>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onlyChild: BooleanConstructor;
}>>, {
    readonly onlyChild: boolean;
}>;
export default _default;
