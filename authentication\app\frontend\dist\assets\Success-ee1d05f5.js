import{_ as a,u as e,c as l,b as s}from"./index-bcbc0702.js";/* empty css                *//* empty css                 *//* empty css                   *//* empty css                  */import{r as t,p as i,A as n,B as u,C as c,D as r,F as o,G as v,I as d,al as p,J as _,u as f,H as m,L as y,ao as g,ap as k,K as w,aB as h,aC as C,an as b,X as j,N as x,a5 as z,aD as A}from"./element-plus-3ab68b46.js";import"./utils-c6a461b2.js";const q={class:"page-container"},D={class:"success-container"},E={class:"success-content"},I={class:"success-actions"},S={key:0,class:"success-details"},L={class:"detail-info"},T={key:0,class:"info-item"},O={key:1,class:"info-item"},R={key:2,class:"info-item"},B={class:"next-steps"},N={class:"contact-info"},P=a({__name:"Success",setup(a){const P=e(),Q=l(),V=t(""),F=t(""),G=t(!1),H=t(!1),J=t(""),K=t(""),X=t(!1),M=t(!1),U=t(!1),W=t(!1),Y=i(()=>{const a=Q.query.type;return"error"===a?"error":"warning"===a?"warning":"success"}),Z=i(()=>{const a=Q.query.type;if("error"===a)return"操作失败";if("warning"===a)return"操作警告";return{buaa:"本校学生认证成功",freshman:"新生认证成功",external:"外校学生认证申请已提交",invite:"邀请码认证申请已提交",admin:"管理员操作成功"}[V.value]||"操作成功"}),$=i(()=>F.value||"您的操作已成功完成"),aa=()=>{P.push("/")},ea=()=>{P.push("/admin")};async function la(){const{applicationApi:a}=await s(()=>import("./application-7212cdc3.js"),["assets/application-7212cdc3.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css","assets/userSession-03354358.js"]);try{(await a.cancel("用户主动取消")).success&&(F.value="已取消申请",U.value=!1)}catch(e){}}async function sa(){const{applicationApi:a}=await s(()=>import("./application-7212cdc3.js"),["assets/application-7212cdc3.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css","assets/userSession-03354358.js"]);try{(await a.revoke("用户主动撤回")).success&&(F.value="已撤回认证",W.value=!1)}catch(e){}}return n(async()=>{V.value=Q.query.type||"",F.value=Q.query.message||"",J.value=Q.query.submitTime||(new Date).toISOString(),["external","invite"].includes(V.value)?(K.value="1-3个工作日",X.value=!0,U.value=!0):["buaa","freshman"].includes(V.value)&&(K.value="即时生效",X.value=!1,W.value=!0),"admin"===V.value&&(M.value=!0),V.value||(setTimeout(()=>{aa()},3e3),await refreshStatus())}),(a,e)=>{const l=y,s=g,t=k,i=w,n=h,P=C,Q=b,F=j;return u(),c("div",q,[r("div",D,[o(F,{class:"success-card",shadow:"always"},{default:v(()=>{return[r("div",E,[o(s,{icon:Y.value,title:Z.value,"sub-title":$.value},{extra:v(()=>[r("div",I,[o(l,{type:"primary",size:"large",onClick:aa},{default:v(()=>e[0]||(e[0]=[d(" 返回首页 ",-1)])),_:1,__:[0]}),M.value?(u(),p(l,{key:0,size:"large",onClick:ea},{default:v(()=>e[1]||(e[1]=[d(" 管理后台 ",-1)])),_:1,__:[1]})):_("",!0),U.value?(u(),p(l,{key:1,type:"danger",plain:"",size:"large",onClick:la},{default:v(()=>e[2]||(e[2]=[d(" 取消申请 ",-1)])),_:1,__:[2]})):_("",!0),G.value?(u(),p(l,{key:2,size:"large",onClick:a.goEdit},{default:v(()=>e[3]||(e[3]=[d(" 编辑资料 ",-1)])),_:1,__:[3]},8,["onClick"])):_("",!0),H.value?(u(),p(l,{key:3,type:"warning",size:"large",onClick:a.goResubmit},{default:v(()=>e[4]||(e[4]=[d(" 去重提 ",-1)])),_:1,__:[4]},8,["onClick"])):_("",!0),W.value?(u(),p(l,{key:4,type:"danger",size:"large",onClick:sa},{default:v(()=>e[5]||(e[5]=[d(" 撤回认证 ",-1)])),_:1,__:[5]})):_("",!0)])]),_:1},8,["icon","title","sub-title"]),X.value?(u(),c("div",S,[o(t,null,{default:v(()=>e[6]||(e[6]=[d("详细信息",-1)])),_:1,__:[6]}),r("div",L,[V.value?(u(),c("div",T,[o(i,null,{default:v(()=>[o(f(x))]),_:1}),r("span",null,"认证类型："+m((g=V.value,{buaa:"本校学生认证",freshman:"新生认证",external:"外校学生认证",invite:"邀请码认证",admin:"管理员操作"}[g]||g)),1)])):_("",!0),J.value?(u(),c("div",O,[o(i,null,{default:v(()=>[o(f(z))]),_:1}),r("span",null,"提交时间："+m((y=J.value,y?new Date(y).toLocaleString("zh-CN"):"")),1)])):_("",!0),K.value?(u(),c("div",R,[o(i,null,{default:v(()=>[o(f(A))]),_:1}),r("span",null,"预计审核时间："+m(K.value),1)])):_("",!0)]),r("div",B,[e[7]||(e[7]=r("h4",null,"后续步骤",-1)),o(P,{direction:"vertical",active:1},{default:v(()=>[o(n,{title:"等待审核",description:"管理员将在1-3个工作日内完成审核"}),o(n,{title:"审核通知",description:"审核结果将通过邮箱和短信通知您"}),o(n,{title:"认证完成",description:"审核通过后即可正常使用相关服务"})]),_:1})])])):_("",!0),r("div",N,[o(Q,{title:"需要帮助？",type:"info",closable:!1,"show-icon":""},{default:v(()=>e[8]||(e[8]=[r("p",null,"如有疑问或需要帮助，请联系管理员：",-1),r("ul",null,[r("li",null,"邮箱：<EMAIL>"),r("li",null,"QQ群：123456789"),r("li",null,"工作时间：周一至周五 9:00-17:00")],-1)])),_:1})])])];var y,g}),_:1})])])}}},[["__scopeId","data-v-f64cc480"]]);export{P as default};
