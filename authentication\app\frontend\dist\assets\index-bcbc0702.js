import{e,r as t,m as n,i as r,h as a,b as o,w as s,d as i,f as c,j as l,t as u,k as f,o as p,n as d,l as h,p as m,s as v,u as g,q as _,v as b,x as y,y as w,E,z as A,A as O,B as P,C as R,D as k,F as j,G as S,H as L,I,J as q,K as T,L as C,M as D,N as $,O as x,P as M,Q as F,R as V,S as N,T as U,U as B}from"./element-plus-3ab68b46.js";import{a as W}from"./utils-c6a461b2.js";
/*!
 * pinia v2.3.1
 * (c) 2025 <PERSON>
 * @license MIT
 */
let H;!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const Q=e=>H=e,G=Symbol();function z(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var J,K;(K=J||(J={})).direct="direct",K.patchObject="patch object",K.patchFunction="patch function";const X=()=>{};function Y(e,t,n,r=X){e.push(t);const a=()=>{const n=e.indexOf(t);n>-1&&(e.splice(n,1),r())};return!n&&f()&&p(a),a}function Z(e,...t){e.slice().forEach(e=>{e(...t)})}const ee=e=>e(),te=Symbol(),ne=Symbol();function re(e,t){e instanceof Map&&t instanceof Map?t.forEach((t,n)=>e.set(n,t)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],a=e[n];z(a)&&z(r)&&e.hasOwnProperty(n)&&!c(r)&&!l(r)?e[n]=re(a,r):e[n]=r}return e}const ae=Symbol();function oe(e){return!z(e)||!e.hasOwnProperty(ae)}const{assign:se}=Object;function ie(e){return!(!c(e)||!e.effect)}function ce(n,r,a={},o,f,p){let h;const m=se({actions:{}},a),v={deep:!0};let g,_,b,y=[],w=[];const E=o.state.value[n];let A;function O(e){let t;g=_=!1,"function"==typeof e?(e(o.state.value[n]),t={type:J.patchFunction,storeId:n,events:b}):(re(o.state.value[n],e),t={type:J.patchObject,payload:e,storeId:n,events:b});const r=A=Symbol();d().then(()=>{A===r&&(g=!0)}),_=!0,Z(y,t,o.state.value[n])}p||E||(o.state.value[n]={}),t({});const P=p?function(){const{state:e}=a,t=e?e():{};this.$patch(e=>{se(e,t)})}:X;const R=(e,t="")=>{if(te in e)return e[ne]=t,e;const r=function(){Q(o);const t=Array.from(arguments),a=[],s=[];let i;Z(w,{args:t,name:r[ne],store:j,after:function(e){a.push(e)},onError:function(e){s.push(e)}});try{i=e.apply(this&&this.$id===n?this:j,t)}catch(c){throw Z(s,c),c}return i instanceof Promise?i.then(e=>(Z(a,e),e)).catch(e=>(Z(s,e),Promise.reject(e))):(Z(a,i),i)};return r[te]=!0,r[ne]=t,r},k={_p:o,$id:n,$onAction:Y.bind(null,w),$patch:O,$reset:P,$subscribe(e,t={}){const r=Y(y,e,t.detached,()=>a()),a=h.run(()=>s(()=>o.state.value[n],r=>{("sync"===t.flush?_:g)&&e({storeId:n,type:J.direct,events:b},r)},se({},v,t)));return r},$dispose:function(){h.stop(),y=[],w=[],o._s.delete(n)}},j=i(k);o._s.set(n,j);const S=(o._a&&o._a.runWithContext||ee)(()=>o._e.run(()=>(h=e()).run(()=>r({action:R}))));for(const e in S){const t=S[e];if(c(t)&&!ie(t)||l(t))p||(E&&oe(t)&&(c(t)?t.value=E[e]:re(t,E[e])),o.state.value[n][e]=t);else if("function"==typeof t){const n=R(t,e);S[e]=n,m.actions[e]=t}}return se(j,S),se(u(j),S),Object.defineProperty(j,"$state",{get:()=>o.state.value[n],set:e=>{O(t=>{se(t,e)})}}),o._p.forEach(e=>{se(j,h.run(()=>e({store:j,app:o._a,pinia:o,options:m})))}),E&&p&&a.hydrate&&a.hydrate(j.$state,E),g=!0,_=!0,j}
/*! #__NO_SIDE_EFFECTS__ */function le(e,t,r){let s,i;const c="function"==typeof t;function l(e,r){const l=a();(e=e||(l?o(G,null):null))&&Q(e),(e=H)._s.has(s)||(c?ce(s,t,i,e):function(e,t,r){const{state:a,actions:o,getters:s}=t,i=r.state.value[e];let c;c=ce(e,function(){i||(r.state.value[e]=a?a():{});const t=h(r.state.value[e]);return se(t,o,Object.keys(s||{}).reduce((t,a)=>(t[a]=n(m(()=>{Q(r);const t=r._s.get(e);return s[a].call(t,t)})),t),{}))},t,r,0,!0)}(s,i,e));return e._s.get(s)}return"string"==typeof e?(s=e,i=c?r:t):(i=e,s=e.id),l.$id=s,l}const ue={},fe=function(e,t,n){if(!t||0===t.length)return e();const r=document.getElementsByTagName("link");return Promise.all(t.map(e=>{if((e=function(e){return"/"+e}(e))in ue)return;ue[e]=!0;const t=e.endsWith(".css"),a=t?'[rel="stylesheet"]':"";if(!!n)for(let n=r.length-1;n>=0;n--){const a=r[n];if(a.href===e&&(!t||"stylesheet"===a.rel))return}else if(document.querySelector(`link[href="${e}"]${a}`))return;const o=document.createElement("link");return o.rel=t?"stylesheet":"modulepreload",t||(o.as="script",o.crossOrigin=""),o.href=e,document.head.appendChild(o),t?new Promise((t,n)=>{o.addEventListener("load",t),o.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${e}`)))}):void 0})).then(()=>e()).catch(e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e})},pe="undefined"!=typeof document;function de(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const he=Object.assign;function me(e,t){const n={};for(const r in t){const a=t[r];n[r]=ge(a)?a.map(e):e(a)}return n}const ve=()=>{},ge=Array.isArray,_e=/#/g,be=/&/g,ye=/\//g,we=/=/g,Ee=/\?/g,Ae=/\+/g,Oe=/%5B/g,Pe=/%5D/g,Re=/%5E/g,ke=/%60/g,je=/%7B/g,Se=/%7C/g,Le=/%7D/g,Ie=/%20/g;function qe(e){return encodeURI(""+e).replace(Se,"|").replace(Oe,"[").replace(Pe,"]")}function Te(e){return qe(e).replace(Ae,"%2B").replace(Ie,"+").replace(_e,"%23").replace(be,"%26").replace(ke,"`").replace(je,"{").replace(Le,"}").replace(Re,"^")}function Ce(e){return Te(e).replace(we,"%3D")}function De(e){return null==e?"":function(e){return qe(e).replace(_e,"%23").replace(Ee,"%3F")}(e).replace(ye,"%2F")}function $e(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const xe=/\/$/;function Me(e,t,n="/"){let r,a={},o="",s="";const i=t.indexOf("#");let c=t.indexOf("?");return i<c&&i>=0&&(c=-1),c>-1&&(r=t.slice(0,c),o=t.slice(c+1,i>-1?i:t.length),a=e(o)),i>-1&&(r=r||t.slice(0,i),s=t.slice(i,t.length)),r=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),a=r[r.length-1];".."!==a&&"."!==a||r.push("");let o,s,i=n.length-1;for(o=0;o<r.length;o++)if(s=r[o],"."!==s){if(".."!==s)break;i>1&&i--}return n.slice(0,i).join("/")+"/"+r.slice(o).join("/")}(null!=r?r:t,n),{fullPath:r+(o&&"?")+o+s,path:r,query:a,hash:$e(s)}}function Fe(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function Ve(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ne(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Ue(e[n],t[n]))return!1;return!0}function Ue(e,t){return ge(e)?Be(e,t):ge(t)?Be(t,e):e===t}function Be(e,t){return ge(t)?e.length===t.length&&e.every((e,n)=>e===t[n]):1===e.length&&e[0]===t}const We={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var He,Qe,Ge,ze;function Je(e){if(!e)if(pe){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(xe,"")}(Qe=He||(He={})).pop="pop",Qe.push="push",(ze=Ge||(Ge={})).back="back",ze.forward="forward",ze.unknown="";const Ke=/^[^#]+#/;function Xe(e,t){return e.replace(Ke,"#")+t}const Ye=()=>({left:window.scrollX,top:window.scrollY});function Ze(e){let t;if("el"in e){const n=e.el,r="string"==typeof n&&n.startsWith("#"),a="string"==typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!a)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}(a,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function et(e,t){return(history.state?history.state.position-t:-1)+e}const tt=new Map;function nt(e,t){const{pathname:n,search:r,hash:a}=t,o=e.indexOf("#");if(o>-1){let t=a.includes(e.slice(o))?e.slice(o).length:1,n=a.slice(t);return"/"!==n[0]&&(n="/"+n),Fe(n,"")}return Fe(n,e)+r+a}function rt(e,t,n,r=!1,a=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:a?Ye():null}}function at(e){const{history:t,location:n}=window,r={value:nt(e,n)},a={value:t.state};function o(r,o,s){const i=e.indexOf("#"),c=i>-1?(n.host&&document.querySelector("base")?e:e.slice(i))+r:location.protocol+"//"+location.host+e+r;try{t[s?"replaceState":"pushState"](o,"",c),a.value=o}catch(l){n[s?"replace":"assign"](c)}}return a.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:a,push:function(e,n){const s=he({},a.value,t.state,{forward:e,scroll:Ye()});o(s.current,s,!0),o(e,he({},rt(r.value,e,null),{position:s.position+1},n),!1),r.value=e},replace:function(e,n){o(e,he({},t.state,rt(a.value.back,e,a.value.forward,!0),n,{position:a.value.position}),!0),r.value=e}}}function ot(e){return"string"==typeof e||"symbol"==typeof e}const st=Symbol("");var it,ct;function lt(e,t){return he(new Error,{type:e,[st]:!0},t)}function ut(e,t){return e instanceof Error&&st in e&&(null==t||!!(e.type&t))}(ct=it||(it={}))[ct.aborted=4]="aborted",ct[ct.cancelled=8]="cancelled",ct[ct.duplicated=16]="duplicated";const ft="[^/]+?",pt={sensitive:!1,strict:!1,start:!0,end:!0},dt=/[.+*?^${}()[\]/\\]/g;function ht(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function mt(e,t){let n=0;const r=e.score,a=t.score;for(;n<r.length&&n<a.length;){const e=ht(r[n],a[n]);if(e)return e;n++}if(1===Math.abs(a.length-r.length)){if(vt(r))return 1;if(vt(a))return-1}return a.length-r.length}function vt(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const gt={type:0,value:""},_t=/[a-zA-Z0-9_]/;function bt(e,t,n){const r=function(e,t){const n=he({},pt,t),r=[];let a=n.start?"^":"";const o=[];for(const c of e){const e=c.length?[]:[90];n.strict&&!c.length&&(a+="/");for(let t=0;t<c.length;t++){const r=c[t];let s=40+(n.sensitive?.25:0);if(0===r.type)t||(a+="/"),a+=r.value.replace(dt,"\\$&"),s+=40;else if(1===r.type){const{value:e,repeatable:n,optional:l,regexp:u}=r;o.push({name:e,repeatable:n,optional:l});const f=u||ft;if(f!==ft){s+=10;try{new RegExp(`(${f})`)}catch(i){throw new Error(`Invalid custom RegExp for param "${e}" (${f}): `+i.message)}}let p=n?`((?:${f})(?:/(?:${f}))*)`:`(${f})`;t||(p=l&&c.length<2?`(?:/${p})`:"/"+p),l&&(p+="?"),a+=p,s+=20,l&&(s+=-8),n&&(s+=-20),".*"===f&&(s+=-50)}e.push(s)}r.push(e)}if(n.strict&&n.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(a+="/?"),n.end?a+="$":n.strict&&!a.endsWith("/")&&(a+="(?:/|$)");const s=new RegExp(a,n.sensitive?"":"i");return{re:s,score:r,keys:o,parse:function(e){const t=e.match(s),n={};if(!t)return null;for(let r=1;r<t.length;r++){const e=t[r]||"",a=o[r-1];n[a.name]=e&&a.repeatable?e.split("/"):e}return n},stringify:function(t){let n="",r=!1;for(const a of e){r&&n.endsWith("/")||(n+="/"),r=!1;for(const e of a)if(0===e.type)n+=e.value;else if(1===e.type){const{value:o,repeatable:s,optional:i}=e,c=o in t?t[o]:"";if(ge(c)&&!s)throw new Error(`Provided param "${o}" is an array but it is not repeatable (* or + modifiers)`);const l=ge(c)?c.join("/"):c;if(!l){if(!i)throw new Error(`Missing required param "${o}"`);a.length<2&&(n.endsWith("/")?n=n.slice(0,-1):r=!0)}n+=l}}return n||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[gt]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${l}": ${e}`)}let n=0,r=n;const a=[];let o;function s(){o&&a.push(o),o=[]}let i,c=0,l="",u="";function f(){l&&(0===n?o.push({type:0,value:l}):1===n||2===n||3===n?(o.length>1&&("*"===i||"+"===i)&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:l,regexp:u,repeatable:"*"===i||"+"===i,optional:"*"===i||"?"===i})):t("Invalid state to consume buffer"),l="")}function p(){l+=i}for(;c<e.length;)if(i=e[c++],"\\"!==i||2===n)switch(n){case 0:"/"===i?(l&&f(),s()):":"===i?(f(),n=1):p();break;case 4:p(),n=r;break;case 1:"("===i?n=2:_t.test(i)?p():(f(),n=0,"*"!==i&&"?"!==i&&"+"!==i&&c--);break;case 2:")"===i?"\\"==u[u.length-1]?u=u.slice(0,-1)+i:n=3:u+=i;break;case 3:f(),n=0,"*"!==i&&"?"!==i&&"+"!==i&&c--,u="";break;default:t("Unknown state")}else r=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${l}"`),f(),s(),a}(e.path),n),a=he(r,{record:e,parent:t,children:[],alias:[]});return t&&!a.record.aliasOf==!t.record.aliasOf&&t.children.push(a),a}function yt(e,t){const n=[],r=new Map;function a(e,n,r){const i=!r,c=Et(e);c.aliasOf=r&&r.record;const l=Rt(t,e),u=[c];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(Et(he({},c,{components:r?r.record.components:c.components,path:e,aliasOf:r?r.record:c})))}let f,p;for(const t of u){const{path:u}=t;if(n&&"/"!==u[0]){const e=n.record.path,r="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&r+u)}if(f=bt(t,n,l),r?r.alias.push(f):(p=p||f,p!==f&&p.alias.push(f),i&&e.name&&!Ot(f)&&o(e.name)),kt(f)&&s(f),c.children){const e=c.children;for(let t=0;t<e.length;t++)a(e[t],f,r&&r.children[t])}r=r||f}return p?()=>{o(p)}:ve}function o(e){if(ot(e)){const t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(o),t.alias.forEach(o))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(o),e.alias.forEach(o))}}function s(e){const t=function(e,t){let n=0,r=t.length;for(;n!==r;){const a=n+r>>1;mt(e,t[a])<0?r=a:n=a+1}const a=function(e){let t=e;for(;t=t.parent;)if(kt(t)&&0===mt(e,t))return t;return}(e);a&&(r=t.lastIndexOf(a,r-1));return r}(e,n);n.splice(t,0,e),e.record.name&&!Ot(e)&&r.set(e.record.name,e)}return t=Rt({strict:!1,end:!0,sensitive:!1},t),e.forEach(e=>a(e)),{addRoute:a,resolve:function(e,t){let a,o,s,i={};if("name"in e&&e.name){if(a=r.get(e.name),!a)throw lt(1,{location:e});s=a.record.name,i=he(wt(t.params,a.keys.filter(e=>!e.optional).concat(a.parent?a.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&wt(e.params,a.keys.map(e=>e.name))),o=a.stringify(i)}else if(null!=e.path)o=e.path,a=n.find(e=>e.re.test(o)),a&&(i=a.parse(o),s=a.record.name);else{if(a=t.name?r.get(t.name):n.find(e=>e.re.test(t.path)),!a)throw lt(1,{location:e,currentLocation:t});s=a.record.name,i=he({},t.params,e.params),o=a.stringify(i)}const c=[];let l=a;for(;l;)c.unshift(l.record),l=l.parent;return{name:s,path:o,params:i,matched:c,meta:Pt(c)}},removeRoute:o,clearRoutes:function(){n.length=0,r.clear()},getRoutes:function(){return n},getRecordMatcher:function(e){return r.get(e)}}}function wt(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Et(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:At(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function At(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]="object"==typeof n?n[r]:n;return t}function Ot(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Pt(e){return e.reduce((e,t)=>he(e,t.meta),{})}function Rt(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function kt({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function jt(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let r=0;r<n.length;++r){const e=n[r].replace(Ae," "),a=e.indexOf("="),o=$e(a<0?e:e.slice(0,a)),s=a<0?null:$e(e.slice(a+1));if(o in t){let e=t[o];ge(e)||(e=t[o]=[e]),e.push(s)}else t[o]=s}return t}function St(e){let t="";for(let n in e){const r=e[n];if(n=Ce(n),null==r){void 0!==r&&(t+=(t.length?"&":"")+n);continue}(ge(r)?r.map(e=>e&&Te(e)):[r&&Te(r)]).forEach(e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))})}return t}function Lt(e){const t={};for(const n in e){const r=e[n];void 0!==r&&(t[n]=ge(r)?r.map(e=>null==e?null:""+e):null==r?r:""+r)}return t}const It=Symbol(""),qt=Symbol(""),Tt=Symbol(""),Ct=Symbol(""),Dt=Symbol("");function $t(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function xt(e,t,n,r,a,o=e=>e()){const s=r&&(r.enterCallbacks[a]=r.enterCallbacks[a]||[]);return()=>new Promise((i,c)=>{const l=e=>{var o;!1===e?c(lt(4,{from:n,to:t})):e instanceof Error?c(e):"string"==typeof(o=e)||o&&"object"==typeof o?c(lt(2,{from:t,to:e})):(s&&r.enterCallbacks[a]===s&&"function"==typeof e&&s.push(e),i())},u=o(()=>e.call(r&&r.instances[a],t,n,l));let f=Promise.resolve(u);e.length<3&&(f=f.then(l)),f.catch(e=>c(e))})}function Mt(e,t,n,r,a=e=>e()){const o=[];for(const s of e)for(const e in s.components){let i=s.components[e];if("beforeRouteEnter"===t||s.instances[e])if(de(i)){const c=(i.__vccOpts||i)[t];c&&o.push(xt(c,n,r,s,e,a))}else{let c=i();o.push(()=>c.then(o=>{if(!o)throw new Error(`Couldn't resolve component "${e}" at "${s.path}"`);const i=(c=o).__esModule||"Module"===c[Symbol.toStringTag]||c.default&&de(c.default)?o.default:o;var c;s.mods[e]=o,s.components[e]=i;const l=(i.__vccOpts||i)[t];return l&&xt(l,n,r,s,e,a)()}))}}return o}function Ft(e){const t=o(Tt),n=o(Ct),r=m(()=>{const n=g(e.to);return t.resolve(n)}),a=m(()=>{const{matched:e}=r.value,{length:t}=e,a=e[t-1],o=n.matched;if(!a||!o.length)return-1;const s=o.findIndex(Ve.bind(null,a));if(s>-1)return s;const i=Nt(e[t-2]);return t>1&&Nt(a)===i&&o[o.length-1].path!==i?o.findIndex(Ve.bind(null,e[t-2])):s}),s=m(()=>a.value>-1&&function(e,t){for(const n in t){const r=t[n],a=e[n];if("string"==typeof r){if(r!==a)return!1}else if(!ge(a)||a.length!==r.length||r.some((e,t)=>e!==a[t]))return!1}return!0}(n.params,r.value.params)),i=m(()=>a.value>-1&&a.value===n.matched.length-1&&Ne(n.params,r.value.params));return{route:r,href:m(()=>r.value.href),isActive:s,isExactActive:i,navigate:function(n={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(n)){const n=t[g(e.replace)?"replace":"push"](g(e.to)).catch(ve);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition(()=>n),n}return Promise.resolve()}}}const Vt=b({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Ft,setup(e,{slots:t}){const n=i(Ft(e)),{options:r}=o(Tt),a=m(()=>({[Ut(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Ut(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&(1===(o=t.default(n)).length?o[0]:o);var o;return e.custom?r:y("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:a.value},r)}}});function Nt(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ut=(e,t,n)=>null!=e?e:null!=t?t:n;function Bt(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const Wt=b({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:n,slots:r}){const a=o(Dt),i=m(()=>e.route||a.value),c=o(qt,0),l=m(()=>{let e=g(c);const{matched:t}=i.value;let n;for(;(n=t[e])&&!n.components;)e++;return e}),u=m(()=>i.value.matched[l.value]);w(qt,m(()=>l.value+1)),w(It,u),w(Dt,i);const f=t();return s(()=>[f.value,u.value,e.name],([e,t,n],[r,a,o])=>{t&&(t.instances[n]=e,a&&a!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=a.leaveGuards),t.updateGuards.size||(t.updateGuards=a.updateGuards))),!e||!t||a&&Ve(t,a)&&r||(t.enterCallbacks[n]||[]).forEach(t=>t(e))},{flush:"post"}),()=>{const t=i.value,a=e.name,o=u.value,s=o&&o.components[a];if(!s)return Bt(r.default,{Component:s,route:t});const c=o.props[a],l=c?!0===c?t.params:"function"==typeof c?c(t):c:null,p=y(s,he({},l,n,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(o.instances[a]=null)},ref:f}));return Bt(r.default,{Component:p,route:t})||p}}});function Ht(){return o(Tt)}function Qt(e){return o(Ct)}const Gt="auth_token",zt="user_info",Jt="user_permissions";function Kt(){return localStorage.getItem(Gt)}function Xt(e){localStorage.setItem(Gt,e)}function Yt(){localStorage.removeItem(Gt),localStorage.removeItem(zt),localStorage.removeItem(Jt)}const Zt=W.create({baseURL:"/api",timeout:3e4,headers:{"Content-Type":"application/json;charset=UTF-8"}});function en(){Yt(),A.confirm("登录状态已过期，请重新登录","提示",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then(()=>{const e=yn.currentRoute.value.fullPath,t=e.startsWith("/admin");yn.push({name:t?"AdminLogin":"Login",query:{redirect:e}})}).catch(()=>{yn.push({name:"Home"})})}Zt.interceptors.request.use(e=>{const t=["/api/config/public","/api/auth/encryption-info","/api/freshman/captcha"].some(t=>e.url.includes(t)),n=["/api/auth/sso","/api/auth/email","/api/auth/sms","/api/freshman/query","/api/freshman/verify-identity"].some(t=>e.url.includes(t));if(!t&&!n){const t=Kt();t&&(e.headers.Authorization=`Bearer ${t}`)}if(["post","put","delete","patch"].includes(e.method)){const t=function(){var e;return(null==(e=document.querySelector('meta[name="csrf-token"]'))?void 0:e.getAttribute("content"))||sessionStorage.getItem("csrf-token")}();t&&(e.headers["X-CSRF-Token"]=t)}return e.headers["X-Request-ID"]=Date.now().toString(36)+Math.random().toString(36).substr(2),e},e=>Promise.reject(e)),Zt.interceptors.response.use(e=>{const t=e.data;if(!1===t.success){const e=t.message||"请求失败";return"UNAUTHORIZED"===t.code?(en(),Promise.reject(new Error(e))):"FORBIDDEN"===t.code?(E.error("权限不足"),Promise.reject(new Error(e))):"RATE_LIMITED"===t.code?(E.warning("请求过于频繁，请稍后再试"),Promise.reject(new Error(e))):"MAINTENANCE"===t.code?(E.warning("系统维护中，请稍后再试"),Promise.reject(new Error(e))):Promise.reject(t)}return t},e=>{let t="网络错误";if(e.response){const{status:n,data:r}=e.response;switch(n){case 400:return r&&(e.response.data=r,r.message&&(e.message=r.message)),Promise.reject(e);case 401:if(r&&r.message){if(t=r.message,"INVALID_CREDENTIALS"===r.error||r.message.includes("用户名")||r.message.includes("密码"))return e.response.data=r,Promise.reject(e)}else t="认证失败，请重新登录";en();break;case 403:t="权限不足";break;case 404:t="请求的资源不存在";break;case 429:t="请求过于频繁，请稍后再试";break;case 500:t="服务器内部错误";break;case 502:t="网关错误";break;case 503:if(r&&"MAINTENANCE_MODE"===r.error)return fe(()=>Promise.resolve().then(()=>wn),void 0).then(({default:e})=>{e.push({name:"Maintenance",query:{redirect:e.currentRoute.value.fullPath}})}),Promise.reject(e);t="服务暂时不可用";break;case 504:t="网关超时";break;default:t=(null==r?void 0:r.message)||`请求失败 (${n})`}}else t=e.request?"网络连接失败，请检查网络设置":e.message||"请求配置错误";return E.error(t),Promise.reject(e)});const tn={get:(e,t={})=>Zt.get(e,{params:t}),post:(e,t={})=>Zt.post(e,t),put:(e,t={})=>Zt.put(e,t),patch:(e,t={})=>Zt.patch(e,t),delete:(e,t={})=>Zt.delete(e,{params:t}),upload:(e,t,n)=>Zt.post(e,t,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:n}),download:(e,t={},n)=>Zt.get(e,{params:t,responseType:"blob"}).then(e=>{const t=new Blob([e.data]),r=window.URL.createObjectURL(t),a=document.createElement("a");a.href=r,a.download=n||"download",document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(r)})};const nn=le("auth",()=>{const e=t(Kt()),n=t(function(){const e=localStorage.getItem(zt);return e?JSON.parse(e):null}()),r=t(function(){const e=localStorage.getItem(Jt);return e?JSON.parse(e):[]}()),a=t(!1),o=m(()=>!!e.value&&!!n.value),s=m(()=>!!n.value&&("admin"===n.value.role||"auditor"===n.value.role));return{token:e,user:n,permissions:r,isLoading:a,isAuthenticated:o,isAdmin:s,login:async t=>{var o,s;try{a.value=!0;const o=await tn.post("/api/admin/login",t);if(o.success)return e.value=o.token,n.value=o.user||{id:o.admin_id||"unknown",username:o.username||"unknown",role:o.role||"admin"},r.value=o.permissions||[],Xt(o.token),function(e){localStorage.setItem(zt,JSON.stringify(e))}(n.value),function(e){localStorage.setItem(Jt,JSON.stringify(e))}(r.value),{success:!0};throw new Error(o.message||"登录失败")}catch(i){let e="登录失败";return(null==(s=null==(o=i.response)?void 0:o.data)?void 0:s.message)?e=i.response.data.message:i.message&&(e=i.message),{success:!1,error:e}}finally{a.value=!1}},logout:async()=>{try{await tn.post("/api/auth/logout")}catch(t){}finally{e.value=null,n.value=null,r.value=[],Yt()}},checkAuth:async()=>{const t=Kt();if(!t)return!1;if(e.value===t&&n.value)return!0;try{const a=await tn.get("/api/admin/profile");return a.success?(e.value=t,n.value=a.data.admin||a.data.user||a.data,r.value=a.data.permissions||[],!0):(e.value=null,n.value=null,r.value=[],Yt(),clearAuthToken(),!1)}catch(a){return!("NETWORK_ERROR"!==a.code||!t||!n.value)||(e.value=null,n.value=null,r.value=[],Yt(),clearAuthToken(),!1)}},refreshToken:async()=>{try{const t=await tn.post("/api/auth/refresh");return!!t.success&&(e.value=t.data.token,Xt(t.data.token),!0)}catch(t){return!1}},updateUser:e=>{n.value={...n.value,...e}},hasPermission:e=>{var t;if(!n.value)return!1;if("admin"===n.value.role)return!0;if(r.value&&Array.isArray(r.value))return r.value.includes(e);return(null==(t={admin:["user.view","pending.view","config.edit","logs.view","reports.view"],auditor:["user.view","pending.view","logs.view","reports.view"],staff:["user.view","pending.view"]}[n.value.role||"staff"])?void 0:t.includes(e))||!1},recordPageVisit:e=>{},reset:()=>{e.value=null,n.value=null,r.value=[],a.value=!1}}}),rn=le("config",()=>{const e=t({buaa_enabled:!0,buaa_sso_enabled:!0,buaa_email_enabled:!0,freshman_enabled:!0,external_enabled:!0,invite_enabled:!0,freshman_available:[],freshman_maintenance_windows:[]}),n=t(!1),r=t(null),a=async()=>{if(!n.value){n.value=!0;try{const t=await Zt.get("/api/config/public");if(t.success){const n=t.data;e.value={buaa_enabled:!1!==n.buaa_enabled,buaa_sso_enabled:!1!==n.buaa_sso_enabled,buaa_email_enabled:!1!==n.buaa_email_enabled,freshman_enabled:!1!==n.freshman_enabled,external_enabled:!1!==n.external_enabled,invite_enabled:!1!==n.invite_enabled,freshman_available:n.freshman_available||[],freshman_maintenance_windows:n.freshman_maintenance_windows||[],site_name:n.site_name||"北航QQ身份认证系统",site_description:n.site_description||"北京航空航天大学QQ身份认证系统",contact_email:n.contact_email||"<EMAIL>",maintenance_mode:n.maintenance_mode||!1,maintenance_message:n.maintenance_message||"系统正在维护中，请稍后再试"},r.value=new Date}}catch(t){}finally{n.value=!1}}},o=t=>!0===e.value[t],s=()=>{if(!e.value.freshman_enabled)return!1;const t=new Date,n=t.toISOString().split("T")[0],r=t.toTimeString().split(" ")[0].substring(0,5),a=e.value.freshman_available;if(2===a.length&&(n<a[0]||n>a[1]))return!1;const o=e.value.freshman_maintenance_windows||[];for(const e of o)if(e.includes("-")){const[t,n]=e.split("-");if(r>=t&&r<=n)return!1}return!0};return{configs:e,loading:n,lastUpdated:r,loadPublicConfigs:a,isFeatureEnabled:o,isInFreshmanWindow:s,getFeatureDisabledReason:t=>{if(!o(t)){return`${{buaa_enabled:"本校学生认证",freshman_enabled:"新生认证",external_enabled:"外校学生认证",invite_enabled:"邀请码认证"}[t]}功能已关闭`}if("freshman_enabled"===t&&!s()){const t=e.value.freshman_available;let n="新生认证功能不在开放时间内";2===t.length&&(n+=`，开放时间：${t[0]} 至 ${t[1]}`);const r=e.value.freshman_maintenance_windows;return r.length>0&&(n+=`，每日维护时间：${r.join(", ")}`),n}return null},init:async()=>{await a()}}}),an=(e,t)=>{const n=e.__vccOpts||e;for(const[r,a]of t)n[r]=a;return n},on={class:"maintenance-page"},sn={class:"maintenance-container"},cn={class:"maintenance-content"},ln={class:"maintenance-icon"},un={class:"maintenance-message"},fn={class:"system-info"},pn={class:"system-name"},dn={class:"contact-info"},hn={class:"status-check"},mn={class:"check-info"},vn={class:"admin-actions"},gn={key:0,class:"back-home"},_n=[{path:"/",name:"Home",component:()=>fe(()=>import("./Home-aeb19a44.js"),["assets/Home-aeb19a44.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/Home-963220bd.css","assets/el-overlay-9042513a.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css"]),meta:{title:"QQ身份认证系统",requiresAuth:!1}},{path:"/register",name:"Register",component:()=>fe(()=>import("./Register-9e3a9587.js"),["assets/Register-9e3a9587.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/Register-2368a580.css"]),meta:{title:"身份认证",requiresAuth:!1}},{path:"/auth/buaa",name:"BuaaAuth",component:()=>fe(()=>import("./BuaaAuth-7caf6836.js"),["assets/BuaaAuth-7caf6836.js","assets/utils-c6a461b2.js","assets/element-plus-3ab68b46.js","assets/AuthLayout-1aa16efb.js","assets/AuthLayout-16297a48.css","assets/BuaaAuth-d2a20528.css","assets/el-form-item-36279550.css","assets/el-input-1c9c9a0b.css"]),meta:{title:"本校学生认证",requiresAuth:!1,requiresFeature:"buaa_enabled"}},{path:"/auth/freshman",name:"FreshmanAuth",component:()=>fe(()=>import("./FreshmanAuth-ea467a82.js"),["assets/FreshmanAuth-ea467a82.js","assets/element-plus-3ab68b46.js","assets/VerificationCode-b10ec6a7.js","assets/VerificationCode-ee4bf028.css","assets/el-form-item-36279550.css","assets/el-input-1c9c9a0b.css","assets/AuthLayout-1aa16efb.js","assets/AuthLayout-16297a48.css","assets/utils-c6a461b2.js","assets/FreshmanAuth-f5e8aa1a.css","assets/el-divider-07810808.css","assets/el-result-7aba8554.css","assets/el-alert-c5e82332.css"]),meta:{title:"新生认证",requiresAuth:!1,requiresFeature:"freshman_enabled"}},{path:"/auth/external",name:"ExternalAuth",component:()=>fe(()=>import("./ExternalAuth-fa45afc6.js"),["assets/ExternalAuth-fa45afc6.js","assets/AuthLayout-1aa16efb.js","assets/element-plus-3ab68b46.js","assets/AuthLayout-16297a48.css","assets/VerificationCode-b10ec6a7.js","assets/VerificationCode-ee4bf028.css","assets/el-form-item-36279550.css","assets/el-input-1c9c9a0b.css","assets/CameraCapture-b9781556.js","assets/CameraCapture-1dc74fa8.css","assets/el-alert-c5e82332.css","assets/auth-36869992.js","assets/utils-c6a461b2.js","assets/ExternalAuth-7476e70a.css","assets/el-divider-07810808.css","assets/el-tag-afac09bb.css","assets/el-select-3cff20ef.css","assets/el-popper-81c3b64d.css"]),meta:{title:"外校学生认证",requiresAuth:!1,requiresFeature:"external_enabled"}},{path:"/auth/invite",name:"InviteAuth",component:()=>fe(()=>import("./InviteAuth-73350cc2.js"),["assets/InviteAuth-73350cc2.js","assets/element-plus-3ab68b46.js","assets/VerificationCode-b10ec6a7.js","assets/VerificationCode-ee4bf028.css","assets/el-form-item-36279550.css","assets/el-input-1c9c9a0b.css","assets/AuthLayout-1aa16efb.js","assets/AuthLayout-16297a48.css","assets/utils-c6a461b2.js","assets/InviteAuth-3a6e871e.css","assets/el-alert-c5e82332.css"]),meta:{title:"邀请码认证",requiresAuth:!1,requiresFeature:"invite_enabled"}},{path:"/success",name:"Success",component:()=>fe(()=>import("./Success-ee1d05f5.js"),["assets/Success-ee1d05f5.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/Success-f0bfd5cf.css","assets/el-card-7155ea48.css","assets/el-alert-c5e82332.css","assets/el-divider-07810808.css","assets/el-result-7aba8554.css"]),meta:{title:"认证成功",requiresAuth:!1}},{path:"/status",name:"ApplicationStatus",component:()=>fe(()=>import("./ApplicationStatus-da006689.js"),["assets/ApplicationStatus-da006689.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/ApplicationStatus-079e1a33.css","assets/el-card-7155ea48.css","assets/el-empty-56b2fa40.css","assets/el-divider-07810808.css","assets/el-tag-afac09bb.css"]),meta:{title:"申请状态",requiresAuth:!1}},{path:"/resubmit",name:"Resubmit",component:()=>fe(()=>import("./Resubmit-fd459f57.js"),["assets/Resubmit-fd459f57.js","assets/userSession-03354358.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/application-7212cdc3.js","assets/CameraCapture-b9781556.js","assets/CameraCapture-1dc74fa8.css","assets/el-alert-c5e82332.css","assets/Resubmit-4a1f06fb.css","assets/el-card-7155ea48.css","assets/el-form-item-36279550.css","assets/el-input-1c9c9a0b.css","assets/el-tag-afac09bb.css","assets/el-divider-07810808.css"]),meta:{title:"重新提交审核",requiresAuth:!1}},{path:"/admin/login",name:"AdminLogin",component:()=>fe(()=>import("./Login-aa416f61.js"),["assets/Login-aa416f61.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/Login-9588fefd.css","assets/el-card-7155ea48.css","assets/el-alert-c5e82332.css","assets/el-divider-07810808.css","assets/el-form-item-36279550.css","assets/el-checkbox-4bf2f35b.css","assets/el-input-1c9c9a0b.css"]),meta:{title:"管理员登录",requiresAuth:!1,hideForAuth:!0}},{path:"/admin",component:()=>fe(()=>import("./AdminLayout-479f227b.js"),["assets/AdminLayout-479f227b.js","assets/el-tooltip-4ed993c7.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/AdminLayout-fda2f69c.css","assets/el-button-group-42f3dab2.css","assets/el-popper-81c3b64d.css","assets/el-dropdown-item-e392ebca.css"]),meta:{requiresAuth:!0,requiresAdmin:!0},children:[{path:"",redirect:"/admin/dashboard"},{path:"dashboard",name:"Dashboard",component:()=>fe(()=>import("./Dashboard-e802fa4a.js"),["assets/Dashboard-e802fa4a.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/Dashboard-274d512d.css","assets/el-button-group-42f3dab2.css","assets/el-col-60e44389.css","assets/el-card-7155ea48.css"]),meta:{title:"管理面板",icon:"DataBoard"}},{path:"users",name:"UserManagement",component:()=>fe(()=>import("./UserManagement-fac69605.js"),["assets/UserManagement-fac69605.js","assets/el-tooltip-4ed993c7.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/UserManagement-c45359c1.css","assets/el-pagination-8c025680.css","assets/el-descriptions-item-3a3080a1.css","assets/el-overlay-9042513a.css","assets/el-form-item-36279550.css","assets/el-card-7155ea48.css","assets/el-tag-afac09bb.css","assets/el-select-3cff20ef.css","assets/el-popper-81c3b64d.css","assets/el-input-1c9c9a0b.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-button-group-42f3dab2.css","assets/el-dropdown-item-e392ebca.css","assets/el-statistic-6d381871.css","assets/el-col-60e44389.css","assets/el-date-picker-025a8032.css"]),meta:{title:"用户管理",icon:"User",permission:"user.view"}},{path:"pending",name:"PendingUsers",component:()=>fe(()=>import("./PendingUsers-33b9227e.js"),["assets/PendingUsers-33b9227e.js","assets/el-tooltip-4ed993c7.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/PendingUsers-6fc3cdae.css","assets/el-pagination-8c025680.css","assets/el-tab-pane-8e0691fd.css","assets/el-overlay-9042513a.css","assets/el-form-item-36279550.css","assets/el-switch-3d8c06e2.css","assets/el-checkbox-4bf2f35b.css","assets/el-radio-group-3b9ac3ad.css","assets/el-card-7155ea48.css","assets/el-tag-afac09bb.css","assets/el-select-3cff20ef.css","assets/el-popper-81c3b64d.css","assets/el-input-1c9c9a0b.css","assets/el-table-column-790a14bb.css","assets/el-statistic-6d381871.css","assets/el-empty-56b2fa40.css","assets/el-descriptions-item-3a3080a1.css","assets/el-col-60e44389.css","assets/el-button-group-42f3dab2.css","assets/el-date-picker-025a8032.css"]),meta:{title:"待审核用户",icon:"Clock",permission:"pending.view"}},{path:"config",name:"SystemConfig",component:()=>fe(()=>import("./SystemConfig-bd2a0f99.js"),["assets/SystemConfig-bd2a0f99.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/SystemConfig-c0d2a2e8.css","assets/el-card-7155ea48.css","assets/el-tab-pane-8e0691fd.css","assets/el-tag-afac09bb.css","assets/el-select-3cff20ef.css","assets/el-popper-81c3b64d.css","assets/el-input-1c9c9a0b.css","assets/el-form-item-36279550.css","assets/el-date-picker-025a8032.css","assets/el-switch-3d8c06e2.css"]),meta:{title:"系统配置",icon:"Setting",permission:"config.edit"}},{path:"logs",name:"AuditLogs",component:()=>fe(()=>import("./AuditLogs-d22bc9c8.js"),["assets/AuditLogs-d22bc9c8.js","assets/el-tooltip-4ed993c7.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/AuditLogs-236f83e9.css","assets/el-pagination-8c025680.css","assets/el-overlay-9042513a.css","assets/el-card-7155ea48.css","assets/el-tag-afac09bb.css","assets/el-select-3cff20ef.css","assets/el-popper-81c3b64d.css","assets/el-input-1c9c9a0b.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-descriptions-item-3a3080a1.css","assets/el-col-60e44389.css","assets/el-date-picker-025a8032.css"]),meta:{title:"审计日志",icon:"Document",permission:"logs.view"}},{path:"reports",name:"Reports",component:()=>fe(()=>import("./Reports-9f70333e.js"),["assets/Reports-9f70333e.js","assets/el-tooltip-4ed993c7.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/Reports-f65901b6.css","assets/el-tab-pane-8e0691fd.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-popper-81c3b64d.css","assets/el-tag-afac09bb.css","assets/el-card-7155ea48.css","assets/el-col-60e44389.css","assets/el-button-group-42f3dab2.css","assets/el-date-picker-025a8032.css","assets/el-input-1c9c9a0b.css"]),meta:{title:"统计报告",icon:"PieChart",permission:"reports.view"}},{path:"security",name:"SecurityAudit",component:()=>fe(()=>import("./SecurityAudit-95b16155.js"),["assets/SecurityAudit-95b16155.js","assets/el-tooltip-4ed993c7.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/SecurityAudit-05fae3b3.css","assets/el-pagination-8c025680.css","assets/el-tag-afac09bb.css","assets/el-select-3cff20ef.css","assets/el-popper-81c3b64d.css","assets/el-input-1c9c9a0b.css","assets/el-table-column-790a14bb.css","assets/el-checkbox-4bf2f35b.css","assets/el-radio-group-3b9ac3ad.css"]),meta:{title:"安全审计",icon:"Lock",permission:"security.view"}}]},{path:"/maintenance",name:"Maintenance",component:an({__name:"Maintenance",setup(e){const t=Ht(),n=Qt(),r=rn(),a=m(()=>r.configs),o=m(()=>a.value.site_name||"QQ身份认证系统"),s=m(()=>a.value.contact_email||"<EMAIL>"),i=m(()=>a.value.maintenance_message||"系统正在进行维护升级，暂时无法提供服务，请稍后再试。"),c=()=>{window.location.href="about:blank"},l=async()=>{await f(),r.configs.maintenance_mode&&window.location.reload()},u=()=>{t.push("/")},f=async()=>{if(await r.loadPublicConfigs(),!r.configs.maintenance_mode){const e=n.query.redirect||"/";t.push(e)}};return O(async()=>{r.lastUpdated||await r.init(),document.title=`系统维护中 - ${o.value}`,await f()}),(e,t)=>{const n=T,r=C;return P(),R("div",on,[k("div",sn,[k("div",cn,[k("div",ln,[j(n,{size:"120",color:"#E6A23C"},{default:S(()=>[j(g(D))]),_:1})]),t[4]||(t[4]=k("h1",{class:"maintenance-title"},"系统维护中",-1)),k("div",un,[k("p",null,L(i.value),1)]),k("div",fn,[k("p",pn,L(o.value),1),k("p",dn,"如有紧急问题，请联系管理员："+L(s.value),1)]),k("div",hn,[k("p",mn,[j(n,null,{default:S(()=>[j(g($))]),_:1}),t[0]||(t[0]=I(' 维护结束后，请点击"刷新页面"按钮返回原页面 ',-1))])]),k("div",vn,[j(r,{onClick:l,icon:g(x),type:"primary"},{default:S(()=>t[1]||(t[1]=[I(" 刷新页面 ",-1)])),_:1,__:[1]},8,["icon"]),j(r,{onClick:c,icon:g(M)},{default:S(()=>t[2]||(t[2]=[I(" 关闭页面 ",-1)])),_:1,__:[2]},8,["icon"])]),a.value.maintenance_mode?q("",!0):(P(),R("div",gn,[j(r,{type:"success",onClick:u,icon:g(F)},{default:S(()=>t[3]||(t[3]=[I(" 返回首页 ",-1)])),_:1,__:[3]},8,["icon"])]))])])])}}},[["__scopeId","data-v-334cd8ef"]]),meta:{title:"系统维护中",requiresAuth:!1}},{path:"/:pathMatch(.*)*",name:"NotFound",component:()=>fe(()=>import("./NotFound-e306b029.js"),["assets/NotFound-e306b029.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/NotFound-f05f2c69.css","assets/el-divider-07810808.css","assets/el-result-7aba8554.css"]),meta:{title:"页面未找到"}}],bn=function(e){const t=yt(e.routes,e),n=e.parseQuery||jt,r=e.stringifyQuery||St,a=e.history,o=$t(),s=$t(),i=$t(),c=v(We);let l=We;pe&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=me.bind(null,e=>""+e),f=me.bind(null,De),p=me.bind(null,$e);function h(e,o){if(o=he({},o||c.value),"string"==typeof e){const r=Me(n,e,o.path),s=t.resolve({path:r.path},o),i=a.createHref(r.fullPath);return he(r,s,{params:p(s.params),hash:$e(r.hash),redirectedFrom:void 0,href:i})}let s;if(null!=e.path)s=he({},e,{path:Me(n,e.path,o.path).path});else{const t=he({},e.params);for(const e in t)null==t[e]&&delete t[e];s=he({},e,{params:f(t)}),o.params=f(o.params)}const i=t.resolve(s,o),l=e.hash||"";i.params=u(p(i.params));const d=function(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(r,he({},e,{hash:(h=l,qe(h).replace(je,"{").replace(Le,"}").replace(Re,"^")),path:i.path}));var h;const m=a.createHref(d);return he({fullPath:d,hash:l,query:r===St?Lt(e.query):e.query||{}},i,{redirectedFrom:void 0,href:m})}function m(e){return"string"==typeof e?Me(n,e,c.value.path):he({},e)}function b(e,t){if(l!==e)return lt(8,{from:t,to:e})}function y(e){return E(e)}function w(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let r="function"==typeof n?n(e):n;return"string"==typeof r&&(r=r.includes("?")||r.includes("#")?r=m(r):{path:r},r.params={}),he({query:e.query,hash:e.hash,params:null!=r.path?{}:e.params},r)}}function E(e,t){const n=l=h(e),a=c.value,o=e.state,s=e.force,i=!0===e.replace,u=w(n);if(u)return E(he(m(u),{state:"object"==typeof u?he({},o,u.state):o,force:s,replace:i}),t||n);const f=n;let p;return f.redirectedFrom=t,!s&&function(e,t,n){const r=t.matched.length-1,a=n.matched.length-1;return r>-1&&r===a&&Ve(t.matched[r],n.matched[a])&&Ne(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(r,a,n)&&(p=lt(16,{to:f,from:a}),D(a,a,!0,!1)),(p?Promise.resolve(p):P(f,a)).catch(e=>ut(e)?ut(e,2)?e:C(e):T(e,f,a)).then(e=>{if(e){if(ut(e,2))return E(he({replace:i},m(e.to),{state:"object"==typeof e.to?he({},o,e.to.state):o,force:s}),t||f)}else e=k(f,a,!0,i,o);return R(f,a,e),e})}function A(e,t){const n=b(e,t);return n?Promise.reject(n):Promise.resolve()}function O(e){const t=M.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function P(e,t){let n;const[r,a,i]=function(e,t){const n=[],r=[],a=[],o=Math.max(t.matched.length,e.matched.length);for(let s=0;s<o;s++){const o=t.matched[s];o&&(e.matched.find(e=>Ve(e,o))?r.push(o):n.push(o));const i=e.matched[s];i&&(t.matched.find(e=>Ve(e,i))||a.push(i))}return[n,r,a]}(e,t);n=Mt(r.reverse(),"beforeRouteLeave",e,t);for(const o of r)o.leaveGuards.forEach(r=>{n.push(xt(r,e,t))});const c=A.bind(null,e,t);return n.push(c),V(n).then(()=>{n=[];for(const r of o.list())n.push(xt(r,e,t));return n.push(c),V(n)}).then(()=>{n=Mt(a,"beforeRouteUpdate",e,t);for(const r of a)r.updateGuards.forEach(r=>{n.push(xt(r,e,t))});return n.push(c),V(n)}).then(()=>{n=[];for(const r of i)if(r.beforeEnter)if(ge(r.beforeEnter))for(const a of r.beforeEnter)n.push(xt(a,e,t));else n.push(xt(r.beforeEnter,e,t));return n.push(c),V(n)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),n=Mt(i,"beforeRouteEnter",e,t,O),n.push(c),V(n))).then(()=>{n=[];for(const r of s.list())n.push(xt(r,e,t));return n.push(c),V(n)}).catch(e=>ut(e,8)?e:Promise.reject(e))}function R(e,t,n){i.list().forEach(r=>O(()=>r(e,t,n)))}function k(e,t,n,r,o){const s=b(e,t);if(s)return s;const i=t===We,l=pe?history.state:{};n&&(r||i?a.replace(e.fullPath,he({scroll:i&&l&&l.scroll},o)):a.push(e.fullPath,o)),c.value=e,D(e,t,n,i),C()}let j;function S(){j||(j=a.listen((e,t,n)=>{if(!F.listening)return;const r=h(e),o=w(r);if(o)return void E(he(o,{replace:!0,force:!0}),r).catch(ve);l=r;const s=c.value;var i,u;pe&&(i=et(s.fullPath,n.delta),u=Ye(),tt.set(i,u)),P(r,s).catch(e=>ut(e,12)?e:ut(e,2)?(E(he(m(e.to),{force:!0}),r).then(e=>{ut(e,20)&&!n.delta&&n.type===He.pop&&a.go(-1,!1)}).catch(ve),Promise.reject()):(n.delta&&a.go(-n.delta,!1),T(e,r,s))).then(e=>{(e=e||k(r,s,!1))&&(n.delta&&!ut(e,8)?a.go(-n.delta,!1):n.type===He.pop&&ut(e,20)&&a.go(-1,!1)),R(r,s,e)}).catch(ve)}))}let L,I=$t(),q=$t();function T(e,t,n){C(e);const r=q.list();return r.length&&r.forEach(r=>r(e,t,n)),Promise.reject(e)}function C(e){return L||(L=!e,S(),I.list().forEach(([t,n])=>e?n(e):t()),I.reset()),e}function D(t,n,r,a){const{scrollBehavior:o}=e;if(!pe||!o)return Promise.resolve();const s=!r&&function(e){const t=tt.get(e);return tt.delete(e),t}(et(t.fullPath,0))||(a||!r)&&history.state&&history.state.scroll||null;return d().then(()=>o(t,n,s)).then(e=>e&&Ze(e)).catch(e=>T(e,t,n))}const $=e=>a.go(e);let x;const M=new Set,F={currentRoute:c,listening:!0,addRoute:function(e,n){let r,a;return ot(e)?(r=t.getRecordMatcher(e),a=n):a=e,t.addRoute(a,r)},removeRoute:function(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map(e=>e.record)},resolve:h,options:e,push:y,replace:function(e){return y(he(m(e),{replace:!0}))},go:$,back:()=>$(-1),forward:()=>$(1),beforeEach:o.add,beforeResolve:s.add,afterEach:i.add,onError:q.add,isReady:function(){return L&&c.value!==We?Promise.resolve():new Promise((e,t)=>{I.add([e,t])})},install(e){e.component("RouterLink",Vt),e.component("RouterView",Wt),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>g(c)}),pe&&!x&&c.value===We&&(x=!0,y(a.location).catch(e=>{}));const t={};for(const r in We)Object.defineProperty(t,r,{get:()=>c.value[r],enumerable:!0});e.provide(Tt,this),e.provide(Ct,_(t)),e.provide(Dt,c);const n=e.unmount;M.add(e),e.unmount=function(){M.delete(e),M.size<1&&(l=We,j&&j(),j=null,c.value=We,x=!1,L=!1),n()}}};function V(e){return e.reduce((e,t)=>e.then(()=>O(t)),Promise.resolve())}return F}({history:function(e){const t=at(e=Je(e)),n=function(e,t,n,r){let a=[],o=[],s=null;const i=({state:o})=>{const i=nt(e,location),c=n.value,l=t.value;let u=0;if(o){if(n.value=i,t.value=o,s&&s===c)return void(s=null);u=l?o.position-l.position:0}else r(i);a.forEach(e=>{e(n.value,c,{delta:u,type:He.pop,direction:u?u>0?Ge.forward:Ge.back:Ge.unknown})})};function c(){const{history:e}=window;e.state&&e.replaceState(he({},e.state,{scroll:Ye()}),"")}return window.addEventListener("popstate",i),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:function(){s=n.value},listen:function(e){a.push(e);const t=()=>{const t=a.indexOf(e);t>-1&&a.splice(t,1)};return o.push(t),t},destroy:function(){for(const e of o)e();o=[],window.removeEventListener("popstate",i),window.removeEventListener("beforeunload",c)}}}(e,t.state,t.location,t.replace),r=he({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:Xe.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}(),routes:_n,scrollBehavior:(e,t,n)=>n||{top:0}});bn.beforeEach(async(e,t,n)=>{const r=nn(),a=rn();if(a.lastUpdated||await a.init(),e.meta.title){const t=a.configs.site_name||"北航QQ身份认证系统";document.title=`${e.meta.title} - ${t}`}const o=e.path.startsWith("/admin"),s="Maintenance"===e.name,i=e.path.startsWith("/auth/")||"Register"===e.name,c="Home"===e.name;if(!i&&!c||o||s||!a.configs.maintenance_mode){if(e.meta.requiresFeature){const t=e.meta.requiresFeature;if(!a.isFeatureEnabled(t))return E.error(a.getFeatureDisabledReason(t)),void n({name:"NotFound"});if("freshman_enabled"===t&&!a.isInFreshmanWindow())return E.error(a.getFeatureDisabledReason(t)),void n({name:"NotFound"})}if(e.meta.requiresAuth){if(!r.isAuthenticated&&(await r.checkAuth(),!r.isAuthenticated))return E.warning("请先登录"),void n({name:"AdminLogin",query:{redirect:e.fullPath}});if(e.meta.requiresAdmin&&!r.isAdmin)return E.error("需要管理员权限"),void n({name:"Home"});if(e.meta.permission&&!r.hasPermission(e.meta.permission))return E.error("权限不足"),void n({name:"Dashboard"})}e.meta.hideForAuth&&r.isAuthenticated?r.isAdmin?n({name:"Dashboard"}):n({name:"Home"}):n()}else n({name:"Maintenance",query:{redirect:e.fullPath}})}),bn.afterEach((e,t)=>{const n=nn();n.isAuthenticated&&n.recordPageVisit(e.path)});const yn=bn,wn=Object.freeze(Object.defineProperty({__proto__:null,default:yn},Symbol.toStringTag,{value:"Module"})),En={id:"app"},An=N({__name:"App",setup(e){const t=nn();return O(async()=>{await t.checkAuth()}),(e,t)=>{const n=V("router-view");return P(),R("div",En,[j(n)])}}});for(const[On,Pn]of Object.entries(U))An.component(On,Pn);An.use(function(){const a=e(!0),o=a.run(()=>t({}));let s=[],i=[];const c=n({install(e){Q(c),c._a=e,e.provide(G,c),e.config.globalProperties.$pinia=c,i.forEach(e=>s.push(e)),i=[]},use(e){return this._a||r?s.push(e):i.push(e),this},_p:s,_a:null,_e:a,_s:new Map,state:o});return c}()),An.use(yn),An.use(B),window.addEventListener("storage",e=>{e.key!==Gt||e.newValue||window.location.reload()}),document.addEventListener("visibilitychange",()=>{if(!document.hidden){const e=Kt();e&&function(e){if(!e)return!0;try{const t=JSON.parse(atob(e.split(".")[1])),n=Math.floor(Date.now()/1e3);return t.exp<n}catch(t){return!0}}(e)&&(function(){Yt();const e=[];for(let t=0;t<localStorage.length;t++){const n=localStorage.key(t);n&&(n.startsWith("user_")||n.startsWith("auth_"))&&e.push(n)}e.forEach(e=>localStorage.removeItem(e))}(),window.location.href="/login")}}),W.defaults.timeout=3e4,W.defaults.headers.common["X-Requested-With"]="XMLHttpRequest",window.addEventListener("unhandledrejection",e=>{var t;null==(t=e.reason)||t.isAxiosError}),An.config.errorHandler=(e,t,n)=>{},An.mount("#app");export{an as _,rn as a,fe as b,Qt as c,nn as d,le as e,tn as r,Zt as s,Ht as u};
