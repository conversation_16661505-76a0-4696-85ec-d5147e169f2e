{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/rate/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Rate from './src/rate.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElRate: SFCWithInstall<typeof Rate> = withInstall(Rate)\nexport default ElRate\n\nexport * from './src/rate'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,MAAM,GAAG,WAAW,CAAC,IAAI;;;;"}