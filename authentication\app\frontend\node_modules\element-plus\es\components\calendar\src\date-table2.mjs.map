{"version": 3, "file": "date-table2.mjs", "sources": ["../../../../../../packages/components/calendar/src/date-table.vue"], "sourcesContent": ["<template>\n  <table\n    :class=\"[nsTable.b(), nsTable.is('range', isInRange)]\"\n    cellspacing=\"0\"\n    cellpadding=\"0\"\n  >\n    <thead v-if=\"!hideHeader\">\n      <tr>\n        <th v-for=\"day in weekDays\" :key=\"day\" scope=\"col\">{{ day }}</th>\n      </tr>\n    </thead>\n\n    <tbody>\n      <tr\n        v-for=\"(row, index) in rows\"\n        :key=\"index\"\n        :class=\"{\n          [nsTable.e('row')]: true,\n          [nsTable.em('row', 'hide-border')]: index === 0 && hideHeader,\n        }\"\n      >\n        <td\n          v-for=\"(cell, key) in row\"\n          :key=\"key\"\n          :class=\"getCellClass(cell)\"\n          @click=\"handlePickDay(cell)\"\n        >\n          <div :class=\"nsDay.b()\">\n            <slot name=\"date-cell\" :data=\"getSlotData(cell)\">\n              <span>{{ cell.text }}</span>\n            </slot>\n          </div>\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\nimport { dateTableEmits, dateTableProps } from './date-table'\nimport { useDateTable } from './use-date-table'\n\nimport type { CalendarDateCell } from './date-table'\n\ndefineOptions({\n  name: 'DateTable',\n})\n\nconst props = defineProps(dateTableProps)\nconst emit = defineEmits(dateTableEmits)\n\nconst {\n  isInRange,\n  now,\n  rows,\n  weekDays,\n  getFormattedDate,\n  handlePickDay,\n  getSlotData,\n} = useDateTable(props, emit)\n\nconst nsTable = useNamespace('calendar-table')\nconst nsDay = useNamespace('calendar-day')\n\nconst getCellClass = ({ text, type }: CalendarDateCell) => {\n  const classes: string[] = [type]\n  if (type === 'current') {\n    const date = getFormattedDate(text, type)\n    if (date.isSame(props.selectedDay, 'day')) {\n      classes.push(nsDay.is('selected'))\n    }\n    if (date.isSame(now, 'day')) {\n      classes.push(nsDay.is('today'))\n    }\n  }\n  return classes\n}\n\ndefineExpose({\n  /** @description toggle date panel */\n  getFormattedDate,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;mCA6Cc,CAAA;AAAA,EACZ,IAAM,EAAA,WAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA;AAAA,MACJ,SAAA;AAAA,MACA,GAAA;AAAA,MACA,IAAA;AAAA,MACA,QAAA;AAAA,MACA,gBAAA;AAAA,MACA,aAAA;AAAA,MACA,WAAA;AAAA,KACF,GAAI,YAAa,CAAA,KAAA,EAAO,IAAI,CAAA,CAAA;AAE5B,IAAM,MAAA,OAAA,GAAU,aAAa,gBAAgB,CAAA,CAAA;AAC7C,IAAM,MAAA,KAAA,GAAQ,aAAa,cAAc,CAAA,CAAA;AAEzC,IAAA,MAAM,YAAe,GAAA,CAAC,EAAE,IAAA,EAAM,MAA6B,KAAA;AACzD,MAAM,MAAA,OAAA,GAAoB,CAAC,IAAI,CAAA,CAAA;AAC/B,MAAA,IAAI,SAAS,SAAW,EAAA;AACtB,QAAM,MAAA,IAAA,GAAO,gBAAiB,CAAA,IAAA,EAAM,IAAI,CAAA,CAAA;AACxC,QAAA,IAAI,IAAK,CAAA,MAAA,CAAO,KAAM,CAAA,WAAA,EAAa,KAAK,CAAG,EAAA;AACzC,UAAA,OAAA,CAAQ,IAAK,CAAA,KAAA,CAAM,EAAG,CAAA,UAAU,CAAC,CAAA,CAAA;AAAA,SACnC;AACA,QAAA,IAAI,IAAK,CAAA,MAAA,CAAO,GAAK,EAAA,KAAK,CAAG,EAAA;AAC3B,UAAA,OAAA,CAAQ,IAAK,CAAA,KAAA,CAAM,EAAG,CAAA,OAAO,CAAC,CAAA,CAAA;AAAA,SAChC;AAAA,OACF;AACA,MAAO,OAAA,OAAA,CAAA;AAAA,KACT,CAAA;AAEA,IAAa,MAAA,CAAA;AAAA,MAAA,gBAAA;AAAA,KAEX,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}