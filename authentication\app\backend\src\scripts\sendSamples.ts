import '@/config/env'
import { emailService } from '@/services/EmailService'

async function main() {
  const to = process.argv[2] || '<EMAIL>'

  const results: Record<string, boolean> = {}

  // 系统通知
  results.notification = await emailService.sendNotification(
    to,
    '系统通知（测试）',
    '这是一封系统通知测试邮件。用于验证腾讯云 SES SDK 发送与模板变量渲染是否正常。'
  )

  // 审核通过
  results.auditApproved = await emailService.sendAuditResult(
    to,
    '张三',
    true
  )

  // 审核拒绝
  results.auditRejected = await emailService.sendAuditResult(
    to,
    '李四',
    false,
    '资料不完整，请补充学号与辅导员信息后重新提交。'
  )

  console.log(JSON.stringify(results))
  const ok = Object.values(results).every(Boolean)
  process.exit(ok ? 0 : 1)
}

main().catch(e => { console.error(e); process.exit(2) })

