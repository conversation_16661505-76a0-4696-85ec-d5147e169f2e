{"version": 3, "file": "constants.mjs", "sources": ["../../../../../../packages/components/radio/src/constants.ts"], "sourcesContent": ["import type { InjectionKey } from 'vue'\nimport type { RadioGroupProps } from './radio-group'\n\nexport interface RadioGroupContext extends RadioGroupProps {\n  changeEvent: (val: RadioGroupProps['modelValue']) => void\n}\n\nexport const radioGroupKey: InjectionKey<RadioGroupContext> =\n  Symbol('radioGroupKey')\n"], "names": [], "mappings": "AAAY,MAAC,aAAa,GAAG,MAAM,CAAC,eAAe;;;;"}