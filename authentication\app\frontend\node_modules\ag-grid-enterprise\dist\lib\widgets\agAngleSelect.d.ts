import { DragService, AgAbstractLabel, IAgLabelParams } from "ag-grid-community";
export declare class AgAngleSelect extends AgAbstractLabel {
    private static TEMPLATE;
    protected readonly eLabel: HTMLElement;
    private readonly eParentCircle;
    private readonly eChildCircle;
    private readonly eAngleValue;
    protected readonly dragService: DragService;
    private parentCircleRect;
    private degrees;
    private radius;
    private offsetX;
    private offsetY;
    private dragListener;
    constructor(config?: IAgLabelParams);
    postConstruct(): void;
    private updateNumberInput;
    private positionChildCircle;
    private calculatePolar;
    private calculateCartesian;
    private setOffsetX;
    private setOffsetY;
    private calculateAngleDrag;
    private toDegrees;
    private toRadians;
    private normalizeNegativeValue;
    private normalizeAngle180;
    getRadius(): number;
    setRadius(r: number): this;
    onValueChange(callbackFn: (newValue: number) => void): this;
    getValue(radians?: boolean): number;
    setValue(degrees: number, radians?: boolean): this;
    setWidth(width: number): this;
    setDisabled(disabled: boolean): this;
    protected destroy(): void;
}
