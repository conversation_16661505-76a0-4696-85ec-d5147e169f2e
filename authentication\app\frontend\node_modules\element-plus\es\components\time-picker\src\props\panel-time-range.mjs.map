{"version": 3, "file": "panel-time-range.mjs", "sources": ["../../../../../../../packages/components/time-picker/src/props/panel-time-range.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { timePanelSharedProps } from './shared'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const panelTimeRangeProps = buildProps({\n  ...timePanelSharedProps,\n  parsedValue: {\n    type: definePropType<[Dayjs, Dayjs]>(Array),\n  },\n} as const)\n\nexport type PanelTimeRangeProps = ExtractPropTypes<typeof panelTimeRangeProps>\nexport type PanelTimeRangePropsPublic = __ExtractPublicPropTypes<\n  typeof panelTimeRangeProps\n>\n"], "names": [], "mappings": ";;;AAEY,MAAC,mBAAmB,GAAG,UAAU,CAAC;AAC9C,EAAE,GAAG,oBAAoB;AACzB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,CAAC;;;;"}