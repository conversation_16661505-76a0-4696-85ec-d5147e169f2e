{"version": 3, "file": "step.mjs", "sources": ["../../../../../../packages/components/tour/src/step.ts"], "sourcesContent": ["import { buildProps, definePropType, iconPropType } from '@element-plus/utils'\nimport { tourContentProps } from './content'\n\nimport type {\n  CSSProperties,\n  ExtractPropTypes,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type { TourBtnProps, TourMask } from './types'\n\nexport const tourStepProps = buildProps({\n  /**\n   * @description get the element the guide card points to. empty makes it show in center of screen\n   */\n  target: {\n    type: definePropType<\n      string | HTMLElement | (() => HTMLElement | null) | null\n    >([String, Object, Function]),\n  },\n  /**\n   * @description the title of the tour content\n   */\n  title: String,\n  /**\n   * @description description\n   */\n  description: String,\n  /**\n   * @description whether to show a close button\n   */\n  showClose: {\n    type: Boolean,\n    default: undefined,\n  },\n  /**\n   * @description custom close icon, default is Close\n   */\n  closeIcon: {\n    type: iconPropType,\n  },\n  /**\n   * @description whether to show the arrow\n   */\n  showArrow: {\n    type: Boolean,\n    default: undefined,\n  },\n  /**\n   * @description position of the guide card relative to the target element\n   */\n  placement: tourContentProps.placement,\n  /**\n   * @description whether to enable masking, change mask style and fill color by pass custom props\n   */\n  mask: {\n    type: definePropType<TourMask>([Boolean, Object]),\n    default: undefined,\n  },\n  /**\n   * @description custom style for content\n   */\n  contentStyle: {\n    type: definePropType<CSSProperties>([Object]),\n  },\n  /**\n   * @description properties of the previous button\n   */\n  prevButtonProps: {\n    type: definePropType<TourBtnProps>(Object),\n  },\n  /**\n   * @description properties of the Next button\n   */\n  nextButtonProps: {\n    type: definePropType<TourBtnProps>(Object),\n  },\n  /**\n   * @description support pass custom scrollIntoView options\n   */\n  scrollIntoViewOptions: {\n    type: definePropType<boolean | ScrollIntoViewOptions>([Boolean, Object]),\n    default: undefined,\n  },\n  /**\n   * @description type, affects the background color and text color\n   */\n  type: {\n    type: definePropType<'default' | 'primary'>(String),\n  },\n})\n\nexport type TourStepProps = ExtractPropTypes<typeof tourStepProps>\nexport type TourStepPropsPublic = __ExtractPublicPropTypes<typeof tourStepProps>\n\nexport const tourStepEmits = {\n  close: () => true,\n}\nexport type TourStepEmits = typeof tourStepEmits\n"], "names": [], "mappings": ";;;;AAEY,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,SAAS,EAAE,gBAAgB,CAAC,SAAS;AACvC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,qBAAqB,EAAE;AACzB,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB;;;;"}