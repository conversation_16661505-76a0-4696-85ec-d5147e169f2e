{"version": 3, "file": "types.js", "sources": ["../../../../../../packages/components/cascader-panel/src/types.ts"], "sourcesContent": ["import type { InjectionKey, VNode } from 'vue'\nimport type {\n  default as CascaderNode,\n  CascaderOption,\n  CascaderProps,\n  ExpandTrigger,\n} from './node'\n\nexport type { CascaderNode, CascaderOption, CascaderProps, ExpandTrigger }\n\nexport type CascaderNodeValue = string | number\nexport type CascaderNodePathValue = CascaderNodeValue[]\nexport type CascaderValue =\n  | CascaderNodeValue\n  | CascaderNodePathValue\n  | (CascaderNodeValue | CascaderNodePathValue)[]\nexport type CascaderConfig = Required<CascaderProps>\nexport type isDisabled = (data: CascaderOption, node: CascaderNode) => boolean\nexport type isLeaf = (data: CascaderOption, node: CascaderNode) => boolean\nexport type Resolve = (dataList?: CascaderOption[]) => void\nexport type LazyLoad = (node: CascaderNode, resolve: Resolve) => void\nexport interface RenderLabelProps {\n  node: CascaderNode\n  data: CascaderOption\n}\nexport type RenderLabel = (props: RenderLabelProps) => VNode | VNode[]\n\nexport interface Tag {\n  node?: CascaderNode\n  key: number\n  text: string\n  hitState?: boolean\n  closable: boolean\n  isCollapseTag: boolean\n}\n\nexport interface ElCascaderPanelContext {\n  config: CascaderConfig\n  expandingNode: CascaderNode | undefined\n  checkedNodes: CascaderNode[]\n  isHoverMenu: boolean\n  initialLoaded: boolean\n  renderLabelFn: RenderLabel\n  lazyLoad: (\n    node?: CascaderNode,\n    cb?: (dataList: CascaderOption[]) => void\n  ) => void\n  expandNode: (node: CascaderNode, silent?: boolean) => void\n  handleCheckChange: (\n    node: CascaderNode,\n    checked: boolean,\n    emitClose?: boolean\n  ) => void\n}\n\nexport const CASCADER_PANEL_INJECTION_KEY: InjectionKey<ElCascaderPanelContext> =\n  Symbol()\n"], "names": [], "mappings": ";;;;AAAY,MAAC,4BAA4B,GAAG,MAAM;;;;"}