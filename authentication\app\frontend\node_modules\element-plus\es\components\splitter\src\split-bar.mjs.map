{"version": 3, "file": "split-bar.mjs", "sources": ["../../../../../../packages/components/splitter/src/split-bar.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { computed, ref } from 'vue'\nimport {\n  ArrowDown,\n  ArrowLeft,\n  ArrowRight,\n  ArrowUp,\n} from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nconst ns = useNamespace('splitter-bar')\n\ndefineOptions({\n  name: 'ElSplitterBar',\n})\n\nconst props = defineProps({\n  index: {\n    type: Number,\n    required: true,\n  },\n  layout: {\n    type: String,\n    values: ['horizontal', 'vertical'] as const,\n    default: 'horizontal',\n  },\n  resizable: {\n    type: Boolean,\n    default: true,\n  },\n  startCollapsible: {\n    type: Boolean,\n  },\n  endCollapsible: {\n    type: <PERSON>olean,\n  },\n})\n\nconst emit = defineEmits(['moveStart', 'moving', 'moveEnd', 'collapse'])\n\nconst isHorizontal = computed(() => props.layout === 'horizontal')\n\nconst barWrapStyles = computed(() => {\n  if (isHorizontal.value) {\n    return { width: 0 }\n  }\n  return { height: 0 }\n})\n\nconst draggerStyles = computed(() => {\n  return {\n    width: isHorizontal.value ? '16px' : '100%',\n    height: isHorizontal.value ? '100%' : '16px',\n    cursor: isHorizontal.value ? 'col-resize' : 'row-resize',\n    touchAction: 'none',\n  }\n})\n\nconst draggerPseudoClass = computed(() => {\n  const prefix = ns.e('dragger')\n  return {\n    [`${prefix}-horizontal`]: isHorizontal.value,\n    [`${prefix}-vertical`]: !isHorizontal.value,\n    [`${prefix}-active`]: !!startPos.value,\n  }\n})\n\nconst startPos = ref<[x: number, y: number] | null>(null)\n\n// Start dragging\nconst onMousedown = (e: MouseEvent) => {\n  if (!props.resizable) return\n  startPos.value = [e.pageX, e.pageY]\n  emit('moveStart', props.index)\n  window.addEventListener('mouseup', onMouseUp)\n  window.addEventListener('mousemove', onMouseMove)\n}\n\nconst onTouchStart = (e: TouchEvent) => {\n  if (props.resizable && e.touches.length === 1) {\n    e.preventDefault()\n    const touch = e.touches[0]\n    startPos.value = [touch.pageX, touch.pageY]\n    emit('moveStart', props.index)\n    window.addEventListener('touchend', onTouchEnd)\n    window.addEventListener('touchmove', onTouchMove)\n  }\n}\n\n// During dragging\nconst onMouseMove = (e: MouseEvent) => {\n  const { pageX, pageY } = e\n  const offsetX = pageX - startPos.value![0]\n  const offsetY = pageY - startPos.value![1]\n  const offset = isHorizontal.value ? offsetX : offsetY\n  emit('moving', props.index, offset)\n}\n\nconst onTouchMove = (e: TouchEvent) => {\n  if (e.touches.length === 1) {\n    e.preventDefault()\n    const touch = e.touches[0]\n    const offsetX = touch.pageX - startPos.value![0]\n    const offsetY = touch.pageY - startPos.value![1]\n    const offset = isHorizontal.value ? offsetX : offsetY\n    emit('moving', props.index, offset)\n  }\n}\n\n// End dragging\nconst onMouseUp = () => {\n  startPos.value = null\n  window.removeEventListener('mouseup', onMouseUp)\n  window.removeEventListener('mousemove', onMouseMove)\n  emit('moveEnd', props.index)\n}\n\nconst onTouchEnd = () => {\n  startPos.value = null\n  window.removeEventListener('touchend', onTouchEnd)\n  window.removeEventListener('touchmove', onTouchMove)\n  emit('moveEnd', props.index)\n}\n\nconst StartIcon = computed(() => (isHorizontal.value ? ArrowLeft : ArrowUp))\nconst EndIcon = computed(() => (isHorizontal.value ? ArrowRight : ArrowDown))\n</script>\n\n<template>\n  <div :class=\"[ns.b()]\" :style=\"barWrapStyles\">\n    <div\n      v-if=\"startCollapsible\"\n      :class=\"[ns.e('collapse-icon'), ns.e(`${layout}-collapse-icon-start`)]\"\n      @click=\"emit('collapse', index, 'start')\"\n    >\n      <slot name=\"start-collapsible\">\n        <component :is=\"StartIcon\" style=\"width: 12px; height: 12px\" />\n      </slot>\n    </div>\n\n    <div\n      :class=\"[\n        ns.e('dragger'),\n        draggerPseudoClass,\n        resizable ? '' : ns.e('disable'),\n      ]\"\n      :style=\"draggerStyles\"\n      @mousedown=\"onMousedown\"\n      @touchstart=\"onTouchStart\"\n    />\n    <div\n      v-if=\"endCollapsible\"\n      :class=\"[ns.e('collapse-icon'), ns.e(`${layout}-collapse-icon-end`)]\"\n      @click=\"emit('collapse', index, 'end')\"\n    >\n      <slot name=\"end-collapsible\">\n        <component :is=\"EndIcon\" style=\"width: 12px; height: 12px\" />\n      </slot>\n    </div>\n  </div>\n</template>\n"], "names": [], "mappings": ";;;;;mCAYc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJA,IAAM,MAAA,EAAA,GAAK,aAAa,cAAc,CAAA,CAAA;AA8BtC,IAAA,MAAM,YAAe,GAAA,QAAA,CAAS,MAAM,KAAA,CAAM,WAAW,YAAY,CAAA,CAAA;AAEjE,IAAM,MAAA,aAAA,GAAgB,SAAS,MAAM;AACnC,MAAA,IAAI,aAAa,KAAO,EAAA;AACtB,QAAO,OAAA,EAAE,OAAO,CAAE,EAAA,CAAA;AAAA,OACpB;AACA,MAAO,OAAA,EAAE,QAAQ,CAAE,EAAA,CAAA;AAAA,KACpB,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAgB,SAAS,MAAM;AACnC,MAAO,OAAA;AAAA,QACL,KAAA,EAAO,YAAa,CAAA,KAAA,GAAQ,MAAS,GAAA,MAAA;AAAA,QACrC,MAAA,EAAQ,YAAa,CAAA,KAAA,GAAQ,MAAS,GAAA,MAAA;AAAA,QACtC,MAAA,EAAQ,YAAa,CAAA,KAAA,GAAQ,YAAe,GAAA,YAAA;AAAA,QAC5C,WAAa,EAAA,MAAA;AAAA,OACf,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,kBAAA,GAAqB,SAAS,MAAM;AACxC,MAAM,MAAA,MAAA,GAAS,EAAG,CAAA,CAAA,CAAE,SAAS,CAAA,CAAA;AAC7B,MAAO,OAAA;AAAA,QACL,CAAC,CAAA,EAAG,MAAM,CAAA,WAAA,CAAa,GAAG,YAAa,CAAA,KAAA;AAAA,QACvC,CAAC,CAAG,EAAA,MAAM,CAAW,SAAA,CAAA,GAAG,CAAC,YAAa,CAAA,KAAA;AAAA,QACtC,CAAC,CAAG,EAAA,MAAM,SAAS,GAAG,CAAC,CAAC,QAAS,CAAA,KAAA;AAAA,OACnC,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAW,IAAmC,IAAI,CAAA,CAAA;AAGxD,IAAM,MAAA,WAAA,GAAc,CAAC,CAAkB,KAAA;AACrC,MAAI,IAAA,CAAC,MAAM,SAAW;AACtB,QAAA,OAAS;AACT,MAAK,QAAA,CAAA,KAAA,GAAA,CAAa,OAAW,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA;AAC7B,MAAO,IAAA,CAAA,WAAA,EAAA,KAAA,CAAiB;AACxB,MAAO,MAAA,CAAA,gBAAA,CAAiB,oBAAwB,CAAA,CAAA;AAAA,MAClD,MAAA,CAAA,gBAAA,CAAA,WAAA,EAAA,WAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,YAAM,GAAA,CAAA,CAAA,KAAe;AACvB,MAAA,IAAE,KAAe,CAAA,SAAA,IAAA,CAAA,CAAA,OAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACjB,QAAM,CAAA,CAAA,cAAU,EAAA,CAAA;AAChB,QAAA,MAAA,KAAiB,GAAA,CAAA,CAAA,OAAO,CAAA,CAAA,CAAA,CAAA;AACxB,QAAK,QAAA,CAAA,KAAA,GAAA,CAAa,MAAM,KAAK,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AAC7B,QAAO,IAAA,CAAA,WAAA,EAAA,KAAA,CAAiB;AACxB,QAAO,MAAA,CAAA,gBAAA,CAAiB,sBAAwB,CAAA,CAAA;AAAA,QAClD,MAAA,CAAA,gBAAA,CAAA,WAAA,EAAA,WAAA,CAAA,CAAA;AAAA,OACF;AAGA,KAAM,CAAA;AACJ,IAAM,MAAA,WAAS,GAAA,CAAA,CAAA,KAAU;AACzB,MAAA,MAAM,EAAU,KAAA,EAAA,KAAA,EAAA,GAAiB,CAAA,CAAA;AACjC,MAAA,MAAM,OAAU,GAAA,KAAA,GAAQ,QAAS,CAAA,KAAA,CAAO,CAAC,CAAA,CAAA;AACzC,MAAM,MAAA,OAAA,GAAsB,KAAA,GAAA,QAAA,CAAA,KAAkB,CAAA,CAAA,CAAA,CAAA;AAC9C,MAAK,MAAA,MAAA,GAAU,YAAM,CAAO,KAAM,GAAA,OAAA,GAAA,OAAA,CAAA;AAAA,MACpC,IAAA,CAAA,QAAA,EAAA,KAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAI,MAAA,WAAU,GAAA,CAAA,CAAA,KAAW;AACvB,MAAA,IAAE,CAAe,CAAA,OAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACjB,QAAM,CAAA,CAAA,cAAU,EAAA,CAAA;AAChB,QAAA,MAAM,KAAU,GAAA,CAAA,CAAA,OAAc,CAAA,CAAA,CAAA,CAAA;AAC9B,QAAA,MAAM,OAAU,GAAA,KAAA,CAAM,KAAQ,GAAA,QAAA,CAAS,MAAO,CAAC,CAAA,CAAA;AAC/C,QAAM,MAAA,OAAA,GAAsB,KAAA,CAAA,KAAA,GAAA,QAAkB,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAC9C,QAAK,MAAA,MAAA,GAAU,YAAM,CAAO,KAAM,GAAA,OAAA,GAAA,OAAA,CAAA;AAAA,QACpC,IAAA,CAAA,QAAA,EAAA,KAAA,CAAA,KAAA,EAAA,MAAA,CAAA,CAAA;AAAA,OACF;AAGA,KAAA,CAAA;AACE,IAAA,MAAA,SAAiB,GAAA,MAAA;AACjB,MAAO,QAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACP,MAAO,MAAA,CAAA,mBAAA,CAAoB,oBAAwB,CAAA,CAAA;AACnD,MAAK,MAAA,CAAA,mBAAsB,CAAA,WAAA,EAAA,WAAA,CAAA,CAAA;AAAA,MAC7B,IAAA,CAAA,SAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,UAAiB,GAAA,MAAA;AACjB,MAAO,QAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACP,MAAO,MAAA,CAAA,mBAAA,CAAoB,sBAAwB,CAAA,CAAA;AACnD,MAAK,MAAA,CAAA,mBAAsB,CAAA,WAAA,EAAA,WAAA,CAAA,CAAA;AAAA,MAC7B,IAAA,CAAA,SAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACA,IAAA,MAAM,YAAmB,QAAA,CAAA,MAAoB,YAAA,CAAA,KAAA,eAA+B,OAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}