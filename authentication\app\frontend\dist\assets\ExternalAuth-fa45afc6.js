import{_ as e,b as a,u as l}from"./index-bcbc0702.js";import{A as i}from"./AuthLayout-1aa16efb.js";import{V as o}from"./VerificationCode-b10ec6a7.js";/* empty css                   */import{C as s}from"./CameraCapture-b9781556.js";/* empty css                     *//* empty css               *//* empty css                  *//* empty css                  *//* empty css                 *//* empty css                 */import{ah as t,a0 as r,$ as d,aq as n,at as u,ar as c,ag as p,as as m,a6 as g,r as h,A as _,d as f,B as b,al as v,G as V,I as C,J as k,C as y,D as F,F as S,H as q,E as x,an as j,R as w,K as E,ai as z,aj as P,au as A,av as R,L as I,ak as T,ap as U}from"./element-plus-3ab68b46.js";import{authApi as Q}from"./auth-36869992.js";import"./utils-c6a461b2.js";const D={key:1,class:"step-content"},L={class:"basic-info"},M={class:"form-actions"},O={key:2,class:"step-content"},$={class:"photo-capture"},B={class:"photo-instructions"},G={key:0,class:"captured-photo"},N=["src"],H={class:"photo-actions"},J={class:"form-actions"},K={key:3,class:"step-content"},X={class:"email-verification"},W={class:"form-actions"},Y={key:0,class:"email-verification-code"},Z={key:4,class:"step-content"},ee={class:"phone-verification"},ae={class:"form-actions"},le={key:0,class:"phone-verification-code"},ie={key:5,class:"step-content"},oe={class:"submit-review"},se={class:"review-info"},te={class:"info-section"},re={class:"info-grid"},de={class:"info-item"},ne={class:"info-item"},ue={class:"info-item"},ce={class:"info-item"},pe={class:"info-item"},me={class:"info-item"},ge={class:"info-section"},he={class:"info-grid"},_e={class:"info-item"},fe={class:"info-item"},be={key:0,class:"info-section"},ve=["src"],Ve={class:"submit-notice"},Ce={class:"form-actions"};const ke=e({name:"ExternalAuth",components:{AuthLayout:i,CameraCapture:s,VerificationCode:o,ChatDotRound:t,User:r,School:d,Postcard:n,Reading:u,CreditCard:c,Message:p,Phone:m,Connection:g},setup(){const e=h(!1);_(async()=>{const{useUserSessionStore:l}=await a(()=>import("./userSession-03354358.js"),["assets/userSession-03354358.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css"]);if(l().hasValidEditToken){e.value=!0;try{const{applicationApi:e}=await a(()=>import("./application-7212cdc3.js"),["assets/application-7212cdc3.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css","assets/userSession-03354358.js"]),l=await e.getDetail();(null==l?void 0:l.success)&&l.data&&(u.qq=u.qq||"",u.real_name=l.data.realName||"",u.school=l.data.school||"",u.student_id=l.data.studentId||"",u.major=l.data.department||"",u.grade=l.data.grade||"",c.email=l.data.email||"",p.phone=l.data.phone||"",Array.isArray(l.data.uploadedImages)&&l.data.uploadedImages.length>0&&(d.value=l.data.uploadedImages[0]))}catch(i){}}});const i=l(),o=h(!1),s=h(0),t=h(!1),r=h(!1),d=h(""),n=["填写信息","实时拍照","邮箱验证","手机验证","提交审核"],u=f({qq:"",real_name:"",school:"",student_id:"",major:"",grade:"",id_card:""}),c=f({email:""}),p=f({phone:""}),m=()=>{s.value<n.length-1&&s.value++};return{loading:o,currentStep:s,emailSent:t,phoneSent:r,capturedPhoto:d,stepTitles:n,basicForm:u,emailForm:c,phoneForm:p,basicRules:{qq:[{required:!0,message:"请输入QQ号",trigger:"blur"},{pattern:/^[1-9][0-9]{4,10}$/,message:"QQ号格式不正确",trigger:"blur"}],real_name:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{min:2,max:10,message:"姓名长度在2到10个字符",trigger:"blur"}],school:[{required:!0,message:"请输入学校名称",trigger:"blur"}],student_id:[{required:!0,message:"请输入学号",trigger:"blur"}],major:[{required:!0,message:"请输入专业",trigger:"blur"}],grade:[{required:!0,message:"请选择年级",trigger:"change"}],id_card:[{required:!0,message:"请输入身份证号",trigger:"blur"},{pattern:/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,message:"身份证号格式不正确",trigger:"blur"}]},emailRules:{email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"邮箱格式不正确",trigger:"blur"}]},phoneRules:{phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"手机号格式不正确",trigger:"blur"}]},nextStep:m,prevStep:()=>{s.value>0&&s.value--},getGradeText:e=>({1:"大一",2:"大二",3:"大三",4:"大四",graduate1:"研一",graduate2:"研二",graduate3:"研三",phd:"博士"}[e]||e),handlePhotoCaptured:e=>{d.value=e},handleCameraError:e=>{x.error("摄像头访问失败："+e.message)},retakePhoto:()=>{d.value=""},sendEmailVerification:async()=>{t.value=!0,x.success("验证码已发送到您的邮箱")},handleEmailVerified:()=>{x.success("邮箱验证成功"),m()},sendPhoneVerification:async()=>{r.value=!0,x.success("验证码已发送到您的手机")},handlePhoneVerified:()=>{x.success("手机验证成功"),m()},submitAuth:async()=>{try{o.value=!0;const l={qq:u.qq,real_name:u.real_name,school:u.school,student_id:u.student_id,email:c.email,phone:p.phone,photo:d.value},{applicationApi:s}=await a(()=>import("./application-7212cdc3.js"),["assets/application-7212cdc3.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css","assets/userSession-03354358.js"]);if(e.value){const e={realName:l.real_name,school:l.school,studentId:l.student_id,email:l.email,phone:l.phone,department:l.major,grade:l.grade,uploadedImages:d.value?[d.value]:void 0,extraInfo:d.value?{capture:{timestamp:Date.now(),source:"edit"}}:void 0},a=await s.edit(e);if(!(null==a?void 0:a.success))throw new Error((null==a?void 0:a.message)||"保存失败");return x.success("编辑已保存"),void i.push({name:"ApplicationStatus"})}const t=await Q.submitExternalAuth(l);if((null==t?void 0:t.editToken)&&(null==t?void 0:t.editTokenExpiresIn)){const{useUserSessionStore:e}=await a(()=>import("./userSession-03354358.js"),["assets/userSession-03354358.js","assets/index-bcbc0702.js","assets/element-plus-3ab68b46.js","assets/utils-c6a461b2.js","assets/index-309cab10.css"]);e().setEditToken(t.editToken,t.editTokenExpiresIn)}x.success("认证申请提交成功，请等待审核"),i.push({name:"Success",query:{type:"external"}})}catch(l){x.error("提交失败："+l.message)}finally{o.value=!1}}}}},[["render",function(e,a,l,t,r,d){const n=j,u=w("ChatDotRound"),c=E,p=z,m=P,g=w("User"),h=w("School"),_=w("Postcard"),f=w("Reading"),x=A,Q=R,ke=w("CreditCard"),ye=I,Fe=T,Se=s,qe=w("Message"),xe=U,je=o,we=w("Phone"),Ee=i;return b(),v(Ee,{title:"外校学生认证",subtitle:"Other Universities Student Authentication","page-title":"外校学生认证","page-description":"其他高校在读学生身份认证","icon-component":"Connection",features:["多高校身份认证","实时拍照验证","人工审核保障"],steps:t.stepTitles,"current-step":t.currentStep,"show-steps":!0},{default:V(()=>[e.editMode?(b(),v(n,{key:0,type:"info",closable:!1,"show-icon":"",style:{"margin-bottom":"12px"}},{default:V(()=>a[12]||(a[12]=[C(" 当前为编辑模式：QQ不可修改，保存后将更新待审核申请。 ",-1)])),_:1,__:[12]})):k("",!0),0===t.currentStep?(b(),y("div",D,[F("div",L,[a[15]||(a[15]=F("h3",null,"基本信息填写",-1)),S(Fe,{model:t.basicForm,rules:t.basicRules,ref:"basicFormRef","label-width":"100px"},{default:V(()=>[S(m,{label:"QQ号",prop:"qq"},{default:V(()=>[S(p,{modelValue:t.basicForm.qq,"onUpdate:modelValue":a[0]||(a[0]=e=>t.basicForm.qq=e),placeholder:"请输入您的QQ号",size:"large",disabled:t.loading||e.editMode},{prefix:V(()=>[S(c,null,{default:V(()=>[S(u)]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),S(m,{label:"真实姓名",prop:"real_name"},{default:V(()=>[S(p,{modelValue:t.basicForm.real_name,"onUpdate:modelValue":a[1]||(a[1]=e=>t.basicForm.real_name=e),placeholder:"请输入真实姓名",size:"large",disabled:t.loading},{prefix:V(()=>[S(c,null,{default:V(()=>[S(g)]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),S(m,{label:"学校名称",prop:"school"},{default:V(()=>[S(p,{modelValue:t.basicForm.school,"onUpdate:modelValue":a[2]||(a[2]=e=>t.basicForm.school=e),placeholder:"请输入完整的学校名称",size:"large",disabled:t.loading},{prefix:V(()=>[S(c,null,{default:V(()=>[S(h)]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),S(m,{label:"学号",prop:"student_id"},{default:V(()=>[S(p,{modelValue:t.basicForm.student_id,"onUpdate:modelValue":a[3]||(a[3]=e=>t.basicForm.student_id=e),placeholder:"请输入学号",size:"large",disabled:t.loading},{prefix:V(()=>[S(c,null,{default:V(()=>[S(_)]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),S(m,{label:"专业",prop:"major"},{default:V(()=>[S(p,{modelValue:t.basicForm.major,"onUpdate:modelValue":a[4]||(a[4]=e=>t.basicForm.major=e),placeholder:"请输入专业名称",size:"large",disabled:t.loading},{prefix:V(()=>[S(c,null,{default:V(()=>[S(f)]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),S(m,{label:"年级",prop:"grade"},{default:V(()=>[S(Q,{modelValue:t.basicForm.grade,"onUpdate:modelValue":a[5]||(a[5]=e=>t.basicForm.grade=e),placeholder:"请选择年级",size:"large",disabled:t.loading,style:{width:"100%"}},{default:V(()=>[S(x,{label:"大一",value:"1"}),S(x,{label:"大二",value:"2"}),S(x,{label:"大三",value:"3"}),S(x,{label:"大四",value:"4"}),S(x,{label:"研一",value:"graduate1"}),S(x,{label:"研二",value:"graduate2"}),S(x,{label:"研三",value:"graduate3"}),S(x,{label:"博士",value:"phd"})]),_:1},8,["modelValue","disabled"])]),_:1}),S(m,{label:"身份证号",prop:"id_card"},{default:V(()=>[S(p,{modelValue:t.basicForm.id_card,"onUpdate:modelValue":a[6]||(a[6]=e=>t.basicForm.id_card=e),placeholder:"请输入身份证号",size:"large",disabled:t.loading,maxlength:"18"},{prefix:V(()=>[S(c,null,{default:V(()=>[S(ke)]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),F("div",M,[S(ye,{type:"primary",size:"large",loading:t.loading,onClick:t.nextStep},{default:V(()=>a[13]||(a[13]=[C(" 下一步 ",-1)])),_:1,__:[13]},8,["loading","onClick"]),S(ye,{size:"large",onClick:a[7]||(a[7]=a=>e.$router.push("/"))},{default:V(()=>a[14]||(a[14]=[C(" 返回首页 ",-1)])),_:1,__:[14]})])]),_:1},8,["model","rules"])])])):k("",!0),1===t.currentStep?(b(),y("div",O,[F("div",$,[a[21]||(a[21]=F("h3",null,"实时拍照验证",-1)),F("div",B,[S(n,{title:"拍照说明",type:"info",closable:!1,"show-icon":""},{default:V(()=>a[16]||(a[16]=[F("ul",null,[F("li",null,"请确保光线充足，面部清晰可见"),F("li",null,"请正面面对摄像头，保持自然表情"),F("li",null,"请勿佩戴帽子、墨镜等遮挡物"),F("li",null,"拍照将用于人工审核身份验证")],-1)])),_:1})]),S(Se,{onCaptured:t.handlePhotoCaptured,onError:t.handleCameraError},null,8,["onCaptured","onError"]),t.capturedPhoto?(b(),y("div",G,[a[19]||(a[19]=F("h4",null,"已拍摄照片",-1)),F("img",{src:t.capturedPhoto,alt:"拍摄的照片",class:"photo-preview"},null,8,N),F("div",H,[S(ye,{type:"success",size:"large",onClick:t.nextStep},{default:V(()=>a[17]||(a[17]=[C(" 确认照片，继续 ",-1)])),_:1,__:[17]},8,["onClick"]),S(ye,{size:"large",onClick:t.retakePhoto},{default:V(()=>a[18]||(a[18]=[C(" 重新拍照 ",-1)])),_:1,__:[18]},8,["onClick"])])])):k("",!0),F("div",J,[S(ye,{size:"large",onClick:t.prevStep},{default:V(()=>a[20]||(a[20]=[C(" 返回上一步 ",-1)])),_:1,__:[20]},8,["onClick"])])])])):k("",!0),2===t.currentStep?(b(),y("div",K,[F("div",X,[a[25]||(a[25]=F("h3",null,"邮箱验证",-1)),S(Fe,{model:t.emailForm,rules:t.emailRules,ref:"emailFormRef","label-width":"100px"},{default:V(()=>[S(m,{label:"邮箱地址",prop:"email"},{default:V(()=>[S(p,{modelValue:t.emailForm.email,"onUpdate:modelValue":a[8]||(a[8]=e=>t.emailForm.email=e),placeholder:"请输入邮箱地址",size:"large",disabled:t.loading},{prefix:V(()=>[S(c,null,{default:V(()=>[S(qe)]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),F("div",W,[S(ye,{type:"primary",size:"large",loading:t.loading,onClick:t.sendEmailVerification},{default:V(()=>a[22]||(a[22]=[C(" 发送验证码 ",-1)])),_:1,__:[22]},8,["loading","onClick"]),S(ye,{size:"large",onClick:t.prevStep},{default:V(()=>a[23]||(a[23]=[C(" 返回上一步 ",-1)])),_:1,__:[23]},8,["onClick"])])]),_:1},8,["model","rules"]),t.emailSent?(b(),y("div",Y,[S(xe,null,{default:V(()=>a[24]||(a[24]=[C("验证码已发送",-1)])),_:1,__:[24]}),S(je,{email:t.emailForm.email,onVerified:t.handleEmailVerified,onBack:a[9]||(a[9]=e=>t.emailSent=!1)},null,8,["email","onVerified"])])):k("",!0)])])):k("",!0),3===t.currentStep?(b(),y("div",Z,[F("div",ee,[a[29]||(a[29]=F("h3",null,"手机号验证",-1)),S(Fe,{model:t.phoneForm,rules:t.phoneRules,ref:"phoneFormRef","label-width":"100px"},{default:V(()=>[S(m,{label:"手机号",prop:"phone"},{default:V(()=>[S(p,{modelValue:t.phoneForm.phone,"onUpdate:modelValue":a[10]||(a[10]=e=>t.phoneForm.phone=e),placeholder:"请输入手机号",size:"large",disabled:t.loading},{prefix:V(()=>[S(c,null,{default:V(()=>[S(we)]),_:1})]),_:1},8,["modelValue","disabled"])]),_:1}),F("div",ae,[S(ye,{type:"primary",size:"large",loading:t.loading,onClick:t.sendPhoneVerification},{default:V(()=>a[26]||(a[26]=[C(" 发送验证码 ",-1)])),_:1,__:[26]},8,["loading","onClick"]),S(ye,{size:"large",onClick:t.prevStep},{default:V(()=>a[27]||(a[27]=[C(" 返回上一步 ",-1)])),_:1,__:[27]},8,["onClick"])])]),_:1},8,["model","rules"]),t.phoneSent?(b(),y("div",le,[S(xe,null,{default:V(()=>a[28]||(a[28]=[C("验证码已发送",-1)])),_:1,__:[28]}),S(je,{phone:t.phoneForm.phone,onVerified:t.handlePhoneVerified,onBack:a[11]||(a[11]=e=>t.phoneSent=!1)},null,8,["phone","onVerified"])])):k("",!0)])])):k("",!0),4===t.currentStep?(b(),y("div",ie,[F("div",oe,[a[43]||(a[43]=F("h3",null,"信息确认",-1)),S(n,{title:"请确认以下信息无误",type:"info",closable:!1,"show-icon":""},{default:V(()=>[F("div",se,[F("div",te,[a[36]||(a[36]=F("h4",null,"基本信息",-1)),F("div",re,[F("div",de,[a[30]||(a[30]=F("label",null,"QQ号：",-1)),F("span",null,q(t.basicForm.qq),1)]),F("div",ne,[a[31]||(a[31]=F("label",null,"真实姓名：",-1)),F("span",null,q(t.basicForm.real_name),1)]),F("div",ue,[a[32]||(a[32]=F("label",null,"学校：",-1)),F("span",null,q(t.basicForm.school),1)]),F("div",ce,[a[33]||(a[33]=F("label",null,"学号：",-1)),F("span",null,q(t.basicForm.student_id),1)]),F("div",pe,[a[34]||(a[34]=F("label",null,"专业：",-1)),F("span",null,q(t.basicForm.major),1)]),F("div",me,[a[35]||(a[35]=F("label",null,"年级：",-1)),F("span",null,q(t.getGradeText(t.basicForm.grade)),1)])])]),F("div",ge,[a[39]||(a[39]=F("h4",null,"联系信息",-1)),F("div",he,[F("div",_e,[a[37]||(a[37]=F("label",null,"邮箱：",-1)),F("span",null,q(t.emailForm.email),1)]),F("div",fe,[a[38]||(a[38]=F("label",null,"手机号：",-1)),F("span",null,q(t.phoneForm.phone),1)])])]),t.capturedPhoto?(b(),y("div",be,[a[40]||(a[40]=F("h4",null,"身份照片",-1)),F("img",{src:t.capturedPhoto,alt:"身份照片",class:"review-photo"},null,8,ve)])):k("",!0)])]),_:1}),F("div",Ve,[S(n,{title:"审核说明",type:"warning",closable:!1,"show-icon":""},{default:V(()=>a[41]||(a[41]=[F("ul",null,[F("li",null,"提交后将进入人工审核流程"),F("li",null,"审核时间通常为1-3个工作日"),F("li",null,"审核结果将通过邮箱和短信通知"),F("li",null,"如有疑问请联系管理员")],-1)])),_:1})]),F("div",Ce,[S(ye,{type:"primary",size:"large",loading:t.loading,onClick:t.submitAuth},{default:V(()=>[C(q(e.editMode?"保存修改":"提交认证申请"),1)]),_:1},8,["loading","onClick"]),S(ye,{size:"large",onClick:t.prevStep},{default:V(()=>a[42]||(a[42]=[C(" 返回修改 ",-1)])),_:1,__:[42]},8,["onClick"])])])])):k("",!0)]),_:1},8,["steps","current-step"])}],["__scopeId","data-v-3c92e674"]]);export{ke as default};
