import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("iotexplorer.tencentcloudapi.com", "2019-04-23", clientConfig);
    }
    async GetCOSURL(req, cb) {
        return this.request("GetCOSURL", req, cb);
    }
    async ModifyStudioProduct(req, cb) {
        return this.request("ModifyStudioProduct", req, cb);
    }
    async DescribeDevicePackages(req, cb) {
        return this.request("DescribeDevicePackages", req, cb);
    }
    async DescribeFirmwareUpdateStatus(req, cb) {
        return this.request("DescribeFirmwareUpdateStatus", req, cb);
    }
    async DescribeAISearchTaskAsync(req, cb) {
        return this.request("DescribeAISearchTaskAsync", req, cb);
    }
    async DescribeP2PRoute(req, cb) {
        return this.request("DescribeP2PRoute", req, cb);
    }
    async DescribeCloudStorageEventsWithAITasks(req, cb) {
        return this.request("DescribeCloudStorageEventsWithAITasks", req, cb);
    }
    async DescribeTopicPolicy(req, cb) {
        return this.request("DescribeTopicPolicy", req, cb);
    }
    async CreateCloudStorageAIService(req, cb) {
        return this.request("CreateCloudStorageAIService", req, cb);
    }
    async GetProjectList(req, cb) {
        return this.request("GetProjectList", req, cb);
    }
    async CallDeviceActionSync(req, cb) {
        return this.request("CallDeviceActionSync", req, cb);
    }
    async DirectBindDeviceInFamily(req, cb) {
        return this.request("DirectBindDeviceInFamily", req, cb);
    }
    async DescribeDeviceLocationSolve(req, cb) {
        return this.request("DescribeDeviceLocationSolve", req, cb);
    }
    async GenerateSignedVideoURL(req, cb) {
        return this.request("GenerateSignedVideoURL", req, cb);
    }
    async CreateTWeSeeRecognitionTask(req, cb) {
        return this.request("CreateTWeSeeRecognitionTask", req, cb);
    }
    async DescribeDeviceBindGateway(req, cb) {
        return this.request("DescribeDeviceBindGateway", req, cb);
    }
    async GetDeviceLocationHistory(req, cb) {
        return this.request("GetDeviceLocationHistory", req, cb);
    }
    async ListEventHistory(req, cb) {
        return this.request("ListEventHistory", req, cb);
    }
    async DescribeStudioProduct(req, cb) {
        return this.request("DescribeStudioProduct", req, cb);
    }
    async GetLoRaGatewayList(req, cb) {
        return this.request("GetLoRaGatewayList", req, cb);
    }
    async ReleaseStudioProduct(req, cb) {
        return this.request("ReleaseStudioProduct", req, cb);
    }
    async GetBatchProductionsList(req, cb) {
        return this.request("GetBatchProductionsList", req, cb);
    }
    async DescribeCloudStorageThumbnailList(req, cb) {
        return this.request("DescribeCloudStorageThumbnailList", req, cb);
    }
    async CreateTRTCSignaturesWithRoomId(req, cb) {
        return this.request("CreateTRTCSignaturesWithRoomId", req, cb);
    }
    async ModifyFenceBind(req, cb) {
        return this.request("ModifyFenceBind", req, cb);
    }
    async InvokeAISearchService(req, cb) {
        return this.request("InvokeAISearchService", req, cb);
    }
    async DescribeProductCloudStorageAIService(req, cb) {
        return this.request("DescribeProductCloudStorageAIService", req, cb);
    }
    async DescribePackageConsumeTasks(req, cb) {
        return this.request("DescribePackageConsumeTasks", req, cb);
    }
    async DescribeCloudStoragePackageConsumeStats(req, cb) {
        return this.request("DescribeCloudStoragePackageConsumeStats", req, cb);
    }
    async GetGatewaySubDeviceList(req, cb) {
        return this.request("GetGatewaySubDeviceList", req, cb);
    }
    async DeletePositionSpace(req, cb) {
        return this.request("DeletePositionSpace", req, cb);
    }
    async DescribeActivateDevice(req, cb) {
        return this.request("DescribeActivateDevice", req, cb);
    }
    async DescribeBindedProducts(req, cb) {
        return this.request("DescribeBindedProducts", req, cb);
    }
    async ModifyLoRaGateway(req, cb) {
        return this.request("ModifyLoRaGateway", req, cb);
    }
    async DescribeDevice(req, cb) {
        return this.request("DescribeDevice", req, cb);
    }
    async DescribeFenceEventList(req, cb) {
        return this.request("DescribeFenceEventList", req, cb);
    }
    async DescribeUnbindedDevices(req, cb) {
        return this.request("DescribeUnbindedDevices", req, cb);
    }
    async CreateIotVideoCloudStorage(req, cb) {
        return this.request("CreateIotVideoCloudStorage", req, cb);
    }
    async DeleteTopicRule(req, cb) {
        return this.request("DeleteTopicRule", req, cb);
    }
    async DescribeCloudStorageAIService(req, cb) {
        return this.request("DescribeCloudStorageAIService", req, cb);
    }
    async DescribeFenceBindList(req, cb) {
        return this.request("DescribeFenceBindList", req, cb);
    }
    async ModifyTWeSeeConfig(req, cb) {
        return this.request("ModifyTWeSeeConfig", req, cb);
    }
    async DeleteLoRaGateway(req, cb) {
        return this.request("DeleteLoRaGateway", req, cb);
    }
    async CancelAssignTWeCallLicense(req, cb) {
        return this.request("CancelAssignTWeCallLicense", req, cb);
    }
    async CreateStudioProduct(req, cb) {
        return this.request("CreateStudioProduct", req, cb);
    }
    async DescribeCloudStorageDate(req, cb) {
        return this.request("DescribeCloudStorageDate", req, cb);
    }
    async DescribeFreeCloudStorageNum(req, cb) {
        return this.request("DescribeFreeCloudStorageNum", req, cb);
    }
    async EnableTopicRule(req, cb) {
        return this.request("EnableTopicRule", req, cb);
    }
    async DescribeGatewaySubDeviceList(req, cb) {
        return this.request("DescribeGatewaySubDeviceList", req, cb);
    }
    async DescribeCloudStorageUsers(req, cb) {
        return this.request("DescribeCloudStorageUsers", req, cb);
    }
    async TransferCloudStorage(req, cb) {
        return this.request("TransferCloudStorage", req, cb);
    }
    async DescribeVideoLicense(req, cb) {
        return this.request("DescribeVideoLicense", req, cb);
    }
    async ChangeP2PRoute(req, cb) {
        return this.request("ChangeP2PRoute", req, cb);
    }
    async SearchStudioProduct(req, cb) {
        return this.request("SearchStudioProduct", req, cb);
    }
    async ModifyTopicRule(req, cb) {
        return this.request("ModifyTopicRule", req, cb);
    }
    async InheritCloudStorageUser(req, cb) {
        return this.request("InheritCloudStorageUser", req, cb);
    }
    async TransferTWeCallDevice(req, cb) {
        return this.request("TransferTWeCallDevice", req, cb);
    }
    async ListFirmwares(req, cb) {
        return this.request("ListFirmwares", req, cb);
    }
    async PublishFirmwareUpdateMessage(req, cb) {
        return this.request("PublishFirmwareUpdateMessage", req, cb);
    }
    async CreateDevice(req, cb) {
        return this.request("CreateDevice", req, cb);
    }
    async DeleteFenceBind(req, cb) {
        return this.request("DeleteFenceBind", req, cb);
    }
    async ControlDeviceData(req, cb) {
        return this.request("ControlDeviceData", req, cb);
    }
    async DescribeFirmwareTask(req, cb) {
        return this.request("DescribeFirmwareTask", req, cb);
    }
    async GenSingleDeviceSignatureOfPublic(req, cb) {
        return this.request("GenSingleDeviceSignatureOfPublic", req, cb);
    }
    async ResetCloudStorage(req, cb) {
        return this.request("ResetCloudStorage", req, cb);
    }
    async InvokeExternalSourceAIServiceTask(req, cb) {
        return this.request("InvokeExternalSourceAIServiceTask", req, cb);
    }
    async DescribeLoRaFrequency(req, cb) {
        return this.request("DescribeLoRaFrequency", req, cb);
    }
    async ModifyPositionSpace(req, cb) {
        return this.request("ModifyPositionSpace", req, cb);
    }
    async DescribeCloudStorageTime(req, cb) {
        return this.request("DescribeCloudStorageTime", req, cb);
    }
    async GetTopicRuleList(req, cb) {
        return this.request("GetTopicRuleList", req, cb);
    }
    async DescribeCloudStorageThumbnail(req, cb) {
        return this.request("DescribeCloudStorageThumbnail", req, cb);
    }
    async ResetCloudStorageAIService(req, cb) {
        return this.request("ResetCloudStorageAIService", req, cb);
    }
    async GetWechatDeviceTicket(req, cb) {
        return this.request("GetWechatDeviceTicket", req, cb);
    }
    async DescribeCloudStorageMultiThumbnail(req, cb) {
        return this.request("DescribeCloudStorageMultiThumbnail", req, cb);
    }
    async CallDeviceActionAsync(req, cb) {
        return this.request("CallDeviceActionAsync", req, cb);
    }
    async ModifyLoRaFrequency(req, cb) {
        return this.request("ModifyLoRaFrequency", req, cb);
    }
    async CreateAISearchTaskAsync(req, cb) {
        return this.request("CreateAISearchTaskAsync", req, cb);
    }
    async DeleteProject(req, cb) {
        return this.request("DeleteProject", req, cb);
    }
    async DescribeTopicRule(req, cb) {
        return this.request("DescribeTopicRule", req, cb);
    }
    async PublishBroadcastMessage(req, cb) {
        return this.request("PublishBroadcastMessage", req, cb);
    }
    async UnbindProducts(req, cb) {
        return this.request("UnbindProducts", req, cb);
    }
    async GetPositionSpaceList(req, cb) {
        return this.request("GetPositionSpaceList", req, cb);
    }
    async DeleteStudioProduct(req, cb) {
        return this.request("DeleteStudioProduct", req, cb);
    }
    async DescribeDeviceData(req, cb) {
        return this.request("DescribeDeviceData", req, cb);
    }
    async DescribeGatewayBindDevices(req, cb) {
        return this.request("DescribeGatewayBindDevices", req, cb);
    }
    async DismissRoomByStrRoomIdFromTRTC(req, cb) {
        return this.request("DismissRoomByStrRoomIdFromTRTC", req, cb);
    }
    async CreatePositionFence(req, cb) {
        return this.request("CreatePositionFence", req, cb);
    }
    async ModifyProductCloudStorageAIService(req, cb) {
        return this.request("ModifyProductCloudStorageAIService", req, cb);
    }
    async CreateLoRaGateway(req, cb) {
        return this.request("CreateLoRaGateway", req, cb);
    }
    async CreateTopicRule(req, cb) {
        return this.request("CreateTopicRule", req, cb);
    }
    async CreateLoRaFrequency(req, cb) {
        return this.request("CreateLoRaFrequency", req, cb);
    }
    async CreateTopicPolicy(req, cb) {
        return this.request("CreateTopicPolicy", req, cb);
    }
    async PublishRRPCMessage(req, cb) {
        return this.request("PublishRRPCMessage", req, cb);
    }
    async DescribeCloudStorage(req, cb) {
        return this.request("DescribeCloudStorage", req, cb);
    }
    async UploadFirmware(req, cb) {
        return this.request("UploadFirmware", req, cb);
    }
    async InvokeCloudStorageAIServiceTask(req, cb) {
        return this.request("InvokeCloudStorageAIServiceTask", req, cb);
    }
    async DescribeActivateLicenseService(req, cb) {
        return this.request("DescribeActivateLicenseService", req, cb);
    }
    async CreateFreeCloudStorage(req, cb) {
        return this.request("CreateFreeCloudStorage", req, cb);
    }
    async ModifyApplication(req, cb) {
        return this.request("ModifyApplication", req, cb);
    }
    async UpdateDevicesEnableState(req, cb) {
        return this.request("UpdateDevicesEnableState", req, cb);
    }
    async ModifyProject(req, cb) {
        return this.request("ModifyProject", req, cb);
    }
    async BindProducts(req, cb) {
        return this.request("BindProducts", req, cb);
    }
    async RemoveUserByRoomIdFromTRTC(req, cb) {
        return this.request("RemoveUserByRoomIdFromTRTC", req, cb);
    }
    async SearchPositionSpace(req, cb) {
        return this.request("SearchPositionSpace", req, cb);
    }
    async ResetTWeCallDevice(req, cb) {
        return this.request("ResetTWeCallDevice", req, cb);
    }
    async ModifyTopicPolicy(req, cb) {
        return this.request("ModifyTopicPolicy", req, cb);
    }
    async DescribeSpaceFenceEventList(req, cb) {
        return this.request("DescribeSpaceFenceEventList", req, cb);
    }
    async UpdateFirmware(req, cb) {
        return this.request("UpdateFirmware", req, cb);
    }
    async GetTWeCallActiveStatus(req, cb) {
        return this.request("GetTWeCallActiveStatus", req, cb);
    }
    async DescribeGatewaySubProducts(req, cb) {
        return this.request("DescribeGatewaySubProducts", req, cb);
    }
    async DescribeDevicePositionList(req, cb) {
        return this.request("DescribeDevicePositionList", req, cb);
    }
    async DescribeFirmware(req, cb) {
        return this.request("DescribeFirmware", req, cb);
    }
    async DescribeDeviceFirmwares(req, cb) {
        return this.request("DescribeDeviceFirmwares", req, cb);
    }
    async BindDevices(req, cb) {
        return this.request("BindDevices", req, cb);
    }
    async CreateBatchProduction(req, cb) {
        return this.request("CreateBatchProduction", req, cb);
    }
    async CreateCloudStorageAIServiceTask(req, cb) {
        return this.request("CreateCloudStorageAIServiceTask", req, cb);
    }
    async DisableTopicRule(req, cb) {
        return this.request("DisableTopicRule", req, cb);
    }
    async UnbindDevices(req, cb) {
        return this.request("UnbindDevices", req, cb);
    }
    async ModifyCloudStorageAIService(req, cb) {
        return this.request("ModifyCloudStorageAIService", req, cb);
    }
    async ResumeWeCallDevice(req, cb) {
        return this.request("ResumeWeCallDevice", req, cb);
    }
    async GetStudioProductList(req, cb) {
        return this.request("GetStudioProductList", req, cb);
    }
    async BindCloudStorageUser(req, cb) {
        return this.request("BindCloudStorageUser", req, cb);
    }
    async DescribeTWeSeeRecognitionTask(req, cb) {
        return this.request("DescribeTWeSeeRecognitionTask", req, cb);
    }
    async DescribeBatchProduction(req, cb) {
        return this.request("DescribeBatchProduction", req, cb);
    }
    async CreateExternalSourceAIServiceTask(req, cb) {
        return this.request("CreateExternalSourceAIServiceTask", req, cb);
    }
    async ActivateTWeCallLicense(req, cb) {
        return this.request("ActivateTWeCallLicense", req, cb);
    }
    async UpdateDeviceTWeCallAuthorizeStatus(req, cb) {
        return this.request("UpdateDeviceTWeCallAuthorizeStatus", req, cb);
    }
    async DeleteCloudStorageEvent(req, cb) {
        return this.request("DeleteCloudStorageEvent", req, cb);
    }
    async DeleteDevices(req, cb) {
        return this.request("DeleteDevices", req, cb);
    }
    async CreateDeviceChannel(req, cb) {
        return this.request("CreateDeviceChannel", req, cb);
    }
    async CheckFirmwareUpdate(req, cb) {
        return this.request("CheckFirmwareUpdate", req, cb);
    }
    async CreateProject(req, cb) {
        return this.request("CreateProject", req, cb);
    }
    async GenerateCloudStorageAIServiceTaskFileURL(req, cb) {
        return this.request("GenerateCloudStorageAIServiceTaskFileURL", req, cb);
    }
    async DescribeCloudStorageOrder(req, cb) {
        return this.request("DescribeCloudStorageOrder", req, cb);
    }
    async PublishMessage(req, cb) {
        return this.request("PublishMessage", req, cb);
    }
    async DescribeDeviceDataHistory(req, cb) {
        return this.request("DescribeDeviceDataHistory", req, cb);
    }
    async ModifyPositionFence(req, cb) {
        return this.request("ModifyPositionFence", req, cb);
    }
    async InvokeTWeSeeRecognitionTask(req, cb) {
        return this.request("InvokeTWeSeeRecognitionTask", req, cb);
    }
    async DeleteDevice(req, cb) {
        return this.request("DeleteDevice", req, cb);
    }
    async GetDeviceSumStatistics(req, cb) {
        return this.request("GetDeviceSumStatistics", req, cb);
    }
    async DescribePositionFenceList(req, cb) {
        return this.request("DescribePositionFenceList", req, cb);
    }
    async DeleteLoRaFrequency(req, cb) {
        return this.request("DeleteLoRaFrequency", req, cb);
    }
    async DescribePackageConsumeTask(req, cb) {
        return this.request("DescribePackageConsumeTask", req, cb);
    }
    async ResetCloudStorageEvent(req, cb) {
        return this.request("ResetCloudStorageEvent", req, cb);
    }
    async DescribeCloudStorageAIServiceCallback(req, cb) {
        return this.request("DescribeCloudStorageAIServiceCallback", req, cb);
    }
    async ModifySpaceProperty(req, cb) {
        return this.request("ModifySpaceProperty", req, cb);
    }
    async DeleteTopicPolicy(req, cb) {
        return this.request("DeleteTopicPolicy", req, cb);
    }
    async GetDeviceList(req, cb) {
        return this.request("GetDeviceList", req, cb);
    }
    async ListTopicPolicy(req, cb) {
        return this.request("ListTopicPolicy", req, cb);
    }
    async DescribeCloudStorageAIServiceTask(req, cb) {
        return this.request("DescribeCloudStorageAIServiceTask", req, cb);
    }
    async InvokeVideosKeywordsAnalyzer(req, cb) {
        return this.request("InvokeVideosKeywordsAnalyzer", req, cb);
    }
    async DescribeInstance(req, cb) {
        return this.request("DescribeInstance", req, cb);
    }
    async DescribeCloudStorageStreamData(req, cb) {
        return this.request("DescribeCloudStorageStreamData", req, cb);
    }
    async PauseTWeCallDevice(req, cb) {
        return this.request("PauseTWeCallDevice", req, cb);
    }
    async GetAuthMiniProgramAppList(req, cb) {
        return this.request("GetAuthMiniProgramAppList", req, cb);
    }
    async DeletePositionFence(req, cb) {
        return this.request("DeletePositionFence", req, cb);
    }
    async ModifyCloudStorageAIServiceCallback(req, cb) {
        return this.request("ModifyCloudStorageAIServiceCallback", req, cb);
    }
    async DescribeCloudStorageAIServiceTasks(req, cb) {
        return this.request("DescribeCloudStorageAIServiceTasks", req, cb);
    }
    async SearchTopicRule(req, cb) {
        return this.request("SearchTopicRule", req, cb);
    }
    async DescribeCsReportCountDataInfo(req, cb) {
        return this.request("DescribeCsReportCountDataInfo", req, cb);
    }
    async DescribeCloudStorageEvents(req, cb) {
        return this.request("DescribeCloudStorageEvents", req, cb);
    }
    async ModifyModelDefinition(req, cb) {
        return this.request("ModifyModelDefinition", req, cb);
    }
    async GetFamilyDeviceUserList(req, cb) {
        return this.request("GetFamilyDeviceUserList", req, cb);
    }
    async DescribeProject(req, cb) {
        return this.request("DescribeProject", req, cb);
    }
    async DescribeModelDefinition(req, cb) {
        return this.request("DescribeModelDefinition", req, cb);
    }
    async DescribeTWeSeeConfig(req, cb) {
        return this.request("DescribeTWeSeeConfig", req, cb);
    }
    async CreatePositionSpace(req, cb) {
        return this.request("CreatePositionSpace", req, cb);
    }
    async DescribeDeviceFirmWare(req, cb) {
        return this.request("DescribeDeviceFirmWare", req, cb);
    }
    async CreateFenceBind(req, cb) {
        return this.request("CreateFenceBind", req, cb);
    }
    async DescribeCloudStoragePackageConsumeDetails(req, cb) {
        return this.request("DescribeCloudStoragePackageConsumeDetails", req, cb);
    }
}
